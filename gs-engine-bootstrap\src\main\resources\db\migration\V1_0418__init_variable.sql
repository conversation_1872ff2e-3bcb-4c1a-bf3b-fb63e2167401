CREATE TABLE IF NOT EXISTS `gs_variable` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `tenant_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '租户ID',
  `var_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '变量编码',
  `var_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '变量名称',
  `description` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '描述',
  `var_type` varchar(16) NOT NULL COMMENT '变量类型',
  `data_type` int NOT NULL COMMENT '数据类型',
  `default_value` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '默认值',
  `yn` int unsigned DEFAULT '0' COMMENT '删除状态:0-未删除，1-已删除',
  `is_system` int unsigned NOT NULL COMMENT '删除状态:0-自定义，1-系统',
  `create_id` bigint unsigned NOT NULL COMMENT '创建用户ID',
  `create_name` varchar(255) NOT NULL COMMENT '创建用户名称',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_id` bigint unsigned NOT NULL COMMENT '最后修改用户ID',
  `update_name` varchar(255) NOT NULL COMMENT '最后修改用户名称',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_var_code` (`var_code`),
  UNIQUE KEY `uk_var_name` (`tenant_id`,`var_name`,`yn`)
) ENGINE=InnoDB AUTO_INCREMENT=60 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='全局变量表';

-- telecom_ai_gs_engine_test.agent_ai_variable definition
CREATE TABLE IF NOT EXISTS `agent_ai_variable` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `tenant_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '租户ID',
  `agent_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'agent编码',
  `var_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '变量编码',
  `var_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '变量名称',
  `description` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '描述',
  `var_type` varchar(16) NOT NULL COMMENT '变量类型',
  `data_type` int NOT NULL COMMENT '数据类型',
  `default_value` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '默认值',
  `env` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '版本',
  `yn` int unsigned DEFAULT '0' COMMENT '删除状态:0-未删除，1-已删除',
  `create_id` bigint unsigned NOT NULL COMMENT '创建用户ID',
  `create_name` varchar(255) NOT NULL COMMENT '创建用户名称',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_id` bigint unsigned NOT NULL COMMENT '最后修改用户ID',
  `update_name` varchar(255) NOT NULL COMMENT '最后修改用户名称',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `version` bigint unsigned NOT NULL COMMENT '版本号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_var_code` (`var_code`,`env`),
  UNIQUE KEY `var_name_UN` (`agent_code`,`var_name`,`env`,`yn`)
) ENGINE=InnoDB AUTO_INCREMENT=59 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='机器人变量表';

-- telecom_ai_gs_engine_test.workflow_variable definition
CREATE TABLE IF NOT EXISTS `workflow_variable` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `app_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '应用编码',
  `workflow_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'workflow编码',
  `var_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '变量编码',
  `var_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '变量名称',
  `var_type` varchar(16) NOT NULL COMMENT '变量类型',
  `data_type` varchar(16) NOT NULL COMMENT '变量数据类型',
  `description` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '变量描述',
  `default_value` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '变量默认值',
  `env` varchar(16) NOT NULL COMMENT '版本',
  `yn` int unsigned DEFAULT '0' COMMENT '删除状态:0-未删除，1-已删除',
  `create_id` bigint unsigned NOT NULL COMMENT '创建用户ID',
  `create_name` varchar(255) NOT NULL COMMENT '创建用户名称',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_id` bigint unsigned NOT NULL COMMENT '最后修改用户ID',
  `update_name` varchar(255) NOT NULL COMMENT '最后修改用户名称',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `version` bigint unsigned NOT NULL COMMENT '版本号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `var_code_uk` (`var_code`,`env`),
  UNIQUE KEY `var_name_uk` (`workflow_id`,`var_name`,`env`,`yn`)
) ENGINE=InnoDB AUTO_INCREMENT=25 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='会话变量表';