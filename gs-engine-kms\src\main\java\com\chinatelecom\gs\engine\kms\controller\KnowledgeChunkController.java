package com.chinatelecom.gs.engine.kms.controller;

import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.common.config.aspect.annotation.DebugLog;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.kms.sdk.constants.KmsApis;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.chunk.*;
import com.chinatelecom.gs.engine.kms.service.KnowledgeChunkAppService;
import com.chinatelecom.gs.engine.sdk.common.annotation.ApiType;
import com.chinatelecom.gs.engine.sdk.common.annotation.HideFromApiTypes;
import com.chinatelelcom.gs.engine.sdk.common.Page;
import com.chinatelelcom.gs.engine.sdk.common.PageImpl;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024年12月12日
 */
@RestController
@Tag(name = "知识分片管理")
@RequestMapping({KmsApis.KMS_API + KmsApis.KNOWLEDGE + KmsApis.CHUNKS,
        KmsApis.RPC + KmsApis.KNOWLEDGE + KmsApis.CHUNKS,
        KmsApis.OPENAPI + KmsApis.KNOWLEDGE + KmsApis.CHUNKS})
public class KnowledgeChunkController {

    @Resource
    private KnowledgeChunkAppService knowledgeChunkAppService;

    @Operation(summary = "分页查询知识分片内容", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "分页查询知识分片内容", groupName = "知识分片管理")
    @AuditLog(businessType = "知识分片管理", operType = "分页查询知识分片内容", operDesc = "分页查询知识分片内容", objId="#knowledgeCode")
    @DebugLog(operation = "分页查询知识分片内容")
    @PostMapping(KmsApis.PAGE_API + KmsApis.KNOWLEDGE_CODE_PATH)
    public Result<PageImpl<ChunkDataVO>> pageChunks(@PathVariable("knowledgeCode") String knowledgeCode, @RequestBody ChunkQueryParam param) {
        param.setKnowledgeCode(knowledgeCode);
        param.setFilterDelFlag(false);
        PageImpl<ChunkDataVO> chunkDataVOPage = (PageImpl<ChunkDataVO>) knowledgeChunkAppService.pageChunks(param);
        return Result.success(chunkDataVOPage);
    }

    @Operation(summary = "更新知识分片", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "更新知识分片", groupName = "知识分片管理")
    @AuditLog(businessType = "知识分片管理", operType = "更新知识分片", operDesc = "更新知识分片", objId="#knowledgeCode")
    @DebugLog(operation = "更新知识分片")
    @PutMapping(KmsApis.KNOWLEDGE_CODE_PATH)
    public Result<Boolean> updateChunks(@PathVariable("knowledgeCode") String knowledgeCode, @RequestBody ChunkUpdateParam param) {
        param.setKnowledgeCode(knowledgeCode);
        knowledgeChunkAppService.updateChunks(param);
        return Result.success(true);
    }

    @Operation(summary = "删除知识分片", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "删除知识分片", groupName = "知识分片管理")
    @AuditLog(businessType = "知识分片管理", operType = "删除知识分片", operDesc = "删除知识分片", objId="#dto.knowledgeCode")
    @DebugLog(operation = "删除知识分片")
    @PostMapping(KmsApis.DELETE_API)
    public Result<Boolean> deleteChunk(@Validated @RequestBody ChunkDeleteParam dto) {
        knowledgeChunkAppService.deleteChunk(dto);
        return Result.success(true);
    }

    @Operation(summary = "知识分片开关", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "知识分片开关", groupName = "知识分片管理")
    @AuditLog(businessType = "知识分片管理", operType = "知识分片开关", operDesc = "知识分片开关", objId="#knowledgeCode")
    @DebugLog(operation = "知识分片开关")
    @PutMapping(KmsApis.SWITCH_API + KmsApis.KNOWLEDGE_CODE_PATH)
    public Result<Boolean> chunkSwitchOn(@PathVariable("knowledgeCode") String knowledgeCode, @RequestParam String chunkId, @RequestParam Boolean on) {
        knowledgeChunkAppService.chunkSwitchOn(knowledgeCode, chunkId, on);
        return Result.success(true);
    }

    @HideFromApiTypes(ApiType.OPENAPI)
    @Operation(summary = "文档分片详情对比", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "文档分片详情对比", groupName = "知识分片管理")
    @AuditLog(businessType = "知识分片管理", operType = "文档分片详情对比", operDesc = "文档分片详情对比", objId="#param.knowledgeCode")
    @PostMapping(KmsApis.COMPARE)
    public Result<Page<ChunkCompareDataVO>> compare(@Validated @RequestBody ChunkCompareQueryParam param) {
        return Result.success(knowledgeChunkAppService.compare(param));
    }

}
