package com.chinatelecom.gs.privilege.util;

import com.chinatelecom.gs.privilege.common.dto.GrantObjectDTO;
import com.chinatelecom.gs.privilege.common.dto.ResourceDTO;
import com.chinatelecom.gs.privilege.common.enums.PrivilegeEnum;
import com.chinatelecom.gs.privilege.common.enums.ResourceTypeEnum;
import com.chinatelecom.gs.privilege.util.dto.PrivilegeResourceResult;
import com.chinatelecom.gs.privilege.util.dto.PrivilegeResources;

import java.util.Collection;
import java.util.List;

/**
 * 资源操作接口，新增、删除资源时，将数据同步到权限表中
 */
public interface PrivilegeUtil {

    /**
     * 新建资源
     * @param resourceDTO
     * @return
     */
    Boolean addResource(ResourceDTO resourceDTO);

    /**
     * 修改资源公开性
     * @param resourceDTO
     * @return
     */
    Boolean editResource(ResourceDTO resourceDTO);

    /**
     * 删除资源
     * @param resourceDTO
     * @return
     */
    Boolean deleteResource(ResourceDTO resourceDTO);

    /**
     * 查询资源
     * @param resourceType
     * @param resourceIds
     * @return
     */
    List<ResourceDTO> queryResource(ResourceTypeEnum resourceType, Collection<String> resourceIds);

    /**
     * 判断当前用户是否有权限访问该类型的资源
     * @param resourceId 资源id
     * @param resourceType 资源类型
     * @return
     */
    GrantObjectDTO hasPrivilege(String resourceId, ResourceTypeEnum resourceType);

    /**
     * 获取当前用户有权限访问的资源id
     * 使用checkResourcePrivileges方法代替
     * @param resourceType
     * @return
     */
    @Deprecated
    PrivilegeResourceResult getPrivilegeResourceIds(ResourceTypeEnum resourceType);

    /**
     * 查询当前用户对resourceId中的资源的权限列表，如果为空，则查询当前用户有权限的资源列表。
     * @param resourceIds 需要筛选的资源id列表，可为空
     * @param resourceType 需要筛选的资源类型
     * @param minimumPrivilege 这批资源id需要满足的最小权限
     * @return
     */
    List<PrivilegeResources> checkResourcePrivileges(List<String> resourceIds, ResourceTypeEnum resourceType, PrivilegeEnum minimumPrivilege);

}
