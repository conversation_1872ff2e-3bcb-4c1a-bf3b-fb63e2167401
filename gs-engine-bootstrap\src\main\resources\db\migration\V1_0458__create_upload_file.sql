CREATE TABLE `file_upload` (
                               `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键 ID',
                               `code` varchar(64) DEFAULT NULL COMMENT 'code',
                               `name` varchar(255) DEFAULT NULL COMMENT '文件名',
                               `status` varchar(10) NOT NULL COMMENT '状态',
                               `total_part_num` int NOT NULL COMMENT '分片总个数',
                               `total_size` bigint DEFAULT NULL COMMENT '文件总大小',
                               `file_key` varchar(255) DEFAULT NULL COMMENT '文件路径',
                               `md5` varchar(64) DEFAULT NULL COMMENT 'MD5',
                               `part_size` int NOT NULL COMMENT '文件大小',
                               `upload_id` varchar(255) DEFAULT NULL COMMENT '文件上传ID',
                               `tenant_id` varchar(128) DEFAULT NULL COMMENT '租户 ID',
                               `create_id` varchar(100) DEFAULT NULL COMMENT '创建用户 ID',
                               `create_name` varchar(225) DEFAULT NULL COMMENT '创建用户名称',
                               `create_time` datetime DEFAULT NULL COMMENT '创建用户时间',
                               `update_id` varchar(255) DEFAULT NULL COMMENT '最后修改用户 ID',
                               `update_name` varchar(255) DEFAULT NULL COMMENT '最后修改用户名称',
                               `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                               `yn` int DEFAULT '0' COMMENT '删除标记，0-未删除，其他表示已删除',
                               PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=40 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='文件分片上传表';