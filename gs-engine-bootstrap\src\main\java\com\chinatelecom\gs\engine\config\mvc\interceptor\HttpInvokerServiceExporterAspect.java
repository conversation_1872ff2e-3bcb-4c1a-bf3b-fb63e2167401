package com.chinatelecom.gs.engine.config.mvc.interceptor;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024年09月19日
 */
@Component
@Aspect
@Slf4j
public class HttpInvokerServiceExporterAspect {


    /**
     * 设置AOP切点
     */
    @Pointcut("execution(* org.springframework.remoting.httpinvoker.HttpInvokerServiceExporter.handleRequest(..))")
    public void mypointcut() {
    }


    /**
     * 执行方法环绕
     *
     * @param point
     */
    @Around(value = "mypointcut()")
    public Object aroundHandleRequest(ProceedingJoinPoint point) throws Throwable {
        log.info("-----------禁用org.springframework.remoting.httpinvoker.HttpInvokerServiceExporter.handleRequest，防止CVE-2016-1000027安全漏洞------------");
        return null;
    }

}
