package com.chinatelecom.gs.engine.kms.service.impl;

import com.chinatelecom.gs.engine.common.utils.BizAssert;
import com.chinatelecom.gs.engine.common.utils.JsonUtils;
import com.chinatelecom.gs.engine.kms.dto.KnowledgeBaseDTO;
import com.chinatelecom.gs.engine.kms.sdk.enums.ExternalKnowledgeBaseFailureType;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.base.ExternalKnowledgeBaseConfig;
import com.chinatelecom.gs.engine.kms.sdk.vo.search.ExternalRecall;
import com.chinatelecom.gs.engine.kms.sdk.vo.search.ExternalSearchRecord;
import com.chinatelecom.gs.engine.kms.sdk.vo.search.ExternalSearchResult;
import com.chinatelecom.gs.engine.kms.service.ExternalKnowledgeBaseSearchService;
import com.chinatelecom.gs.engine.kms.util.ExternalKnowledgeBaseAuthHttpUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.*;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

/**
 * 外部知识库搜索服务实现
 */
@Service
@Slf4j
public class ExternalKnowledgeBaseSearchServiceImpl implements ExternalKnowledgeBaseSearchService {

    @Resource(name = "commonExecutorService")
    private ExecutorService commonExecutorService;

    @Override
    public List<ExternalSearchRecord> search(String knowledgeBaseCode, String query, ExternalKnowledgeBaseConfig config, Map<String, KnowledgeBaseDTO> knowledgeBaseDTOMap) {
        BizAssert.notEmpty(knowledgeBaseCode, "AA001", "知识库编码不能为空");
        BizAssert.notNull(config, "AA003", "外部知识库配置不能为空");
        BizAssert.notEmpty(config.getExternalUrl(), "AA004", "外部知识库URL不能为空");
        log.info("开始搜索外部知识库: knowledgeBaseCode={}, query={}", knowledgeBaseCode, query);
        try {
            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));

            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("knowledgeId", config.getExternalId());
            requestBody.put("query", query);

            Map<String, Object> retrievalSetting = new HashMap<>();
            retrievalSetting.put("topk", config.getTopN() != null ? config.getTopN() : 3);
            if (config.getThreshold() != null) {
                retrievalSetting.put("scoreThreshold", config.getThreshold());
            }
            requestBody.put("retrievalSetting", retrievalSetting);

            // 使用URI构建器，用于处理URL参数
            UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromHttpUrl(config.getExternalUrl());

            // 将知识库编码设置到外部知识库配置中，用于OAuth鉴权
            if (StringUtils.isEmpty(config.getExternalId())) {
                config.setExternalId(knowledgeBaseCode);
            }

            // 使用通用鉴权工具添加鉴权信息和公共头信息
            headers = ExternalKnowledgeBaseAuthHttpUtils.addAuthInfo(config, headers, uriBuilder);

            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);
            String url = uriBuilder.build().toUriString();

            log.info("外部知识库搜索请求: url={}, body={}, headers={}", url, JsonUtils.toJsonString(requestBody), headers);

            HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory();
            factory.setConnectTimeout(3000);
            // Manual migration to `SocketConfig.Builder.setSoTimeout(Timeout)` necessary; see: https://docs.spring.io/spring-framework/docs/6.0.0/javadoc-api/org/springframework/http/client/HttpComponentsClientHttpRequestFactory.html#setReadTimeout(int)
            factory.setReadTimeout(5000);

            RestTemplate template = new RestTemplate(factory);

            // 添加自定义的MappingJackson2HttpMessageConverter
            MappingJackson2HttpMessageConverter converter = new MappingJackson2HttpMessageConverter();
            converter.setSupportedMediaTypes(Arrays.asList(
                MediaType.APPLICATION_JSON,
                MediaType.TEXT_HTML,  // 添加对text/html的支持
                MediaType.APPLICATION_OCTET_STREAM
            ));

            // 移除默认的转换器，添加自定义的
            template.setMessageConverters(Collections.singletonList(converter));

            ResponseEntity<ExternalSearchResult> responseEntity = template.exchange(
                    url,
                    HttpMethod.POST,
                    requestEntity,
                    ExternalSearchResult.class
            );

            if (responseEntity.getStatusCode() == HttpStatus.OK && responseEntity.getBody() != null) {
                ExternalSearchResult result = responseEntity.getBody();
                log.info("外部知识库搜索响应: {}", JsonUtils.toJsonString(result));

                if (!"0000000".contains(result.getCode())) {
                    log.error("外部知识库搜索失败: code={}, message={}", result.getCode(), result.getMessage());
                    config.setFailReason(ExternalKnowledgeBaseFailureType.API_ERROR);
                    return Collections.emptyList();
                }

                if (result.getData() != null && CollectionUtils.isNotEmpty(result.getData().getRecords())) {
                    // 清除失败信息
                    config.setFailReason(null);
                    List<ExternalSearchRecord> records = result.getData().getRecords();
                    // 添加知识库信息到元数据
                    if (CollectionUtils.isNotEmpty(records)) {
                        for (ExternalSearchRecord record : records) {
                            if (record.getMetadata() == null) {
                                record.setMetadata(new HashMap<>());
                            }
                            KnowledgeBaseDTO knowledgeBaseDTO = knowledgeBaseDTOMap.get(knowledgeBaseCode);
                            record.getMetadata().put("knowledgeBaseCode", knowledgeBaseDTO.getCode());
                            record.getMetadata().put("knowledgeBaseName", knowledgeBaseDTO.getName());
                        }
                    }
                    return records;
                }
            }

            config.setFailReason(ExternalKnowledgeBaseFailureType.EMPTY_RESULT);
            log.warn("外部知识库返回结果为空");
            return Collections.emptyList();
        } catch (Exception e) {
            log.error("外部知识库搜索异常: knowledgeBaseCode={}, query={}, error={}", knowledgeBaseCode, query, e.getMessage(), e);

            ExternalKnowledgeBaseFailureType failureType;
            Throwable rootCause = getRootCause(e);

            if (rootCause instanceof java.net.ConnectException || rootCause instanceof java.net.UnknownHostException) {
                failureType = ExternalKnowledgeBaseFailureType.NETWORK_ERROR;
            } else if (rootCause instanceof java.net.SocketTimeoutException) {
                failureType = ExternalKnowledgeBaseFailureType.TIMEOUT_ERROR;
            } else if (e.getMessage() != null && e.getMessage().contains("401")) {
                failureType = ExternalKnowledgeBaseFailureType.AUTH_ERROR;
            } else if (e.getMessage() != null && e.getMessage().contains("404")) {
                failureType = ExternalKnowledgeBaseFailureType.NOT_FOUND;
            } else if (e.getMessage() != null && e.getMessage().contains("500")) {
                failureType = ExternalKnowledgeBaseFailureType.SERVER_ERROR;
            } else {
                failureType = ExternalKnowledgeBaseFailureType.UNKNOWN_ERROR;
            }

            config.setFailReason(failureType);
            return Collections.emptyList();
        }
    }


    /**
     * 异步搜索外部知识库
     *
     * @param knowledgeBaseCode   知识库编码
     * @param query               查询内容
     * @param config              外部知识库配置
     * @param knowledgeBaseDTOMap
     * @return 外部知识库搜索结果的CompletableFuture
     */
    @Async("commonExecutorService")
    @Override
    public CompletableFuture<ExternalRecall> searchAsync(String knowledgeBaseCode, String query, ExternalKnowledgeBaseConfig config, Map<String, KnowledgeBaseDTO> knowledgeBaseDTOMap) {
        return CompletableFuture.supplyAsync(() -> {
            List<ExternalSearchRecord> records = search(knowledgeBaseCode, query, config, knowledgeBaseDTOMap);
            ExternalRecall recall = new ExternalRecall();
            recall.setRecallRecords(records);
            KnowledgeBaseDTO knowledgeBaseDTO = knowledgeBaseDTOMap.get(knowledgeBaseCode);
            if (knowledgeBaseDTO != null) {
                recall.setKnowledgeBaseCode(knowledgeBaseDTO.getCode());
                recall.setKnowledgeBaseName(knowledgeBaseDTO.getName());

            }
            return recall;
        }, commonExecutorService);
    }

    @Override
    public List<ExternalRecall> searchConcurrently(String query, Map<String, ExternalKnowledgeBaseConfig> configMap, Map<String, KnowledgeBaseDTO> knowledgeBaseDTOMap) {
        if (configMap == null || configMap.isEmpty()) {
            return Collections.emptyList();
        }

        // 创建异步任务列表
        List<CompletableFuture<ExternalRecall>> futures = new ArrayList<>();

        for (Map.Entry<String, ExternalKnowledgeBaseConfig> entry : configMap.entrySet()) {
            String knowledgeBaseCode = entry.getKey();
            ExternalKnowledgeBaseConfig config = entry.getValue();

            CompletableFuture<ExternalRecall> future = searchAsync(knowledgeBaseCode, query, config, knowledgeBaseDTOMap)
                    .exceptionally(throwable -> {
                        // 发生异常时，设置失败类型
                        ExternalKnowledgeBaseFailureType failureType;
                        if (throwable.getCause() instanceof java.net.ConnectException
                                || throwable.getCause() instanceof java.net.UnknownHostException) {
                            failureType = ExternalKnowledgeBaseFailureType.NETWORK_ERROR;
                        } else if (throwable.getCause() instanceof java.net.SocketTimeoutException) {
                            failureType = ExternalKnowledgeBaseFailureType.TIMEOUT_ERROR;
                        } else if (throwable.getMessage() != null && throwable.getMessage().contains("401")) {
                            failureType = ExternalKnowledgeBaseFailureType.AUTH_ERROR;
                        } else {
                            failureType = ExternalKnowledgeBaseFailureType.UNKNOWN_ERROR;
                        }
                        config.setFailReason(failureType);
                        log.error("外部知识库搜索异常: knowledgeBaseCode={}, failureType={}", knowledgeBaseCode, failureType, throwable);
                        return null;
                    });
            futures.add(future);
        }

        // 等待所有任务完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        // 收集所有结果
        List<ExternalRecall> allRecords = futures.stream()
                .map(CompletableFuture::join)
                .collect(Collectors.toList());
        return allRecords;
    }

    /**
     * 构建HTTP请求头，包括鉴权信息
     *
     * @param config 外部知识库配置
     * @return HTTP请求头
     */
    private HttpHeaders buildHeaders(ExternalKnowledgeBaseConfig config) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        // 使用URI构建器，用于处理URL参数
        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromHttpUrl(config.getExternalUrl());

        // 使用通用鉴权工具添加鉴权信息和公共头信息
        headers = ExternalKnowledgeBaseAuthHttpUtils.addAuthInfo(config, headers, uriBuilder);

        return headers;
    }

    private Throwable getRootCause(Throwable throwable) {
        while (throwable.getCause() != null && throwable.getCause() != throwable) {
            throwable = throwable.getCause();
        }
        return throwable;
    }
}
