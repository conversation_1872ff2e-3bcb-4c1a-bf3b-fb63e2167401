package com.chinatelecom.gs.engine.channel.common.utils;

import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import lombok.extern.slf4j.Slf4j;

import static com.fasterxml.jackson.databind.PropertyNamingStrategies.UPPER_CAMEL_CASE;
import static com.fasterxml.jackson.dataformat.xml.ser.ToXmlGenerator.Feature.WRITE_XML_DECLARATION;

/**
 * <AUTHOR>
 * @date 2023/12/22 15:10
 * @description
 */
@Slf4j
public class XmlUtil {

    private static XmlMapper xmlMapper = new XmlMapper();

    static {
        xmlMapper.setPropertyNamingStrategy(UPPER_CAMEL_CASE);
        xmlMapper.configure(WRITE_XML_DECLARATION, false);
    }

    public static String xmlToString(Object obj) {
        try {
            return xmlMapper.writeValueAsString(obj);
        }  catch (JsonProcessingException e) {
            log.error("转换xml对象失败，{}", obj, e);
            throw new BizException("转换xml数据失败");
        }
    }

    public static <T> T readObj(String xmlString, Class<T> clazz) {
        try {
            return xmlMapper.readValue(xmlString, clazz);
        } catch (Exception e) {
            log.error("转换xml字符串到对象失败, {}", xmlString, e);
            throw new BizException("转换xml字符串到对象失败");
        }
    }
}
