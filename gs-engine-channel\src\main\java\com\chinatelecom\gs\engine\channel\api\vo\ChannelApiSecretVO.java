package com.chinatelecom.gs.engine.channel.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/8/24 10:48
 */
@Data
public class ChannelApiSecretVO {
//    @ApiModelProperty(value = "密钥名称", required = true)
//    private String secretName;
//    @ApiModelProperty(value = "密钥ID", required = true)
//    private String secretId;
//    @ApiModelProperty(value = "密钥", required = true)
//    private String secret;
    @Schema(description = "应用code", required = true)
    private String agentCode;
    @Schema(description = "访问令牌", required = true)
    private String accessToken;
}
