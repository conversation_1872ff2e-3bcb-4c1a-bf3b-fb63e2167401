package com.chinatelecom.gs.engine.channel.dao.convert;

import com.chinatelecom.gs.engine.channel.dao.po.ChannelApiSecretPO;
import com.chinatelecom.gs.engine.channel.service.dto.ChannelApiSecretDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * @author: xktang
 * @date: 2024/8/1 上午10:10
 * @version: 1.0
 */
@Mapper
public interface ChannelConvert {
    ChannelConvert INSTANCE = Mappers.getMapper(ChannelConvert.class);

    ChannelApiSecretDTO po2dto(ChannelApiSecretPO po);
}
