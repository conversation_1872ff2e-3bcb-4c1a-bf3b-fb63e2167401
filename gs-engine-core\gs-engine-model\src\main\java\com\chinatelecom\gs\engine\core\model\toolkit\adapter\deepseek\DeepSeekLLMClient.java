package com.chinatelecom.gs.engine.core.model.toolkit.adapter.deepseek;


import com.alibaba.fastjson2.JSON;
import com.chinatelecom.gs.engine.common.enums.LogStatusEnum;
import com.chinatelecom.gs.engine.core.model.toolkit.AbstractLLMClient;
import com.chinatelecom.gs.engine.core.model.toolkit.ContentLLMResponse;
import com.chinatelecom.gs.engine.core.model.toolkit.StreamingResponseHandler;
import com.chinatelecom.gs.engine.core.model.toolkit.core.dto.*;
import com.chinatelecom.gs.engine.core.model.toolkit.core.internal.URLUtils;
import com.chinatelecom.gs.engine.core.sdk.vo.llm.*;
import com.google.gson.FieldNamingPolicy;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import lombok.Builder;
import lombok.SneakyThrows;
import okhttp3.OkHttpClient;
import okhttp3.ResponseBody;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.MalformedURLException;
import java.net.URL;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;

public class DeepSeekLLMClient extends AbstractLLMClient {

    private final static String THINK_START = "<think>";
    private final static String THINK_END = "</think>";

    private static final String toolCallStartToken = "<tool_call>";

    private static final String toolCallEndToken = "</tool_call>";

    private final DeepSeekApi deepSeekApi;

    private String appKey;

    private String appSign;

    private String url;

    private static final Gson GSON = new GsonBuilder().excludeFieldsWithModifiers().setFieldNamingPolicy(FieldNamingPolicy.LOWER_CASE_WITH_UNDERSCORES).create();

    private static final Logger log = LoggerFactory.getLogger(DeepSeekLLMClient.class);

    @SneakyThrows
    @Builder
    public DeepSeekLLMClient(Model.ModelConfig modelConfig) {
        OkHttpClient okHttpClient = buildOkHttpClient(modelConfig);

        String[] splitUrl = URLUtils.splitURL(modelConfig.getBaseUrl());

        Retrofit retrofit = new Retrofit.Builder()
                .baseUrl(splitUrl[0])
                .client(okHttpClient)
                .addConverterFactory(GsonConverterFactory.create(GSON))
                .build();

        this.deepSeekApi = retrofit.create(DeepSeekApi.class);
        this.appKey = modelConfig.getApi();
        this.appSign = modelConfig.getSecret();
        this.url = new URL(modelConfig.getBaseUrl()).toString();
        this.modelConfig = modelConfig;
    }


    @Override
    public <R> Response<String> completion(LLMRequest<R> request) {
        DeepSeekRequest deepSeekRequest = toRequest(request, false);
        DeepSeekResponse response = innerCompletion(deepSeekRequest);
        return Response.from(getContent(response));
    }


    @Override
    public <R> Response<WrapLLMMessage> completionMessage(LLMRequest<R> request) {
        DeepSeekRequest deepSeekRequest = toRequest(request, false);
        DeepSeekResponse response = innerCompletion(deepSeekRequest);
        return Response.from(getMessage(response));
    }

    @Override
    public <R> void streamingCompletion(LLMRequest<R> request, StreamingResponseHandler<String> handler) {
        DeepSeekRequest deepSeekRequest = toRequest(request, true);
        this.innerStreamingCompletion(deepSeekRequest, handler);
    }


    @Override
    public <R> void streamingCompletionMessage(LLMRequest<R> request, StreamingResponseHandler<WrapLLMMessage> handler) {
        DeepSeekRequest deepSeekRequest = toRequest(request, true);
        this.innerStreamingMessageCompletion(deepSeekRequest, handler);
    }

    @Override
    @SneakyThrows
    public <R> void streamingDirectCompletionMessage(LLMRequest<R> request, StreamingResponseHandler<WrapLLMMessage> handler) {
        DeepSeekDirectRequest deepSeekRequest = toDirectRequest(request, true);
        this.innerDirectStreamCompletion(deepSeekRequest, handler);
    }


    private void innerDirectStreamCompletion(DeepSeekDirectRequest request, StreamingResponseHandler<WrapLLMMessage> handler) throws MalformedURLException {
        URL urlObject = new URL(url);
        // 获取协议、主机、端口和文件之前的路径
        String prefix = urlObject.getProtocol() + "://" + urlObject.getHost();
        if (urlObject.getPort() > -1) {
            prefix += ":" + urlObject.getPort();
        }
        deepSeekApi.streamingCompletion(prefix + "/generate", this.appKey, this.appSign, request).enqueue(new Callback<ResponseBody>() {
            @Override
            public void onResponse(Call<ResponseBody> call, retrofit2.Response<ResponseBody> response) {
                StringBuilder content = new StringBuilder();
                try (InputStream is = response.body().byteStream()) {
                    while (true) {
                        byte[] bytes = new byte[1024];
                        int len = is.read(bytes);
                        if (len <= 0) {
                            log.info("===========流式输出结束===========");
                            WrapLLMMessage wrapLLMMessage = new WrapLLMMessage();
                            wrapLLMMessage.setContent(content.toString());
                            wrapLLMMessage.setRole(MessageRoleEnum.assistant.getCode());
                            handler.onComplete(Response.from(wrapLLMMessage));
                            break;
                        }
                        String partialResponse = new String(bytes, 0, len);
                        log.debug("【llm】流式结果 {}",partialResponse);

                        String responseLine = null;
                        try {
                            DeepSeekDirectResponse directResponse = JSON.parseObject(partialResponse, DeepSeekDirectResponse.class);
                            responseLine = directResponse.getText().get(0);
                        } catch (Exception e) {
                            log.error("gson fail ", e);
                            continue;
                        }
                        // handle cur token and tokenUsage
                        content.append(responseLine);

                        WrapLLMMessage wrapLLMMessage = new WrapLLMMessage();
                        wrapLLMMessage.setContent(content.toString());
                        wrapLLMMessage.setRole(MessageRoleEnum.assistant.getCode());

                        handler.onNext(call, wrapLLMMessage);
                    }
                } catch (Exception e) {
                    WrapLLMMessage wrapLLMMessage = new WrapLLMMessage();
                    wrapLLMMessage.setContent(content.toString());
                    wrapLLMMessage.setRole(MessageRoleEnum.assistant.getCode());
                    handler.onError(e, Response.from(wrapLLMMessage));
                }
            }

            @Override
            public void onFailure(Call<ResponseBody> call, Throwable throwable) {
                handler.onError(throwable, null);
            }
        });
    }


    private <R> DeepSeekDirectRequest toDirectRequest(LLMRequest<R> request, boolean stream) {
        DeepSeekDirectRequest directRequest = new DeepSeekDirectRequest();
        directRequest.setStream(stream);
        directRequest.setTemperature(request.getTemperature());
        directRequest.setModel(request.getLlmModelInfo().getModelName());
        if (Objects.nonNull(request.getRequestMessage())) {
            directRequest.setPrompt(request.getRequestMessage().getContent());
        } else {
            directRequest.setPrompt(request.getText());
        }
        directRequest.setTop_p(request.getTopp());
        log.info("【llm】 直连推理服务请求 {}", GSON.toJson(directRequest));
        return directRequest;
    }


    private <R> DeepSeekRequest toRequest(LLMRequest<R> request, boolean stream) {
        DeepSeekRequest deepSeekRequest = new DeepSeekRequest();
        deepSeekRequest.setStream(stream);
        deepSeekRequest.setTemperature(request.getTemperature());
        List<DeepSeekMessage> messages = new ArrayList<>();
        ChatTemplateKwargs chatTemplateKwargs = new ChatTemplateKwargs();
        chatTemplateKwargs.setEnable_thinking(request.isEnableThinking());
        deepSeekRequest.setChat_template_kwargs(chatTemplateKwargs);

        if (!CollectionUtils.isEmpty(request.getHistories())) {
            for (LLMMessage history : request.getHistories()) {
                DeepSeekMessage message = new DeepSeekMessage();
                message.setRole(history.getRole());
                message.setContent(history.getContent());
                if (message.getRole().equalsIgnoreCase(MessageRoleEnum.tool.getCode())) {
                    message.setTool_call_id(history.getTool_call_id());
                }
                if (!CollectionUtils.isEmpty(history.getToolCalls())) {
                    message.setTool_calls(history.getToolCalls());
                }
                messages.add(message);
            }
        }

        if (request.getRequestMessage() != null) {
            LLMMessage msg = request.getRequestMessage();

            DeepSeekMessage message = new DeepSeekMessage();
            message.setRole(msg.getRole());
            message.setContent(msg.getContent());
            if (message.getRole().equalsIgnoreCase(MessageRoleEnum.tool.getCode())) {
                message.setTool_call_id(msg.getTool_call_id());
            } else if (message.getRole().equalsIgnoreCase(MessageRoleEnum.user.getCode())) {
                if (StringUtils.isNotBlank(request.getSystemSettings())) {
                    message.setContent(request.getSystemSettings() + "\n\n" + msg.getContent());
                } else {
                    message.setContent(msg.getContent());
                }
            }
            if (!CollectionUtils.isEmpty(msg.getToolCalls())) {
                message.setTool_calls(msg.getToolCalls());
            }
            messages.add(message);
        } else {

            DeepSeekMessage current = new DeepSeekMessage();
            if (StringUtils.isNotBlank(request.getSystemSettings())) {
                current.setContent(request.getSystemSettings() + "\n\n" + request.getText());
            } else {
                current.setContent(request.getText());
            }
            current.setRole(MessageRoleEnum.user.getCode());
            messages.add(current);
        }

//        if (!CollectionUtils.isEmpty(request.getTools())) {
//            deepSeekRequest.setTools(request.getTools());
//        }
        if (!CollectionUtils.isEmpty(request.getTools())) {
            deepSeekRequest.setTools(request.getTools());
        }
        deepSeekRequest.setModel(request.getLlmModelInfo().getModelName());
        deepSeekRequest.setMessages(messages);
        deepSeekRequest.setTemperature(request.getTemperature());
        deepSeekRequest.setTop_p(request.getTopp());
        log.info("【llm】 推理服务请求 {}", GSON.toJson(deepSeekRequest));
        return deepSeekRequest;
    }


    private DeepSeekResponse innerCompletion(DeepSeekRequest request) {
        LocalDateTime startTime = LocalDateTime.now();
        try {
            retrofit2.Response<DeepSeekResponse> response = deepSeekApi.chatCompletion(this.url, this.appKey, this.appSign, request).execute();
            sendTraceMessage(request, response.body(), LogStatusEnum.SUCCESS, startTime, LocalDateTime.now(), null);
            return response.body();
        } catch (Exception e) {
            sendTraceMessage(request, null, LogStatusEnum.FAILED, startTime, LocalDateTime.now(), null);
            throw new RuntimeException(e);
        }
    }

    private void innerStreamingCompletion(DeepSeekRequest request, StreamingResponseHandler<String> handler) {
        AtomicReference<LocalDateTime> firstTokenTimeRef = new AtomicReference<>();
        LocalDateTime startTime = LocalDateTime.now();
        deepSeekApi.streamingChatCompletion(this.url, this.appKey, this.appSign, request).enqueue(new Callback<ResponseBody>() {
            @Override
            public void onResponse(Call<ResponseBody> call, retrofit2.Response<ResponseBody> response) {
                StringBuilder content = new StringBuilder();
                StringBuilder think = new StringBuilder();
                Integer promptTokens = null;
                Integer completionTokens = null;
                boolean isThink = true;
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(response.body().byteStream()))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        if (StringUtils.isNotEmpty(line)) {
                            line = line.replaceFirst("data: ", "");
                            log.debug("【原始输出】 {}", line);
                            if (Objects.isNull(firstTokenTimeRef.get())) {
                                firstTokenTimeRef.set(LocalDateTime.now());
                            }
                            if (!line.equalsIgnoreCase("[DONE]")) {
                                DeepSeekResponse deepSeekResponse = null;
                                try {
                                    deepSeekResponse = GSON.fromJson(line, DeepSeekResponse.class);
                                } catch (Exception e) {
                                    log.error("gson fail", e);
                                    continue;
                                }
                                if(Objects.nonNull(deepSeekResponse)){
                                    if(Objects.nonNull(deepSeekResponse.promptTokens())){
                                        promptTokens = deepSeekResponse.promptTokens();
                                    }
                                    if(Objects.nonNull(deepSeekResponse.completionTokens())){
                                        completionTokens = deepSeekResponse.completionTokens();
                                    }
                                }
                                String partialContent = getStreamContent(deepSeekResponse);

                                if (!StringUtils.isEmpty(partialContent)) {

                                    // 思考内容
                                    if (StringUtils.startsWith(partialContent.trim(), THINK_START)) {
                                        isThink = true;
                                        partialContent = StringUtils.removeStart(partialContent.trim(), THINK_START);
                                    } else if (StringUtils.equals(partialContent.trim(), THINK_END)) {
                                        isThink = false;
                                        continue;
                                    }

                                    if (isThink) {
                                        think.append(partialContent);
                                    } else {
                                        // 目前只推送结果内容，不推送思考内容
                                        content.append(partialContent);
                                        handler.onNext(call, partialContent);
                                    }
                                }
                            }
                        }

                    }
                    log.info("===========流式输出结束===========");
                    log.info("思考内容：{}", think);
                    log.info("结果内容：{}", content);
                    handler.onComplete(Response.from(content.toString()));
                    sendTraceMessage(request, new ContentLLMResponse(think.toString() + content.toString(),promptTokens, completionTokens), LogStatusEnum.SUCCESS, startTime, LocalDateTime.now(), firstTokenTimeRef.get());
                } catch (IOException e) {
                    sendTraceMessage(request, new ContentLLMResponse(think.toString() + content.toString(),promptTokens, completionTokens), LogStatusEnum.FAILED, startTime, LocalDateTime.now(), firstTokenTimeRef.get());
                    handler.onError(e, Response.from(content.toString()));
                }
            }

            @Override
            public void onFailure(Call<ResponseBody> call, Throwable throwable) {
                handler.onError(throwable, null);
            }
        });
    }


    private void innerStreamingMessageCompletion(DeepSeekRequest request, StreamingResponseHandler<WrapLLMMessage> handler) {
        deepSeekApi.streamingChatCompletion(this.url, this.appKey, this.appSign, request).enqueue(new Callback<ResponseBody>() {
            @Override
            public void onResponse(Call<ResponseBody> call, retrofit2.Response<ResponseBody> response) {
                AtomicReference<LocalDateTime> firstTokenTimeRef = new AtomicReference<>();
                LocalDateTime startTime = LocalDateTime.now();
                StringBuilder content = new StringBuilder();
                StringBuilder think = new StringBuilder();
                StringBuilder tool = new StringBuilder();
                boolean isThink = true;
                boolean isTool = false;
                WrapLLMMessage lastMessage = null;

                try (BufferedReader reader = new BufferedReader(new InputStreamReader(response.body().byteStream()))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        if (StringUtils.isNotEmpty(line)) {
                            line = line.replaceFirst("data: ", "");
                            log.debug("【原始输出】 {}", line);
                            if (Objects.isNull(firstTokenTimeRef.get())) {
                                firstTokenTimeRef.set(LocalDateTime.now());
                            }
                            if (!line.equalsIgnoreCase("[DONE]")) {
                                DeepSeekResponse deepSeekResponse = null;
                                try {
                                    deepSeekResponse = GSON.fromJson(line, DeepSeekResponse.class);
                                } catch (Exception e) {
                                    log.error("gson fail ", e);
                                    continue;
                                }
                                String partialContent = getStreamContent(deepSeekResponse);
                                if (!StringUtils.isEmpty(partialContent)) {

                                    // 思考内容
                                    if (StringUtils.startsWith(partialContent.trim(), THINK_START)) {
                                        isThink = true;
                                        partialContent = StringUtils.removeStart(partialContent.trim(), THINK_START);
                                    } else if (StringUtils.equals(partialContent.trim(), THINK_END)) {
                                        isThink = false;
                                        continue;
                                    }

                                    if (isThink) {
                                        think.append(partialContent);
                                    } else {
                                        content.append(partialContent);
                                    }

                                    if (StringUtils.startsWith(partialContent.trim(), toolCallStartToken)) {
                                        isTool = true;
                                        partialContent = StringUtils.removeStart(partialContent.trim(), toolCallStartToken);
                                    } else if (StringUtils.equals(partialContent.trim(), toolCallEndToken)) {
                                        isTool = false;
                                        continue;
                                    }

                                    if (isTool) {
                                        tool.append(partialContent);
                                    } else {
                                        String toolStr= tool.toString();
                                        ToolCall toolCall = null;
                                        if(StringUtils.isNotEmpty(toolStr)){
                                            Function function = JSON.parseObject(toolStr.trim(), Function.class);
                                            toolCall = new ToolCall();
                                            toolCall.setFunction(function);
                                        }
                                        WrapLLMMessage llmMessage = getStreamMessage(deepSeekResponse, toolCall, isThink, partialContent);
                                        if (llmMessage != null) {
                                            lastMessage = appendLastMessage(lastMessage, llmMessage);
                                            handler.onNext(call, llmMessage);
                                        }
                                    }
                                }
                            }
                        }
                    }
                    log.info("===========流式输出结束===========");
                    log.info("思考内容：{}", think);
                    log.info("结果内容：{}", content);
                    if (Objects.nonNull(lastMessage)) {
                        lastMessage.setContent(content.toString());
                        lastMessage.setReasoning_content(think.toString());
                        if (StringUtils.isEmpty(lastMessage.getRole())) {
                            lastMessage.setRole(MessageRoleEnum.assistant.getCode());
                        }
                    }
                    sendTraceMessage(request, new ContentLLMResponse(think.toString() + content.toString()), LogStatusEnum.SUCCESS, startTime, LocalDateTime.now(), firstTokenTimeRef.get());
                    handler.onComplete(Response.from(lastMessage));
                } catch (Exception e) {
                    log.error("llm innerStreamingMessageCompletion fail", e);
                    sendTraceMessage(request, new ContentLLMResponse(think.toString() + content.toString()), LogStatusEnum.FAILED, startTime, LocalDateTime.now(), firstTokenTimeRef.get());
                    handler.onError(e, Response.from(lastMessage));
                }
            }

            @Override
            public void onFailure(Call<ResponseBody> call, Throwable throwable) {
                handler.onError(throwable, null);
            }
        });
    }

    private WrapLLMMessage getStreamMessage(DeepSeekResponse deepSeekResponse, ToolCall toolCall, boolean isThink, String partialContent) {

        if (CollectionUtils.isEmpty(deepSeekResponse.getChoices())) {
            return null;
        }

        DeepSeekResponse.Choice choice = deepSeekResponse.getChoices().get(0);

        if (Objects.isNull(choice.getDelta())) {
            return null;
        }
        DeepSeekMessage deepSeekMessage = choice.getDelta();


        WrapLLMMessage llmMessage = new WrapLLMMessage();
        llmMessage.setRole(deepSeekMessage.getRole());
        if (Objects.nonNull(toolCall)) {
            llmMessage.setToolCalls(Collections.singletonList(toolCall));
        } else {
            llmMessage.setToolCalls(deepSeekMessage.getTool_calls());
        }
        if (isThink) {
            llmMessage.setReasoning_content(partialContent);
        } else {
            llmMessage.setContent(partialContent);
        }
        return llmMessage;
    }

    private String getContent(DeepSeekResponse deepSeekResponse) {
        if (CollectionUtils.isEmpty(deepSeekResponse.getChoices())) {
            return "";
        }

        DeepSeekResponse.Choice choice = deepSeekResponse.getChoices().get(0);

        if (Objects.isNull(choice.getMessage())) {
            return "";
        }

        String content = choice.getMessage().getContent();
        return parseContent(content).getRight();
    }

    /**
     * 解析内容分离思考内容和回答内容， left：思考内容  right:回答内容
     *
     * @param content
     * @return
     */
    private static Pair<String, String> parseContent(String content) {
        String think = null;
        String result = null;
        if (StringUtils.contains(content, THINK_END)) {
            String[] contentArray = StringUtils.splitByWholeSeparator(content, THINK_END, 2);
            if (contentArray != null && contentArray.length > 1) {
                think = StringUtils.trim(contentArray[0]);
                result = contentArray[1];
                if (StringUtils.startsWith(think, THINK_START)) {
                    think = StringUtils.removeStart(think, THINK_START);
                }
                return Pair.of(think, result);
            }
        }
        return Pair.of(StringUtils.EMPTY, content);
    }


    private WrapLLMMessage getMessage(DeepSeekResponse deepSeekResponse) {
        if (CollectionUtils.isEmpty(deepSeekResponse.getChoices())) {
            return null;
        }

        DeepSeekResponse.Choice choice = deepSeekResponse.getChoices().get(0);

        if (Objects.isNull(choice.getMessage())) {
            return null;
        }
        DeepSeekMessage deepSeekMessage = choice.getMessage();
        String content = deepSeekMessage.getContent();

        WrapLLMMessage llmMessage = new WrapLLMMessage();
        llmMessage.setRole(deepSeekMessage.getRole());
        llmMessage.setToolCalls(deepSeekMessage.getTool_calls());
        Pair<String, String> pair = parseContent(content);
        llmMessage.setReasoning_content(pair.getLeft());
        llmMessage.setContent(pair.getRight());
        return llmMessage;
    }


    private String getStreamContent(DeepSeekResponse deepSeekResponse) {
        if (deepSeekResponse == null || CollectionUtils.isEmpty(deepSeekResponse.getChoices())) {
            return "";
        }

        DeepSeekResponse.Choice choice = deepSeekResponse.getChoices().get(0);

        if (Objects.isNull(choice.getDelta())) {
            return "";
        }

        return choice.getDelta().getContent();
    }

    private WrapLLMMessage appendLastMessage(WrapLLMMessage lastMessage, WrapLLMMessage currentMessage) {
        if (Objects.isNull(lastMessage)) {
            lastMessage = currentMessage;
        }

        if (!StringUtils.isEmpty(currentMessage.getRole())) {
            lastMessage.setRole(currentMessage.getRole());
        }

        if (!CollectionUtils.isEmpty(currentMessage.getToolCalls())) {
            if (CollectionUtils.isEmpty(lastMessage.getToolCalls())) {
                lastMessage.setToolCalls(currentMessage.getToolCalls());
            } else {
                ToolCall current = currentMessage.getToolCalls().get(0);
                Function currentFunction = current.getFunction();

                ToolCall last = lastMessage.getToolCalls().get(0);
                Function lastFunction = last.getFunction();
                if (StringUtils.isEmpty(lastFunction.getArguments())) {
                    lastFunction.setArguments(currentFunction.getArguments());
                } else {
                    lastFunction.setArguments(lastFunction.getArguments() + currentFunction.getArguments());
                }
                last.setFunction(lastFunction);

                lastMessage.setToolCalls(Collections.singletonList(last));
            }
        }
        return lastMessage;
    }

}
