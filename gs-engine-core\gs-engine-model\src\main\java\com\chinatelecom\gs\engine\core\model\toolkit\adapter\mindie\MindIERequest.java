package com.chinatelecom.gs.engine.core.model.toolkit.adapter.mindie;

import com.chinatelecom.gs.engine.core.sdk.vo.llm.BaseLLMRequest;
import lombok.Data;

@Data
public class MindIERequest implements BaseLLMRequest {

    private String model;

    private String prompt;

    private boolean stream = true;

    private Integer max_tokens = 3000;

    /**
     * qwen 2.5 top_p
     * 1.5  private Integer topk = 5;
     */
    private Float top_p = 0.5f;

    private double temperature = 0.3;

    private double repetition_penalty = 1.03;

    /**
     * 获取输入内容
     *
     * @return
     */
    @Override
    public String inputContent() {
        return this.prompt;
    }
}
