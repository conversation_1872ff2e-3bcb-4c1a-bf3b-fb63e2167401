# 本地环境启动使用配置,需要配合nacos使用
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://${app.db.kms.host}:${app.db.kms.port}/${app.db.kms.dbname}?useUnicode=true&characterEncoding=utf8&useSSL=false&allowMultiQueries=true
    username: ${app.db.kms.username}
    password: ${app.db.kms.password}

  flyway:
    enabled: false # 是否启用flyway
    baseline-on-migrate: true  # 空库时候自动执行建库脚本
    placeholderReplacement: false

  session:
    store-type: redis
  redis:
    database: 0
    connect-timeout: 10s
    #    cluster:
    #      nodes: ${app.redis.nodes}
    username: ${app.redis.username}
    password: ${app.redis.password}
    host: ${app.redis.host}
    port: ${app.redis.port}
  kafka:
    bootstrap-servers: ${app.kafka.servers:127.0.0.1:9092} # kafka集群信息，多个节点通过“,”分隔

springfox:
  documentation:
    enabled: true # swagger开关, 生产应该关闭


logging:
  level:
    com.chinatelecom.ai.kms.infra.mapper: debug
    org.apache.kafka.clients.NetworkClient: error

mybatis-plus:
  global-config:
    sequence:
      workerId: 0 # 解决启动速度问题，生产不可配置
      dataCenterId: 0 # 解决启动速度问题，生产不可配置


platform:
  client:
    app-url: http://ip:port
    server-url: http://ip:port
    include-urls: /web/*,/platform/client/*
    exclude-urls: /web/app/listAllGroup, /menu/auth/list, /common/web/entityRecognition/pluginPredict
    auth-exclude-urls: /web/user
    app-code: cloud-platform #创建的应用appCode
    app-secret: cloud-platfok #创建的应用的appSecret
    manager: redis
    filters:
      - com.chinatelecom.cloud.platform.client.filter.LogoutFilter
      - com.chinatelecom.cloud.platform.client.filter.LoginFilter
      - com.chinatelecom.cloud.platform.client.filter.AuthFilter
