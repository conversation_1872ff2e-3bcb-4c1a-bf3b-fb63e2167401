package com.chinatelecom.gs.engine.core.model.respository.respository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.enums.DimensionEnum;
import com.chinatelecom.gs.engine.common.infra.base.impl.BaseByCodeRepositoryImpl;
import com.chinatelecom.gs.engine.core.model.converter.IntentConfigVoConverter;
import com.chinatelecom.gs.engine.core.model.entity.dto.AgentIntentConfigDTO;
import com.chinatelecom.gs.engine.core.model.entity.po.AgentIntentConfigPO;
import com.chinatelecom.gs.engine.core.model.mapper.AgentIntentConfigMapper;
import com.chinatelecom.gs.engine.core.model.respository.respository.AgentIntentConfigRepository;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 意图高级配置表 Repository 实现类
 *
 * <AUTHOR>
 * @since 2025-02-12
 */
@Repository
public class AgentIntentConfigRepositoryImpl extends BaseByCodeRepositoryImpl<AgentIntentConfigMapper, AgentIntentConfigPO, AgentIntentConfigDTO> implements AgentIntentConfigRepository {


    protected IntentConfigVoConverter converter() {
        return IntentConfigVoConverter.INSTANCE;
    }

    @Override
    public AgentIntentConfigDTO selectAllOrDefault(String appCode) {
        LambdaQueryWrapper<AgentIntentConfigPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AgentIntentConfigPO::getAppCode, appCode)
                .or()
                .eq(AgentIntentConfigPO::getAppCode, "0");

        List<AgentIntentConfigPO> po = getBaseMapper().selectList(wrapper);
        List<AgentIntentConfigDTO> configs = convertToDto(po);
        // 优先查找租户维度的配置
        return configs.stream()
                .filter(c -> DimensionEnum.TENANT.equals(c.getDimension()))
                .findFirst()
                .orElse(
                        // 如果没有租户维度的配置，使用SYSTEM维度的配置
                        configs.stream()
                                .filter(c -> DimensionEnum.SYSTEM.equals(c.getDimension()))
                                .findFirst()
                                .orElseThrow(() -> new BizException("AD019", "系统配置缺失"))
                );

    }

    @Override
    public AgentIntentConfigDTO selectByCode(String code) {
        LambdaQueryWrapper<AgentIntentConfigPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AgentIntentConfigPO::getCode, code).last(getOnlyOneSql());

        AgentIntentConfigPO po = getBaseMapper().selectOne(wrapper);
        return convertToDto(po);
    }

    @Override
    public AgentIntentConfigDTO selectByAppCode() {
        LambdaQueryWrapper<AgentIntentConfigPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AgentIntentConfigPO::getAppCode, RequestContext.getAppCode()).last(getOnlyOneSql());

        AgentIntentConfigPO po = getBaseMapper().selectOne(wrapper);
        return convertToDto(po);
    }

    @Override
    public boolean existsByModelCode(String modelCode) {
        LambdaQueryWrapper<AgentIntentConfigPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AgentIntentConfigPO::getEmbeddingModelCode, modelCode)
                .or()
                .eq(AgentIntentConfigPO::getRerankModelCode, modelCode);
        return getBaseMapper().exists(wrapper);
    }

}
