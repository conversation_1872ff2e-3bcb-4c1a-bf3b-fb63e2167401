package com.chinatelecom.gs.engine.channel.service.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/1/16 15:26
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class WeixinGetTokenRespDTO {

    private Integer errcode;

    private String errmsg;

    @JSONField(name = "access_token")
    private String accessToken;

    @JSONField(name = "expires_in")
    private Integer expiresIn;
}
