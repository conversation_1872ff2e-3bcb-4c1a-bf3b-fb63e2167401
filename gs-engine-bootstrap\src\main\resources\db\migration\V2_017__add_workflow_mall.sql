alter table plugin_mall_info add column `tenant_id` varchar(100) null default null comment '租户id';

alter table plugin_mall_info add column `app_code` varchar(100) null default null comment 'appCode';

alter table plugin_mall_info add column `source_system` varchar(100) null default null comment '来源系统';

alter table agent_plugin_bind add column `shelved` varchar(100) null default null comment '是否上架, 0:未上架, 1:已上架';

alter table agent_workflow_bind add column `shelved` varchar(100) null default null comment '是否上架, 0:未上架, 1:已上架';

alter table agent_ai_trigger add column `shelved` varchar(100) null default null comment '是否上架, 0:未上架, 1:已上架';

CREATE TABLE if not exists `bot_workflow_mall_info` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增 id',
    `workflow_id` varchar(255) NOT NULL COMMENT '业务流id',
    `workflow_name` varchar(255) NULL COMMENT '业务流英文名称',
    `workflow_cn_name` varchar(255) NOT NULL COMMENT '业务流名称',
    `source` varchar(255) NULL COMMENT '来源, 0:自建, 1:官方',
    `source_system` varchar(255) NULL COMMENT '来源系统',
    `yn` int NOT NULL DEFAULT '0',
    `app_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '空间id',
    `tenant_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '租户id',
    `create_id` varchar(255) NOT NULL,
    `create_name` varchar(255) NOT NULL,
    `update_id` varchar(255) NOT NULL,
    `update_name` varchar(255) NOT NULL,
    `create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '记录创建时间',
    `update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '记录最后一次更新时间',
    PRIMARY KEY (`id`),
    key idx_work_flow(workflow_id, workflow_cn_name, source, yn)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

