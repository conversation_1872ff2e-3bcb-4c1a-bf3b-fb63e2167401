package com.chinatelecom.gs.engine.channel.common.constants;

/**
 * <AUTHOR>
 * @date 2023/12/25 11:17
 * @description
 */
public interface ChatConstants {

    String DEFAULT_ANSWER_CONTENT = "抱歉，系统出现问题，请联系管理员";

    String QYWX_RESPONSE_FAILED = "failed";

    String TOKEN = "token";

    String ENCODED_AES = "encoded-aes";

    String CORPID = "corpid";

    String SECRET = "secret";

    String AGENTID = "agentid";

    String X_APPID = "X-Appid";

    String MSG_SIGNATURE = "msg_signature";

    String NONCE = "nonce";

    String TIMESTAMP = "timestamp";

    String CONTENT = "content";

    String MEDIA_ID = "media_id";

    String APP_ID = "app_id";

    String OPENAPI_SOURCE = "openapi";

    /**
     * 表示是否是保密消息，0表示可对外分享，1表示不能分享且内容显示水印，默认为0
     */
    int SAFE = 0;

    /**
     * 渠道id
     */
    String CHANNEL_ID = "channelId";
}
