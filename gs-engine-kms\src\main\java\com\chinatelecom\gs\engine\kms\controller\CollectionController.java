package com.chinatelecom.gs.engine.kms.controller;

import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.common.auth.KsMenuConfig;
import com.chinatelecom.gs.engine.common.auth.PermissionTag;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.kms.base.model.CodeParam;
import com.chinatelecom.gs.engine.kms.sdk.constants.KmsApis;
import com.chinatelecom.gs.engine.kms.sdk.vo.collection.CollectionCreateParam;
import com.chinatelecom.gs.engine.kms.sdk.vo.collection.CollectionQueryParam;
import com.chinatelecom.gs.engine.kms.sdk.vo.collection.CollectionUpdateParam;
import com.chinatelecom.gs.engine.kms.sdk.vo.collection.CollectionVO;
import com.chinatelecom.gs.engine.kms.service.CollectionAppService;
import com.chinatelecom.gs.engine.kms.service.CollectionFetchService;
import com.chinatelelcom.gs.engine.sdk.common.Page;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;


/**
 * <p>
 * 收藏表 Controller
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-06
 */
@RestController
@Slf4j
@Tag(name = "收藏表 Controller")
@RequestMapping(KmsApis.KMS_API + KmsApis.MINE + KmsApis.COLLECT)
public class CollectionController {

    @Resource
    private CollectionAppService collectionAppService;
    @Resource
    private CollectionFetchService collectionFetchService;

    @Operation(summary = "收藏表分页列表", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "收藏表分页列表", groupName = "收藏管理")
    @AuditLog(businessType = "收藏管理", operType = "收藏表分页列表", operDesc = "收藏表分页列表", objId="null")
    @PostMapping(KmsApis.PAGE_API)
    @PermissionTag(code = {KsMenuConfig.COLLECTION, KsMenuConfig.COLLECTION_1})
    public Result<Page<CollectionVO>> page(@Validated @RequestBody CollectionQueryParam param) {
        return Result.success(collectionFetchService.pageQuery(param));
    }

    @Operation(summary = "收藏表新增", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "收藏新增", groupName = "收藏管理")
    @AuditLog(businessType = "收藏管理", operType = "收藏新增", operDesc = "收藏新增", objId="#createParam.targetCode")
    @PostMapping
    public Result<Boolean> add(@Validated @RequestBody CollectionCreateParam createParam) {
        collectionAppService.create(createParam);
        return Result.success(true);
    }

    @Operation(summary = "收藏表更新", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "收藏更新", groupName = "收藏管理")
    @AuditLog(businessType = "收藏管理", operType = "收藏更新", operDesc = "收藏更新", objId="#code")
    @PutMapping(KmsApis.CODE_PATH)
    @PermissionTag(code = {KsMenuConfig.COLLECTION, KsMenuConfig.COLLECTION_1})
    public Result<Boolean> update(@PathVariable("code") String code, @Validated @RequestBody CollectionUpdateParam param) {
        return Result.success(collectionAppService.update(code, param));
    }

    @Operation(summary = "收藏表删除", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "收藏删除", groupName = "收藏管理")
    @AuditLog(businessType = "收藏管理", operType = "收藏删除", operDesc = "收藏删除", objId="#codes")
    @PostMapping(KmsApis.DELETE_API)
    @PermissionTag(code = {KsMenuConfig.COLLECTION, KsMenuConfig.COLLECTION_1})
    public Result<Boolean> delete(@Validated @RequestBody CodeParam codes) {
        return Result.success(collectionAppService.delete(codes));
    }

    @Operation(summary = "根据targetCode删除收藏", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "根据目标编码删除收藏", groupName = "收藏管理")
    @AuditLog(businessType = "收藏管理", operType = "根据目标编码删除收藏", operDesc = "根据目标编码删除收藏", objId="#param.targetCode")
    @PostMapping(KmsApis.TARGET_API + KmsApis.DELETE_API)
    public Result<Boolean> deleteByTarget(@Validated @RequestBody CollectionUpdateParam param) {
        return Result.success(collectionAppService.deleteByTarget(param.getTargetCode(), param.getTargetType()));
    }
}
