UPDATE bot_workflow_node SET `data` = '{"inputs":{"bizParam":{"skillParam":{"enableBotSkills":true,"skillList":[]},"kmsParam":{"retriever":3,"searchStrategy":"MIX","faq":{"enableSortModel":true,"sortCode":"model_992668744299715591","llmAccept":0.6,"topn":3,"faqAccept":0.7},"doc":{"enableSortModel":true,"sortCode":"model_992668744299715591","docAccept":0.5,"topn":5}},"prompt":"{{query}}","systemPrompt":"","modelCode":"model_901039658069659653","modelName":"TeleChat","temperature":0.1,"topP":0.7,"maxTokens":1024},"branches":[{"branchParam":{},"portId":"23f9d4da-4025-4c52-907a-298c3dee2292"},{"branchParam":{},"portId":"28a9dd7e-ae96-4a52-b32d-e7eff2ea1b83"}],"requestParams":[{"enabled":true,"name":"query","paramType":1,"required":false,"subParameters":[],"value":"","valueRefName":"$.BOT_USER_INPUT","valueRefNodeId":"c31eb8dd-3246-4780-b73d-853d1eedda4e","valueRefNodeName":"开始","valueRefSource":"input","valueRefType":"ref","varDataType":1,"variableRef":false}],"settingOnError":{}},"nodeMeta":{"description":"","imageflow":false,"subTitle":"","title":"融合策略"},"outputs":[{"name":"output","paramDesc":"","paramType":1,"varDataType":1,"variableRef":false}]}' WHERE workflow_id = 'ab1070c5-d83e-4a0d-aaa7-d7c0deb99883' AND node_id = 'dfbffd79-3fdd-40f4-8523-3be3f645fc7f' AND version = 1;

UPDATE bot_workflow_node SET `data` = '{"inputs":{"bizParam":{"skillParam":{"enableBotSkills":true,"skillList":[]},"kmsParam":{"retriever":3,"searchStrategy":"MIX","faq":{"enableSortModel":true,"sortCode":"model_992668744299715591","llmAccept":0.6,"topn":3,"faqAccept":0.7},"doc":{"enableSortModel":true,"sortCode":"model_992668744299715591","docAccept":0.5,"topn":5}},"prompt":"{{query}}","systemPrompt":"","modelCode":"model_901039658069659653","modelName":"TeleChat","temperature":0.1,"topP":0.7,"maxTokens":1024},"branches":[{"branchParam":{},"portId":"23f9d4da-4025-4c52-907a-298c3dee2292"},{"branchParam":{},"portId":"28a9dd7e-ae96-4a52-b32d-e7eff2ea1b83"}],"requestParams":[{"enabled":true,"name":"query","paramType":1,"required":false,"subParameters":[],"value":"","valueRefName":"$.BOT_USER_INPUT","valueRefNodeId":"c31eb8dd-3246-4780-b73d-853d1eedda4e","valueRefNodeName":"开始","valueRefSource":"input","valueRefType":"ref","varDataType":1,"variableRef":false}],"settingOnError":{}},"nodeMeta":{"description":"","imageflow":false,"subTitle":"","title":"融合策略"},"outputs":[{"name":"output","paramDesc":"","paramType":1,"varDataType":1,"variableRef":false}]}' WHERE workflow_id = 'ab1070c5-d83e-4a0d-aaa7-d7c0deb99883' AND node_id = 'dfbffd79-3fdd-40f4-8523-3be3f645fc7f' AND version = 2;