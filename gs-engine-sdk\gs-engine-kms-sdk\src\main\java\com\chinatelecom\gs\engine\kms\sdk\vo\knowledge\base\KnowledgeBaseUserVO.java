package com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.base;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@Schema(description = "用户知识库信息视图对象")
public class KnowledgeBaseUserVO implements Serializable {
    @Schema(description = "用户ID", example = "user_001")
    private String userId;

    @Schema(description = "租户ID", example = "tenant_001")
    private String tenantId;

    @Schema(description = "知识库列表")
    private List<KnowledgeBaseVO> knowledgeBases;
}