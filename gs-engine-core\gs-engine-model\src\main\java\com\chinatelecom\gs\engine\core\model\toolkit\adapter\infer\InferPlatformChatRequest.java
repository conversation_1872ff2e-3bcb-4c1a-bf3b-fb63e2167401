package com.chinatelecom.gs.engine.core.model.toolkit.adapter.infer;

import com.chinatelecom.gs.engine.core.sdk.vo.llm.BaseLLMRequest;
import com.chinatelecom.gs.engine.core.sdk.vo.llm.Tool;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

@Data
public class InferPlatformChatRequest implements BaseLLMRequest {

    protected String model;

    protected List<InferPlatformMessage> messages;

    protected boolean stream = true;

//    private Integer max_tokens = 30000;

    /**
     * qwen 2.5 top_p
     * 1.5  private Integer topk = 5;
     */
    protected Float top_p = 0.5f;


    protected double temperature = 0.3;

//    /**
//     * 默认值为{"type": "text"} 返回内容的格式。可选值：{"type": "text"}或{"type": "json_object"}。设置为{"type": "json_object"}时会输出标准格式的JSON字符串。如果指定该参数为{"type": "json_object"}，您需要在System Message或User Message中指引模型输出JSON格式，如：“请按照json格式输出。”
//     */
//    private String response_format = "text";


    protected List<Tool> tools;

    private InferPlatformChatRequest.ChatTemplateKwargs chat_template_kwargs;

    /**
     * 获取输入内容
     *
     * @return
     */
    @Override
    public String inputContent() {
        StringBuilder inputBuilder = new StringBuilder();
        if(CollectionUtils.isNotEmpty(messages)){
            for(InferPlatformMessage message : messages){
                inputBuilder.append(message.getContent());
            }
        }
        return inputBuilder.toString();
    }

    @Data
    public static class ChatTemplateKwargs {
        private boolean enable_thinking;
    }
}
