package com.chinatelecom.gs.engine.channel.manage;

import com.alibaba.fastjson2.JSONObject;
import com.chinatelecom.gs.engine.channel.dao.repository.ChannelInfoRepository;
import com.chinatelecom.gs.engine.channel.service.dto.ChannelInfoDTO;
import com.chinatelecom.gs.engine.common.utils.JsonUtils;
import com.chinatelecom.gs.engine.core.corekit.common.core.RedisKey;
import com.chinatelecom.gs.engine.robot.manage.common.enums.AgentConfigEnum;
import com.chinatelecom.gs.engine.robot.manage.info.dao.service.AgentBasicInfoService;
import com.chinatelecom.gs.engine.robot.manage.info.dao.service.AgentConfigService;
import com.chinatelecom.gs.engine.robot.manage.info.domain.po.AgentBasicInfoPO;
import com.chinatelecom.gs.engine.robot.manage.info.domain.po.AgentConfigPO;
import com.chinatelecom.gs.engine.robot.manage.info.enums.AgentTemplateCategoryEnum;
import com.chinatelecom.gs.engine.robot.manage.info.service.AgentBasicConfigService;
import com.chinatelecom.gs.engine.robot.sdk.config.ChannelOffSiteSwitchParam;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * @description: 场外渠道开关缓存服务
 * @author: xktang
 * @date: 2024/8/1 下午6:09
 * @version: 1.0
 */
@Slf4j
@Service
public class OffSiteChannelSwitchCacheService {
    @Autowired
    private RedisKey redisKey;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private AgentBasicConfigService agentBasicConfigService;
    @Autowired
    private AgentConfigService agentConfigService;
    @Autowired
    private ChannelInfoRepository channelInfoRepository;
    @Autowired
    private AgentBasicInfoService agentBasicInfoService;

    public boolean channelOpen(String agentCode, String channelId) {
        Long agentEditVersion = agentBasicConfigService.getAgentEditVersion(agentCode);
        if (agentEditVersion == 1) {
            log.info("{} : {} ==> 当前机器人未进行发布，场外部署关闭", agentCode, channelId);
            return false;
        }
        boolean offsiteOffSwitch = offsiteOffSwitch(agentCode, agentEditVersion);
        if (!offsiteOffSwitch) {
            log.info("{} : {} ==> 当前机器人站外部署未开启", agentCode, agentEditVersion);
            return false;
        }
        ChannelInfoDTO channelInfo = channelInfoRepository.getChannelInfo(channelId, agentCode);
        boolean enable = channelInfo.isEnable();
        if (!enable) {
            log.info("{} : {} ==> 该渠道未启用", agentCode, channelId);
            return false;
        }
        return true;
    }

    public boolean offsiteOffSwitch(String agentCode, long agentEditVersion) {
        try {
            String offSiteSwitchKey = redisKey.getOffSiteSwitchKey(agentCode, agentEditVersion);
            RBucket<Boolean> bucket = redissonClient.getBucket(offSiteSwitchKey);
            Boolean offsiteSwitch = bucket.get();

            if (offsiteSwitch != null) {
                return offsiteSwitch;
            }

            String configKey = AgentConfigEnum.BOT_CONFIG_OFF_SITE_DEPLOYMENT_SWITCH.getCode();
            AgentBasicInfoPO agentBasicInfoPO = agentBasicInfoService.queryByAgentCodeAndVersion(agentCode, agentEditVersion);
            if (Objects.isNull(agentBasicInfoPO)) {
                throw new BizException("AD007", "bot不存在");
            }
            boolean isSystemAgent = AgentTemplateCategoryEnum.SYSTEM.getCode().equals(agentBasicInfoPO.getTemplateCategoryId());
            AgentConfigPO agentConfigPO = agentConfigService.queryConfigByKey(agentCode, configKey, agentEditVersion, isSystemAgent);

            if (agentConfigPO == null) {
                log.info("{} : {} ==> 当前机器人站外部署开关未配置", agentCode, agentEditVersion);
                initAgentChannelConfig(agentCode, agentEditVersion);
                bucket.set(false, 1, TimeUnit.MINUTES);
                return false;
            }

            String configValue = agentConfigPO.getConfigValue();
            ChannelOffSiteSwitchParam channelOffSiteSwitchParam = JsonUtils.parseObject(configValue, ChannelOffSiteSwitchParam.class);

            if (channelOffSiteSwitchParam == null) {
                log.info("{} : {} ==> 当前机器人站外部署开关为空", agentCode, agentEditVersion);
                return false;
            }

            boolean open = channelOffSiteSwitchParam.isOpen();
            bucket.set(open, 1, TimeUnit.MINUTES);
            return open;

        } catch (Exception e) {
            log.error("{} : {} ==> 当前机器人站外部署开关获取失败", agentCode, agentEditVersion, e);
            return false;
        }
    }

    public boolean updateOffSiteSwitch(String agentCode, boolean open, long agentEditVersion) {
        invalidateCache(agentCode, agentEditVersion);

        String configKey = AgentConfigEnum.BOT_CONFIG_OFF_SITE_DEPLOYMENT_SWITCH.getCode();
        AgentBasicInfoPO agentBasicInfoPO = agentBasicInfoService.queryByAgentCodeAndVersion(agentCode, agentEditVersion);
        if (Objects.isNull(agentBasicInfoPO)) {
            throw new BizException("AD007", "bot不存在");
        }
        boolean isSystemAgent = AgentTemplateCategoryEnum.SYSTEM.getCode().equals(agentBasicInfoPO.getTemplateCategoryId());
        AgentConfigPO agentConfigPO = agentConfigService.queryConfigByKey(agentCode, configKey, agentEditVersion, isSystemAgent);
        if (agentConfigPO == null) {
            initAgentChannelConfig(agentCode, agentEditVersion);
            agentConfigPO = agentConfigService.queryConfigByKey(agentCode, configKey, agentEditVersion, isSystemAgent);
        }

        agentConfigPO.setConfigValue(JSONObject.toJSONString(new ChannelOffSiteSwitchParam(open)));
        return agentConfigService.updateById(agentConfigPO);
    }

    private void initAgentChannelConfig(String agentCode, Long version) {
        log.info("{} : {} ==> 初始化机器人站外部署开关", agentCode, version);
        AgentConfigPO agentConfig = new AgentConfigPO();
        agentConfig.setAgentCode(agentCode);
        agentConfig.setConfigKey(AgentConfigEnum.BOT_CONFIG_OFF_SITE_DEPLOYMENT_SWITCH.getCode());
        agentConfig.setConfigValue(JsonUtils.toJsonString(new ChannelOffSiteSwitchParam(false)));
        agentConfig.setVersion(version);
        agentConfigService.save(agentConfig);
    }

    private void invalidateCache(String agentCode, Long agentEditVersion) {
        String offSiteSwitchKey = redisKey.getOffSiteSwitchKey(agentCode, agentEditVersion);
        RBucket<Boolean> bucket = redissonClient.getBucket(offSiteSwitchKey);
        bucket.delete();
    }
}
