package com.chinatelecom.gs.privilege.service;

import com.chinatelecom.gs.privilege.common.vo.AccessControlTeamInfo;
import com.chinatelecom.gs.privilege.service.dto.ObjectCheckInfo;

import java.util.List;

public interface TeamService {

    List<AccessControlTeamInfo> getAllTeams(String corpCode);

    /**
     * 判断teamCode是否合法存在
     * @param corpCode
     * @param teamCodes
     * @return
     */
    Boolean isTeamCodesExist(String corpCode, List<ObjectCheckInfo> teamCodes);

    void construct();
}
