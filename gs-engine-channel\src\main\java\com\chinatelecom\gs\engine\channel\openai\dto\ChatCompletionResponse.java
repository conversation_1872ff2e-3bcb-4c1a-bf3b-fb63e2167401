package com.chinatelecom.gs.engine.channel.openai.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * OpenAI Chat Completion API 响应格式
 */
@Data
public class ChatCompletionResponse {
    /**
     * 响应ID
     */
    @Schema(description = "响应的唯一标识符")
    private String id;
    
    /**
     * 对象类型，固定为"chat.completion"
     */
    @Schema(description = "对象类型，同步响应为\"chat.completion\"")
    private String object = "chat.completion";
    
    /**
     * 创建时间戳（秒）
     */
    @Schema(description = "创建响应的Unix时间戳（以秒为单位）")
    private long created;
    
    /**
     * 使用的模型
     */
    @Schema(description = "用于补全的模型")
    private String model;
    
    /**
     * 系统指纹，用于确定性生成
     */
    @Schema(description = "用于确定性生成的系统指纹")
    private String system_fingerprint;
    
    /**
     * 补全选项列表
     */
    @Schema(description = "补全选项列表")
    private List<ChatCompletionChoice> choices;
    
    /**
     * 使用统计
     */
    @Schema(description = "标记使用统计（仅在非流式响应中）")
    private Usage usage;
    
    @Data
    public static class ChatCompletionChoice {
        /**
         * 索引
         */
        @Schema(description = "选项的索引")
        private int index;
        
        /**
         * 消息
         */
        @Schema(description = "消息对象（在非流式响应中）")
        private ChatMessage message;
        
        /**
         * 结束原因：stop, length, content_filter, function_call, null
         */
        @Schema(description = "补全结束的原因：\"stop\", \"length\", \"content_filter\", \"function_call\"或null")
        private String finish_reason;
    }
    
    @Data
    public static class Usage {
        /**
         * 提示token数
         */
        @Schema(description = "提示中的标记数(粗略估算 仅用来兼容openai规范)")
        private int prompt_tokens;
        
        /**
         * 补全token数
         */
        @Schema(description = "补全中的标记数(粗略估算 仅用来兼容openai规范)")
        private int completion_tokens;
        
        /**
         * 总token数
         */
        @Schema(description = "使用的总标记数(粗略估算 仅用来兼容openai规范)")
        private int total_tokens;
    }
}
