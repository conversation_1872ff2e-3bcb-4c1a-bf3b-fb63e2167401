package com.chinatelecom.gs.engine.robot.manage.info.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.utils.IdGenerator;
import com.chinatelecom.gs.engine.core.manager.convert.ConfigConvert;
import com.chinatelecom.gs.engine.core.manager.service.BaseConfigAppService;
import com.chinatelecom.gs.engine.core.manager.vo.config.PublishConfig;
import com.chinatelecom.gs.engine.core.model.enums.ModelTypeEnum;
import com.chinatelecom.gs.engine.core.model.toolkit.safe.SafeCheckAssistService;
import com.chinatelecom.gs.engine.core.model.toolkit.safe.config.SafeCenterConfig;
import com.chinatelecom.gs.engine.core.model.toolkit.safe.config.SafeLLMContext;
import com.chinatelecom.gs.engine.core.model.toolkit.safe.enums.ContentTypeEnum;
import com.chinatelecom.gs.engine.core.model.toolkit.safe.enums.EndMarkEnum;
import com.chinatelecom.gs.engine.core.model.toolkit.safe.service.SafeFenceConfigService;
import com.chinatelecom.gs.engine.core.sdk.rpc.ModelServiceClient;
import com.chinatelecom.gs.engine.core.sdk.vo.ModelPageListParam;
import com.chinatelecom.gs.engine.robot.manage.info.service.GenerateDescriptionService;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.agent.config.SafeFenceConfig;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import com.chinatelelcom.gs.engine.sdk.common.utils.SpringContextUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.hc.client5.http.classic.methods.HttpPost;
import org.apache.hc.client5.http.config.RequestConfig;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.CloseableHttpResponse;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.core5.http.ContentType;
import org.apache.hc.core5.http.io.entity.EntityUtils;
import org.apache.hc.core5.http.io.entity.StringEntity;
import org.apache.hc.core5.net.URIBuilder;
import org.apache.hc.core5.util.Timeout;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.*;

import static com.amazonaws.services.s3.Headers.CONTENT_TYPE;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class GenerateDescriptionServiceImpl implements GenerateDescriptionService {
    @Resource
    private ModelServiceClient modelServiceClient;

    @Resource
    private SafeFenceConfigService safeFenceConfigService;

    private static final String PROMPT_TEMPLATE = "假设你是一个智能数据库助手，请根据表的结构和数据生成表的描述，主要描述该表的主要功能以及可用于哪些内容查询，描述的长度不要超过500个字符，表的结构为:#{data}";



    @Override
    public String generateDescription(String query) {
        ModelPageListParam modelPageListParam = modelServiceClient.getDefaultModel(ModelTypeEnum.LLM.getCode());
        if (modelPageListParam == null) {
            throw new BizException("AE023", "默认模型不存在");
        }
        Map<String, Object> parameter = Maps.newHashMap();
        parameter.put("model", modelPageListParam.getModelCallName());
        parameter.put("stream", false);
        parameter.put("messages", buildMessages(query));
        log.info("【生成表描述】输入参数：{}", JSON.toJSONString(parameter));

        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            URIBuilder uriBuilder = new URIBuilder(modelPageListParam.getExternalModelUrl());
            HttpPost httpPost = new HttpPost(uriBuilder.build());

            RequestConfig requestConfig = getRequestConfig(3000);
            httpPost.setConfig(requestConfig);
            httpPost.setEntity(new StringEntity(JSON.toJSONString(parameter), StandardCharsets.UTF_8));
            httpPost.setHeader(CONTENT_TYPE, ContentType.APPLICATION_JSON.getMimeType());
            log.info("request url{},body:{},header:{}", uriBuilder.getPath(), httpPost.getEntity(),
                    httpPost.getHeaders());

            try (CloseableHttpResponse res = httpClient.execute(httpPost)) {
                org.apache.hc.core5.http.HttpEntity entity = res.getEntity();
                String httpResult = EntityUtils.toString(entity);
                log.info("http result:{}", httpResult);
                if (httpResult == null) {
                    return null;
                }
                JSONObject result = JSON.parseObject(httpResult);
                String content = Optional.ofNullable(result.getJSONArray("choices"))
                        .map(choices -> choices.getJSONObject(0))
                        .map(message -> message.getJSONObject("message"))
                        .map(message -> message.getString("content"))
                        .orElse("");
                log.info("生成表描述结果：{}", content);
                content = content.replaceAll("\n\n", "\n");

                SafeLLMContext safeLLMContext = safeFenceConfigService.buildSafeLLMContext();
                SafeCheckAssistService safeCheckAssistService = SpringContextUtils.getBean(SafeCheckAssistService.class);
                boolean isPassSafeFence = safeCheckAssistService.checkContentIsSafe(content, ContentTypeEnum.MODEL_OUTPUT, safeLLMContext, EndMarkEnum.END, false);
                if (!isPassSafeFence) {
                    log.info("【LLM】 大模型输出触发安全围栏");
                    content = safeLLMContext.getFenceScript();
                }

                return content;
            }
        } catch (Exception e) {
            log.error("生成表描述失败", e);
            return null;
        }
    }

    private RequestConfig getRequestConfig(int timeOut) {
        return RequestConfig.custom()
                .setConnectTimeout(Timeout.ofSeconds(timeOut))
                .setConnectionRequestTimeout(Timeout.ofSeconds(timeOut))
                .setResponseTimeout(Timeout.ofSeconds(timeOut))
                .build();
    }

    private List<Map<String, Object>> buildMessages(String query) {
        Map<String, Object> systemMessage = new HashMap<>();
        systemMessage.put("role", "system");
        systemMessage.put("content", "你是一个人工智能助手");

        Map<String, Object> userMessage = getUserMessage(query);

        return Lists.newArrayList(systemMessage, userMessage);
    }

    private Map<String, Object> getUserMessage(String query) {
        String content = PROMPT_TEMPLATE.replace("#{data}", query);

        Map<String, Object> userMessage = Maps.newHashMap();
        userMessage.put("role", "user");
        userMessage.put("content", content);
        return userMessage;
    }
}
