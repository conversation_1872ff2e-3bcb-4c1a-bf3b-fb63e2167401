package com.chinatelecom.gs.engine.kms.controller;

import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.common.auth.KsMenuConfig;
import com.chinatelecom.gs.engine.common.auth.MenuConfig;
import com.chinatelecom.gs.engine.common.auth.PermissionTag;
import com.chinatelecom.gs.engine.common.config.aspect.annotation.VisitsLog;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.common.utils.BizAssert;
import com.chinatelecom.gs.engine.kms.base.model.CodeParam;
import com.chinatelecom.gs.engine.kms.dto.SwitchParam;
import com.chinatelecom.gs.engine.kms.sdk.api.KmsKnowledgeBaseApi;
import com.chinatelecom.gs.engine.kms.sdk.constants.KmsApis;
import com.chinatelecom.gs.engine.kms.sdk.vo.base.BaseNameVO;
import com.chinatelecom.gs.engine.kms.sdk.vo.common.CodeBatchParam;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.base.*;
import com.chinatelecom.gs.engine.kms.service.KnowledgeBaseService;
import com.chinatelecom.gs.engine.sdk.common.annotation.ApiType;
import com.chinatelecom.gs.engine.sdk.common.annotation.HideFromApiTypes;
import com.chinatelelcom.gs.engine.sdk.common.PageImpl;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import com.chinatelelcom.gs.engine.sdk.common.enums.AppSourceType;
import com.chinatelelcom.gs.engine.sdk.common.enums.RequestSourceType;
import com.google.common.collect.ImmutableList;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024年12月12日
 */
@RestController
@Tag(name = "知识库管理 Controller")
@RequestMapping({KmsApis.KMS_API + KmsApis.KNOWLEDGE_BASE, KmsApis.RPC + KmsApis.KNOWLEDGE_BASE, KmsApis.OPENAPI + KmsApis.KNOWLEDGE_BASE})
public class KnowledgeBaseController implements KmsKnowledgeBaseApi {

    @Resource
    private KnowledgeBaseService knowledgeBaseService;

    @Operation(summary = "分页查询知识库", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "分页查询知识库", groupName = "知识库管理")
    @AuditLog(businessType = "知识库管理", operType = "分页查询知识库", operDesc = "分页查询知识库", objId="null")
    @PostMapping(KmsApis.PAGE_API)
    @PermissionTag(code = {KsMenuConfig.KNOWLEDGE_BASE, KsMenuConfig.KNOWLEDGE_BASE_1, MenuConfig.WORD_KNOWLEDGE, MenuConfig.QA_KNOWLEDGE,
            KsMenuConfig.KNWL_SEARCH, KsMenuConfig.KNWL_SEARCH_1, MenuConfig.ROBOT, KsMenuConfig.BOT,
            KsMenuConfig.CUSTOM_REPORT, KsMenuConfig.CUSTOM_REPORT_1, MenuConfig.CUSTOM_REPORT,
    })
    public Result<PageImpl<KnowledgeBaseVO>> page(@Validated @RequestBody KnowledgeBaseQueryParam appQueryParam) {
        if (appQueryParam.getSourceSystem() == null) {
            AppSourceType appSourceType = RequestContext.getAppSourceType();
            BizAssert.notNull(appSourceType, "AA054", "无法获取操作来源信息");
            appQueryParam.setSourceSystem(appSourceType);
        }
        if (!appQueryParam.isCheckRole() && Objects.equals(RequestContext.getRequestSourceType(), RequestSourceType.WEB)) {
            // web请求必须校验权限，避免越权
            BizAssert.throwBizException("AA108", "web请求必须校验权限");
        }

        PageImpl<KnowledgeBaseVO> knowledgeBaseVOPage = (PageImpl<KnowledgeBaseVO>) knowledgeBaseService.pageQuery(appQueryParam);
        return Result.success(knowledgeBaseVOPage);
    }

    @HideFromApiTypes(ApiType.OPENAPI)
    @Operation(summary = "查询所有有权限的知识库列表", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "查询所有有权限的知识库列表", groupName = "知识库管理")
    @AuditLog(businessType = "知识库管理", operType = "查询所有有权限的知识库列表", operDesc = "查询所有有权限的知识库列表", objId="null")
    @PostMapping(KmsApis.ALL_AUTH)
    @Override
    public Result<PageImpl<BaseNameVO>> allAuth(KnowledgeBaseQueryParam appQueryParam) {
        PageImpl<BaseNameVO> knowledgeBaseVOPage = (PageImpl<BaseNameVO>) knowledgeBaseService.allAuth(appQueryParam);
        return Result.success(knowledgeBaseVOPage);
    }

    @Operation(summary = "知识库详情", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "知识库详情", groupName = "知识库管理")
//    @AuditLog(businessType = "知识库管理", operType = "知识库详情", operDesc = "知识库详情", objId="#code")
    @GetMapping(KmsApis.CODE_PATH)
    public Result<KnowledgeBaseVO> get(@PathVariable("code") String code) {
        return Result.success(knowledgeBaseService.get(code));
    }

    @Operation(summary = "删除知识库", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "删除知识库", groupName = "知识库管理")
    @AuditLog(businessType = "知识库管理", operType = "删除知识库", operDesc = "删除知识库", objId="#codeParam.codes")
    @PostMapping(KmsApis.DELETE_API)
    @PermissionTag(code = {KsMenuConfig.KNOWLEDGE_BASE, KsMenuConfig.KNOWLEDGE_BASE_1, MenuConfig.WORD_KNOWLEDGE, MenuConfig.QA_KNOWLEDGE})
    public Result<Boolean> delete(@Validated @RequestBody CodeParam codeParam) {
        return Result.success(knowledgeBaseService.delete(codeParam));
    }

    @Operation(summary = "新增知识库", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "新增知识库", groupName = "知识库管理")
    @AuditLog(businessType = "知识库管理", operType = "新增知识库", operDesc = "新增知识库", objId="#_RESULT_.data.code")
    @PostMapping
    @VisitsLog(paramKey = "#dto.type")
    @PermissionTag(code = {KsMenuConfig.KNOWLEDGE_BASE, KsMenuConfig.KNOWLEDGE_BASE_1, MenuConfig.WORD_KNOWLEDGE, MenuConfig.QA_KNOWLEDGE})
    public Result<KnowledgeBaseVO> add(@Validated @RequestBody KnowledgeBaseCreateParam dto) {
        if (dto.getSourceSystem() == null) {
            AppSourceType appSourceType = RequestContext.getAppSourceType();
            BizAssert.notNull(appSourceType, "AA054", "无法获取操作来源信息");
            dto.setSourceSystem(appSourceType);
        }

        return Result.success(knowledgeBaseService.create(dto));
    }

    @Operation(summary = "更新知识库", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "更新知识库", groupName = "知识库管理")
    @AuditLog(businessType = "知识库管理", operType = "更新知识库", operDesc = "更新知识库", objId="#code")
    @PutMapping(KmsApis.CODE_PATH)
    @PermissionTag(code = {KsMenuConfig.KNOWLEDGE_BASE, KsMenuConfig.KNOWLEDGE_BASE_1, MenuConfig.WORD_KNOWLEDGE, MenuConfig.QA_KNOWLEDGE})
    public Result<Boolean> update(@PathVariable("code") String code, @Validated @RequestBody KnowledgeBaseUpdateParam dto) {
        return Result.success(knowledgeBaseService.update(code, dto));
    }

    @Operation(summary = "重命名知识库", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "重命名知识库", groupName = "知识库管理")
    @AuditLog(businessType = "知识库管理", operType = "重命名知识库", operDesc = "重命名知识库", objId="#code")
    @PutMapping(KmsApis.RENAME_API + KmsApis.CODE_PATH)
    @PermissionTag(code = {KsMenuConfig.KNOWLEDGE_BASE, KsMenuConfig.KNOWLEDGE_BASE_1, MenuConfig.WORD_KNOWLEDGE, MenuConfig.QA_KNOWLEDGE})
    public Result<KnowledgeBaseVO> rename(@PathVariable("code") String code, @Validated @RequestBody KnowledgeBaseRenameParam param) {
        return Result.success(knowledgeBaseService.rename(code, param.getName()));
    }

    @Operation(summary = "知识库开关", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "知识库开关", groupName = "知识库管理")
    @AuditLog(businessType = "知识库管理", operType = "知识库开关", operDesc = "知识库开关", objId="#code")
    @PutMapping(KmsApis.SWITCH_API + KmsApis.CODE_PATH)
    @PermissionTag(code = {KsMenuConfig.KNOWLEDGE_BASE, KsMenuConfig.KNOWLEDGE_BASE_1, MenuConfig.WORD_KNOWLEDGE, MenuConfig.QA_KNOWLEDGE})
    public Result<String> switchOn(@PathVariable("code") String code, @RequestParam Boolean on) {
        knowledgeBaseService.switchOn(ImmutableList.of(code), on);
        return Result.success(code);
    }

    @Operation(summary = "批量知识库开关", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "批量知识库开关", groupName = "知识库管理")
    @AuditLog(businessType = "知识库管理", operType = "批量知识库开关", operDesc = "批量知识库开关", objId="#switchParam.codes")
    @PutMapping(KmsApis.SWITCH_API)
    @PermissionTag(code = {KsMenuConfig.KNOWLEDGE_BASE, KsMenuConfig.KNOWLEDGE_BASE_1, MenuConfig.WORD_KNOWLEDGE, MenuConfig.QA_KNOWLEDGE})
    public Result<String> batchSwitchOn(@RequestBody @Validated SwitchParam switchParam) {
        knowledgeBaseService.switchOn(switchParam.getCodes(), switchParam.getOn());
        return Result.success(null);
    }

    @HideFromApiTypes(ApiType.OPENAPI)
    @Operation(summary = "更新知识库高级配置", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "更新知识库高级配置", groupName = "知识库管理")
    @AuditLog(businessType = "知识库管理", operType = "更新知识库高级配置", operDesc = "更新知识库高级配置", objId="#code")
    @PutMapping(KmsApis.UPDATE + KmsApis.CONFIG + KmsApis.CODE_PATH)
    @PermissionTag(code = {KsMenuConfig.KNOWLEDGE_BASE, KsMenuConfig.KNOWLEDGE_BASE_1, MenuConfig.WORD_KNOWLEDGE, MenuConfig.QA_KNOWLEDGE})
    public Result<KnowledgeBaseVO> updateConfig(@PathVariable("code") String code, @Validated @RequestBody KnowledgeBaseUpdateConfigParam param) {
        return Result.success(knowledgeBaseService.updateConfig(code, param.getConfig()));
    }

    @HideFromApiTypes(ApiType.OPENAPI)
    @Override
    @Operation(summary = "统计知识库待发布文档信息", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "统计知识库待发布文档信息", groupName = "知识库管理")
    @AuditLog(businessType = "知识库管理", operType = "统计知识库待发布文档信息", operDesc = "统计知识库待发布文档信息", objId="#code")
    @PostMapping(KmsApis.STATISTICS + KmsApis.PUBLISH)
    @PermissionTag(code = {KsMenuConfig.KNOWLEDGE_BASE, KsMenuConfig.KNOWLEDGE_BASE_1})
    public Result<List<KBPublishStatisticsVO>> statisticsPublish(@Validated @RequestBody CodeBatchParam codeParam) {
        return Result.success(knowledgeBaseService.statisticsPublish(codeParam));
    }

}
