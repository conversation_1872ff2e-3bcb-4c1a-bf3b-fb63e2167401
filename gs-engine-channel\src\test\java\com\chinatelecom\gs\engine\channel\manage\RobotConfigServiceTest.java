package com.chinatelecom.gs.engine.channel.manage;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinatelecom.gs.engine.channel.dao.po.ChannelConfigPO;
import com.chinatelecom.gs.engine.channel.dao.repository.ChannelConfigRepository;
import com.chinatelecom.gs.engine.channel.dao.repository.ChannelInfoRepository;
import com.chinatelecom.gs.engine.channel.service.dto.ChannelInfoDTO;
import com.chinatelecom.gs.engine.channel.service.dto.RobotConfigDTO;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

public class RobotConfigServiceTest {

    @InjectMocks
    private RobotConfigService robotConfigService;

    @Mock
    private ChannelInfoRepository channelInfoRepository;

    @Mock
    private ChannelConfigRepository configRepository;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    // ------------------ addConfig 测试 ------------------

    @Test
    public void testAddConfig_ChannelInfoIsNull_ThrowsBizException() {
        String channelId = "1";
        RobotConfigDTO dto = new RobotConfigDTO();
        dto.setBotCode("bot1");

        when(channelInfoRepository.getChannelInfo(channelId, dto.getBotCode())).thenReturn(null);

        BizException exception = assertThrows(BizException.class, () -> {
            robotConfigService.addConfig(channelId, dto);
        });

        assertEquals("A0049", exception.getCode());
    }

    @Test
    public void testAddConfig_SaveReturnsTrue_ReturnsTrue() {
        String channelId = "1";
        RobotConfigDTO dto = new RobotConfigDTO();
        dto.setBotCode("bot1");

        ChannelInfoDTO info = new ChannelInfoDTO();
        info.setChannelId(channelId);
        info.setAgentCode("bot1");

        when(channelInfoRepository.getChannelInfo(channelId, dto.getBotCode())).thenReturn(info);
        when(configRepository.save(any(ChannelConfigPO.class))).thenReturn(true);

        Boolean result = robotConfigService.addConfig(channelId, dto);

        assertTrue(result);
        verify(configRepository).save(any(ChannelConfigPO.class));
    }

    // ------------------ getRobotConfig 测试 ------------------

    @Test
    public void testGetRobotConfig_ChannelInfoIsNull_ThrowsBizException() {
        String channelId = "1";
        String robotCode = "bot1";

        when(channelInfoRepository.getChannelInfo(channelId, robotCode)).thenReturn(null);

        BizException exception = assertThrows(BizException.class, () -> {
            robotConfigService.getRobotConfig(channelId, robotCode);
        });

        assertEquals("A0049", exception.getCode());
    }

    @Test
    public void testGetRobotConfig_ConfigPOIsNull_ThrowsBizException() {
        String channelId = "1";
        String robotCode = "bot1";

        ChannelInfoDTO info = new ChannelInfoDTO();
        info.setChannelId(channelId);
        info.setAgentCode(robotCode);

        when(channelInfoRepository.getChannelInfo(channelId, robotCode)).thenReturn(info);
        when(configRepository.getOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        BizException exception = assertThrows(BizException.class, () -> {
            robotConfigService.getRobotConfig(channelId, robotCode);
        });

        assertEquals("A0055", exception.getCode());
    }

    @Test
    public void testGetRobotConfig_ConfigValueIsEmpty_ThrowsBizException() {
        String channelId = "1";
        String robotCode = "bot1";

        ChannelInfoDTO info = new ChannelInfoDTO();
        info.setChannelId(channelId);
        info.setAgentCode(robotCode);

        ChannelConfigPO configPO = new ChannelConfigPO();
        configPO.setConfigValue("");

        when(channelInfoRepository.getChannelInfo(channelId, robotCode)).thenReturn(info);
        when(configRepository.getOne(any(LambdaQueryWrapper.class))).thenReturn(configPO);

        BizException exception = assertThrows(BizException.class, () -> {
            robotConfigService.getRobotConfig(channelId, robotCode);
        });

        assertEquals("A0055", exception.getCode());
    }

    @Test
    public void testGetRobotConfig_SuccessfulParse_ReturnsDTO() {
        String channelId = "1";
        String robotCode = "bot1";

        ChannelInfoDTO info = new ChannelInfoDTO();
        info.setChannelId(channelId);
        info.setAgentCode(robotCode);

        ChannelConfigPO configPO = new ChannelConfigPO();
        configPO.setConfigValue("{\"botCode\":\"bot1\"}");

        when(channelInfoRepository.getChannelInfo(channelId, robotCode)).thenReturn(info);
        when(configRepository.getOne(any(LambdaQueryWrapper.class))).thenReturn(configPO);

        // ✅ 直接调用真实方法，不再 mock
        RobotConfigDTO result = robotConfigService.getRobotConfig(channelId, robotCode);

        assertNotNull(result);
        assertEquals("bot1", result.getBotCode());
    }


}
