package com.chinatelecom.gs.engine.common.config.mybatis;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.handler.TenantLineHandler;
import com.baomidou.mybatisplus.extension.plugins.inner.*;
import com.chinatelecom.gs.engine.common.config.mybatis.handler.DmSQLInterceptor;
import com.chinatelecom.gs.engine.common.config.mybatis.handler.UpdateFillHandler;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.context.RequestInfo;
import com.chinatelecom.gs.engine.common.log.track.mapper.LogMapper;
import com.chinatelecom.gs.engine.common.utils.JsonUtils;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import com.chinatelelcom.gs.engine.sdk.common.utils.SpringContextUtils;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.StringValue;
import org.apache.commons.lang3.StringUtils;
import org.mybatis.spring.annotation.MapperScan;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;


@Slf4j
@Configuration
@MapperScan(basePackages = {
        "com.chinatelecom.gs.engine.core.manager.infra.mapper",
        "com.chinatelecom.gs.engine.core.model.mapper",
        "com.chinatelecom.gs.engine.kms.infra.mapper",
        "com.chinatelecom.gs.workflow.core.dao.mapper",
        "com.chinatelecom.gs.engine.robot.manage.info.dao.mapper",
        "com.chinatelecom.gs.engine.robot.manage.data.dao.mapper",
        "com.chinatelecom.gs.plugin.hub.infra.mapper",
        "com.chinatelecom.gs.engine.channel.dao.mapper",
        "com.chinatelecom.gs.engine.core.entity.mapper",
        "com.chinatelecom.gs.engine.core.corekit.mapper",
        "com.chinatelecom.gs.privilege.dao.mapper",
        "com.chinatelecom.gs.engine.common.log.track.mapper",
})
public class MybatisPlusConfig {
    @Value("${spring.datasource.driver-class-name}")
    String driverClassName;
    @Value("${app.db.kms.dbname}")
    private String dbName;

    @Resource
    private RedissonClient redissonClient;

//    @Autowired
//    private SqlSessionFactory sqlSessionFactory;

    private static final Set<String> INTERCEPTOR_APP_CODE_TABLES = Sets.newHashSet(
            "tag_tree_node", "`tag_tree_node`",
            "collection", "`collection`",
            "knowledge_faq", "`knowledge_faq`",
            "knowledge_extra_info", "`knowledge_extra_info`",
            "knowledge", "`knowledge`",
            "file", "`file`",
            "attachment", "`attachment`",
            "faq_similar_question", "`faq_similar_question`",
            "knowledge_base", "`knowledge_base`",
            "knowledge_essential", "`knowledge_essential`",
            "knowledge_extra_info", "`knowledge_extra_info`",
            "knowledge_extract", "`knowledge_extract`",
            "knowledge_faq", "`knowledge_faq`",
            "knowledge_faq_prod", "`knowledge_faq_prod`",
            "knowledge_prod", "`knowledge_prod`",
            "knowledge_report", "`knowledge_report`",
            "agent_ai_model","agent_basic_info",
            "agent_config","agent_dialog_record",
            "agent_knowledge_base","agent_version_info",
            "knowledge_audit","`knowledge_audit`",
            "knowledge_audit_data","`knowledge_audit_data`",
            "workflow_variable",
            "agent_intent_manager","`agent_intent_manager`",
            "agent_intent_example","`agent_intent_example`",
            "gs_sensitive", "`gs_sensitive`"


    );

    private static final Set<String> INTERCEPTOR_IGNORE_UPDATE_TABLES = Sets.newHashSet(
            "agent_index_create_record", "agent_dialog_record"
    );

    private static final Set<String> DYNAMIC_TABLES = Sets.newHashSet("gs_log");

    @PostConstruct
    public void init(){
        JacksonTypeHandler.setObjectMapper(JsonUtils.newInstance());
    }


    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {

        // 达梦驱动判断
        boolean isDmDriver = driverClassName.contains("dm.jdbc");

        MybatisPlusInterceptor mybatisPlusInterceptor = new MybatisPlusInterceptor();
        if (isDmDriver) {
            // 添加达梦拦截器
            log.info("达梦,启动!");
            mybatisPlusInterceptor.addInnerInterceptor(new DmSQLInterceptor());
        }


        // 更新操作自动填充用户信息
        mybatisPlusInterceptor.addInnerInterceptor(new UpdateFillInterceptor(new UpdateFillHandler() {
            @Override
            public boolean ignoreTable(String tableName) {
                return INTERCEPTOR_IGNORE_UPDATE_TABLES.contains(tableName);
            }
        }));

        // 防止全表更新删除操作, 必须在租户插件之前
        mybatisPlusInterceptor.addInnerInterceptor(new BlockAttackInnerInterceptor());
//
        // 这里添加应用自动隔离处理， 使用租户插件， 如果需要拦截，需要手动指定表明，默认不会配置拦截
        mybatisPlusInterceptor.addInnerInterceptor(new TenantLineInnerInterceptor(new TenantLineHandler() {
            @Override
            public String getTenantIdColumn() {
                return "app_code";
            }

            @Override
            public Expression getTenantId() {
                return getTenantIdExpression();
            }

            @Override
            public boolean ignoreTable(String tableName) {
                if(tableName.contains("gs_log")){
                    return true;
                }

                boolean ignoreTable = !INTERCEPTOR_APP_CODE_TABLES.contains(tableName)
                        || (Objects.nonNull(RequestContext.get()) && Objects.equals(false, RequestContext.get().getTenant()));
                if (ignoreTable) {
                    return true;
                }

                if (TenantAspect.isIgnoreTenant()) {
                    return true;
                }
                return false;
            }
        }));

        //动态表结构
        mybatisPlusInterceptor.addInnerInterceptor(new DynamicTableNameInnerInterceptor((sql, tableName) -> {
            if (DYNAMIC_TABLES.contains(tableName)) {
                String yearMouth = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMM"));
                String realTableName = tableName + "_" + yearMouth;
                LogMapper logMapper = SpringContextUtils.getBean(LogMapper.class);
                if (logMapper.existTable(dbName, realTableName) == 0) {
                    String lockKey = "table_create:" + tableName;
                    RLock lock = redissonClient.getLock(lockKey);
                    try {
                        if (lock.tryLock(0, TimeUnit.MILLISECONDS) && logMapper.existTable(dbName, realTableName) == 0) {
                            //创建新表
                            logMapper.createTable(realTableName);
                        }
                    } catch (Exception e) {
                        log.error("创建表发生异常！", e);
                        if (e instanceof InterruptedException) {
                            Thread.currentThread().interrupt();
                        }
                    } finally {
                        //任务执行完毕需要释放锁
                        if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                            lock.unlock();
                        }
                    }
                }
                log.debug("获取到表名：{}", realTableName);
                return realTableName;
            }
            return tableName;
        }));


        // 分页插件根据驱动类型动态设置
        PaginationInnerInterceptor paginationInnerInterceptor = new PaginationInnerInterceptor(isDmDriver ? DbType.DM : DbType.MYSQL);
        // 设置分页上限
        paginationInnerInterceptor.setMaxLimit(5000L);
        // 设置leftJoin 优化
        paginationInnerInterceptor.setOptimizeJoin(true);
        mybatisPlusInterceptor.addInnerInterceptor(paginationInnerInterceptor);

        // 乐观锁插件
        OptimisticLockerInnerInterceptor optimisticLockerInnerInterceptor = new OptimisticLockerInnerInterceptor();
        mybatisPlusInterceptor.addInnerInterceptor(optimisticLockerInnerInterceptor);

        return mybatisPlusInterceptor;
    }

    private Expression getTenantIdExpression() {
        RequestInfo requestInfo = RequestContext.getAndCheck();
        if (requestInfo == null || StringUtils.isBlank(requestInfo.getAppCode())) {
            throw new BizException("B0001", "无法获取到应用信息");
        }
        return new StringValue(requestInfo.getAppCode());
    }


}
