package com.chinatelecom.gs.engine.core.corekit.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.chinatelecom.gs.engine.common.infra.base.impl.BaseExtendServiceImpl;
import com.chinatelecom.gs.engine.core.corekit.domain.po.SensitiveBasePO;
import com.chinatelecom.gs.engine.core.corekit.domain.request.SensitiveBasePageRequest;
import com.chinatelecom.gs.engine.core.corekit.domain.request.SensitiveBaseRequest;
import com.chinatelecom.gs.engine.core.corekit.domain.response.SensitiveBaseResponse;
import com.chinatelecom.gs.engine.core.corekit.mapper.SensitiveBaseMapper;
import com.chinatelecom.gs.engine.core.corekit.service.SensitiveBaseService;
import com.chinatelelcom.gs.engine.sdk.common.Page;
import com.chinatelelcom.gs.engine.sdk.common.PageImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import com.chinatelecom.gs.engine.common.utils.IdGenerator;


import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class SensitiveBaseServiceImpl extends BaseExtendServiceImpl<SensitiveBaseMapper, SensitiveBasePO> implements SensitiveBaseService {

    @Override
    public Boolean saveSensitiveBase(SensitiveBaseRequest request) {
        SensitiveBasePO sensitiveBasePO = null;
        if (Objects.nonNull(request.getCode())) {
            LambdaQueryWrapper<SensitiveBasePO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SensitiveBasePO::getCode, request.getCode());
            sensitiveBasePO = this.getOne(queryWrapper);
            if (Objects.nonNull(sensitiveBasePO)) {
                BeanUtils.copyProperties(request, sensitiveBasePO);
            } else {
                sensitiveBasePO = new SensitiveBasePO();
                sensitiveBasePO.setName(request.getName());
                sensitiveBasePO.setCode(Objects.nonNull(request.getCode()) ? request.getCode() : IdGenerator.getId(StringUtils.EMPTY));
            }
        }

        return this.saveOrUpdate(sensitiveBasePO);
    }

    @Override
    public Boolean delete(List<String> codes) {
        return this.removeByIds(codes);
    }

    @Override
    public Page<SensitiveBaseResponse> pageQuery(SensitiveBasePageRequest request) {
        IPage<SensitiveBasePO> page = new PageDTO<>(request.getPageNum(), request.getPageSize());
        IPage<SensitiveBasePO> pageRecord = this.page(page, Wrappers.<SensitiveBasePO>lambdaQuery()
                .like(StringUtils.isNotEmpty(request.getKeyword()), SensitiveBasePO::getName, request.getKeyword())
                .or()
                .like(StringUtils.isNotEmpty(request.getKeyword()), SensitiveBasePO::getCode, request.getKeyword())
                .orderByDesc(SensitiveBasePO::getUpdateTime)
                .orderByAsc(SensitiveBasePO::getId)
        );

        PageImpl<SensitiveBaseResponse> pageVo = new PageImpl<>();
        pageVo.setPages(pageRecord.getPages());
        pageVo.setCurrent(pageRecord.getCurrent());
        pageVo.setSize(pageRecord.getSize());
        pageVo.setTotal(pageRecord.getTotal());
        List<SensitiveBaseResponse> records = pageRecord.getRecords().stream().map(record -> BeanUtil.copyProperties(record, SensitiveBaseResponse.class)).collect(Collectors.toList());
        pageVo.setRecords(records);
        return pageVo;
    }
}
