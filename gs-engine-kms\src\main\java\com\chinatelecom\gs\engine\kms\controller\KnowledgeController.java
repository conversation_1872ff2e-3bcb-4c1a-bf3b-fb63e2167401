package com.chinatelecom.gs.engine.kms.controller;

import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.common.auth.KsMenuConfig;
import com.chinatelecom.gs.engine.common.auth.MenuConfig;
import com.chinatelecom.gs.engine.common.auth.PermissionTag;
import com.chinatelecom.gs.engine.common.config.aspect.annotation.DebugLog;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.common.utils.JsonUtils;
import com.chinatelecom.gs.engine.common.utils.UserInfoUtils;
import com.chinatelecom.gs.engine.kms.base.model.CodeParam;
import com.chinatelecom.gs.engine.kms.dto.knowledge.KnowledgeImportByKeyDTO;
import com.chinatelecom.gs.engine.kms.sdk.constants.KmsApis;
import com.chinatelecom.gs.engine.kms.sdk.enums.EnvType;
import com.chinatelecom.gs.engine.kms.sdk.enums.KnowledgeInfoType;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.BatchTagParam;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.KnowledgeCodeParam;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.KnowledgeImportByKeyParam;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.KnowledgeReparseBatchParam;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.KnowledgeReparseBatchVO;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.KnowledgeReparseParam;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.KnowledgeUpdateParam;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.KnowledgeVO;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.KnowledgeWithExtraVO;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.base.KnowledgeParseConfig;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.publish.KnowledgeProd;
import com.chinatelecom.gs.engine.kms.service.KnowledgeAppService;
import com.chinatelecom.gs.engine.kms.service.KnowledgeBaseService;
import com.chinatelecom.gs.engine.kms.service.KnowledgeExtraInfoAppService;
import com.chinatelecom.gs.engine.kms.service.KnowledgeProdAppService;
import com.chinatelecom.gs.engine.sdk.common.annotation.ApiType;
import com.chinatelecom.gs.engine.sdk.common.annotation.HideFromApiTypes;
import com.chinatelecom.gs.privilege.common.enums.PrivilegeEnum;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import com.chinatelelcom.gs.engine.sdk.common.enums.RequestSourceType;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import com.google.common.collect.Lists;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024年12月04日
 */
@RestController
@Slf4j
@Tag(name = "知识管理 Controller")
@RequestMapping({KmsApis.KMS_API + KmsApis.KNOWLEDGE,
        KmsApis.RPC + KmsApis.KNOWLEDGE,
        KmsApis.OPENAPI + KmsApis.KNOWLEDGE})
public class KnowledgeController {

    @Resource
    private KnowledgeAppService knowledgeAppService;

    @Resource
    private KnowledgeExtraInfoAppService knowledgeExtraInfoAppService;

    @Resource
    private KnowledgeProdAppService knowledgeProdAppService;

    @Resource
    @Lazy
    private KnowledgeBaseService knowledgeBaseService;


    @Operation(summary = "从路径导入知识", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "从路径导入知识", groupName = "知识管理")
    @DebugLog(operation = "从路径导入知识")
    @AuditLog(businessType = "知识管理", operType = "从路径导入知识", objId="#param.knowledgeBaseCode")
    @PostMapping(value = KmsApis.IMPORT_BY_KEY_API)
    @PermissionTag(code = {KsMenuConfig.KNOWLEDGE_BASE, KsMenuConfig.KNOWLEDGE_BASE_1, MenuConfig.WORD_KNOWLEDGE, MenuConfig.QA_KNOWLEDGE})
    public Result<List<KnowledgeVO>> importKnowledgeByKey(@Validated @RequestBody KnowledgeImportByKeyParam param) {
        List<KnowledgeVO> knowledgeVOS = Lists.newArrayList();
        if (CollectionUtils.isEmpty(param.getFiles())) {
            throw new BizException("AA001", "上传文件为空");
        }
        knowledgeBaseService.checkKnowledgeBaseAuth(param.getKnowledgeBaseCode(), PrivilegeEnum.edit);
        for (KnowledgeImportByKeyParam.FileData fileData : param.getFiles()) {
            KnowledgeImportByKeyDTO importByKeyDTO = KnowledgeImportByKeyDTO.builder()
                    .knowledgeBaseCode(param.getKnowledgeBaseCode())
                    .parentCode(param.getParentCode())
                    .config(param.getConfig())
                    .prevCode(param.getPrevCode())
                    .fileKey(fileData.getFileKey())
                    .fileName(fileData.getFileName())
                    .build();
            KnowledgeVO importResult = knowledgeAppService.importKnowledge(importByKeyDTO);
            knowledgeVOS.add(importResult);
        }
        return Result.success(knowledgeVOS);
    }

    @Operation(summary = "重新解析", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "重新解析", groupName = "知识管理")
    @AuditLog(businessType = "知识管理", operType = "重新解析", operDesc = "重新解析", objId="#param.knowledgeCode")
    @DebugLog(operation = "重新解析")
    @PostMapping(value = KmsApis.REPARSE)
    @PermissionTag(code = {KsMenuConfig.KNOWLEDGE_BASE, KsMenuConfig.KNOWLEDGE_BASE_1, MenuConfig.WORD_KNOWLEDGE, MenuConfig.QA_KNOWLEDGE})
    public Result<Boolean> reparse(@Validated @RequestBody KnowledgeReparseParam param) {
        knowledgeAppService.reparse(param);
        return Result.success(true);
    }

    @Operation(summary = "批量重新解析", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "批量重新解析", groupName = "知识管理")
    @AuditLog(businessType = "知识管理", operType = "批量重新解析", operDesc = "重新解析", objId="#param.knowledgeCodes")
    @DebugLog(operation = "批量重新解析")
    @PostMapping(value = KmsApis.REPARSE + KmsApis.BATCH)
    @PermissionTag(code = {KsMenuConfig.KNOWLEDGE_BASE, KsMenuConfig.KNOWLEDGE_BASE_1, MenuConfig.WORD_KNOWLEDGE, MenuConfig.QA_KNOWLEDGE})
    public Result<List<KnowledgeReparseBatchVO>> reparseBatch(@Validated @RequestBody KnowledgeReparseBatchParam param) {
        List<String> knowledgeCodes = param.getKnowledgeCodes();
        List<KnowledgeReparseBatchVO> result = new ArrayList<>(knowledgeCodes.size());
        for (String knowledgeCode : knowledgeCodes) {
            try {
                KnowledgeReparseParam oneParam = new KnowledgeReparseParam();
                oneParam.setKnowledgeCode(knowledgeCode);
                knowledgeAppService.reparse(oneParam);
                result.add(new KnowledgeReparseBatchVO(knowledgeCode, true));
            } catch (Exception e) {
                log.error("重新解析提交失败", e);
                result.add(new KnowledgeReparseBatchVO(knowledgeCode, false));
            }
        }
        return Result.success(result);
    }

    @Operation(summary = "获取知识解析配置", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "获取知识解析配置", groupName = "知识管理")
    @AuditLog(businessType = "知识管理", operType = "获取知识解析配置", operDesc = "获取知识解析配置", objId="#param.knowledgeCode")
    @DebugLog(operation = "获取知识解析配置")
    @PostMapping(value = KmsApis.CONFIG)
    public Result<KnowledgeParseConfig> config(@Validated @RequestBody KnowledgeCodeParam param) {
        String content = knowledgeExtraInfoAppService.getContentByType(param.getKnowledgeCode(), KnowledgeInfoType.PARSE_CONFIG);
        KnowledgeParseConfig config = null;
        if (StringUtils.isNotBlank(content)) {
            config = JsonUtils.parseObject(content, KnowledgeParseConfig.class);
        }
        return Result.success(config);
    }

    @Operation(summary = "知识详情", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "知识详情", groupName = "知识管理")
    @AuditLog(businessType = "知识管理", operType = "知识详情", operDesc = "知识详情", objId="#code")
    @GetMapping(KmsApis.CODE_PATH)
    public Result<KnowledgeVO> get(@PathVariable("code") String code,
                                   @RequestParam(value = "env", required = false, defaultValue = "TEST") String env) {
        // 先获取测试环境数据
        KnowledgeVO testVO = knowledgeAppService.get(code);
        if (testVO == null) {
            return Result.success(null);
        }

        // 如果env为PROD，则查询生产环境数据并覆盖测试环境数据
        if (EnvType.PROD.name().equalsIgnoreCase(env)) {
            KnowledgeProd prodKnowledge = knowledgeProdAppService.queryPublishWithProd(code);
            if (prodKnowledge != null) {
                testVO.setKnowledgeBaseCode(prodKnowledge.getKnowledgeBaseCode());
                testVO.setName(prodKnowledge.getName());
                testVO.setType(prodKnowledge.getType());
                testVO.setOn(prodKnowledge.getOn());
                testVO.setTagCodes(prodKnowledge.getTags());
            }
        }
        return Result.success(testVO);
    }

    @Operation(summary = "知识删除", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "知识删除", groupName = "知识管理")
    @AuditLog(businessType = "知识管理", operType = "知识删除", operDesc = "知识删除", objId="#codeParam.codes")
    @PostMapping(KmsApis.DELETE_API)
    @PermissionTag(code = {KsMenuConfig.KNOWLEDGE_BASE, KsMenuConfig.KNOWLEDGE_BASE_1, MenuConfig.WORD_KNOWLEDGE, MenuConfig.QA_KNOWLEDGE})
    public Result<Boolean> delete(@Validated @RequestBody CodeParam codeParam) {
        if (CollectionUtils.isNotEmpty(codeParam.getCodes())) {
            for (String code : codeParam.getCodes()) {
                knowledgeAppService.publishDeleteFlag(code);
            }
        }
        return Result.success(true);
    }

    @Operation(summary = "知识开关", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "知识开关", groupName = "知识管理")
    @AuditLog(businessType = "知识管理", operType = "知识开关", operDesc = "知识开关", objId="#code")
    @PutMapping(KmsApis.SWITCH_API + KmsApis.CODE_PATH)
    @PermissionTag(code = {KsMenuConfig.KNOWLEDGE_BASE, KsMenuConfig.KNOWLEDGE_BASE_1, MenuConfig.WORD_KNOWLEDGE, MenuConfig.QA_KNOWLEDGE})
    public Result<String> switchOn(@PathVariable("code") String code, @RequestParam Boolean on) {
        knowledgeAppService.switchOn(code, on);
        return Result.success(null);
    }

    @Operation(summary = "更新知识详情", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "更新知识详情", groupName = "知识管理")
    @AuditLog(businessType = "知识管理", operType = "更新知识详情", operDesc = "更新知识详情", objId="#code")
    @PutMapping(KmsApis.CODE_PATH)
    public Result<Boolean> update(@PathVariable("code") String code, @Validated @RequestBody KnowledgeUpdateParam dto) {
        return Result.success(knowledgeAppService.update(code, dto));

    }

    @Operation(summary = "批量打标", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "批量打标", groupName = "知识管理")
    @AuditLog(businessType = "知识管理", operType = "批量打标", operDesc = "批量打标", objId="#param.tagCodes")
    @PostMapping(KmsApis.BATCH_TAGGING_API)
    public Result<Boolean> batchTagging(@Validated @RequestBody BatchTagParam batchTagParam) {
        return Result.success(knowledgeAppService.batchTagging(batchTagParam));
    }

    @HideFromApiTypes(ApiType.OPENAPI)
    @Operation(summary = "获取知识扩展信息", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "获取知识扩展信息", groupName = "知识管理")
    @AuditLog(businessType = "知识管理", operType = "获取知识扩展信息", operDesc = "获取知识扩展信息", objId="#code")
    @GetMapping(KmsApis.EXTRA_API + KmsApis.INFO_API+ KmsApis.CODE_PATH)
    public Result<KnowledgeWithExtraVO> getKnowledgeWithExtra(@PathVariable("code") String code) {
        // bot判断总结之后会直接从这里查询文章片段，这里需要限制多模态搜索
        if (RequestSourceType.RPC == RequestContext.getRequestSourceType()) {
            // 没有多模态禁止查询
            boolean supportMultiModelRecall = UserInfoUtils.checkSupportMultiModelRecall();
            if (!supportMultiModelRecall) {
                log.info("当前查询不具备多模态权限，限制多模态文件片段的查询");
                return Result.failed("A0019", "前查询不具备多模态权限，限制多模态文件片段的查询");
            }
        }

        return Result.success(knowledgeAppService.getKnowledgeWithExtra(code));
    }
}
