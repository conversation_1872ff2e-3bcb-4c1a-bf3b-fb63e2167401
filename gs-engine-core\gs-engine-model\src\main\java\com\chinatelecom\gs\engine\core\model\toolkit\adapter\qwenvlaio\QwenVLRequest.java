package com.chinatelecom.gs.engine.core.model.toolkit.adapter.qwenvlaio;

import com.chinatelecom.gs.engine.core.sdk.vo.llm.BaseLLMRequest;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

@Data
public class QwenVLRequest implements BaseLLMRequest {

    private String model;

    private List<QwenVLMessage> messages;

    private Integer max_tokens = 2000;

    boolean stream = true;

//    private boolean do_sample = true;
//
//    private double repetition_penalty = 1.0;
//
    private double temperature = 0;

    private double top_p = 0.001;

    private Integer top_k = 1;

    /**
     * 获取输入内容
     *
     * @return
     */
    @Override
    public String inputContent() {
        StringBuilder inputBuilder = new StringBuilder();
        if(CollectionUtils.isNotEmpty(messages)){
            for(QwenVLMessage message: messages){
                if(CollectionUtils.isNotEmpty(message.getContent())){
                    for(QwenVLMessage.ContentDetail content: message.getContent()){
                        inputBuilder.append(content.getText());
                    }
                }
            }
        }
        return inputBuilder.toString();
    }
}
