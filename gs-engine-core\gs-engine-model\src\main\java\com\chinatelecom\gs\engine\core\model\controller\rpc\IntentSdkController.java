package com.chinatelecom.gs.engine.core.model.controller.rpc;

import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.common.constants.Constants;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.core.model.service.IntentRecognitionService;
import com.chinatelecom.gs.engine.core.sdk.rpc.IntentServiceClient;
import com.chinatelecom.gs.engine.core.sdk.vo.IntentionRecognitionParam;
import com.chinatelecom.gs.engine.core.sdk.vo.IntentionRecognitionResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.util.List;

/**
 * Author: tangxuetao130
 */
@Tag(name = "模型rpc接口")
@RestController
@RequestMapping(Constants.BASE_PREFIX + Constants.RPC_PREFIX + "/intent")
public class IntentSdkController implements IntentServiceClient {


    @Autowired
    private IntentRecognitionService intentRecognitionService;

    @Operation(summary = "意图识别", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "意图识别", groupName = "意图管理")
    @PostMapping("/intent/recognition")
    @AuditLog(businessType = "模型rpc接口", operType = "意图识别", operDesc = "意图识别", objId="#param.query")
    public List<IntentionRecognitionResponse> intentionRecognition(@RequestBody @Valid IntentionRecognitionParam param) {
        return intentRecognitionService.intentRecognitionList(param);
    }

}
