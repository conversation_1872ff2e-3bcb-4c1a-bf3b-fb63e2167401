package com.chinatelecom.gs.engine.kms.common.report;

import com.chinatelecom.gs.engine.common.utils.IdGenerator;
import com.chinatelecom.gs.engine.common.utils.JsonUtils;
import com.chinatelecom.gs.engine.core.sdk.vo.llm.LLMMessage;
import com.chinatelecom.gs.engine.kms.sdk.enums.ReportGenStatus;
import com.chinatelecom.gs.engine.kms.sdk.enums.SseEventType;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.report.ReportOutline;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import okhttp3.ResponseBody;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import retrofit2.Call;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2025年05月21日
 */
@Data
@Slf4j
public class MultipleSseStream {


    private LinkedBlockingQueue<String> queue = new LinkedBlockingQueue();

    /**
     * 是否被终止
     */
    @Getter
    private volatile boolean aborted = false;

    /**
     * 是否正常完成
     */
    @Getter
    private volatile boolean finished = false;

    /**
     * 记录正文内容
     */
    private StringBuffer contentBuffer = new StringBuffer();

    /**
     * 当前请求的大纲数据
     */
    private AtomicReference<MultipleReportRequest> currentRequest = new AtomicReference<>();

    /**
     * 记录当前大纲是否已经输出过了
     */
    private final Map<String, Boolean> outputOutlineMap = new ConcurrentHashMap<>();

    @Getter
    private final SseEmitter emitter;

    public MultipleSseStream(SseEmitter emitter) {
        this.emitter = emitter;
    }

    /**
     * 更新当前请求数据
     *
     * @param request
     */
    public void setCurrentRequest(MultipleReportRequest request) {
        currentRequest.set(request);
    }

    /**
     * 获取当前请求数据
     *
     * @return
     */
    public MultipleReportRequest getCurrentRequest() {
        return currentRequest.get();
    }

    public void onNext(Call<ResponseBody> call, LLMMessage token) {
        if (token == null) {
            return;
        }
        if (aborted) {
            if (call != null && !call.isCanceled()) {
                call.cancel();
            }
            return;
        }
        if (finished) {
            throw new BizException("AA105", "报告已经终止，无法继续输出");
        }

        //输出大纲标题
        sendOutlineLevel(token);

        // 做协议转换
        ReportMessage reportMessage = buildReportMessage(token);
        if (StringUtils.isNotEmpty(reportMessage.getText())) {
            contentBuffer.append(reportMessage.getText());
        }
        queue.add(JsonUtils.toJsonString(reportMessage));
    }

    public void push(int timeout) {
        try {
            long start = System.currentTimeMillis();
            int timeoutMillis = timeout * 1000;
            while (!finished) {
                String output = queue.poll(1, TimeUnit.SECONDS);
                if (output != null) {
                    sendMessage(output);
                }

                if (aborted) {
                    // 被终止，做终止处理
                    log.warn("输出已终止");
                    break;
                }
                if (System.currentTimeMillis() - start > timeoutMillis) {
                    log.warn("输出超时，中断处理");
                    abort(new BizException("B0001", "输出超时"));
                    break;
                }
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            abort(e);
            emitter.completeWithError(e);
            log.error("推送数据异常，可能用户手动终止，该错误将忽略", e);
        } finally {
            emitter.complete();
        }
    }

    /**
     * 大纲生成阶段性开始
     */
    public void phasedStart() {
        contentBuffer.delete(0, contentBuffer.length());
        MultipleReportRequest request = getCurrentRequest();
        if (request != null) {
            ReportOutline outline = request.getReportOutline();
            if (outline != null) {
                outline.setStatus(ReportGenStatus.pending);
            }
        }
    }

    /**
     * 大纲生成阶段性完成
     */
    public void phasedCompletion() {
        String content = contentBuffer.toString();
        MultipleReportRequest request = getCurrentRequest();
        if (request != null) {
            ReportOutline outline = request.getReportOutline();
            log.info("当前大纲生成完成，大纲：{}，内容：{}", outline.getLevelTitleMd(), content);
            if (outline != null) {
                outline.setContent(content);
                outline.setStatus(ReportGenStatus.finished);
                // 推送节点状态更新处理
                queue.add(JsonUtils.toJsonString(buildReportMessage(SseEventType.add, StringUtils.EMPTY)));
            }
        }
    }

    public void finallyComplete() {
        try {
            if (!finished) {
                String message = JsonUtils.toJsonString(buildReportMessage(SseEventType.finish, StringUtils.EMPTY));
                sendMessage(message);
            }
        } catch (IOException e) {
            log.error("发送消息异常", e);
        } finally {
            finished = true;
        }
    }

    public void finished() {
        finished = true;
    }

    public void abort(Throwable e) {
        aborted = true;
    }

    private void sendMessage(String message) throws IOException {
        emitter.send(SseEmitter.event()
                .id(IdGenerator.id())
                .name("msg") // 自定义事件名
                .data(message)
                .reconnectTime(5000));
    }


    /**
     * 构造报告输出协议
     *
     * @param token
     * @return
     */
    private ReportMessage buildReportMessage(LLMMessage token) {
        ReportMessage msg = buildReportMessage(SseEventType.add, token.getContent());
        msg.setReasoningContent(token.getReasoning_content());
        return msg;
    }

    /**
     * 构造报告输出协议
     *
     * @param token
     * @return
     */
    private ReportMessage buildReportMessage(SseEventType eventType, String token) {
        ReportMessage msg = new ReportMessage();
        msg.setEventType(eventType);
        MultipleReportRequest req = getCurrentRequest();
        if (req != null && req.getReportOutline() != null) {
            ReportOutline reportOutline = req.getReportOutline();
            if (reportOutline != null) {
                ReportContentExtra extra = ReportContentExtra.builder().level(reportOutline.getLevel())
                        .title(reportOutline.getTitle()).status(reportOutline.getStatus()).build();
                msg.setExtraData(extra);
            }
        }
        msg.setText(token);
        return msg;
    }

    /**
     * 发送当前大纲的标题信息, 理论上只有第一次输出正文之前会执行
     */
    private void sendOutlineLevel(LLMMessage token) {
        String content = token.getContent();
        MultipleReportRequest request = getCurrentRequest();
        if (StringUtils.isNotEmpty(content) && request != null) {
            ReportOutline reportOutline = request.getReportOutline();
            if (reportOutline != null) {
                String level = reportOutline.getLevel();
                if (StringUtils.isNotBlank(level)) {
                    if (!outputOutlineMap.containsKey(level)) {
                        // 直接输出大纲标题
                        queue.add(JsonUtils.toJsonString(buildReportMessage(SseEventType.add, reportOutline.getLevelTitleMd())));
                        outputOutlineMap.put(level, true);
                    }
                }
            }
        }
    }
}
