package com.chinatelecom.gs.engine.kms.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.utils.BizAssert;
import com.chinatelecom.gs.engine.common.utils.IPageUtils;
import com.chinatelecom.gs.engine.common.utils.IdGenerator;
import com.chinatelecom.gs.engine.common.utils.TransactionCommitHandler;
import com.chinatelecom.gs.engine.core.manager.convert.ConfigConvert;
import com.chinatelecom.gs.engine.core.manager.service.BaseConfigAppService;
import com.chinatelecom.gs.engine.core.manager.vo.config.PublishConfig;
import com.chinatelecom.gs.engine.kms.base.model.CodeParam;
import com.chinatelecom.gs.engine.kms.base.service.impl.BaseAppServiceByCodeImpl;
import com.chinatelecom.gs.engine.kms.convert.dto.FaqQuestionMapper;
import com.chinatelecom.gs.engine.kms.convert.vo.KnowledgeFaqVoConverter;
import com.chinatelecom.gs.engine.kms.dto.FaqSimilarQuestionDTO;
import com.chinatelecom.gs.engine.kms.dto.KnowledgeFaqDTO;
import com.chinatelecom.gs.engine.kms.dto.SkipFaqQuestion;
import com.chinatelecom.gs.engine.kms.flow.parse.dto.FaqExportData;
import com.chinatelecom.gs.engine.kms.infra.po.KnowledgeFaqPO;
import com.chinatelecom.gs.engine.kms.model.dto.KnowledgeDTO;
import com.chinatelecom.gs.engine.kms.repository.*;
import com.chinatelecom.gs.engine.kms.sdk.enums.PublishStatus;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.faq.KnowledgeFaqCreateParam;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.faq.KnowledgeFaqQueryParam;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.faq.KnowledgeFaqUpdateParam;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.faq.KnowledgeFaqVO;
import com.chinatelecom.gs.engine.kms.search.SearchService;
import com.chinatelecom.gs.engine.kms.service.KnowledgeAppService;
import com.chinatelecom.gs.engine.kms.service.KnowledgeBaseService;
import com.chinatelecom.gs.engine.kms.service.KnowledgeFaqAppService;
import com.chinatelecom.gs.engine.kms.util.KnowledgePublishCheckUtils;
import com.chinatelecom.gs.engine.kms.util.SortUtils;
import com.chinatelecom.gs.privilege.common.enums.PrivilegeEnum;
import com.chinatelelcom.gs.engine.sdk.common.Page;
import com.chinatelelcom.gs.engine.sdk.common.PageImpl;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import com.chinatelelcom.gs.engine.sdk.common.utils.SpringContextUtils;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableSet;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 问答表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-05
 */
@Service
@Validated
@Slf4j
public class KnowledgeFaqAppServiceImpl extends BaseAppServiceByCodeImpl<KnowledgeFaqRepository,
        KnowledgeFaqQueryParam, KnowledgeFaqVoConverter, KnowledgeFaqPO, KnowledgeFaqDTO, KnowledgeFaqVO, KnowledgeFaqCreateParam, KnowledgeFaqUpdateParam>
        implements KnowledgeFaqAppService {

    @Resource
    @Lazy
    private KnowledgeAppService knowledgeAppService;

    @Resource
    @Lazy
    private KnowledgeBaseService knowledgeBaseService;

    @Resource
    @Lazy
    private KnowledgeFaqProdRepository knowledgeFaqProdRepository;

    public SkipFaqQuestion convert(KnowledgeFaqDTO faqQuestionDTO) {
        return FaqQuestionMapper.INSTANCE.toSkipFaqQuestion(faqQuestionDTO);
    }

    public SkipFaqQuestion convert(FaqSimilarQuestionDTO faqQuestionDTO) {
        return FaqQuestionMapper.INSTANCE.toSkipFaqQuestion(faqQuestionDTO);
    }


    @Resource
    private SearchService searchService;
    @Resource
    private FaqSimilarQuestionRepository faqSimilarQuestionRepository;
    @Resource
    private KnowledgeRepository knowledgeRepository;
    @Resource
    private TransactionCommitHandler transactionCommitHandler;
    @Resource
    private BaseConfigAppService configService;
    @Resource
    private KnowledgeProdRepository knowledgeProdRepository;
    protected KnowledgeFaqVoConverter converter() {
        return KnowledgeFaqVoConverter.INSTANCE;
    }

    @Override
    protected Wrapper<KnowledgeFaqPO> pageQueryWrapper(KnowledgeFaqQueryParam query) {
        LambdaQueryWrapper<KnowledgeFaqPO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(query.getQuestion())) {
            lambdaQueryWrapper.like(KnowledgeFaqPO::getQuestion, query.getQuestion());
        }
        if (StringUtils.isNotBlank(query.getKnowledgeCode())) {
            lambdaQueryWrapper.eq(KnowledgeFaqPO::getKnowledgeCode, query.getKnowledgeCode());
        }
        if (query.getIsAuto() != null) {
            lambdaQueryWrapper.eq(KnowledgeFaqPO::getIsAuto, query.getIsAuto());
        }
        if (StringUtils.isNotBlank(query.getCode())) {
            lambdaQueryWrapper.eq(KnowledgeFaqPO::getCode, query.getCode());
        }

        //获取开关配置
        PublishConfig config = configService.getConfigOrDefault(ConfigConvert.PUBLISH_CONFIG, RequestContext.getTenantId());
        if (!config.getPublishSwitch()) {
            // 发布功能关闭时不显示删除未发布的数据
            PublishStatus publishStatus = query.getPublishStatus();
            if (publishStatus != null) {
                if (publishStatus != PublishStatus.DELETE_UNPUBLISH) {
                    lambdaQueryWrapper.eq(KnowledgeFaqPO::getPublishStatus, publishStatus);
                } else {
                    return null;
                }
            } else {
                lambdaQueryWrapper.in(KnowledgeFaqPO::getPublishStatus, PublishStatus.NOT_DELETE.stream().map(PublishStatus::name).collect(Collectors.toList()));
            }
        }

        if (query.getOrder() != null) {
            SortUtils.buildOrderBy(lambdaQueryWrapper, query.getOrder());
        } else {
            lambdaQueryWrapper.orderByDesc(KnowledgeFaqPO::getId);
        }
        return lambdaQueryWrapper;
    }

    public static boolean hasDuplicates(List<String> list) {
        long distinctCount = list.stream().distinct().count();
        return distinctCount != list.size();
    }

    @Override
    public Page<KnowledgeFaqVO> pageQuery(KnowledgeFaqQueryParam query) {
        IPage<KnowledgeFaqDTO> page = new PageDTO<>(query.getPageNum(), query.getPageSize());
        Wrapper<KnowledgeFaqPO> queryWrapper = pageQueryWrapper(query);
        if (queryWrapper == null) {
            return PageImpl.of(0, 0);
        }
        page = repository.page(page, queryWrapper);
        List<KnowledgeFaqDTO> records = page.getRecords();
        fetchSimilarQuestion(records);
        return IPageUtils.convert(page, dto -> converter().convertVO(dto));
    }

    private void fetchSimilarQuestion(List<KnowledgeFaqDTO> records) {
        for (KnowledgeFaqDTO faqDTO : records) {
            List<FaqSimilarQuestionDTO> similarQuestionDTOS = faqSimilarQuestionRepository.selectByFaqCode(faqDTO.getCode());
            faqDTO.setCorpus(similarQuestionDTOS.stream()
                    .map(FaqSimilarQuestionDTO::getSimilarQuestion)
                    .collect(Collectors.toList()));
        }
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean switchOn(String code, Boolean on) {
        KnowledgeFaqDTO faqDTO = repository.selectByCode(code);
        BizAssert.notNull(faqDTO, "AA022", "问答对不存在");
        KnowledgePublishCheckUtils.updatePublishStatusCheck(faqDTO.getPublishStatus());

        repository.switchOn(code, on);
        boolean updateUpdateStatus = this.updatePublishStatus2Unpublished(null, ImmutableList.of(code));

        updateFaqKnowledgeStatus(faqDTO.getKnowledgeCode());
        // 更新索引
        searchService.faqSwitchOn(faqDTO.getKnowledgeCode(), code, on, updateUpdateStatus);
        return true;
    }

    @Override
    public boolean importFaq(Collection<KnowledgeFaqDTO> faqList) {
        for (KnowledgeFaqDTO dto : faqList) {
            dto.setCode(IdGenerator.getId(StringUtils.EMPTY));
            if (dto.getCorpus() != null) {
                dto.setCorpusCount(dto.getCorpus().size());
            }
            this.save(dto);
        }
        return true;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void publishDeleteFlag(Collection<String> faqCodes) {
        if (CollectionUtils.isNotEmpty(faqCodes)) {
            List<KnowledgeFaqDTO> faqDTOList = repository.listByCodes(faqCodes);
            BizAssert.notEmpty(faqDTOList, "AA022", "问答对不存在");

            for (KnowledgeFaqDTO faqDTO : faqDTOList) {
                BizAssert.assertFalse(PublishStatus.NOT_ALLOW_DELETE.contains(faqDTO.getPublishStatus()),
                        "AA029", "当前发布状态不允许删除：{}", faqDTO.getPublishStatus());
                if (Objects.equals(faqDTO.getPublishStatus(), PublishStatus.UNPUBLISHED)) {
                    // 直接删除
                    // 执行真正的删除操作
                    CodeParam codeParam = new CodeParam();
                    codeParam.setCodes(ImmutableList.of(faqDTO.getCode()));
                    searchService.deleteFaqData(faqDTO.getKnowledgeCode(), faqDTO.getCode());
                    SpringContextUtils.getBean(this.getClass()).delete(codeParam);
                    faqCodes.remove(faqDTO.getCode());
                }
            }

            Set<String> knowledgeCodes = faqDTOList.stream().map(KnowledgeFaqDTO::getKnowledgeCode).collect(Collectors.toSet());
            BizAssert.assertTrue(knowledgeCodes.size() == 1, "AA030", "禁止跨文档进行批量操纵");

            if (CollectionUtils.isNotEmpty(faqCodes)) {
                repository.updatePublishStatusByFaqCodes(faqCodes, PublishStatus.DELETE_UNPUBLISH);
            }
            Optional<String> first = knowledgeCodes.stream().findFirst();
            if (first.isPresent()) {
                String knowledgeCode = first.get();
                updateFaqKnowledgeStatus(knowledgeCode);
                searchService.updatePublishStatusByFaqCodeDel(knowledgeCode, faqCodes);
            }
        }
    }

    @Override
    public List<SkipFaqQuestion> createBatch(List<KnowledgeFaqCreateParam> createParams) {
        List<SkipFaqQuestion> createResult = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(createParams)) {
            for (KnowledgeFaqCreateParam param : createParams) {
                try {
                    SkipFaqQuestion result = skipCreate(param);
                    if (result != null) {
                        createResult.add(result);
                    }
                } catch (Exception e) {
                    log.error("导入FAQ失败，question:{}", param.getQuestion(), e);
                    createResult.add(convert(converter().convertCreate(param)));
                }
            }
        }
        return createResult;
    }

    @Override
    public KnowledgeFaqVO create(KnowledgeFaqCreateParam createParam) {
        KnowledgeFaqDTO dto = converter().convertCreate(createParam);
        if (StringUtils.isNotBlank(createParam.getCode())) {
            KnowledgeFaqDTO existDto = repository.selectByCode(createParam.getCode());
            BizAssert.isNull(existDto, "A0003", "编码重复");
        } else {
            dto.setCode(IdGenerator.getId(StringUtils.EMPTY));
        }
        if (dto.getCorpus() != null) {
            dto.setCorpusCount(dto.getCorpus().size());
        }
        return this.save(dto);
    }

    @Override
    public KnowledgeFaqVO get(String code) {
        LambdaQueryWrapper<KnowledgeFaqPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(KnowledgeFaqPO::getCode, code);
        KnowledgeFaqDTO dto = repository.selectOne(wrapper);
        BizAssert.notNull(dto, "AA012", "未查询到问答对：{}", code);
        PrivilegeEnum hasPrivilege = knowledgeBaseService.checkKnowledgeBaseAuth(dto.getKnowledgeBaseCode(), PrivilegeEnum.view);

        fetchSimilarQuestion(Collections.singletonList(dto));
        return converter().convertVO(dto);
    }

    private KnowledgeFaqVO save(KnowledgeFaqDTO dto) {
        setKnowledgeBaseCode(dto);
        checkSimilarQuestion(dto);
        repository.save(dto);
        saveSimilarQuestion(dto);
        updateFaqKnowledgeStatus(dto.getKnowledgeCode());
        searchService.pushFaqData(dto);
        return converter().convertVO(dto);
    }

    @Override
    public SkipFaqQuestion skipCreate(KnowledgeFaqCreateParam createParam) {
        KnowledgeFaqDTO dto = converter().convertCreate(createParam);
        if (StringUtils.isNotBlank(createParam.getCode())) {
            KnowledgeFaqDTO existDto = repository.selectByCode(createParam.getCode());
            BizAssert.isNull(existDto, "A0003", "编码重复");
        } else {
            dto.setCode(IdGenerator.getId(StringUtils.EMPTY));
        }
        if (dto.getCorpus() != null) {
            dto.setCorpusCount(dto.getCorpus().size());
        }
        setKnowledgeBaseCode(dto);

        Map<String, SkipFaqQuestion> skipFaqQuestionMap = this.skipSimilarQuestion(dto);
        String mainQuestion = dto.getQuestion();
        SkipFaqQuestion skipFaqQuestion = skipFaqQuestionMap.get(mainQuestion);
        if (skipFaqQuestion != null) {
            //如果问题重复 中断
            log.error("问题重复，中断入库。faqCode:{} 问题：{}", skipFaqQuestion.getFaqCode(), skipFaqQuestion.getQuestion());
            return skipFaqQuestion;
        }

        repository.save(dto);
        saveSimilarQuestion(dto);
        updateFaqKnowledgeStatus(dto.getKnowledgeCode());
        searchService.pushFaqData(dto);
        return null;
    }

    @Override
    public boolean updatePublishStatus2Unpublished(Collection<String> knowledgeCodes, Collection<String> faqs) {
        if (CollectionUtils.isEmpty(knowledgeCodes) && CollectionUtils.isEmpty(faqs)) {
            return false;
        }
        if (knowledgeCodes == null) {
            knowledgeCodes = ImmutableList.of();
        }
        if (faqs == null) {
            faqs = ImmutableList.of();
        }

        boolean result = false;
        Set<String> hasPublishRecord = knowledgeFaqProdRepository.selectRecordByCode(knowledgeCodes, faqs);
        Collection<String> noPublishRecord = CollectionUtils.subtract(faqs, hasPublishRecord);
        if (CollectionUtils.isNotEmpty(hasPublishRecord)) {
            result = repository.updatePublishStatus2Update(hasPublishRecord);
        }
        if (CollectionUtils.isNotEmpty(noPublishRecord)) {
            repository.updatePublishStatus2Unpublished(noPublishRecord);
        }
        return result;
    }

    @Override
    public PublishStatus updateFaqKnowledgeStatus(String knowledgeCode) {
        // 统计整合当前FAQ的状态，调整文档的状态
        Set<PublishStatus> faqPublishStatus = repository.statisticalPublishState(knowledgeCode);
        Set<String> hasPublishRecord = knowledgeProdRepository.selectRecordByCode(ImmutableSet.of(knowledgeCode));
        PublishStatus publishStatus = convert(faqPublishStatus, hasPublishRecord);
        knowledgeRepository.updatePublishStatus(ImmutableList.of(knowledgeCode), publishStatus);
        return publishStatus;
    }

    @Override
    public void export(String knowledgeCode, HttpServletResponse response) {
        // 查询数据
        List<KnowledgeFaqDTO> list = repository.queryByKnowledgeCode(knowledgeCode);
        fetchSimilarQuestion(list);

        // 转换为导出对象
        List<FaqExportData> exportList = list.stream()
                .map(dto -> {
                    FaqExportData r = new FaqExportData();
                    BeanUtils.copyProperties(dto, r);
                    // 处理意图例句和规则的换行显示
                    r.setCorpusList(dto.getCorpus());
                    return r;
                })
                .collect(Collectors.toList());

        try (OutputStream outputStream = response.getOutputStream()) {
            // 创建导出参数
            ExportParams params = new ExportParams("问答列表", "sheet1", ExcelType.XSSF);
            params.setHeight((short) 40);
            // 导出Excel文件
            Workbook workbook = ExcelExportUtil.exportExcel(params, FaqExportData.class, exportList);

            // 设置响应头
            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("问答列表导出.xlsx", "UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);

            // 写入响应流
            workbook.write(outputStream);
            outputStream.flush();
            workbook.close();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

    }

    public void setKnowledgeBaseCode(KnowledgeFaqDTO dto) {
        KnowledgeDTO knowledgeDTO = knowledgeRepository.selectByCode(dto.getKnowledgeCode());
        dto.setKnowledgeBaseCode(knowledgeDTO.getKnowledgeBaseCode());
    }

    private void saveSimilarQuestion(KnowledgeFaqDTO dto) {
        List<FaqSimilarQuestionDTO> similarList = new ArrayList<>();
        for (String corpus : dto.getCorpus()) {
            FaqSimilarQuestionDTO similarQDTO = new FaqSimilarQuestionDTO();
            similarQDTO.setSimilarQuestion(corpus);
            similarQDTO.setKnowledgeCode(dto.getKnowledgeCode());
            similarQDTO.setFaqCode(dto.getCode());
            similarQDTO.setKnowledgeBaseCode(dto.getKnowledgeBaseCode());
            similarQDTO.setCode(IdGenerator.getId(StringUtils.EMPTY));
            similarList.add(similarQDTO);
        }
        if (CollectionUtils.isNotEmpty(similarList)) {
            faqSimilarQuestionRepository.saveBatch(similarList);
        }
    }

    protected void checkSimilarQuestion(KnowledgeFaqDTO dto) {
        List<String> questions = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dto.getCorpus())) {
            questions.addAll(dto.getCorpus());
        }
        questions.add(dto.getQuestion());
        if (hasDuplicates(questions)) {
            String message = "相似问题和问题存在重复，请检查！" + dto.getQuestion();
            throw new BizException("A0000", ArrayUtils.toArray(message), message, message);
        }
        List<KnowledgeFaqDTO> questionMains = repository.findQuestion(questions, dto.getKnowledgeBaseCode());
        if (CollectionUtils.isNotEmpty(questionMains)) {
            // 过滤掉code相同的记录(修改操作)
            questionMains = questionMains.stream()
                    .filter(q -> !q.getCode().equals(dto.getCode()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(questionMains)) {
                StringBuilder message = new StringBuilder("问题已存在，请检查：");
                for (KnowledgeFaqDTO questionMain : questionMains) {
                    message.append(questionMain.getQuestion());
                }
                throw new BizException("A0000", ArrayUtils.toArray(message.toString()), message.toString(), message);
            }
        }
        List<FaqSimilarQuestionDTO> similarQuestions = faqSimilarQuestionRepository.findSimilarQuestion(questions, dto.getKnowledgeCode());
        if (CollectionUtils.isNotEmpty(similarQuestions)) {
            // 过滤掉faqCode相同的记录(修改操作)
            similarQuestions = similarQuestions.stream()
                    .filter(q -> !q.getFaqCode().equals(dto.getCode()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(similarQuestions)) {
                StringBuilder message = new StringBuilder("相似问题已存在，请检查：");
                for (FaqSimilarQuestionDTO similarQuestion : similarQuestions) {
                    message.append(similarQuestion.getSimilarQuestion());
                }
                throw new BizException("A0000", ArrayUtils.toArray(message.toString()), message.toString(), message);
            }
        }
    }

    protected Map<String, SkipFaqQuestion> skipSimilarQuestion(KnowledgeFaqDTO dto) {
        Set<String> questions = new HashSet<>();
        if (CollectionUtils.isNotEmpty(dto.getCorpus())) {
            questions.addAll(dto.getCorpus());
        }

        String masterQuestion = dto.getQuestion();
        questions.add(masterQuestion);
        Map<String, SkipFaqQuestion> skipFaqQuestionMap = new HashMap<>();

        //过滤重复问题
        List<KnowledgeFaqDTO> questionMains = repository.findQuestion(questions, dto.getKnowledgeBaseCode());

        for (KnowledgeFaqDTO questionMain : questionMains) {
            skipFaqQuestionMap.put(questionMain.getQuestion(), convert(questionMain));
            questions.remove(questionMain.getQuestion());
        }
        //过滤重复相似问
        List<FaqSimilarQuestionDTO> similarQuestions = faqSimilarQuestionRepository.findSimilarQuestion(questions, dto.getKnowledgeCode());
        if (CollectionUtils.isNotEmpty(similarQuestions)) {
            for (FaqSimilarQuestionDTO similarQuestion : similarQuestions) {
                skipFaqQuestionMap.put(similarQuestion.getSimilarQuestion(), convert(similarQuestion));
                questions.remove(similarQuestion.getSimilarQuestion());
            }
        }
        questions.remove(masterQuestion);
        dto.setCorpus(new ArrayList<>(questions));
        return skipFaqQuestionMap;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean update(String code, KnowledgeFaqUpdateParam updateParam) {
        KnowledgeFaqDTO faqDTO = repository.selectByCode(code);
        BizAssert.notNull(faqDTO, "AA022", "问答对不存在");
        boolean isReset = updateParam.isReset();

        KnowledgeFaqDTO dto = converter().convertUpdate(updateParam);
        dto.setId(null);
        dto.setCode(code);
        dto.setKnowledgeCode(faqDTO.getKnowledgeCode());
        dto.setKnowledgeBaseCode(faqDTO.getKnowledgeBaseCode());
        if (dto.getCorpus() != null) {
            dto.setCorpusCount(dto.getCorpus().size());
        }
        if (isReset) {
            dto.setPublishStatus(PublishStatus.PUBLISHED);
        } else {
            this.updatePublishStatus2Unpublished(null, ImmutableList.of(code));
        }
        if (!isReset) {
            updateFaqKnowledgeStatus(faqDTO.getKnowledgeCode());
        }

        faqSimilarQuestionRepository.deleteByFaqCode(code);
        checkSimilarQuestion(dto);
        repository.updateOne(dto, Wrappers.<KnowledgeFaqPO>lambdaUpdate().eq(KnowledgeFaqPO::getCode, code));
        saveSimilarQuestion(dto);
        //索引更新
        transactionCommitHandler.handle(() -> updateFaqIndex(code, faqDTO, dto));
        return true;
    }

    private void updateFaqIndex(String code, KnowledgeFaqDTO faqDTO, KnowledgeFaqDTO dto) {
        try {
            // 删除索引
            searchService.deleteFaqData(faqDTO.getKnowledgeCode(), code);
            // 重新索引
            searchService.pushFaqData(dto);
            log.info("FAQ索引更新成功，code:{}", code);
        } catch (Exception e) {
            log.error("FAQ索引更新失败，code:{}", code, e);
            throw new RuntimeException("FAQ索引更新失败", e);
        }
    }

    @Override
    public boolean delete(CodeParam codes) {
        repository.remove(Wrappers.<KnowledgeFaqPO>lambdaUpdate().in(KnowledgeFaqPO::getCode, codes.getCodes()));
        faqSimilarQuestionRepository.deleteByFaqCodes(codes.getCodes());
        return true;

    }


    private PublishStatus convert(Set<PublishStatus> faqPublishStatus, Set<String> hasPublishRecord) {
        if (CollectionUtils.isEmpty(faqPublishStatus)) {
            if (CollectionUtils.isEmpty(hasPublishRecord)) {
                return PublishStatus.UNPUBLISHED;
            } else {
                return PublishStatus.UPDATE_UNPUBLISH;
            }
        }

        Integer size = faqPublishStatus.size();
        if (Objects.equals(size, 1)) {
            PublishStatus publishStatus = faqPublishStatus.stream().findFirst().orElse(null);
            if (publishStatus == null) {
                if (CollectionUtils.isEmpty(hasPublishRecord)) {
                    return PublishStatus.UNPUBLISHED;
                } else {
                    return PublishStatus.UPDATE_UNPUBLISH;
                }
            } else {
                return publishStatus;
            }
        } else if (size > 1) {
            if (faqPublishStatus.contains(PublishStatus.PUBLISHED_FAILED)) {
                return PublishStatus.PUBLISHED_FAILED;
            } else if (faqPublishStatus.contains(PublishStatus.REJECTED)) {
                return PublishStatus.REJECTED;
            } else if (faqPublishStatus.contains(PublishStatus.UNDER_REVIEW)) {
                return PublishStatus.UNDER_REVIEW;
            } else {
                return PublishStatus.UPDATE_UNPUBLISH;
            }
        } else {
            throw new BizException("A0003", "状态列表不可能为空");
        }
    }


}
