package com.chinatelecom.gs.engine.kms.controller;

import cn.hutool.core.lang.tree.Tree;
import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.common.auth.KsMenuConfig;
import com.chinatelecom.gs.engine.common.auth.PermissionTag;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.common.platform.StatOpenApi;
import com.chinatelecom.gs.engine.kms.base.model.CodeParam;
import com.chinatelecom.gs.engine.kms.sdk.constants.KmsApis;
import com.chinatelecom.gs.engine.kms.sdk.vo.tag.*;
import com.chinatelecom.gs.engine.kms.service.TagApplicationService;
import com.chinatelelcom.gs.engine.sdk.common.Page;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.List;


@RestController
@Slf4j
@RefreshScope
@Tag(name = "标签管理接口")
@RequestMapping({KmsApis.KMS_API + KmsApis.TAG_API, KmsApis.OPENAPI + KmsApis.TAG_API, KmsApis.RPC + KmsApis.TAG_API})
public class TagAppController {

    @Resource
    private TagApplicationService tagApplicationService;

    @Value("${platform.super.tenant:SuperAis}")
    private String superTenant;


    @Operation(summary = "标签分页查询", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "系统配置")})})
    @PlatformRestApi(name = "标签分页查询", groupName = "标签管理")
    @AuditLog(businessType = "标签管理", operType = "标签分页查询", operDesc = "标签分页查询", objId = "null")
    @PostMapping(KmsApis.PAGE_API)
    @StatOpenApi(name = "标签分页查询", groupName = "标签管理")
    public Result<Page<TagVO>> page(@Validated @RequestBody TagQueryParam queryParam) {
        return Result.success(tagApplicationService.pageQuery(queryParam));
    }

    @Operation(summary = "新增标签", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "系统配置")})})
    @PlatformRestApi(name = "新增标签", groupName = "标签管理")
    @AuditLog(businessType = "标签管理", operType = "新增标签", operDesc = "新增标签", objId = "#_RESULT_.data.code")
    @PostMapping
    @PermissionTag(code = {KsMenuConfig.TAG_MANAGE, KsMenuConfig.TAG_MANAGE_1})
    @StatOpenApi(name = "新增标签", groupName = "标签管理")
    public Result<TagVO> add(@Validated @RequestBody TagCreateParam createParam) {
        TagVO result = tagApplicationService.create(createParam);
        return Result.success(result);
    }


    @Operation(summary = "更新标签", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "系统配置")})})
    @PlatformRestApi(name = "更新标签", groupName = "标签管理")
    @AuditLog(businessType = "标签管理", operType = "更新标签", operDesc = "更新标签", objId = "#code")
    @PutMapping(KmsApis.CODE_PATH)
    @PermissionTag(code = {KsMenuConfig.TAG_MANAGE, KsMenuConfig.TAG_MANAGE_1})
    @StatOpenApi(name = "更新标签", groupName = "标签管理")
    public Result<TagVO> update(@PathVariable("code") String code, @Validated @RequestBody TagUpdateParam dto) {
        return Result.success(tagApplicationService.updateTag(code, dto));
    }

    @Operation(summary = "移除标签", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "系统配置")})})
    @PlatformRestApi(name = "移除标签", groupName = "标签管理")
    @AuditLog(businessType = "标签管理", operType = "移除标签", operDesc = "移除标签", objId = "#codes.codes")
    @PostMapping(KmsApis.DELETE_API)
    @PermissionTag(code = {KsMenuConfig.TAG_MANAGE, KsMenuConfig.TAG_MANAGE_1})
    @StatOpenApi(name = "移除标签", groupName = "标签管理")
    public Result<Boolean> delete(@Validated @RequestBody CodeParam codes) {
        return Result.success(tagApplicationService.delete(codes));
    }

    @Operation(summary = "标签详情", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "系统配置")})})
    @PlatformRestApi(name = "标签详情", groupName = "标签管理")
    @AuditLog(businessType = "标签管理", operType = "标签详情", operDesc = "标签详情", objId = "#param")
    @GetMapping(KmsApis.CODE_PATH)
    @StatOpenApi(name = "标签详情", groupName = "标签管理")
    public Result<TagVO> get(@PathVariable("code") String code) {
        return Result.success(tagApplicationService.get(code));
    }

    @Operation(summary = "获取标签树", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "系统配置")})})
    @PlatformRestApi(name = "获取标签树", groupName = "标签管理")
    @AuditLog(businessType = "标签管理", operType = "获取标签树", operDesc = "获取标签树", objId = "null")
    @PostMapping(KmsApis.TREE_API)
    @StatOpenApi(name = "获取标签树", groupName = "标签管理")
    public Result<List<Tree<String>>> tree(@RequestBody TagQueryParam queryParam) {
        List<Tree<String>> trees = tagApplicationService.listTagTree(queryParam);
        return Result.success(trees);
    }

    @Operation(summary = "解除标签关联", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "系统配置")})})
    @PlatformRestApi(name = "解除标签关联", groupName = "标签管理")
    @AuditLog(businessType = "标签管理", operType = "解除标签关联", operDesc = "解除标签关联", objId = "#param.codes")
    @PostMapping(KmsApis.UNBIND_API)
    @StatOpenApi(name = "解除标签关联", groupName = "标签管理")
    public Result<Boolean> unbind(@RequestBody TagUnbindParam param) {
        return Result.success(tagApplicationService.unbind(param.getCodes()));
    }

    @Operation(summary = "完整标签树查询", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "系统配置")})})
    @PlatformRestApi(name = "完整标签树查询", groupName = "完整标签树查询")
    @AuditLog(businessType = "完整标签树查询", operType = "完整标签树查询", operDesc = "完整标签树查询", objId = "#null")
    @PostMapping(KmsApis.FULL_TREE_API)
    @StatOpenApi(name = "完整标签树查询", groupName = "完整标签树查询")
    public Result<Tree<String>> treeFull(@RequestBody TagQueryParam queryParam) {
        Tree<String> trees = tagApplicationService.listTagTreeFull(queryParam);
        return Result.success(trees);
    }


    @Operation(summary = "手动刷新标签数据", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "系统配置")})})
    @PlatformRestApi(name = "手动刷新标签数据", groupName = "标签管理")
    @AuditLog(businessType = "标签管理", operType = "手动刷新标签数据", operDesc = "手动刷新标签数据", objId = "null")
    @PostMapping("/refresh")
    @StatOpenApi(name = "手动刷新标签数据", groupName = "标签管理")
    public Result<Boolean> manualRefreshTag(@Validated @RequestBody TagRefreshParam param) {
        return Result.success(tagApplicationService.manualRefreshTag(param.getCode()));
    }

    @Operation(summary = "检查标签是否被引用", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "系统配置")})})
    @PlatformRestApi(name = "检查标签是否被引用", groupName = "标签管理")
    @AuditLog(businessType = "标签管理", operType = "检查标签是否被引用", operDesc = "检查标签是否被引用", objId = "#code")
    @GetMapping("/isReferenced/{code}")
    @StatOpenApi(name = "检查标签是否被引用", groupName = "标签管理")
    public Result<Boolean> isReferenced(@PathVariable("code") String code) {
        return Result.success(tagApplicationService.isReferenced(code));
    }

}
