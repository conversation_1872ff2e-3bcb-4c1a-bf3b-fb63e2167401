package com.chinatelecom.gs.engine.common.cache.lock;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 锁对象抽象
 *
 */
@AllArgsConstructor
public class ZLock implements AutoCloseable {
    @Getter
    private final String key;
    @Getter
    private final Object lock;

    private final DistributedLock locker;

    @Override
    public void close() {
        locker.unlock(this);
    }
}
