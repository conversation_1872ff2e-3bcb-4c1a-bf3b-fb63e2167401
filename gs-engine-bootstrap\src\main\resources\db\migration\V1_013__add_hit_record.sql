CREATE TABLE IF NOT EXISTS `agent_question_hit_record`
(
    `id`             bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增id',
    `tenant_id`      varchar(128) COLLATE utf8mb4_unicode_ci          DEFAULT '',
    `agent_code`     varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '机器人编码',
    `kms_code`       varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '知识库ID',
    `knowledge_id`   varchar(32) COLLATE utf8mb4_unicode_ci  NOT NULL DEFAULT '' COMMENT '知识ID',
    `knowledge_type` varchar(32) COLLATE utf8mb4_unicode_ci           DEFAULT NULL COMMENT '知识类型',
    `title`          varchar(256) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '知识标题',
    `channel_code`   varchar(128) COLLATE utf8mb4_unicode_ci          DEFAULT NULL COMMENT '入口编码',
    `session_id`     varchar(128) COLLATE utf8mb4_unicode_ci          DEFAULT NULL COMMENT '会话ID',
    `message_id`     varchar(128) COLLATE utf8mb4_unicode_ci          DEFAULT NULL COMMENT '消息ID',
    `yn`             bigint unsigned NOT NULL DEFAULT '0' COMMENT '记录删除标识',
    `create_time`    datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '记录创建时间',
    `update_time`    datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '记录最后一次更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_message_id` (`knowledge_id`,`message_id`),
    KEY              `agent_code_key` (`agent_code`,`yn`)
) ENGINE=InnoDB AUTO_INCREMENT=1991 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='知识点命中记录表';
