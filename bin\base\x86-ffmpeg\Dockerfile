FROM artifact.srdcloud.cn/ct_aics-release-docker-local/python3-jdk8:latest AS build

RUN sed -i 's/http:\/\/deb.debian.org/https:\/\/artifact.srdcloud.cn\/artifactory\/huawei-release-debian-remote/g' /etc/apt/sources.list && \
    sed -i 's/http:\/\/security.debian.org/https:\/\/artifact.srdcloud.cn\/artifactory\/huawei-release-debian-remote/g' /etc/apt/sources.list

RUN apt update && apt install -y ffmpeg