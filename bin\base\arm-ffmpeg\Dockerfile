FROM artifact.srdcloud.cn/ct_aics-release-docker-local/python3-jdk8-arm:latest AS build

RUN sed -i 's/http:\/\/deb.debian.org/https:\/\/artifact.srdcloud.cn\/artifactory\/huawei-release-debian-remote/g' /etc/apt/sources.list && \
    sed -i 's/http:\/\/security.debian.org/https:\/\/artifact.srdcloud.cn\/artifactory\/huawei-release-debian-remote/g' /etc/apt/sources.list

RUN sed -i 's/^deb /deb [arch=arm64] /g' /etc/apt/sources.list

RUN apt update
RUN apt install -y ffmpeg
