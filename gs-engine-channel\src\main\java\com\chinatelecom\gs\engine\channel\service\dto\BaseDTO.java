package com.chinatelecom.gs.engine.channel.service.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/12/22 10:48
 * @description
 */
@Data
public class BaseDTO {
    /**
     * 主键
     */
    protected Long id;

    /**
     * 租户ID
     */
    protected String tenantId;

    /**
     * 删除标记,1:删除,0:未删除
     */
    protected Long yn;

    /**
     * 创建用户ID
     */
    protected String createId;

    /**
     * 创建用户名
     */
    protected String createName;

    /**
     * 创建时间
     */
    protected LocalDateTime createTime;

    /**
     * 最后修改用户ID
     */
    protected String updateId;

    /**
     * 最后修改用户名
     */
    protected String updateName;

    /**
     * 更新时间
     */
    protected LocalDateTime updateTime;
}
