package com.chinatelecom.gs.engine.core.corekit.controller.web;

import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.common.constants.Apis;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.core.corekit.service.FormService;
import com.chinatelecom.gs.engine.core.sdk.request.FormDelRequest;
import com.chinatelecom.gs.engine.core.sdk.request.FormSaveRequest;
import com.chinatelecom.gs.engine.core.sdk.vo.FormVO;
import com.chinatelecom.gs.engine.robot.sdk.dto.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.Objects;

/**
 * @USER: pengmc1
 * @DATE: 2025/7/21 11:16
 */

@Slf4j
@Tag(name = "表单管理")
@RestController
@RequestMapping(Apis.BASE_PREFIX + Apis.WEB_API + Apis.FORM)
public class FormController {

    @Resource
    private FormService formService;

    /**
     * 表单新增/编辑
     * @param request
     * @return
     */
    @PostMapping("/save")
    @Operation(summary = "表单新增/编辑", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "BOT管理")})})
    @PlatformRestApi(name = "表单新增/编辑", groupName = "表单管理")
    @AuditLog(businessType = "表单管理", operType = "表单新增/编辑", operDesc = "表单新增/编辑", objId="#request.formCode")
    public Result<Boolean> save(@Valid @RequestBody FormSaveRequest request){
        return Result.success(formService.saveForm(request));
    }

    /**
     * 发布表单
     * @param request
     * @return
     */
    @PostMapping("/publish")
    @Operation(summary = "表单发布", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "BOT管理")})})
    @PlatformRestApi(name = "表单发布", groupName = "表单管理")
    @AuditLog(businessType = "表单管理", operType = "表单发布", operDesc = "表单发布", objId="#request.formCode")
    public Result<Boolean> publish(@Valid @RequestBody FormSaveRequest request){
        return Result.success(formService.publishForm(request));
    }

    /**
     * 表单详情
     * @param formCode
     * @return
     */
    @GetMapping("/detail")
    @Operation(summary = "表单详情", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "BOT管理")})})
    @PlatformRestApi(name = "表单详情", groupName = "表单管理")
    @AuditLog(businessType = "表单管理", operType = "表单详情", operDesc = "表单详情", objId="#formCode")
    public Result<FormVO> detail(@RequestParam String formCode){
        FormVO formVO = formService.getDetail(formCode, null);
        if(Objects.isNull(formVO)){
            return Result.failed(Result.INVALID_PARAM,"表单不存在","表单不存在");
        }
        return Result.success(formVO);
    }

    /**
     * 表单删除
     * @param request
     * @return
     */
    @PostMapping("/delete")
    @Operation(summary = "表单删除", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "BOT管理")})})
    @PlatformRestApi(name = "表单删除", groupName = "表单管理")
    @AuditLog(businessType = "表单管理", operType = "表单删除", operDesc = "表单删除", objId="null")
    public Result<Boolean> delete(@Valid @RequestBody FormDelRequest request){
        return Result.success(formService.deleteForm(request));
    }
}
