package com.chinatelecom.gs.engine.core.corekit.controller.web;

import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.common.auth.KsMenuConfig;
import com.chinatelecom.gs.engine.common.auth.MenuConfig;
import com.chinatelecom.gs.engine.common.auth.PermissionTag;
import com.chinatelecom.gs.engine.common.constants.Apis;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.core.corekit.domain.request.RobotAiInstructPageRequest;
import com.chinatelecom.gs.engine.core.corekit.domain.request.RobotAiInstructRequest;
import com.chinatelecom.gs.engine.core.corekit.domain.response.RobotAiInstructResponse;
import com.chinatelecom.gs.engine.core.corekit.domain.vo.RobotAiInstructPageVO;
import com.chinatelecom.gs.engine.core.corekit.service.RobotAiInstructService;
import com.chinatelecom.gs.engine.robot.sdk.dto.Result;
import com.chinatelelcom.gs.engine.sdk.common.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import java.util.List;


/**
 * @author: Wei
 * @date: 2025-02-05 09:24
 */
@Slf4j
@Tag(name = "指令管理")
@PermissionTag(code = {MenuConfig.ROBOT, KsMenuConfig.BOT})
@RestController
@RequestMapping(Apis.BASE_PREFIX + Apis.WEB_API + "/instruct")
public class RobotAiInstructController {

    @Resource
    private RobotAiInstructService robotAiInstructService;

    @PostMapping("/save")
    @Operation(summary = "指令新增/编辑", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "BOT管理")})})
    @PlatformRestApi(name = "指令新增/编辑", groupName = "指令管理")
    @AuditLog(businessType = "指令管理", operType = "指令新增/编辑", operDesc = "指令新增/编辑", objId = "#request.instructCode")
    public Result<Boolean> save(@Valid @RequestBody RobotAiInstructRequest request) {
        return Result.success(robotAiInstructService.saveInstruct(request));
    }

    @GetMapping("/detail")
    @Operation(summary = "指令详情", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "BOT管理")})})
    @PlatformRestApi(name = "指令详情", groupName = "指令管理")
    @AuditLog(businessType = "指令管理", operType = "指令详情", operDesc = "指令详情", objId = "#instructCode")
    public Result<RobotAiInstructResponse> detail(@RequestParam String instructCode) {
        return Result.success(robotAiInstructService.detail(instructCode));
    }

    @PostMapping("/delete")
    @Operation(summary = "指令删除", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "BOT管理")})})
    @PlatformRestApi(name = "指令删除", groupName = "指令管理")
    @AuditLog(businessType = "指令管理", operType = "指令删除", operDesc = "指令删除", objId = "null")
    public Result<Boolean> delete(@Valid @RequestBody @NotEmpty(message = "Id不能为空") List<String> ids) {
        return Result.success(robotAiInstructService.delete(ids));
    }

    @GetMapping("/page")
    @Operation(summary = "指令分页查询", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "BOT管理")})})
    @PlatformRestApi(name = "指令分页查询", groupName = "指令管理")
    @AuditLog(businessType = "指令管理", operType = "指令分页查询", operDesc = "指令分页查询", objId = "null")
    public Result<Page<RobotAiInstructPageVO>> page(RobotAiInstructPageRequest request) {
        return Result.success(robotAiInstructService.pageQuery(request));
    }

    @GetMapping("/list")
    @Operation(summary = "指令列表", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "BOT管理")})})
    @PlatformRestApi(name = "指令列表", groupName = "指令管理")
    @AuditLog(businessType = "指令管理", operType = "指令列表", operDesc = "指令列表", objId = "null")
    public Result<List<RobotAiInstructPageVO>> list(RobotAiInstructPageRequest request) {
        return Result.success(robotAiInstructService.listQuery(request));
    }

}
