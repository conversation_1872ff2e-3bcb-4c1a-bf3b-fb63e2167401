package com.chinatelecom.gs.engine.core.entity.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.infra.base.impl.BaseExtendServiceImpl;
import com.chinatelecom.gs.engine.common.utils.JsonUtils;
import com.chinatelecom.gs.engine.core.entity.domain.po.EntityDataPO;
import com.chinatelecom.gs.engine.core.entity.domain.po.EntityPO;
import com.chinatelecom.gs.engine.core.entity.domain.query.EntityQuery;
import com.chinatelecom.gs.engine.core.entity.domain.request.EntityDeleteRequest;
import com.chinatelecom.gs.engine.core.entity.mapper.EntityMapper;
import com.chinatelecom.gs.engine.core.entity.service.EntityDataService;
import com.chinatelecom.gs.engine.core.entity.service.EntityService;
import com.chinatelecom.gs.engine.robot.sdk.v2.enums.EntityAbilityEnum;
import com.chinatelecom.gs.engine.robot.sdk.v2.enums.EntityTypeEnum;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.entity.AbilityConfig;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.entity.EntityDataDetailVO;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.entity.EntityDetailVO;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.entity.EntityVO;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.page.Page;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.page.PageImpl;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @USER: pengmc1
 * @DATE: 2025/1/16 19:44
 */
@Slf4j
@Service
public class EntityServiceImpl extends BaseExtendServiceImpl<EntityMapper, EntityPO> implements EntityService {

    @Resource
    private EntityMapper entityMapper;

    @Resource
    private EntityDataService entityDataService;

    /**
     * 获取实体基本信息
     *
     * @param entityCode
     * @return
     */
    @Override
    public EntityVO getEntity(String entityCode) {
        LambdaQueryWrapper<EntityPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EntityPO::getEntityCode, entityCode);
        queryWrapper.eq(Objects.nonNull(RequestContext.getTenantId()), EntityPO::getTenantId, RequestContext.getTenantId());
        EntityPO entityPO = entityMapper.selectOne(queryWrapper);
        return toEntityVO(entityPO);
    }

    /**
     * 批量获取实体基本信息
     * @param entityCodes
     * @return
     */
    @Override
    public List<EntityVO> getEntityList(List<String> entityCodes) {
        LambdaQueryWrapper<EntityPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(EntityPO::getEntityCode, entityCodes);
        queryWrapper.eq(Objects.nonNull(RequestContext.getTenantId()), EntityPO::getTenantId, RequestContext.getTenantId());
        List<EntityPO> entityPOS = entityMapper.selectList(queryWrapper);
        return entityPOS.stream().map(this::toEntityVO).collect(Collectors.toList());
    }

    /**
     * 查询实体详情
     *
     * @param entityCode
     * @return
     */
    @Override
    public EntityDetailVO getEntityDetail(String entityCode) {
        LambdaQueryWrapper<EntityPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EntityPO::getEntityCode, entityCode);
        queryWrapper.eq(Objects.nonNull(RequestContext.getTenantId()), EntityPO::getTenantId, RequestContext.getTenantId());
        EntityPO entityPO = entityMapper.selectOne(queryWrapper);
        if (entityPO == null) {
            return null;
        }
        return toEntityDetailVO(entityPO);
    }

    /**
     * 批量查询实体详情
     *
     * @param entityCodes
     * @return
     */
    @Override
    public List<EntityDetailVO> getEntityDetailList(List<String> entityCodes) {
        LambdaQueryWrapper<EntityPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(EntityPO::getEntityCode, entityCodes);
        queryWrapper.eq(Objects.nonNull(RequestContext.getTenantId()), EntityPO::getTenantId, RequestContext.getTenantId());
        List<EntityPO> entityPOS = entityMapper.selectList(queryWrapper);
        return entityPOS.stream().map(this::toEntityDetailVO).collect(Collectors.toList());
    }

    /**
     * 查询实体详情列表
     *
     * @param query
     * @return
     */
    @Override
    public Page<EntityDetailVO> queryEntityDetailList(EntityQuery query) {
        LambdaQueryWrapper<EntityPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(query.getEntityName() != null, EntityPO::getEntityName, query.getEntityName());
        queryWrapper.eq(query.getEntityType() != null, EntityPO::getEntityType, query.getEntityType());
        queryWrapper.eq(query.getEntityCode() != null, EntityPO::getEntityCode, query.getEntityCode());
        queryWrapper.eq(Objects.nonNull(RequestContext.getTenantId()), EntityPO::getTenantId, RequestContext.getTenantId());
        queryWrapper.orderByDesc(EntityPO::getUpdateTime);
        PageDTO<EntityPO> page = this.page(new PageDTO<>(query.getPageNum(), query.getPageSize()), queryWrapper);
        return PageImpl.of(page.getCurrent(), page.getSize(), page.getTotal(), page.getPages(), page.getRecords().stream().map(this::toEntityDetailVO).collect(Collectors.toList()));
    }

    /**
     * 查询实体详情列表
     *
     * @param tenantId
     * @param entityType
     * @param pageNum
     * @param pageSize
     * @return
     */
    @Override
    public Page<EntityDetailVO> queryEntityDetailList(String tenantId, String entityType, Integer pageNum, Integer pageSize) {
        LambdaQueryWrapper<EntityPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(entityType != null, EntityPO::getEntityType, entityType);
        queryWrapper.eq(EntityPO::getTenantId, tenantId);
        PageDTO<EntityPO> page = this.page(new PageDTO<>(pageNum, pageSize), queryWrapper);
        return PageImpl.of(page.getCurrent(), page.getSize(), page.getTotal(), page.getPages(), page.getRecords().stream().map(this::toEntityDetailVO).collect(Collectors.toList()));
    }

    /**
     * 获取所有租户ID
     *
     * @return
     */
    @Override
    public List<String> queryAllTenant() {
        QueryWrapper<EntityPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("distinct tenant_id");
        return entityMapper.selectObjs(queryWrapper);
    }

    /**
     * 添加实体
     * @param query 请求
     * @return 是否成功
     */
    @Override
    public Boolean addEntity(EntityDetailVO query) {
        LambdaQueryWrapper<EntityPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EntityPO::getEntityCode, query.getEntityCode());
        queryWrapper.eq(EntityPO::getTenantId, RequestContext.getTenantId());
        List<Object> entitys = entityMapper.selectObjs(queryWrapper);
        if (!CollectionUtils.isEmpty(entitys)) {
            throw new BizException("A0069", "实体编码已存在，无法添加");
        }
        EntityPO entityPO = toEntityPO(query);
        entityDataService.saveEntityData(query);
        return this.save(entityPO);
    }

    /**
     * 更新实体
     * @param query 请求
     * @return 是否成功
     */
    @Override
    public Boolean updateByCode(EntityDetailVO query) {
        LambdaQueryWrapper<EntityPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EntityPO::getEntityCode, query.getEntityCode());
        queryWrapper.eq(EntityPO::getTenantId, RequestContext.getTenantId());
        List<Object> entitys = entityMapper.selectObjs(queryWrapper);
        if (CollectionUtils.isEmpty(entitys)) {
            throw new BizException("A0004", "实体编码不存在，无法更新");
        }
        EntityPO entityPO = toEntityPO(query);
        entityDataService.saveEntityData(query);
        return this.update(entityPO, queryWrapper);
    }

    private EntityPO toEntityPO(EntityDetailVO query) {
        EntityPO entityPO = new EntityPO();
        BeanUtils.copyProperties(query, entityPO);
        if (Objects.nonNull(query.getAbility())) {
            entityPO.setAbility(query.getAbility().getCode());
        }
        if (Objects.nonNull(query.getAbilityConfig())) {
            entityPO.setAbilityConfig(JsonUtils.toJsonString(query.getAbilityConfig()));
        }
        if (Objects.nonNull(query.getAbilityConfigMap())) {
            LinkedHashMap<String, AbilityConfig> originMap = query.getAbilityConfigMap();
            LinkedHashMap<String, AbilityConfig> orderMap = new LinkedHashMap<>();
            if (!CollectionUtils.isEmpty(query.getCheckedKeys())) {
                for (String key : query.getCheckedKeys()) {
                    if (Objects.nonNull(originMap.get(key))) {
                        orderMap.put(key, originMap.get(key));
                    }
                }
            } else {
                orderMap = originMap;
            }
            entityPO.setAbilityConfigMap(JsonUtils.toJsonString(orderMap));
        }
        entityPO.setTenantId(RequestContext.getTenantId());
        entityPO.setEntityType(query.getEntityType().getCode());
        entityPO.setValidatorSwitch(query.getValidatorSwitch() ? 1 : 0);
        return entityPO;
    }

    /**
     * 批量删除实体
     * @param query 删除实体请求
     * @return 是否成功
     */
    @Override
    public Boolean delete(EntityDeleteRequest query) {
        LambdaQueryWrapper<EntityPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(EntityPO::getEntityCode, query.getEntityCodes());
        queryWrapper.eq(EntityPO::getTenantId, RequestContext.getTenantId());
        entityDataService.delete(query.getEntityCodes());
        return entityMapper.delete(queryWrapper) > 0;
    }

    /**
     * 转换为实体VO
     * @param entityPO
     * @return
     */
    public EntityVO toEntityVO(EntityPO entityPO){
        EntityVO entityVO = new EntityVO();
        BeanUtils.copyProperties(entityPO, entityVO);
        entityVO.setEntityType(EntityTypeEnum.getByCode(entityPO.getEntityType()));
        entityVO.setAbility(EntityAbilityEnum.getByCode(entityPO.getAbility()));
        if(StringUtils.isNotBlank(entityPO.getAbilityConfig())){
            entityVO.setAbilityConfig(JSON.parseObject(entityPO.getAbilityConfig(), AbilityConfig.class));
        }
        if(StringUtils.isNotBlank(entityPO.getAbilityConfigMap())){
            Gson gson = new Gson();
            Type type = new TypeToken<LinkedHashMap<String, AbilityConfig>>(){}.getType();
            LinkedHashMap<String, AbilityConfig> map = gson.fromJson(entityPO.getAbilityConfigMap(), type);
            entityVO.setAbilityConfigMap(map);
        }
        entityVO.setValidatorSwitch(Objects.nonNull(entityPO.getValidatorSwitch()) && entityPO.getValidatorSwitch().intValue() == 1);
        return entityVO;
    }

    /**
     * 转换为实体详情VO
     * @param entityPO
     * @return
     */
    public EntityDetailVO toEntityDetailVO(EntityPO entityPO){
        EntityDetailVO entityDetailVO = new EntityDetailVO();
        BeanUtils.copyProperties(entityPO, entityDetailVO);
        entityDetailVO.setEntityType(EntityTypeEnum.getByCode(entityPO.getEntityType()));
        entityDetailVO.setAbility(EntityAbilityEnum.getByCode(entityPO.getAbility()));
        if(StringUtils.isNotBlank(entityPO.getAbilityConfig())){
            entityDetailVO.setAbilityConfig(JSON.parseObject(entityPO.getAbilityConfig(), AbilityConfig.class));
            LinkedHashMap<String, AbilityConfig> map = new LinkedHashMap<>();
            map.put(entityPO.getAbility(), JSON.parseObject(entityPO.getAbilityConfig(), AbilityConfig.class));
            entityDetailVO.setAbilityConfigMap(map);
            entityDetailVO.setCheckedKeys(new ArrayList<>(map.keySet()));
        }
        if(StringUtils.isNotBlank(entityPO.getAbilityConfigMap())){
            Gson gson = new Gson();
            Type type = new TypeToken<LinkedHashMap<String, AbilityConfig>>(){}.getType();
            LinkedHashMap<String, AbilityConfig> map = gson.fromJson(entityPO.getAbilityConfigMap(), type);
            entityDetailVO.setAbilityConfigMap(map);
            entityDetailVO.setCheckedKeys(new ArrayList<>(map.keySet()));
        }
        entityDetailVO.setValidatorSwitch(entityPO.getValidatorSwitch().intValue() == 1);
        List<EntityDataPO> entityDataPOS = entityDataService.getEntityDataList(entityPO.getEntityCode(), entityPO.getTenantId());
        List<EntityDataDetailVO> collect = entityDataPOS.stream().map(o -> {
            EntityDataDetailVO vo = new EntityDataDetailVO();
            BeanUtils.copyProperties(o, vo);
            return vo;
        }).collect(Collectors.toList());
        entityDetailVO.setEntityDataList(collect);
        return entityDetailVO;
    }
}
