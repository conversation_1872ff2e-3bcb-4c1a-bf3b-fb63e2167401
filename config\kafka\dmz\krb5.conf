[libdefaults]
    default_realm = HADOOP.CHINATELECOM.CN
    dns_lookup_realm = false
    dns_lookup_kdc = false
    ticket_lifetime = 24h
    renew_lifetime = 7d
    forwardable = true
    default_tkt_enctypes = aes128-cts-hmac-sha1-96
    default_tgs_enctypes = aes128-cts-hmac-sha1-96
    permitted_enctypes = aes128-cts-hmac-sha1-96 arcfour-hmac-md5 des-cbc-md5 des-cbc-crc des3-cbc-sha1 des3-cbc-sha1-kd
    udp_preference_limit = 1

[realms]
    HADOOP.CHINATELECOM.CN = {
        kdc = 10.142.104.183:88
        admin_server = 10.142.104.183:749

        kdc = 10.142.153.77:88
        admin_server = 10.142.153.77:749
        default_domain = hadoop.chinatelecom.cn
    }

[domain_realm]
    .hadoop.chinatelecom.cn = HADOOP.CHINATELECOM.CN
    hadoop.chinatelecom.cn = HADOOP.CHINATELECOM.CN

[logging]
    kdc = FILE:/var/log/krb5kdc.log
    admin_server = FILE:/var/log/kadmin.log
    default = FILE:/var/log/krb5lib.log