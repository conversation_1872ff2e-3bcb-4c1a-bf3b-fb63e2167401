ALTER TABLE `gs_log_tpl`
  ADD UNIQUE INDEX `log_id_unique_index` (`log_id`),
  ADD COLUMN `prompt_tokens` INT DEFAULT NULL COMMENT '输入token数',
  ADD COLUMN `completion_tokens` INT DEFAULT NULL COMMENT '输出token数',
  MODIFY COLUMN `cost_time` INT DEFAULT NULL COMMENT '总耗时',
  MODIFY COLUMN `first_token_cost_time` INT DEFAULT NULL COMMENT '首Token耗时',
  MODIFY COLUMN `start_time` datetime(3) DEFAULT NULL COMMENT '执行开始时间',
  MODIFY COLUMN `end_time` datetime(3) DEFAULT NULL COMMENT '执行结束时间',
  MODIFY COLUMN `send_time` datetime(3) DEFAULT NULL COMMENT '消息发送时间',
  MODIFY COLUMN `add_time` datetime(3) DEFAULT NULL COMMENT '入库时间',
  MODIFY COLUMN `first_token_time` datetime(3) DEFAULT NULL COMMENT '首Token时间';