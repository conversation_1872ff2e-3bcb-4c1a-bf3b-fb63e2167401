//package com.chinatelecom.csbotplatform.channel.api.config;
//
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.stereotype.Component;
//import org.springframework.web.servlet.HandlerInterceptor;
//import org.springframework.web.servlet.ModelAndView;
//
//import jakarta.servlet.http.HttpServletRequest;
//import jakarta.servlet.http.HttpServletResponse;
//import java.util.Optional;
//import java.util.Set;
//
///**
// * <AUTHOR>
// * @date 2024/1/5 10:26
// * @description 请求白名单过滤器
// */
//@Slf4j
//@Component
//public class WhiteListWebInterceptor implements HandlerInterceptor {
//
//    @Value("${request.ip.whitelist:}")
//    private Set<String> whiteList;
//
//    @Value("${request.switch:false}")
//    private Boolean whiteListSwitch;
//
//    @Override
//    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
//        String remoteHost = request.getRemoteHost();
//        String forward = request.getHeader("X-FORWARD-FOR");
//        log.info("请求ip为 remoteHost:{}, forward:{}", remoteHost, forward);
//
//        if (!whiteListSwitch) {
//            return true;
//        }
//
//        if (whiteList.contains(remoteHost)) {
//            return true;
//        }
//
//        String[] forwardIps = Optional.ofNullable(forward).orElse(StringUtils.EMPTY).split(",");
//        for (String forwardIp : forwardIps) {
//            if (whiteList.contains(forwardIp)) {
//                return true;
//            }
//        }
//
//        return false;
//    }
//
//    @Override
//    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {
//        HandlerInterceptor.super.postHandle(request, response, handler, modelAndView);
//    }
//
//    @Override
//    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
//        HandlerInterceptor.super.afterCompletion(request, response, handler, ex);
//    }
//
//}
