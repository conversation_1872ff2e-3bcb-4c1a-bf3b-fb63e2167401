<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinatelecom.gs.engine.kms.infra.mapper.CatalogMapper">

    <resultMap id="BaseResultMap" type="com.chinatelecom.gs.engine.kms.sdk.vo.catalog.CatalogVO">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="code" jdbcType="VARCHAR" property="code" />
        <result column="name" jdbcType="VARCHAR" property="name" />
    </resultMap>

    <select id="pageCatalog" resultMap="BaseResultMap">
        select c.*, k.status, k.publish_status from catalog c LEFT Join knowledge k
        on c.knowledge_code=k.code
        where c.knowledge_base_code = #{knowledgeBaseCode} AND c.yn = 0
        <choose>
            <!-- 如果有searchParentCodes，则在指定的目录列表中搜索 -->
            <when test="searchParentCodes != null and searchParentCodes.size() > 0">
                AND c.parent_code IN
                <foreach collection="searchParentCodes" item="searchParentCode" open="(" separator="," close=")">
                    #{searchParentCode}
                </foreach>
            </when>
            <!-- 否则按原来的逻辑，在指定的父目录下搜索 -->
            <otherwise>
                <if test="parentCode != null and parentCode != ''">
                    AND c.parent_code = #{parentCode}
                </if>
            </otherwise>
        </choose>
        <if test="name != null and name != ''">
            AND c.name like CONCAT('%', #{name}, '%')
        </if>
        <if test="statusList != null and statusList.size() > 0">
            AND k.indexing_status IN
            <foreach collection="statusList" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
        <if test="publishStatusList != null and publishStatusList.size() > 0">
            AND (k.publish_status IN
            <foreach collection="publishStatusList" item="publishStatus" open="(" separator="," close=")">
                #{publishStatus}
            </foreach>
            <if test="filterPublishStatus != null and filterPublishStatus == false">
            OR k.publish_status IS NULL
            </if>
            )
        </if>
        <if test="on != null and on == false">
            AND (k.on is null or k.on = 0)
        </if>
        <if test="on != null and on == true">
            AND (k.on is null or k.on = 1)
        </if>
        <if test="includeKnowledge != null and includeKnowledge == false">
            AND c.type = 'title'
        </if>
        order by c.type desc, c.id desc
    </select>

</mapper>
