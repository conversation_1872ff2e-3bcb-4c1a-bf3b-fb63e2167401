package com.chinatelecom.gs.engine.task.web;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.common.auth.KsMenuConfig;
import com.chinatelecom.gs.engine.common.auth.MenuConfig;
import com.chinatelecom.gs.engine.common.auth.PermissionTag;
import com.chinatelecom.gs.engine.common.auth.PermissionTypeEnum;
import com.chinatelecom.gs.engine.common.constants.Constants;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.context.RequestInfo;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.common.platform.StatOpenApi;
import com.chinatelecom.gs.engine.common.utils.VariableUtils;
import com.chinatelecom.gs.engine.core.corekit.service.GlobalVariableAppService;
import com.chinatelecom.gs.engine.core.sdk.request.GlobalVariableQueryRequest;
import com.chinatelecom.gs.engine.core.sdk.rpc.GlobalVariableRpcApi;
import com.chinatelecom.gs.engine.core.sdk.vo.GlobalVariableDataDetailVO;
import com.chinatelecom.gs.engine.sdk.common.annotation.ApiType;
import com.chinatelecom.gs.engine.sdk.common.annotation.HideFromApiTypes;
import com.chinatelecom.gs.engine.task.sdk.TaskApis;
import com.chinatelecom.gs.engine.task.sdk.entity.Param;
import com.chinatelecom.gs.engine.task.sdk.enums.WorkflowTypeEnum;
import com.chinatelecom.gs.engine.task.sdk.param.QueryWorkflowParam;
import com.chinatelecom.gs.engine.task.sdk.vo.variable.WorkFlowVariableDataDetailVO;
import com.chinatelecom.gs.engine.task.sdk.vo.variable.WorkFlowVariableQueryRequest;
import com.chinatelecom.gs.engine.task.sdk.x6.Node;
import com.chinatelecom.gs.privilege.common.dto.GrantObjectDTO;
import com.chinatelecom.gs.privilege.common.enums.PrivilegeEnum;
import com.chinatelecom.gs.privilege.common.enums.ResourceTypeEnum;
import com.chinatelecom.gs.privilege.util.PrivilegeUtil;
import com.chinatelecom.gs.workflow.core.domain.dto.Result;
import com.chinatelecom.gs.workflow.core.domain.param.*;
import com.chinatelecom.gs.workflow.core.domain.po.BotWorkflowPO;
import com.chinatelecom.gs.workflow.core.service.BotWorkflowConfigService;
import com.chinatelecom.gs.workflow.core.service.WorkFlowVariableAppService;
import com.chinatelecom.gs.workflow.core.workflow.core.DagDefine;
import com.chinatelecom.gs.workflow.core.workflow.core.enums.NodeTypeEnum;
import com.chinatelecom.gs.workflow.core.workflow.core.model.biz.param.VariableAssignNodeBizParam;
import com.chinatelelcom.gs.engine.sdk.common.enums.VariableTypeEnum;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;


/**
 * @USER: pengmc1
 * @DATE: 2024/10/9 18:06
 */
@Slf4j
@Tag(name = "业务流管理")
@PermissionTag(code = {MenuConfig.DIALOG_FLOW, MenuConfig.WORKFLOW, KsMenuConfig.WORKFLOW_FLOW})
@PermissionTag(code = MenuConfig.STRATEGY, type = PermissionTypeEnum.MENU)
@RestController
@RequestMapping({TaskApis.TASK_API + Constants.WEB_PREFIX, TaskApis.TASK_API + Constants.API_PREFIX})
public class BotWorkflowController {
    private static final int MAX_SIZE = 500;
    private static final String WRITE = "write";
    private static final String READ = "read";

    @Autowired
    private PrivilegeUtil privilegeUtil;

    @Resource
    private GlobalVariableAppService globalVariableAppService;

    @Resource
    private GlobalVariableRpcApi globalVariableRpcApi;

    @Resource
    private WorkFlowVariableAppService workFlowVariableAppService;

    @Resource
    private BotWorkflowConfigService botWorkflowConfigService;

    @Resource
    private DagDefine dagDefine;

    /**
     * 查询工作流列表
     *
     * @param queryWorkflowParam 查询参数
     * @return
     */
    @Operation(summary = "查询业务流列表", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "业务流管理")})})
    @PlatformRestApi(name = "查询业务流列表", groupName = "业务流管理")
    @StatOpenApi(name = "查询业务流列表", groupName = "业务流管理")
    @PostMapping(value = "/page")
    @AuditLog(businessType = "业务流管理", operType = "查询业务流列表", operDesc = "查询业务流列表", objId = "null")
    @HideFromApiTypes(ApiType.OPENAPI)
    public Result<Page<BotWorkflowRsp>> page(@RequestBody QueryWorkflowParam queryWorkflowParam) {
        return Result.success(botWorkflowConfigService.queryWorkflowList(queryWorkflowParam));
    }

    /**
     * 查询策略模板
     *
     * @param queryWorkflowParam 查询参数
     * @return
     */
    @Operation(summary = "分页查询策略模板", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "业务流管理")})})
    @PlatformRestApi(name = "分页查询策略模板", groupName = "业务流管理")
    @StatOpenApi(name = "分页查询策略模板", groupName = "业务流管理")
    @PostMapping(value = "/queryTemplate")
    @HideFromApiTypes(ApiType.OPENAPI)
    @AuditLog(businessType = "业务流管理", operType = "分页查询策略模板", operDesc = "分页查询策略模板", objId = "null")
    public Result<Page<BotWorkflowRsp>> queryTemplate(@RequestBody QueryWorkflowParam queryWorkflowParam) {
        Page<BotWorkflowRsp> workflowRspPage = botWorkflowConfigService.queryWorkflowList(queryWorkflowParam);
        return Result.success(workflowRspPage);
    }

    /**
     * 查询画布可关联工作流列表
     *
     * @param queryWorkflowParam 查询参数
     * @return
     */
    @Operation(summary = "查询画布可关联工作流列表", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "业务流管理")})})
    @PlatformRestApi(name = "查询画布可关联工作流列表", groupName = "业务流管理")
    @StatOpenApi(name = "查询画布可关联工作流列表", groupName = "业务流管理")
    @PostMapping(value = "/canvas/page")
    @AuditLog(businessType = "业务流管理", operType = "查询画布可关联工作流列表", operDesc = "查询画布可关联工作流列表", objId = "null")
    @HideFromApiTypes(ApiType.OPENAPI)
    public Result<Page<BotWorkflowRsp>> queryCanvasPage(@RequestBody QueryWorkflowParam queryWorkflowParam) {
        return Result.success(botWorkflowConfigService.queryCanvasWorkflowList(queryWorkflowParam));
    }

    /**
     * 保存意图
     *
     * @param botWorkflowIntentParam
     * @return
     */
    @Operation(summary = "保存意图", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "业务流管理")})})
    @PlatformRestApi(name = "保存意图", groupName = "业务流管理")
    @StatOpenApi(name = "保存意图", groupName = "业务流管理")
    @PostMapping(value = "/saveIntent")
    @AuditLog(businessType = "业务流管理", operType = "保存意图", operDesc = "保存意图", objId = "#botWorkflowIntentParam.workflowId")
    @HideFromApiTypes(ApiType.OPENAPI)
    public Result<Boolean> saveIntent(@RequestBody BotWorkflowIntentParam botWorkflowIntentParam) {
        return Result.success(botWorkflowConfigService.saveIntent(botWorkflowIntentParam));
    }

    /**
     * 保存意图
     *
     * @return
     */
    @Operation(summary = "查询关联意图", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "业务流管理")})})
    @PlatformRestApi(name = "查询关联意图", groupName = "业务流管理")
    @StatOpenApi(name = "查询关联意图", groupName = "业务流管理")
    @GetMapping(value = "/queryRelatedIntent")
    @AuditLog(businessType = "业务流管理", operType = "查询关联意图", operDesc = "查询关联意图", objId = "#workflowId")
    public Result<List<IntentEntity>> queryRelatedIntent(@RequestParam(value = "workflowId") String workflowId) {
        return Result.success(botWorkflowConfigService.queryRelatedIntent(workflowId));
    }

    /**
     * 查询工作流详情
     *
     * @param workflowId
     * @return
     */
    @Operation(summary = "查询业务流详情", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "业务流管理")})})
    @PlatformRestApi(name = "查询业务流详情", groupName = "业务流管理")
    @StatOpenApi(name = "查询业务流详情", groupName = "业务流管理")
    @GetMapping("/detail/{workflowId}")
    @AuditLog(businessType = "业务流管理", operType = "查询业务流详情", operDesc = "查询业务流详情", objId = "#workflowId")
    @HideFromApiTypes(ApiType.OPENAPI)
    public Result<BotWorkflowDetailRsp> detail(@PathVariable("workflowId") String workflowId, @RequestParam(value = "test", required = false) Boolean test) {
        return Result.success(botWorkflowConfigService.botWorkflowDetail(workflowId, test == null || Boolean.TRUE.equals(test)));
    }

    /**
     * 创建工作流基本信息
     *
     * @param botWorkflowCreateParam 创建参数
     * @return
     */
    @Operation(summary = "创建业务流基本信息", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "业务流管理")})})
    @PlatformRestApi(name = "创建业务流基本信息", groupName = "业务流管理")
    @StatOpenApi(name = "创建业务流基本信息", groupName = "业务流管理")
    @PostMapping("/baseInfo/create")
    @AuditLog(businessType = "业务流管理", operType = "创建业务流基本信息", operDesc = "创建业务流基本信息", objId = "null")
    @HideFromApiTypes(ApiType.OPENAPI)
    public Result<String> create(@RequestBody @Valid BotWorkflowCreateParam botWorkflowCreateParam) {
        return Result.success(botWorkflowConfigService.createBotWorkflow(botWorkflowCreateParam));
    }

    /**
     * 更新工作流基本信息
     *
     * @param botWorkflowUpdateParam 更新参数
     * @return
     */
    @Operation(summary = "更新业务流基本信息", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "业务流管理")})})
    @PlatformRestApi(name = "更新业务流基本信息", groupName = "业务流管理")
    @StatOpenApi(name = "更新业务流基本信息", groupName = "业务流管理")
    @PostMapping("/baseInfo/update")
    @HideFromApiTypes(ApiType.OPENAPI)
    @AuditLog(businessType = "业务流管理", operType = "更新业务流基本信息", operDesc = "更新业务流基本信息", objId = "#botWorkflowUpdateParam.workflowId")
    public Result<Boolean> update(@Valid @RequestBody BotWorkflowUpdateParam botWorkflowUpdateParam) {
        return Result.success(botWorkflowConfigService.updateBotWorkflow(botWorkflowUpdateParam));
    }

    /**
     * 创建子流程
     *
     * @param botWorkflowCreateSubParam 创建参数
     * @return
     */
    @Operation(summary = "创建子流程", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "业务流管理")})})
    @PlatformRestApi(name = "创建子流程", groupName = "业务流管理")
    @StatOpenApi(name = "创建子流程", groupName = "业务流管理")
    @PostMapping("/createSubWorkflow")
    @AuditLog(businessType = "业务流管理", operType = "创建子流程", operDesc = "创建子流程", objId = "#botWorkflowCreateSubParam.mainWorkflowId")
    @HideFromApiTypes(ApiType.OPENAPI)
    public Result<String> createSubWorkflow(@RequestBody @Valid BotWorkflowCreateSubParam botWorkflowCreateSubParam) {
        return Result.success(botWorkflowConfigService.createBotSubWorkflow(botWorkflowCreateSubParam));
    }

    /**
     * 查询主流程列表
     *
     * @param querySubWorkflowParam 创建参数
     * @return
     */
    @Operation(summary = "查询子流程列表", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "业务流管理")})})
    @PlatformRestApi(name = "查询子流程列表", groupName = "业务流管理")
    @StatOpenApi(name = "查询子流程列表", groupName = "业务流管理")
    @PostMapping("/querySubWorkflowList")
    @HideFromApiTypes(ApiType.OPENAPI)
    @AuditLog(businessType = "业务流管理", operType = "查询子流程列表", operDesc = "查询子流程列表", objId = "#querySubWorkflowParam.mainWorkflowId")
    public Result<List<BotWorkflowDetailRsp>> querySubWorkflowList(@RequestBody @Valid QuerySubWorkflowParam querySubWorkflowParam) {
        return Result.success(botWorkflowConfigService.querySubWorkflowList(querySubWorkflowParam));
    }

    /**
     * 保存工作流画布和节点边线
     *
     * @param botWorkflowParam 画布参数
     * @return
     */
    @Operation(summary = "保存画布", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "业务流管理")})})
    @PlatformRestApi(name = "保存画布", groupName = "业务流管理")
    @StatOpenApi(name = "保存画布", groupName = "业务流管理")
    @PostMapping("/save")
    @AuditLog(businessType = "业务流管理", operType = "保存画布", operDesc = "保存画布", objId = "#botWorkflowParam.workflowId")
    @HideFromApiTypes(ApiType.OPENAPI)
    public Result<Boolean> save(@Validated @RequestBody BotWorkflowParam botWorkflowParam) {
        botWorkflowParam.setNeedUpdateAgent(true);
        return Result.success(botWorkflowConfigService.saveBotWorkflow(botWorkflowParam));
    }

    /**
     * 保存工作流画布和节点边线
     *
     * @param workflowId 画布参数
     * @return
     */
    @Operation(summary = "检查是否存在未发布的知识库文档", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "业务流管理")})})
    @PlatformRestApi(name = "检查是否存在未发布的知识库文档", groupName = "业务流管理")
    @StatOpenApi(name = "检查是否存在未发布的知识库文档", groupName = "业务流管理")
    @HideFromApiTypes(ApiType.OPENAPI)
    @GetMapping("/checkKms")
    @AuditLog(businessType = "业务流管理", operType = "检查是否存在未发布的知识库文档", operDesc = "检查是否存在未发布的知识库文档", objId = "#workflowId")
    public Result<List<KnowledgeResponse>> checkKms(String workflowId) {
        return Result.success(botWorkflowConfigService.checkKms(workflowId));
    }

    /**
     * 删除工作流
     *
     * @param workflowId
     * @return
     */
    @Operation(summary = "删除工作流", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "业务流管理")})})
    @PlatformRestApi(name = "删除工作流", groupName = "业务流管理")
    @StatOpenApi(name = "删除工作流", groupName = "业务流管理")
    @GetMapping("/delete/{workflowId}")
    @AuditLog(businessType = "业务流管理", operType = "删除工作流", operDesc = "删除工作流", objId = "#workflowId")
    @HideFromApiTypes(ApiType.OPENAPI)
    public Result<Boolean> delete(@PathVariable("workflowId") String workflowId) {
        return Result.success(botWorkflowConfigService.deleteWorkflow(workflowId));
    }

    /**
     * 发布工作流
     */
    @Operation(summary = "发布业务流", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "业务流管理")})})
    @PlatformRestApi(name = "发布业务流", groupName = "业务流管理")
    @StatOpenApi(name = "发布业务流", groupName = "业务流管理")
    @GetMapping("/release")
    @HideFromApiTypes(ApiType.OPENAPI)
    @AuditLog(businessType = "业务流管理", operType = "发布业务流", operDesc = "发布业务流", objId = "#workflowId")
    public Result<Boolean> release(@RequestParam("workflowId") String workflowId) {
        return Result.success(botWorkflowConfigService.releaseWorkflow(workflowId));
    }

    /**
     * 复制工作流
     */
    @Operation(summary = "复制业务流", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "业务流管理")})})
    @PlatformRestApi(name = "复制业务流", groupName = "业务流管理")
    @StatOpenApi(name = "复制业务流", groupName = "业务流管理")
    @GetMapping("/copyWorkflow")
    @AuditLog(businessType = "业务流管理", operType = "复制业务流", operDesc = "复制业务流", objId = "#workflowId")
    @HideFromApiTypes(ApiType.OPENAPI)
    public Result<String> copyWorkflow(@RequestParam("workflowId") String workflowId) {
        return Result.success(botWorkflowConfigService.copyWorkflow(workflowId, false));
    }

    /**
     * 查询场景分类
     */
    @Operation(summary = "查询场景分类", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "业务流管理")})})
    @PlatformRestApi(name = "查询场景分类", groupName = "业务流管理")
    @StatOpenApi(name = "查询场景分类", groupName = "业务流管理")
    @GetMapping("/querySceneList")
    @HideFromApiTypes(ApiType.OPENAPI)
    @AuditLog(businessType = "业务流管理", operType = "查询场景分类", operDesc = "查询场景分类", objId = "null")
    public Result<WorkflowSceneRsp> querySceneList(@RequestParam(value = "name", required = false) String name) {
        return Result.success(botWorkflowConfigService.querySceneList(name));
    }

    /**
     * 查询引用变量
     */
    @Operation(summary = "查询引用的节点变量", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "业务流管理")})})
    @PlatformRestApi(name = "查询引用的节点变量", groupName = "业务流管理")
    @StatOpenApi(name = "查询引用的节点变量", groupName = "业务流管理")
    @GetMapping("/getNodeVariable")
    @AuditLog(businessType = "业务流管理", operType = "查询引用的节点变量", operDesc = "查询引用的节点变量", objId = "#workflowId")
    @HideFromApiTypes(ApiType.OPENAPI)
    public Result<List<NodeVariableRsp>> getNodeVariables(@Validated @RequestParam("workflowId") String workflowId, @Validated @RequestParam("nodeId") String nodeId) {
        return Result.success(botWorkflowConfigService.getVariables(workflowId, nodeId));
    }

    /**
     * 查询引用变量
     */
    @Operation(summary = "查询引用的全局变量", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "业务流管理")})})
    @PlatformRestApi(name = "查询引用的全局变量", groupName = "业务流管理")
    @StatOpenApi(name = "查询引用的全局变量", groupName = "业务流管理")
    @GetMapping("/getGlobalVariable")
    @AuditLog(businessType = "业务流管理", operType = "查询引用的全局变量", operDesc = "查询引用的全局变量", objId = "#workflowId")
    @HideFromApiTypes(ApiType.OPENAPI)
    public Result<RefGlobalVariableRsp> getGlobalVariable(@Validated @RequestParam("workflowId") String workflowId, @RequestParam(value = "test", required = false) Boolean test) {
        return Result.success(botWorkflowConfigService.getGlobalVariable(workflowId, test));
    }

    /**
     * 查询引用变量
     */
    @Operation(summary = "查询引用的变量", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "业务流管理")})})
    @PlatformRestApi(name = "查询引用的变量", groupName = "业务流管理")
    @StatOpenApi(name = "查询引用的变量", groupName = "业务流管理")
    @GetMapping("/getVariables")
    @AuditLog(businessType = "业务流管理", operType = "查询引用的变量", operDesc = "查询引用的变量", objId = "#workflowId")
    @HideFromApiTypes(ApiType.OPENAPI)
    public Result<List<VariableGroupResp>> getVariables(@RequestParam("workflowId") String workflowId,
                                                        @RequestParam("nodeId") String nodeId,
                                                        @RequestParam("modeType") String modeType
    ) {
        RequestInfo requestInfo = RequestContext.get();
        BotWorkflowPO workflowDetail = botWorkflowConfigService.queryWorkflowDetailByLastVersion(workflowId, true);
        if (workflowDetail == null) {
            throw new BizException("AD031", "业务流不存在");
        }
        if (!Objects.equals(WorkflowTypeEnum.STRATEGY_TEMPLATE.getCode(), workflowDetail.getWorkflowType())) {
            checkResourceAuth(workflowId);
        }

        //1.拉取全部全局变量
        VariableGroupResp globalGroup = getGlobalVarGroup(modeType);
        //2.拉取全部workflow变量
        VariableGroupResp workFlowGroup = getWorkFlowVarGroup(workflowId);
        return Result.success(Arrays.asList(globalGroup, workFlowGroup));
    }

    private VariableGroupResp getGlobalVarGroup(String modeType) {
        GlobalVariableQueryRequest globalVarPageQuery = new GlobalVariableQueryRequest();
        globalVarPageQuery.setPageNum(1);
        globalVarPageQuery.setPageSize(MAX_SIZE);
        if (StringUtils.equalsIgnoreCase(modeType, WRITE)) {
            globalVarPageQuery.setSystemFlag(false);
        }
        com.chinatelelcom.gs.engine.sdk.common.Page<GlobalVariableDataDetailVO> globalVarPage = globalVariableRpcApi.page(globalVarPageQuery);
        VariableGroupResp group = new VariableGroupResp();
        group.setVariableType(VariableTypeEnum.GLOBAL.getCode());
        group.setVariableGroupName(VariableTypeEnum.GLOBAL.getDesc());
        group.setVariables(globalVarPage.getRecords().stream().map(e -> convert(e)).collect(Collectors.toList()));
        return group;
    }

    private VariableGroupResp getWorkFlowVarGroup(String workflowId) {
        WorkFlowVariableQueryRequest workFlowVarPageQuery = new WorkFlowVariableQueryRequest();
        workFlowVarPageQuery.setPageNum(1);
        workFlowVarPageQuery.setPageSize(MAX_SIZE);
        workFlowVarPageQuery.setWorkFlowId(workflowId);
        com.chinatelelcom.gs.engine.sdk.common.Page<WorkFlowVariableDataDetailVO> workFlowVarPage = workFlowVariableAppService.page(workFlowVarPageQuery, true);
        VariableGroupResp group = new VariableGroupResp();
        group.setVariableType(VariableTypeEnum.WORKFLOW.getCode());
        group.setVariableGroupName(VariableTypeEnum.WORKFLOW.getDesc());
        group.setVariables(workFlowVarPage.getRecords().stream().map(e -> convert(e)).collect(Collectors.toList()));
        return group;
    }

    private Param.VariableRefParam convert(GlobalVariableDataDetailVO varDetail) {
        Param.VariableRefParam variableRefParam = new Param.VariableRefParam();
        variableRefParam.setVariableCode(varDetail.getVariableCode());
        variableRefParam.setVariableType(varDetail.getVariableType());
        variableRefParam.setVariableName(varDetail.getVariableName());
        variableRefParam.setDataType(varDetail.getDataType());
        variableRefParam.setVariableSelector(VariableUtils.buildSelector(varDetail.getVariableType(), varDetail.getIsSystem() ? "sys" : "custom", varDetail.getVariableName()));
        return variableRefParam;
    }

    private Param.VariableRefParam convert(WorkFlowVariableDataDetailVO varDetail) {
        Param.VariableRefParam variableRefParam = new Param.VariableRefParam();
        variableRefParam.setVariableCode(varDetail.getVariableCode());
        variableRefParam.setVariableType(varDetail.getVariableType());
        variableRefParam.setVariableName(varDetail.getVariableName());
        variableRefParam.setDataType(varDetail.getDataType());
        variableRefParam.setVariableSelector(buildSelector(varDetail.getVariableType(), "workFlowId", varDetail.getVariableName()));
        return variableRefParam;
    }

    private String[] buildSelector(String variableType, String id, String variableName) {
        return new String[]{variableType, id, variableName};
    }

    /**
     * 查询调试前置动作
     */
    @Operation(summary = "查询调试前置动作", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "业务流管理")})})
    @PlatformRestApi(name = "查询调试前置动作", groupName = "业务流管理")
    @StatOpenApi(name = "查询调试前置动作", groupName = "业务流管理")
    @GetMapping("/getTestAction")
    @AuditLog(businessType = "业务流管理", operType = "查询调试前置动作", operDesc = "查询调试前置动作", objId = "#workflowId")
    @HideFromApiTypes(ApiType.OPENAPI)
    public Result<String> getTestAction(@RequestParam("workflowId") String workflowId, @RequestParam(name = "nodeId", required = false) String nodeId,
                                        @RequestParam(name = "shelved", required = false, defaultValue = "false") Boolean shelved) {
        BotWorkflowDetailRsp workflowDetail;
        if (Boolean.TRUE.equals(shelved)) {
            workflowDetail = botWorkflowConfigService.botWorkflowDetail(workflowId, false);
        } else {
            workflowDetail = botWorkflowConfigService.botWorkflowDetail(workflowId, true);
        }
        List<Node> nodes = Optional.ofNullable(workflowDetail).map(BotWorkflowDetailRsp::getNodes).map(nodesList -> nodesList.stream()
                .filter(e -> StringUtils.isEmpty(nodeId) || StringUtils.equalsIgnoreCase(nodeId, e.getNodeId()))
                .collect(Collectors.toList())).orElseGet(Collections::emptyList);

        //1.调试时时候触发bot变量关联弹窗
        Optional<Node> opt = nodes.stream().filter(e -> needBotVarAssociate(e)).findAny();
        if (opt.isPresent()) {
            return Result.success("bot_var_associate");
        }
        // 默认不处理
        return Result.success();
    }

    private boolean needBotVarAssociate(Node node) {
        NodeTypeEnum nodeType = NodeTypeEnum.getByCode(node.getNodeType());
        return switch (nodeType) {
            case VARIABLE_ACQUIRE -> true;
            case QUERY_MESSAGE_LIST -> true;
            case DIALOG_STRATEGY -> true;
            case VARIABLE_ASSIGN -> needBotVarAssociateByAssignNode(node);
            default -> false;
        };
    }

    private boolean needBotVarAssociateByAssignNode(Node node) {
        VariableAssignNodeBizParam assignBizParam = getBizParam(node, VariableAssignNodeBizParam.class);
        for (VariableAssignNodeBizParam.AssignVariableTask task : assignBizParam.getAssignedVariableList()) {
            if (needSelector(task.getAssignedVariable().getRefVariable())) {
                return true;
            }
        }
        return false;
    }

    private boolean needSelector(Param.VariableRefParam refParam) {
        if (refParam != null && StringUtils.isNotEmpty(refParam.getVariableName())) {
            return refParam.getVariableSelector() == null ? true : false;
        }
        return false;
    }

    public <T> T getBizParam(Node node, Class<T> t) {
        Object bizParamObj = Optional.ofNullable(node).map(Node::getData).map(Node.NodeData::getInputs).map(Node.Input::getBizParam).orElse(null);
        if (Objects.isNull(bizParamObj)) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(bizParamObj), t);
    }

    private void checkResourceAuth(String workflowId) {
        GrantObjectDTO grantObjectDTO = privilegeUtil.hasPrivilege(workflowId, ResourceTypeEnum.WORKFLOW);
        if (grantObjectDTO == null) {
            throw new BizException("CA007", "没有权限访问该资源");
        }
        boolean checkResult = grantObjectDTO.getPrivilege() != null && !grantObjectDTO.getPrivilege().equals(PrivilegeEnum.no_permission);
        if (!checkResult) {
            throw new BizException("CA007", "没有权限访问该资源");
        }
    }
}
