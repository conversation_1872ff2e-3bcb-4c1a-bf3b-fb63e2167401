package com.chinatelecom.gs.privilege.common.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AccessControlTeamInfo implements Serializable {
    @Serial
    private static final long serialVersionUID = -211523100291285816L;

    private String teamName;

    private String teamCode;

    private String parentTeamCode;

    private String parentTeamName;

    private List<AccessControlTeamInfo> childTeams = Collections.synchronizedList(new ArrayList<>());

    public void addChild(AccessControlTeamInfo teamInfo) {
        if (teamInfo == null) {
            throw new IllegalArgumentException("teamInfo cannot be null");
        }

        synchronized (this) {
            if (childTeams == null) {
                childTeams = Collections.synchronizedList(new ArrayList<>());
            }
        }

        childTeams.add(teamInfo);
    }
}
