package com.chinatelecom.gs.engine.channel.service.messagehandler;

import com.chinatelecom.gs.engine.channel.dao.repository.ChannelMessageRecordRepository;
import com.chinatelecom.gs.engine.channel.service.dto.ChannelMsgRecordDTO;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Collections;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class MessageRecordServiceTest {

    @Mock
    private ChannelMessageRecordRepository recordRepository;

    @InjectMocks
    private MessageRecordService messageRecordService;

    private AutoCloseable closeable;

    @BeforeEach
    void setUp() {
        closeable = MockitoAnnotations.openMocks(this);
        // 通过反射设置私有字段值
        ReflectionTestUtils.setField(messageRecordService, "messageQueue", new LinkedBlockingQueue<>(1000));
    }

    @AfterEach
    void tearDown() throws Exception {
        closeable.close();
    }



    @Test
    void testRecordMessage_Interrupted() {
        Thread.currentThread().interrupt();
        messageRecordService.recordMessage(new ChannelMsgRecordDTO());
        assertTrue(Thread.interrupted()); // 验证中断状态被重置
    }


    @Test
    void testShutdown_Graceful() throws InterruptedException {
        ScheduledExecutorService mockExecutor = mock(ScheduledExecutorService.class);
        ReflectionTestUtils.setField(messageRecordService, "executorService", mockExecutor);

        when(mockExecutor.awaitTermination(5000, TimeUnit.MILLISECONDS)).thenReturn(true);
        messageRecordService.shutdown();

        verify(mockExecutor).shutdown();
        verify(mockExecutor).awaitTermination(5000, TimeUnit.MILLISECONDS);
    }

    @Test
    void testShutdown_Forceful() throws InterruptedException {
        ScheduledExecutorService mockExecutor = mock(ScheduledExecutorService.class);
        ReflectionTestUtils.setField(messageRecordService, "executorService", mockExecutor);

        when(mockExecutor.awaitTermination(5000, TimeUnit.MILLISECONDS)).thenReturn(false);
        messageRecordService.shutdown();

        verify(mockExecutor).shutdownNow();
    }

    @Test
    void testShutdown_Interrupted() throws InterruptedException {
        ScheduledExecutorService mockExecutor = mock(ScheduledExecutorService.class);
        ReflectionTestUtils.setField(messageRecordService, "executorService", mockExecutor);

        when(mockExecutor.awaitTermination(5000, TimeUnit.MILLISECONDS)).thenThrow(new InterruptedException());
        messageRecordService.shutdown();

        verify(mockExecutor).shutdownNow();
        assertTrue(Thread.interrupted()); // 验证中断状态被重置
    }
}