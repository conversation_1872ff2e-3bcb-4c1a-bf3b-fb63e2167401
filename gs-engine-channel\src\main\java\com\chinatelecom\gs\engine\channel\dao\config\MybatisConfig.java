//package com.chinatelecom.csbotplatform.channel.dao.config;
//
//import com.baomidou.mybatisplus.annotation.DbType;
//import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
//import com.baomidou.mybatisplus.extension.plugins.handler.TenantLineHandler;
//import com.baomidou.mybatisplus.extension.plugins.inner.BlockAttackInnerInterceptor;
//import com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor;
//import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
//import com.baomidou.mybatisplus.extension.plugins.inner.TenantLineInnerInterceptor;
//import com.google.common.collect.Lists;
//import net.sf.jsqlparser.expression.Expression;
//import net.sf.jsqlparser.expression.LongValue;
//import org.mybatis.spring.annotation.MapperScan;
//import org.springframework.context.annotation.Bean;
//import org.springframework.stereotype.Component;
//
///**
// * <AUTHOR>
// * @date 2023/12/25 20:38
// * @description
// */
//@Component
//@MapperScan("com.chinatelecom.csbotplatform.channel.dao.mapper")
//public class MybatisConfig {
//
//    private static final LongValue DEFAULT_TENANT_ID = new LongValue(0L);
//
//    @Bean
//    public MybatisPlusInterceptor mybatisPlusInterceptor() {
//        MybatisPlusInterceptor mybatisPlusInterceptor = new MybatisPlusInterceptor();
//
//        // 防止全表更新删除操作, 必须在租户插件之前
//        mybatisPlusInterceptor.addInnerInterceptor(new BlockAttackInnerInterceptor());
//
//        // 创建租户SQL解析器 如果用了分页插件注意先 add TenantLineInnerInterceptor 再 add PaginationInnerInterceptor
//        mybatisPlusInterceptor.addInnerInterceptor(new TenantLineInnerInterceptor(new TenantLineHandler() {
//            @Override
//            public Expression getTenantId() {
//                return DEFAULT_TENANT_ID;
//            }
//
//            @Override
//            public boolean ignoreTable(String tableName) {
//                return Lists.newArrayList().contains(tableName);
//            }
//        }));
//
//        // 分页插件
//        PaginationInnerInterceptor paginationInnerInterceptor = new PaginationInnerInterceptor(DbType.MYSQL);
//        // 设置分页上限
//        paginationInnerInterceptor.setMaxLimit(5000L);
//        // 设置leftJoin 优化
//        paginationInnerInterceptor.setOptimizeJoin(true);
//        mybatisPlusInterceptor.addInnerInterceptor(paginationInnerInterceptor);
//
//        // 乐观锁插件
//        OptimisticLockerInnerInterceptor optimisticLockerInnerInterceptor = new OptimisticLockerInnerInterceptor();
//        mybatisPlusInterceptor.addInnerInterceptor(optimisticLockerInnerInterceptor);
//
//        return mybatisPlusInterceptor;
//    }
//}
