package com.chinatelecom.gs.engine.core.manager.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinatelecom.gs.engine.common.log.track.LogMessage;
import com.chinatelecom.gs.engine.common.log.track.LogPO;
import com.chinatelecom.gs.engine.common.mq.KmsSearchMessage;
import com.chinatelecom.gs.engine.common.utils.IpMaskUtils;
import com.chinatelecom.gs.engine.core.manager.service.LogEsService;
import com.chinatelecom.gs.engine.core.manager.service.LogService;
import com.chinatelecom.gs.engine.core.manager.service.SearchKmsLogEsService;
import com.chinatelecom.gs.engine.core.manager.vo.LogMessageVO;
import com.chinatelecom.gs.engine.core.manager.vo.LogVO;
import org.junit.Assert;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class LogAppServiceImplTest {

    @InjectMocks
    private LogAppServiceImpl logAppService;

    @Mock
    private LogService logService;

    @Mock
    private LogEsService logEsService;

    @Mock
    private SearchKmsLogEsService searchKmsLogEsService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        // 设置esLogMessageEnable为true
        ReflectionTestUtils.setField(logAppService, "esLogMessageEnable", true);
    }

    @Test
    void testSaveLog_WithValidLogMessage_SavesToLogService() {
        // Given
        LogMessage logMessage = new LogMessage();
        logMessage.setLogId("test-log-id");
        logMessage.setMessageId("test-message-id");
        logMessage.setSessionId("test-session-id");

        // When
        logAppService.saveLog(logMessage);

        // Then
        ArgumentCaptor<LogPO> logPOCaptor = ArgumentCaptor.forClass(LogPO.class);
        verify(logService).save(logPOCaptor.capture());

        LogPO capturedLogPO = logPOCaptor.getValue();
        assertEquals("test-log-id", capturedLogPO.getLogId());
        assertEquals("test-message-id", capturedLogPO.getMessageId());
        assertEquals("test-session-id", capturedLogPO.getSessionId());

        // Verify that logEsService.saveLog is also called when esLogMessageEnable is true
        verify(logEsService).saveLog(logMessage);
    }

    @Test
    void testSaveLog_WithEsLogMessageDisabled_DoesNotSaveToEsService() {
        // Given
        LogAppServiceImpl logAppServiceSpy = Mockito.spy(logAppService);
        ReflectionTestUtils.setField(logAppServiceSpy, "esLogMessageEnable", false);

        LogMessage logMessage = new LogMessage();
        logMessage.setLogId("test-log-id");

        // When
        logAppServiceSpy.saveLog(logMessage);

        // Then
        verify(logService).save(any(LogPO.class));
        verify(logEsService, never()).saveLog(any(LogMessage.class));
    }

    @Test
    void testGetLogDetail_WithBlankMessageId_ReturnsNull() {
        // When
        LogMessageVO result = logAppService.getLogDetail("test-session-id", "");

        // Then
        Assert.assertNull(result);
    }

    @Test
    void testGetLogDetail_WithNullMessageId_ReturnsNull() {
        // When
        LogMessageVO result = logAppService.getLogDetail("test-session-id", null);

        // Then
        Assert.assertNull(result);
    }

    @Test
    void testGetLogDetail_WithNoLogsFound_ReturnsNull() {
        // Given
        when(logService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());
        when(logEsService.searchLogsByMessageId("test-message-id")).thenReturn(Collections.emptyList());

        // When
        LogMessageVO result = logAppService.getLogDetail("test-session-id", "test-message-id");

        // Then
        Assert.assertNull(result);
    }

    @Test
    void testGetLogDetail_WithLogsFromDatabase_ReturnsLogMessageVO() {
        // Given
        LogPO logPO1 = new LogPO();
        logPO1.setLogId("log-1");
        logPO1.setMessageId("test-message-id");
        logPO1.setSessionId("test-session-id");
        logPO1.setPLogId(null); // Root node

        LogPO logPO2 = new LogPO();
        logPO2.setLogId("log-2");
        logPO2.setMessageId("test-message-id");
        logPO2.setSessionId("test-session-id");
        logPO2.setPLogId("log-1"); // Child of log-1

        when(logService.list(any(LambdaQueryWrapper.class))).thenReturn(Arrays.asList(logPO1, logPO2));

        // Mock IpMaskUtils
        try (MockedStatic<IpMaskUtils> mockedIpMaskUtils = mockStatic(IpMaskUtils.class)) {
            mockedIpMaskUtils.when(() -> IpMaskUtils.maskIpAddresses(anyString())).thenAnswer(invocation -> invocation.getArgument(0));

            // When
            LogMessageVO result = logAppService.getLogDetail("test-session-id", "test-message-id");

            // Then
            assertNotNull(result);
            assertEquals("2", result.getLogCount().toString());
            assertNotNull(result.getLogs());
            assertEquals(1, result.getLogs().size());
            assertEquals("log-1", result.getLogs().get(0).getLogId());
            assertEquals(1, result.getLogs().get(0).getChildren().size());
            assertEquals("log-2", result.getLogs().get(0).getChildren().get(0).getLogId());
        }
    }

    @Test
    void testGetLogDetail_WithDuplicateLogs_RemovesDuplicates() {
        // Given
        LogPO logPO1 = new LogPO();
        logPO1.setLogId("log-1");
        logPO1.setMessageId("test-message-id");
        logPO1.setSessionId("test-session-id");
        logPO1.setPLogId(null);

        // Duplicate log with same logId
        LogPO logPO2 = new LogPO();
        logPO2.setLogId("log-1"); // Same logId as logPO1
        logPO2.setMessageId("test-message-id");
        logPO2.setSessionId("test-session-id");
        logPO2.setPLogId(null);

        when(logService.list(any(LambdaQueryWrapper.class))).thenReturn(Arrays.asList(logPO1, logPO2));

        try (MockedStatic<IpMaskUtils> mockedIpMaskUtils = mockStatic(IpMaskUtils.class)) {
            mockedIpMaskUtils.when(() -> IpMaskUtils.maskIpAddresses(anyString())).thenAnswer(invocation -> invocation.getArgument(0));

            // When
            LogMessageVO result = logAppService.getLogDetail("test-session-id", "test-message-id");

            // Then
            Assertions.assertNotNull(result);
            Assert.assertEquals("1", result.getLogCount().toString()); // Should be deduplicated
        }
    }

    @Test
    void testGetLogDetail_WithLogsFromEsService_ReturnsLogMessageVO() {
        // Given
        when(logService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        LogVO logVO1 = new LogVO();
        logVO1.setLogId("log-1");
        logVO1.setMessageId("test-message-id");
        logVO1.setSessionId("test-session-id");
        logVO1.setPLogId(null);

        LogVO logVO2 = new LogVO();
        logVO2.setLogId("log-2");
        logVO2.setMessageId("test-message-id");
        logVO2.setSessionId("test-session-id");
        logVO2.setPLogId("log-1");

        when(logEsService.searchLogsByMessageId("test-message-id")).thenReturn(Arrays.asList(logVO1, logVO2));

        try (MockedStatic<IpMaskUtils> mockedIpMaskUtils = mockStatic(IpMaskUtils.class)) {
            mockedIpMaskUtils.when(() -> IpMaskUtils.maskIpAddresses(anyString())).thenAnswer(invocation -> invocation.getArgument(0));

            // When
            LogMessageVO result = logAppService.getLogDetail("test-session-id", "test-message-id");

            // Then
            assertNotNull(result);
            assertEquals("2", result.getLogCount().toString());
            assertNotNull(result.getLogs());
            assertEquals(1, result.getLogs().size());
            assertEquals("log-1", result.getLogs().get(0).getLogId());
            assertEquals(1, result.getLogs().get(0).getChildren().size());
            assertEquals("log-2", result.getLogs().get(0).getChildren().get(0).getLogId());
        }
    }

    @Test
    void testGetLogDetail_WithIpMasking_AppliesMaskingToFields() {
        // Given
        LogPO logPO = new LogPO();
        logPO.setLogId("log-1");
        logPO.setMessageId("test-message-id");
        logPO.setSessionId("test-session-id");
        logPO.setPLogId(null);
        logPO.setConfig("Config with IP 127.0.0.1");
        logPO.setUrl("URL with IP http://127.0.0.1:8080");
        logPO.setInputData("Input with IP 127.0.0.1");
        logPO.setOutputData("Output with IP 127.0.0.1");
        logPO.setExtraData("Extra with IP 127.0.0.1");

        when(logService.list(any(LambdaQueryWrapper.class))).thenReturn(Arrays.asList(logPO));

        try (MockedStatic<IpMaskUtils> mockedIpMaskUtils = mockStatic(IpMaskUtils.class)) {
            mockedIpMaskUtils.when(() -> IpMaskUtils.maskIpAddresses(anyString())).thenReturn("MASKED_DATA");

            // When
            LogMessageVO result = logAppService.getLogDetail("test-session-id", "test-message-id");

            // Then
            assertNotNull(result);
            assertEquals("1", result.getLogCount().toString());
            LogVO logVO = result.getLogs().get(0);
            assertEquals("MASKED_DATA", logVO.getConfig());
            assertEquals("MASKED_DATA", logVO.getUrl());
            assertEquals("MASKED_DATA", logVO.getInputData());
            assertEquals("MASKED_DATA", logVO.getOutputData());
            assertEquals("MASKED_DATA", logVO.getExtraData());
        }
    }


    @Test
    void testBuildTree_WithValidLogList_BuildsTreeStructure() {
        // Given
        LogVO logVO1 = new LogVO();
        logVO1.setLogId("log-1");
        logVO1.setPLogId(null);

        LogVO logVO2 = new LogVO();
        logVO2.setLogId("log-2");
        logVO2.setPLogId("log-1");

        LogVO logVO3 = new LogVO();
        logVO3.setLogId("log-3");
        logVO3.setPLogId("log-1");

        LogVO logVO4 = new LogVO();
        logVO4.setLogId("log-4");
        logVO4.setPLogId("log-2");

        List<LogVO> logVOList = Arrays.asList(logVO1, logVO2, logVO3, logVO4);
        Map<String, LogVO> logVOMap = new HashMap<>();
        for (LogVO logVO : logVOList) {
            logVOMap.put(logVO.getLogId(), logVO);
        }

        // When
        LogVO root = logAppService.buildTree(logVOList, logVOMap);

        // Then
        assertNotNull(root);
        assertEquals(1, root.getChildren().size());
        assertEquals("log-1", root.getChildren().get(0).getLogId());
        assertEquals(2, root.getChildren().get(0).getChildren().size());
        assertEquals("log-2", root.getChildren().get(0).getChildren().get(0).getLogId());
        assertEquals("log-3", root.getChildren().get(0).getChildren().get(1).getLogId());
        assertEquals(1, root.getChildren().get(0).getChildren().get(0).getChildren().size());
        assertEquals("log-4", root.getChildren().get(0).getChildren().get(0).getChildren().get(0).getLogId());
    }

    @Test
    void testBuildTree_WithOrphanedNodes_HandlesOrphanedNodesGracefully() {
        // Given
        LogVO logVO1 = new LogVO();
        logVO1.setLogId("log-1");
        logVO1.setPLogId(null);

        LogVO logVO2 = new LogVO();
        logVO2.setLogId("log-2");
        logVO2.setPLogId("non-existent-parent"); // Parent doesn't exist

        List<LogVO> logVOList = Arrays.asList(logVO1, logVO2);
        Map<String, LogVO> logVOMap = new HashMap<>();
        logVOMap.put("log-1", logVO1);
        // Note: log-2's parent is not in the map

        // When
        LogVO root = logAppService.buildTree(logVOList, logVOMap);

        // Then
        assertNotNull(root);
        // 修复：只有pLogId为null的节点才会被添加到根节点中
        assertEquals(1, root.getChildren().size()); // Only log-1 is added to root
        Set<String> rootLogIds = new HashSet<>();
        for (LogVO logVO : root.getChildren()) {
            rootLogIds.add(logVO.getLogId());
        }
        assertTrue(rootLogIds.contains("log-1"));
        // 修复：log-2不会出现在根节点的子节点中，因为它的父节点不存在
        assertFalse(rootLogIds.contains("log-2"));

        // 验证log-2是否存在于返回的列表中（应该不存在）
        boolean log2Exists = root.getChildren().stream()
                .anyMatch(child -> "log-2".equals(child.getLogId()));
        assertFalse(log2Exists);
    }


    @Test
    void testSaveSearchKmsLog_ForwardsToSearchKmsLogEsService() {
        // Given
        KmsSearchMessage kmsSearchMessage = new KmsSearchMessage();
        kmsSearchMessage.setQuery("test query");

        // When
        logAppService.saveSearchKmsLog(kmsSearchMessage);

        // Then
        verify(searchKmsLogEsService).saveLog(kmsSearchMessage);
    }

    @Test
    void testGetLogVOList_WithValidSessionIdAndMessageId_ReturnsLogsFromDatabase() {
        // Given
        LogPO logPO = new LogPO();
        logPO.setLogId("log-1");
        logPO.setMessageId("test-message-id");
        logPO.setSessionId("test-session-id");

        when(logService.list(any(LambdaQueryWrapper.class))).thenReturn(Arrays.asList(logPO));

        // When
        // Use reflection to access private method
        List<LogVO> result = null;
        try {
            java.lang.reflect.Method method = LogAppServiceImpl.class.getDeclaredMethod("getLogVOList", String.class, String.class);
            method.setAccessible(true);
            result = (List<LogVO>) method.invoke(logAppService, "test-session-id", "test-message-id");
        } catch (Exception e) {
            Assert.fail("Exception should not be thrown: " + e.getMessage());
        }

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("log-1", result.get(0).getLogId());
        verify(logService).list(any(LambdaQueryWrapper.class));
        verify(logEsService, never()).searchLogsByMessageId(anyString());
    }

    @Test
    void testGetLogVOList_WithEmptyDatabaseResult_ReturnsLogsFromEsService() {
        // Given
        when(logService.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        LogVO logVO = new LogVO();
        logVO.setLogId("log-1");
        logVO.setMessageId("test-message-id");
        when(logEsService.searchLogsByMessageId("test-message-id")).thenReturn(Arrays.asList(logVO));

        // When
        List<LogVO> result = null;
        try {
            java.lang.reflect.Method method = LogAppServiceImpl.class.getDeclaredMethod("getLogVOList", String.class, String.class);
            method.setAccessible(true);
            result = (List<LogVO>) method.invoke(logAppService, "test-session-id", "test-message-id");
        } catch (Exception e) {
            Assert.fail("Exception should not be thrown: " + e.getMessage());
        }

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("log-1", result.get(0).getLogId());
        verify(logService).list(any(LambdaQueryWrapper.class));
        verify(logEsService).searchLogsByMessageId("test-message-id");
    }
}
