#!/bin/bash
set -e

source ./bin/env.sh

function start() {
    # 开启启动前，打印启动参数
    echo "[start] 开始启动 $BASE_PATH/$SERVER_NAME"
    echo "[start] JAVA_OPS: $JAVA_OPS"
    echo "[start] JAVA_AGENT: $JAVA_AGENT"
    echo "[start] PROFILES: $PROFILE"

    export SEARCH_ROOT_PATH=$BASE_PATH

    # 开始启动
    java -server $JAVA_OPS $JAVA_AGENT  $JAVA_BASE_OPS -jar $BASE_PATH/$SERVER_NAME.jar
    echo "[start] 启动 $BASE_PATH/$SERVER_NAME 完成"
}

start
