package com.chinatelecom.gs.engine.kms.controller;


import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.kms.model.vo.AuditFlowParam;
import com.chinatelecom.gs.engine.kms.model.vo.AuditFlowVO;
import com.chinatelecom.gs.engine.kms.sdk.constants.KmsApis;
import com.chinatelecom.gs.engine.kms.service.AuditFlowAppService;
import com.chinatelelcom.gs.engine.sdk.common.Page;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * <p>
 * 知识审核表 Controller
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-03
 */
@Slf4j
@Tag(name = "知识审核表")
@RestController
@RequestMapping({KmsApis.KMS_API + KmsApis.AUDIT})
public class KnowledgeAuditController {

    @Autowired
    private AuditFlowAppService auditFlowAppService;

    @Operation(summary = "已发布流程列表查询", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "已发布流程列表查询", groupName = "知识审核")
    @AuditLog(businessType = "知识审核", operType = "已发布流程列表查询", operDesc = "已发布流程列表查询", objId="null")
    @PostMapping(KmsApis.FLOW)
    public Result<Page<AuditFlowVO>> flowList(@Validated @RequestBody AuditFlowParam param) {
        Page<AuditFlowVO> pageResult = auditFlowAppService.flowList(param);
        return Result.success(pageResult);
    }


//
//    @ApiOperation(value = "知识审核表分页列表")
//    @PostMapping(KmsApis.PAGE_API)
//    public Result<Page<KnowledgeAuditVO>> page(@Validated @RequestBody KnowledgeAuditQueryParam param) {
//
//        return Result.success(knowledgeAuditAppService.pageQuery(param));
//    }
//
//    @ApiOperation(value = "知识审核表详情", response = KnowledgeAuditPO.class)
//    @GetMapping(KmsApis.CODE_PATH)
//    public Result<KnowledgeAuditVO> get(@PathVariable("code") String code) {
//        return Result.success(knowledgeAuditAppService.get(code));
//    }
//
//    @ApiOperation(value = "知识审核表新增")
//    @PostMapping
//    public Result<KnowledgeAuditVO> add(@Validated @RequestBody KnowledgeAuditCreateParam createParam) {
//        return Result.success(knowledgeAuditAppService.create(createParam));
//    }

//
//
//    @ApiOperation(value = "知识审核表分页列表", response = KnowledgeAuditPO.class)
//    @PostMapping(KmsApis.PAGE_API)
//    public Result<Page<KnowledgeAuditVO>> page(@Validated @RequestBody KnowledgeAuditQueryParam param) {
//
//        return Result.success(knowledgeAuditAppService.pageQuery(param));
//    }
//
//    @ApiOperation(value = "知识审核表详情", response = KnowledgeAuditPO.class)
//    @GetMapping(KmsApis.CODE_PATH)
//    public Result<KnowledgeAuditVO> get(@PathVariable("code") String code) {
//        return Result.success(knowledgeAuditAppService.get(code));
//    }
//
//    @ApiOperation(value = "知识审核表新增")
//    @PostMapping
//    public Result<KnowledgeAuditVO> add(@Validated @RequestBody KnowledgeAuditCreateParam createParam) {
//        return Result.success(knowledgeAuditAppService.create(createParam));
//    }
//
//
//    @ApiOperation(value = "知识审核表更新")
//    @PutMapping(KmsApis.CODE_PATH)
//    public Result<Boolean> update(@PathVariable("code") String code, @Validated @RequestBody KnowledgeAuditUpdateParam param) {
//        return Result.success(knowledgeAuditAppService.update(code, param));
//    }
//
//
//    @ApiOperation(value = "知识审核表删除")
//    @PostMapping(KmsApis.DELETE_API)
//    public Result<Boolean> delete(@Validated @RequestBody CodeParam codes) {
//        return Result.success(knowledgeAuditAppService.delete(codes));
//    }
}


