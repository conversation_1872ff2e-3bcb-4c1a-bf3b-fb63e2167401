package com.chinatelecom.gs.engine.channel.application.listener;

import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.context.RequestInfo;
import org.springframework.context.ApplicationEvent;

/**
 * @description: TODO
 * @author: xktang
 * @date: 2024/7/17 上午9:35
 * @version: 1.0
 */
public class ChannelOffSiteSwitchOpenEvent extends ApplicationEvent {

    private static final String PLATFORM_USER = "agent";

    private final RequestInfo userInfo;

    public ChannelOffSiteSwitchOpenEvent(String agentCode) {
        super(agentCode);
        this.userInfo = RequestContext.get();
    }

    @Override
    public String getSource() {
        initUserInfo();
        return (String) super.getSource();
    }

    private void initUserInfo() {
        if (userInfo == null) {
            RequestInfo userInfo = new RequestInfo();
            userInfo.setUserId(PLATFORM_USER);
            userInfo.setUserName(PLATFORM_USER);
            RequestContext.set(userInfo);
        }
        RequestContext.set(this.userInfo);
    }
}
