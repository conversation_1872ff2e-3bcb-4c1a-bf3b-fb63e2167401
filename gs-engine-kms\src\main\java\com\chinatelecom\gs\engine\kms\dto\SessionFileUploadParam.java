package com.chinatelecom.gs.engine.kms.dto;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.constraints.NotNull;

/**
 * 会话文件上传参数
 */
@Schema(description = "会话文件上传参数")
@Data
public class SessionFileUploadParam extends SessionFileParam{

    /**
     * 文件
     */
    @NotNull(message = "文件不能为空")
    @Schema(description = "文件(二进制)", required = true)
    private MultipartFile file;


}
