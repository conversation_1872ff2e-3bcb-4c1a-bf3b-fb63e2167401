package com.chinatelecom.gs.engine.core.model.controller.rpc;

import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.common.constants.Constants;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.core.model.enums.ModelTypeEnum;
import com.chinatelecom.gs.engine.core.model.sdkservice.ModelSdkService;
import com.chinatelecom.gs.engine.core.model.service.AiIntentService;
import com.chinatelecom.gs.engine.core.sdk.rpc.ModelServiceClient;
import com.chinatelecom.gs.engine.core.sdk.vo.ModelIntentParam;
import com.chinatelecom.gs.engine.core.sdk.vo.ModelPageListParam;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

/**
 * Author: tangxuetao130
 */
@Tag(name = "模型rpc接口")
@RestController
@RequestMapping(Constants.BASE_PREFIX + Constants.RPC_PREFIX + "/model")
@Slf4j
public class ModelSdkController implements ModelServiceClient {

    @Autowired
    private ModelSdkService modelSdkService;

    @Autowired
    private AiIntentService aiIntentService;

    @Operation(summary = "根据模型编码查询模型信息", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "根据模型编码查询模型信息", groupName = "模型管理")
    @GetMapping("/queryByModelCode")
    @AuditLog(businessType = "模型rpc接口", operType = "根据模型编码查询模型信息", operDesc = "根据模型编码查询模型信息", objId="#modelCode")
    public ModelPageListParam queryByModelCode(String modelCode) {
        return modelSdkService.queryByModelCode(modelCode);
    }

    @Operation(summary = "查询默认模型", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "查询所有默认模型", groupName = "模型管理")
    @GetMapping("/queryDefaultModel")
    @AuditLog(businessType = "模型rpc接口", operType = "查询默认模型", operDesc = "查询默认模型", objId="null")
    public List<ModelPageListParam> queryDefaultModel() {
        log.info("查询所有默认模型");
        return modelSdkService.queryDefaultModel();
    }
    /**
     * 查询默认模型
     *
     * @return ModelPageListParam
     */
    @GetMapping("/getDefaultModel")
    @PlatformRestApi(name = "查询默认模型", groupName = "模型管理")
    @AuditLog(businessType = "模型rpc接口", operType = "查询默认模型", operDesc = "查询默认模型", objId="#modeType")
    public ModelPageListParam getDefaultModel(@RequestParam("modeType") String modeType) {
        ModelTypeEnum modelTypeEnum = ModelTypeEnum.valueOf(modeType);
        if (Objects.isNull(modelTypeEnum)) {
            throw new BizException("模型类型不存在");
        }
        return modelSdkService.getDefaultModel(modeType);
    }

    @Operation(summary = "查询模型意图列表", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "查询模型意图列表", groupName = "模型管理")
    @GetMapping("/intent/list")
    @AuditLog(businessType = "模型rpc接口", operType = "查询模型意图列表", operDesc = "查询模型意图列表", objId="#code")
    public List<ModelIntentParam> queryIntentList(String code) {
        return modelSdkService.queryIntentList(code);
    }
}
