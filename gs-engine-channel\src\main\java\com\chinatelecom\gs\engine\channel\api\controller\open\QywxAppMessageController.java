package com.chinatelecom.gs.engine.channel.api.controller.open;


import cn.hutool.core.text.CharSequenceUtil;
import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.channel.service.dto.QywxRequestMessage;
import com.chinatelecom.gs.engine.channel.service.messagehandler.qywxapp.QywxAppMessageService;
import com.chinatelecom.gs.engine.common.context.Constants;
import com.chinatelecom.gs.engine.sdk.common.annotation.ApiType;
import com.chinatelecom.gs.engine.sdk.common.annotation.HideFromApiTypes;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.xml.sax.InputSource;

import jakarta.annotation.Resource;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.StringReader;

/**
 * <AUTHOR>
 * @date 2023/12/22 14:40
 * @description 企业微信应用程序消息对接接口
 */
@Slf4j
@Tag(name = "企业微信应用消息接口")
@RestController
@RequestMapping(value = Constants.API_PREFIX + "/channel/qywx")
public class QywxAppMessageController {
    @Resource
    private QywxAppMessageService appMessageService;

    @Operation(summary = "接收企业微信应用消息", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PostMapping(value = "/{channelId}/chat", produces = MediaType.APPLICATION_XML_VALUE)
    @AuditLog(businessType = "企业微信应用消息接口", operType = "接收企业微信应用消息", operDesc = "接收企业微信应用消息", objId="#channelId")
    @HideFromApiTypes(ApiType.OPENAPI)
    public String qywxChat(@PathVariable("channelId") String channelId,
                           @RequestParam("msg_signature") String msgSignature,
                           @RequestParam("timestamp") String timestamp,
                           @RequestParam("nonce") String nonce,
                           @RequestBody String qwMessage) {
        log.info("接收到消息: msgSignature:{}, timestamp:{}, nonce:{}, message:{}",
                msgSignature, timestamp, nonce, qwMessage);
        if (CharSequenceUtil.isNotBlank(qwMessage) && parseXml(qwMessage)) {
            throw new BizException("AD038", "qwMessage错误");
        }

        return appMessageService.chat(channelId, qwMessage, msgSignature, timestamp, nonce);
    }

    @Operation(summary = "接收企业微信应用消息", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PostMapping(value = "/{channelId}/asyncChat", produces = MediaType.APPLICATION_XML_VALUE)
    @AuditLog(businessType = "企业微信应用消息接口", operType = "异步接收企业微信应用消息", operDesc = "异步接收企业微信应用消息", objId="#channelId")
    @HideFromApiTypes(ApiType.OPENAPI)
    public String qywxAsyncChat(@PathVariable("channelId") String channelId,
                                @RequestParam("msg_signature") String msgSignature,
                                @RequestParam("timestamp") String timestamp,
                                @RequestParam("nonce") String nonce,
                                @RequestBody String qwMessage) {
        log.info("异步接收到消息: msgSignature:{}, timestamp:{}, nonce:{}, message:{}",
                msgSignature, timestamp, nonce, qwMessage);
        if (CharSequenceUtil.isNotBlank(qwMessage) && parseXml(qwMessage)) {
            throw new BizException("AD038", "qwMessage错误");
        }
        return appMessageService.asyncChat(QywxRequestMessage.builder()
                .channelId(channelId)
                .msgSignature(msgSignature)
                .timestamp(timestamp)
                .nonce(nonce)
                .qwMessage(qwMessage)
                .build());
    }

    @Operation(summary = "连接初始化", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @GetMapping("/{channelId}/chat")
    @AuditLog(businessType = "企业微信应用消息接口", operType = "连接初始化", operDesc = "连接初始化", objId="#channelId")
    @HideFromApiTypes(ApiType.OPENAPI)
    public String handShake(@PathVariable("channelId") String channelId,
                            @RequestParam("msg_signature") String msgSignature,
                            @RequestParam("timestamp") String timestamp,
                            @RequestParam("nonce") String nonce,
                            @RequestParam("echostr") String echoStr) {
        log.info("接收到握手消息 msgSignature:{}, timestamp:{}, nonce:{}, echoStr:{}", msgSignature, timestamp, nonce, echoStr);
        return appMessageService.handshake(channelId, msgSignature, timestamp, nonce, echoStr);
    }

    @Operation(summary = "连接初始化", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @GetMapping("/{channelId}/asyncChat")
    @AuditLog(businessType = "企业微信应用消息接口", operType = "连接初始化", operDesc = "连接初始化", objId="#channelId")
    @HideFromApiTypes(ApiType.OPENAPI)
    public String asyncHandShake(@PathVariable("channelId") String channelId,
                                 @RequestParam("msg_signature") String msgSignature,
                                 @RequestParam("timestamp") String timestamp,
                                 @RequestParam("nonce") String nonce,
                                 @RequestParam("echostr") String echoStr) {
        log.info("异步接收到握手消息 msgSignature:{}, timestamp:{}, nonce:{}, echoStr:{}", msgSignature, timestamp, nonce, echoStr);
        return appMessageService.handshake(channelId, msgSignature, timestamp, nonce, echoStr);
    }

    /**
     * 判断是否是xml
     *
     * @param xmlString String
     * @return boolean
     */
    private boolean parseXml(String xmlString) {
        try {
            //防止xxe
            boolean parseResult = xmlString.contains("<!DOCTYPE") || xmlString.contains("<!ENTITY");
            if (parseResult) {
                throw new BizException("", "xml非法");
            }
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            factory.setExpandEntityReferences(false);
            // 禁用DTD解析，防止XXE攻击
            // Apache Xerces
            factory.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true);
            // Disable external entities
            factory.setFeature("http://xml.org/sax/features/external-general-entities", false);
            // Disable parameter entities
            factory.setFeature("http://xml.org/sax/features/external-parameter-entities", false);
            DocumentBuilder builder = factory.newDocumentBuilder();
            InputSource is = new InputSource(new StringReader(xmlString));
            builder.parse(is);
            return false;
        } catch (Exception e) {
            return true;
        }
    }
}
