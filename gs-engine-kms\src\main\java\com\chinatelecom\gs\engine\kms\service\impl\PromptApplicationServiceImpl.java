package com.chinatelecom.gs.engine.kms.service.impl;

import com.alibaba.fastjson.JSON;
import com.chinatelecom.gs.engine.common.config.property.GsGlobalConfig;
import com.chinatelecom.gs.engine.common.utils.BizAssert;
import com.chinatelecom.gs.engine.common.utils.JsonFixer;
import com.chinatelecom.gs.engine.common.utils.TemplateUtil;
import com.chinatelecom.gs.engine.core.model.enums.ModelTypeEnum;
import com.chinatelecom.gs.engine.core.model.toolkit.StreamingChatLanguageModel;
import com.chinatelecom.gs.engine.core.model.toolkit.core.dto.*;
import com.chinatelecom.gs.engine.core.sdk.rpc.ModelServiceClient;
import com.chinatelecom.gs.engine.core.sdk.vo.ModelPageListParam;
import com.chinatelecom.gs.engine.core.sdk.vo.llm.WrapLLMMessage;
import com.chinatelecom.gs.engine.kms.common.KmsStreamHandler;
import com.chinatelecom.gs.engine.kms.common.SseEmitterUTF8;
import com.chinatelecom.gs.engine.kms.common.report.*;
import com.chinatelecom.gs.engine.kms.dto.PromptTemplateDTO;
import com.chinatelecom.gs.engine.kms.repository.PromptTemplateRepository;
import com.chinatelecom.gs.engine.kms.rpc.extract.vo.FaqInfoVO;
import com.chinatelecom.gs.engine.kms.sdk.enums.DefaultPromptCodeType;
import com.chinatelecom.gs.engine.kms.sdk.enums.ResponseType;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.chunk.ChunkDataVO;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.report.ReportOutlineQueryParam;
import com.chinatelecom.gs.engine.kms.sdk.vo.prompt.PromptRequest;
import com.chinatelecom.gs.engine.kms.sdk.vo.prompt.SimilarRequest;
import com.chinatelecom.gs.engine.kms.sdk.vo.search.BaseItem;
import com.chinatelecom.gs.engine.kms.service.ModelAppService;
import com.chinatelecom.gs.engine.kms.service.PromptApplicationService;
import com.chinatelecom.gs.engine.kms.util.Utils;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import com.google.common.collect.ImmutableMap;
import freemarker.template.Template;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import jakarta.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.chinatelecom.gs.engine.common.constants.Constants.PROMPT_PARAM_CONTENT;
import static com.chinatelecom.gs.engine.common.constants.Constants.PROMPT_PARAM_LAST_CONTENT;

@Service
@Slf4j
public class PromptApplicationServiceImpl implements PromptApplicationService {

    @Resource
    private StreamingChatLanguageModel streamingChatLanguageModel;
    @Resource
    private PromptTemplateRepository promptTemplateRepository;
    @Resource
    private ModelAppService modelAppService;

    @Resource
    private GsGlobalConfig gsGlobalConfig;

    @Resource
    protected ModelServiceClient remoteServiceClient;


    @Resource
    @Qualifier("extractExecutorService")
    private ExecutorService extractExecutorService;

    @Resource
    @Qualifier("reportSseThreadPool")
    private ExecutorService reportSseThreadPool;

    @Resource
    @Qualifier("defaultPoolExecutor")
    private ExecutorService defaultPoolExecutor;


    @Override
    public SseEmitter modelRequest(PromptRequest promptRequest) {
        SseEmitterUTF8 sseEmitter = new SseEmitterUTF8(30 * 60 * 1000L);
        return modelRequest(promptRequest, sseEmitter);
    }

    @Override
    public SseEmitter modelRequest(PromptRequest promptRequest, SseEmitter sseEmitter) {
        LLMRequest<WrapLLMMessage> llmRequest = processMessRequest(promptRequest);
        KmsStreamHandler kmsStreamHandler = new KmsStreamHandler(sseEmitter);
        this.streamingChatLanguageModel.generateMessage(llmRequest, kmsStreamHandler);
        return sseEmitter;
    }

    @Override
    public Response<String> syncModelRequest(PromptRequest promptRequest) {
        LLMRequest<String> llmRequest = processRequest(promptRequest);
        return streamingChatLanguageModel.syncGenerate(llmRequest);
    }

    @Override
    public List<FaqInfoVO> extractFaqInfo(List<ChunkDataVO> faqContentList) {
        List<FaqInfoVO> faqList = new ArrayList<>();
        CountDownLatch countDownLatch = new CountDownLatch(faqContentList.size());

        try {
        AtomicInteger size = new AtomicInteger();
            for (ChunkDataVO faqContent : faqContentList) {

                    extractExecutorService.execute(() -> {
                        try {
                            faqList.addAll(genFaq(faqContent));
                        } finally {
                            countDownLatch.countDown();
                        }
                    });

            }
            countDownLatch.await();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("FAQ提取线程被中断", e);
            throw new BizException("BA001",new String[]{"FAQ提取过程被中断"}, "FAQ提取过程被中断");
        } catch (Exception e) {
            log.error("FAQ提取过程中发生异常", e);
            throw new BizException("BA001",new String[]{"FAQ提取失败"},"FAQ提取失败");
        }

        // 去重并返回结果
        return faqList.stream()
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public List<FaqInfoVO> extractFaqInfoSingle(ChunkDataVO faqContent) {
        return extractFaqInfoSingle(faqContent, null);
    }

    @Override
    public List<FaqInfoVO> extractFaqInfoSingle(ChunkDataVO faqContent, String modelCode) {
        List<FaqInfoVO> faqList = new ArrayList<>();
        try {
            faqList.addAll(genFaq(faqContent, modelCode));
        } catch (Exception e) {
            if (e instanceof BizException exception && "C0005".equals(((exception.getCode())))) {
                throw new BizException("C0005", e.getMessage());
            }
            log.error("FAQ提取过程中发生异常", e);
        }
        return faqList.stream()
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
    }

    public List<FaqInfoVO> genFaq(String faqContent) {
        ChunkDataVO chunkData = new ChunkDataVO();
        chunkData.setFileContent(faqContent);
        return this.genFaq(chunkData);
    }


    @Retryable(backoff = @Backoff(delay = 2000, multiplier = 1.5), maxAttempts = 3)
    private List<FaqInfoVO> genFaq(ChunkDataVO faqContent) {
        if (faqContent == null || StringUtils.isBlank(faqContent.getFileContent())) {
            log.warn("FAQ内容为空，无法进行提取");
            return Collections.emptyList();
        }

        try {
            // 构建请求
            PromptRequest req = buildFaqPromptRequest(faqContent.getFileContent());
            // 调用模型获取响应
            Response<String> response = syncModelRequest(req);
            // 解析响应内容
            return parseFaqLlmResponse(response, faqContent.getSource());
        } catch (BizException e) {
            log.error("FAQ提取过程发生业务异常: {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("FAQ提取过程发生未知异常", e);
            throw new BizException("BA001", new String[]{"FAQ提取失败"}, "FAQ提取过程发生未知异常");
        }
    }

    private List<FaqInfoVO> genFaq(ChunkDataVO faqContent, String modelCode) {
        if (faqContent == null || StringUtils.isBlank(faqContent.getFileContent())) {
            log.warn("FAQ内容为空，无法进行提取");
            return Collections.emptyList();
        }
        try {
            PromptRequest req = buildFaqPromptRequest(faqContent.getFileContent(), modelCode);
            Response<String> response = syncModelRequest(req);
            return parseFaqLlmResponse(response, faqContent.getSource());
        } catch (BizException e) {
            log.error("FAQ提取过程发生业务异常: {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("FAQ提取过程发生未知异常", e);
            throw new BizException("BA001", new String[]{"FAQ提取失败"}, "FAQ提取过程发生未知异常");
        }
    }

    private PromptRequest buildFaqPromptRequest(String content, String modelCode) {
        PromptRequest req = PromptRequest.builder()
                .code(DefaultPromptCodeType.FAQ_EXTRACT.name())
                .promptParam(ImmutableMap.of(PROMPT_PARAM_CONTENT, content))
                .build();
        if (modelCode != null && !modelCode.isEmpty()) {
            req.setModelCode(modelCode);
        } else {
            req.setModelCode(gsGlobalConfig.getKmsBizConfig().getFastLlmModelCode());
        }
        return req;
    }

    private PromptRequest buildFaqPromptRequest(String content) {
        PromptRequest req = PromptRequest.builder()
                .code(DefaultPromptCodeType.FAQ_EXTRACT.name())
                .promptParam(ImmutableMap.of(PROMPT_PARAM_CONTENT, content))
                .build();
        req.setModelCode(gsGlobalConfig.getKmsBizConfig().getFastLlmModelCode());
        return req;
    }


    private List<FaqInfoVO> parseFaqLlmResponse(Response<String> response, BaseItem.Source source) {
        String content = response.content();
        if (StringUtils.isBlank(content)) {
            return Collections.emptyList();
        }

        try {
            List<FaqInfoVO> faqList = JsonFixer.parseObjectArray(content, FaqInfoVO.class);
            return processFaqList(faqList, source);
        } catch (Exception e) {
            log.error("解析大模型返回的问答对失败: {}", content, e);
            throw new BizException("BA003", new String[]{"大模型返回问答对不是标准json格式"}, "解析大模型返回的问答对失败");
        }
    }


    private List<FaqInfoVO> processFaqList(List<FaqInfoVO> faqList, BaseItem.Source source) {
        if (CollectionUtils.isEmpty(faqList)) {
            return Collections.emptyList();
        }
        return faqList.stream()
                .filter(Objects::nonNull)
                .peek(faq -> {
                    faq.setSource(source);
                    // 去除问题和答案中的序号前缀
                    faq.setQuestion(removeNumberPrefix(faq.getQuestion()));
                })
                .collect(Collectors.toList());
    }


    @Override
    public List<String> genSimilar(String question) {
        SimilarRequest similarRequest = new SimilarRequest();
        similarRequest.setQuestion(question);
        return this.genSimilar(similarRequest);

    }

    @Override
    public List<String> genSimilar(SimilarRequest similarRequest) {
        String question = similarRequest.getQuestion();
        Set<String> result = new HashSet<>(); // 使用 Set 避免重复
        String promptType = similarRequest.getPromptType();
        PromptRequest req;

        // 根据 promptType 构建 PromptRequest，避免重复代码
        if (StringUtils.isEmpty(promptType) || DefaultPromptCodeType.FAQ_SIMILAR.name().equals(promptType)) {
            req = PromptRequest.builder().code(DefaultPromptCodeType.FAQ_SIMILAR.name())
                    .promptParam(ImmutableMap.of(PROMPT_PARAM_CONTENT, question)).build();
        } else if (DefaultPromptCodeType.INTENT_SIMILAR.name().equals(promptType)) {
            req = PromptRequest.builder().code(DefaultPromptCodeType.INTENT_SIMILAR.name())
                    .promptParam(ImmutableMap.of(PROMPT_PARAM_CONTENT, question)).build();
        } else {
            // 如果 promptType 不合法，可以抛出异常或者返回空列表，具体看业务需求
            log.warn("Invalid promptType: {}", promptType);
            return Collections.emptyList();
        }
        req.setModelCode(gsGlobalConfig.getKmsBizConfig().getFastLlmModelCode());

        LLMRequest<String> llmRequest = processRequest(req);

        Response<String> response = streamingChatLanguageModel.syncGenerate(llmRequest);

        if (response == null) {
            throw new BizException("BA004", new String[]{"模型请求失败"}, "模型请求失败");
        }

        log.debug("【llm】同步请求返回结果 {}", JSON.toJSONString(response));

        if (StringUtils.isNotBlank(response.content())) {
            String content = response.content();
            List<String> similar = parseSimilar(content);
            similar.remove(question);
            result.addAll(similar);
        }

        // 根据 size 进行截取
        int size = similarRequest.getSize();
        if (size > 0) {
            List<String> list = new ArrayList<>(result);
            return list.subList(0, Math.min(result.size(), size));
        }

        return new ArrayList<>(result); // 返回结果
    }


    private static ExternalModelInfo buildLlmModelInfo(ModelPageListParam modelPage) {
        ExternalModelInfo llmModelInfo = new ExternalModelInfo();
        llmModelInfo.setModelName(modelPage.getModelCallName());
        llmModelInfo.setProvider(ModelProviderEnum.from(modelPage.getModelProvider()));
        llmModelInfo.setExtraConfig(modelPage.getExternalModelConfig());
        llmModelInfo.setModelUrl(modelPage.getExternalModelUrl());
        llmModelInfo.setModelSecret(modelPage.getModelSecret());
        llmModelInfo.setModelApi(modelPage.getApiKey());

        if (Objects.nonNull(modelPage.getExtraDataVO())) {
            llmModelInfo.setMaxToken(modelPage.getExtraDataVO().getMaxInputToken());
            llmModelInfo.setTransformerType(modelPage.getExtraDataVO().getTransformerType());
            llmModelInfo.setNativeCall(modelPage.getExtraDataVO().getNativeCall());
            llmModelInfo.setNativeCallUrl(modelPage.getExtraDataVO().getNativeCallUrl());
        }
        return llmModelInfo;
    }


    /**
     * 解析相似问大模型结果
     *
     * @param content
     * @return
     */
    private List<String> parseSimilar(String content) {
        if (StringUtils.isBlank(content)) {
            return new ArrayList<>();
        }

        // 首先尝试按换行符分割
        String[] lines = StringUtils.splitPreserveAllTokens(content, "\n");

        // 如果只有一行且包含多个空格，则按空格分割
        if (lines.length == 1 && StringUtils.countMatches(lines[0], " ") > 2) {
            lines = lines[0].split("\\s+"); // 使用正则表达式切分多个空格
        }

        // 统一处理逻辑
        return Arrays.stream(lines)
                .map(String::trim) // 去除每行前后的空白字符
                .filter(StringUtils::isNotBlank) // 过滤掉空白行
                .map(PromptApplicationServiceImpl::removeNumberPrefix) // 去除序号前缀
                .map(s -> s.length() > 80 ? s.substring(0, 80) : s) // 截断超过80个字符的字符串
                .distinct() // 去重
                .collect(Collectors.toList());
    }
    public LLMRequest<WrapLLMMessage> processMessRequest(PromptRequest request) throws BizException {
        LLMRequest<WrapLLMMessage> llmRequest = new LLMRequest<>();

        // 获取模型
        ExternalModelInfo llmModelInfo = buildModelInfo(request);

        llmRequest.setLlmModelInfo(llmModelInfo);

        String prompt = request.getPrompt();
        String code = request.getCode();
        PromptTemplateDTO promptTemplateDTO = null;

        boolean isValidEnumValue = Arrays.stream(DefaultPromptCodeType.values())
                .anyMatch(type -> type.name().equals(code));

        if (isValidEnumValue) {
            promptTemplateDTO = promptTemplateRepository.selectByCode(request.getCode());
        } else {
            promptTemplateDTO = promptTemplateRepository.selectByCodeAndAppCode(request.getCode());
        }

        if (promptTemplateDTO == null) {
            throw new BizException("BA005", new String[]{"未找到Prompt模板"},"未找到Prompt模板");
        }
        if (StringUtils.isBlank(prompt)) {
            Template template = TemplateUtil.createTemplate(promptTemplateDTO.getContent());
            Map<String, Object> param = request.getPromptParam() == null ? ImmutableMap.of() : request.getPromptParam();
            prompt = TemplateUtil.apply(template, param);
        }

        WrapLLMMessage llmMessage = new WrapLLMMessage();
        llmMessage.setContent(prompt);
        llmMessage.setRole(MessageRoleEnum.user.getCode());
        llmRequest.setRequestMessage(llmMessage);
        llmRequest.setText(prompt);
        // 设置请求ID为随机UUID
        llmRequest.setRequestId(Utils.randomUUID());

        // 返回构建好的LLMRequest对象
        return llmRequest;
    }
    public LLMRequest<String> processRequest(PromptRequest request) throws BizException {
        LLMRequest<String> llmRequest = new LLMRequest<>();

        // 获取模型
        ExternalModelInfo llmModelInfo = buildModelInfo(request);

        llmRequest.setLlmModelInfo(llmModelInfo);

        String prompt = request.getPrompt();
        String code = request.getCode();
        PromptTemplateDTO promptTemplateDTO = null;

        boolean isValidEnumValue = Arrays.stream(DefaultPromptCodeType.values())
                .anyMatch(type -> type.name().equals(code));

        if (isValidEnumValue) {
            promptTemplateDTO = promptTemplateRepository.selectByCode(request.getCode());
        } else {
            promptTemplateDTO = promptTemplateRepository.selectByCodeAndAppCode(request.getCode());
        }

        if (promptTemplateDTO == null) {
            throw new BizException("BA005", new String[]{"未找到Prompt模板"},"未找到Prompt模板");
        }
        if (StringUtils.isBlank(prompt)) {
            Template template = TemplateUtil.createTemplate(promptTemplateDTO.getContent());
            Map<String, Object> param = request.getPromptParam() == null ? ImmutableMap.of() : request.getPromptParam();
            prompt = TemplateUtil.apply(template, param);
        }

        llmRequest.setText(prompt);
        // 设置请求ID为随机UUID
        llmRequest.setRequestId(Utils.randomUUID());

        // 返回构建好的LLMRequest对象
        return llmRequest;
    }

    @Override
    public SseEmitter reportOutline(ReportOutlineQueryParam param, PromptRequest outlineRequest) {
        BizAssert.notNull(outlineRequest, "A0003", "报告请求大纲数据不能为空");
        SseEmitterUTF8 sseEmitter = new SseEmitterUTF8(30 * 60 * 1000L);
        OutlineGenStreamHandler outlineGenStreamHandler = new OutlineGenStreamHandler(sseEmitter, param);
        outlineGenStreamHandler.setDefaultPoolExecutor(defaultPoolExecutor);
        LLMRequest<String> llmRequest = processRequest(outlineRequest);
        this.streamingChatLanguageModel.generateMessage(llmRequest, outlineGenStreamHandler);
        // 提交大纲成功写任务
        outlineGenStreamHandler.submitRewrite(param);
        return sseEmitter;
    }


    private ExternalModelInfo buildModelInfo(PromptRequest request) {
        String modelCode = request.getModelCode();
        ModelPageListParam modelPage = null;
        if (StringUtils.isNotBlank(modelCode)) {
            modelPage = modelAppService.queryByModelCode(modelCode);
            BizAssert.notNull(modelPage, "B0001", "模型信息不能为空");
            BizAssert.equals(modelPage.getModelType(), ModelTypeEnum.LLM.getCode(), "B0001", "指定模型类型错误，必须指定LLM类型模型");
        } else {
            modelPage = modelAppService.getDefaultModelParam(ModelTypeEnum.LLM);
        }
        BizAssert.notNull(modelPage, "B0001", "模型信息不能为空");
        // 构建基础请求信息
        ExternalModelInfo llmModelInfo = buildLlmModelInfo(modelPage);
        return llmModelInfo;
    }

    @Override
    public SseEmitter exceptionResponse(Exception exception) throws IOException {
        SseEmitterUTF8 sseEmitter = new SseEmitterUTF8(30 * 60 * 1000L);

        defaultPoolExecutor.submit(() -> {
            try {
                boolean safeIntercept = false;
                String msg = null;
                if (exception instanceof BizException bizException) {
                    String code = bizException.getCode();
                    if ("C0005".equals(code)) {
                        safeIntercept = true;
                    }
                    msg = bizException.getUserTip();
                } else {
                    msg = gsGlobalConfig.getRag().getResponseText().get(ResponseType.ERROR);
                }

                String error = null;
                if (safeIntercept) {
                    error = ReportMessage.ofCover(msg).toString();
                } else {
                    error = ReportMessage.ofError(msg).toString();
                }
                sseEmitter.send(error);

                String finish = ReportMessage.ofFinish(StringUtils.EMPTY).toString();
                sseEmitter.send(finish);

            } catch (IOException e) {
                log.error("发送消息异常", e);
            } finally {
                sseEmitter.complete();
            }
        });

        return sseEmitter;
    }

    @Override
    public List<FaqInfoVO> extractFaqPreciseInfo(String code) {
        return new ArrayList<>();
    }

    @Override
    public SseEmitter multipleReportRequest(List<MultipleReportRequest> reportRequests) {
        BizAssert.notEmpty(reportRequests, "A0003", "报告请求大纲数据不能为空");
        SseEmitterUTF8 sseEmitter = new SseEmitterUTF8(60 * 60 * 1000L);
        final MultipleSseStream sseStream = new MultipleSseStream(sseEmitter);
        final MultipleReportStreamHandler streamHandler = new MultipleReportStreamHandler(sseStream);
        Integer reportSseTimeout = gsGlobalConfig.getKmsBizConfig().getReportSseTimeout();
        // 提交请求任务
        try {
            reportSseThreadPool.submit(() -> {
                try {
                    log.info("开始处理正文编写");
                    for (int i = 0; i < reportRequests.size(); i++) {
                        MultipleReportRequest request = reportRequests.get(i);
                        MultipleReportRequest lastRequest = i > 0 ? reportRequests.get(i - 1) : null;

                        LLMRequest<String> llmRequest = processLlmRequest(request, lastRequest);

                        sseStream.setCurrentRequest(request);
                        sseStream.phasedStart();

                        // 处理每个请求
                        CountDownLatch countDownLatch = new CountDownLatch(1);
                        streamHandler.setLatch(countDownLatch);
                        this.streamingChatLanguageModel.generateMessage(llmRequest, streamHandler);
                        countDownLatch.await(10, TimeUnit.MINUTES);

                        sseStream.phasedCompletion();
                        // 已经终止
                        if (sseStream.isAborted()) {
                            break;
                        }
                    }
                } catch (BizException e) {
                    log.error("请求模型异常", e);
                    if (StringUtils.equals("C0005", e.getCode())) {
                        streamHandler.sendCoverMsg(e.getMessage());
                    } else {
                        streamHandler.sendMessage(ReportMessage.ofError(e.getUserTip()).toString());
                    }
                    sseStream.abort(e);
                } catch (Exception e) {
                    log.error("请求模型异常", e);
                    streamHandler.sendMessage(ReportMessage.ofError("请求模型异常").toString());
                    sseStream.abort(e);
                } finally {
                    if (!streamHandler.isSafeCheckFailed()) {
                        sseStream.finallyComplete();
                    }
                    sseStream.finished();
                }
            });

            // 提交推送任务
            reportSseThreadPool.submit(() -> {
                log.info("开始监听写作输出");
                sseStream.push(reportSseTimeout);
            });
        } catch (RejectedExecutionException e) {
            log.error("线程池已满", e);
            sseStream.abort(e);
            throw new BizException("AA079", "线程池已满");
        } catch (Exception e) {
            sseStream.abort(e);
        }
        log.info("任务提交完毕");

        return sseEmitter;
    }

    private LLMRequest<String> processLlmRequest(MultipleReportRequest request, MultipleReportRequest lastRequest) {
        PromptRequest promptRequest = request.getPromptRequest();
        // 设置上次生成内容
        if (lastRequest != null) {
            Map<String, Object> promptParam = promptRequest.getPromptParam();
            if (StringUtils.isNotBlank(lastRequest.getReportOutline().getContent())) {
                promptParam.put(PROMPT_PARAM_LAST_CONTENT, lastRequest.getReportOutline().getContent());
            }

        }
        return processRequest(promptRequest);
    }

    protected final class Constants {

        public static final int GENERATE_FAQ_RETRY_COUNT = 3;

        private Constants() {
            throw new IllegalStateException("Utility class");
        }

    }



    public static String removeNumberPrefix(String text) {
        if (text == null || text.isEmpty()) {
            return text;
        }
        // 匹配文本开头的数字+点+空格模式
        // 支持多种序号格式：
        // 1. 数字+点，如：1. 2.
        // 2. 数字+括号，如：1) 2）(1) （2）
        // 3. 数字+顿号/句号，如：1、2、
        // 4. 罗马数字+点/括号，如：I. II. I) II)
        return text.replaceAll("^\\s*(?:\\d+|[IVXivx]+)[\\.\\)\\）\\、\\u3001\\u3002\\uff09]\\s*", "");
    }
}
