update agent_default_config set config_value = '{"doc": {"docAccept": 0.2, "enableSortModel": true, "sortCode": "model_925301025288359936", "topn": 8}, "faq": {"enableLLMModel": true,"enableSortModel": true,"faqAccept": 0.9,"llmAccept": 0.5,"sortCode": "model_925301025288359936","topn": 3 },"retriever": 8,"searchStrategy": "MIX"}' where config_key = "agentConfig.kms.config";

update agent_default_config set config_value = '{"globalVarSwitch": false,"securityFenceSwitch": false,"securityFenceScript": "检测到您输入的内容可能不太合适哦~请换一种表达方式吧!感谢您的理解!","globalVarList": null,"noAnswerCount": 3,"noAnswerCountSwitch": true,"noAnswerCountType": "CONTINUE_TRIGGER","manuCount": 3,"manuCountSwitch": true,"manuCountType": "CONTINUE_TRIGGER","manuKeyWordList": ["转人工"],"manuScript": "您好，为了更好地帮助您，我将为您转接人工客服，请稍等片刻，感谢您的理解!"}' where config_key = "agentConfig.global.policy";
