package com.chinatelecom.gs.engine.task.handler;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.chinatelecom.gs.engine.common.utils.LlmUtil;
import com.chinatelecom.gs.engine.core.model.enums.ModelTypeEnum;
import com.chinatelecom.gs.engine.core.model.toolkit.StreamingChatLanguageModel;
import com.chinatelecom.gs.engine.core.model.toolkit.core.dto.LLMRequest;
import com.chinatelecom.gs.engine.core.model.toolkit.core.dto.ModelProviderEnum;
import com.chinatelecom.gs.engine.core.model.toolkit.core.dto.Response;
import com.chinatelecom.gs.engine.core.sdk.rpc.ModelServiceClient;
import com.chinatelecom.gs.engine.core.sdk.vo.ModelPageListParam;
import com.chinatelecom.gs.engine.kms.sdk.vo.prompt.PromptTemplateVO;
import com.chinatelecom.gs.engine.kms.service.PromptTemplateAppService;
import com.chinatelecom.gs.engine.task.config.WorkflowConfig;
import com.chinatelecom.gs.engine.task.handler.script.ScriptExecutor;
import com.chinatelecom.gs.engine.task.handler.script.ScriptHolder;
import com.chinatelecom.gs.engine.task.handler.script.ScriptRequest;
import com.chinatelecom.gs.engine.task.sdk.entity.Param;
import com.chinatelecom.gs.workflow.core.workflow.core.context.DagContext;
import com.chinatelecom.gs.workflow.core.workflow.core.enums.EnvTypeEnum;
import com.chinatelecom.gs.workflow.core.workflow.core.enums.NodeTypeEnum;
import com.chinatelecom.gs.workflow.core.workflow.core.enums.ScriptTypeEnum;
import com.chinatelecom.gs.workflow.core.workflow.core.handler.AbstractNodeHandler;
import com.chinatelecom.gs.workflow.core.workflow.core.model.node.DagNode;
import com.chinatelecom.gs.workflow.core.workflow.core.model.result.NodeResult;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.util.*;

/**
 * 代码节点处理器
 *
 * @USER: pengmc1
 * @DATE: 2024/8/21 16:51
 */
@Slf4j
@Component
public class ScriptNodeHandler extends AbstractNodeHandler<Map<String, Object>> {

    @Resource
    private ScriptHolder scriptHolder;

    @Resource
    private StreamingChatLanguageModel streamingChatLanguageModel;

    @Resource
    private ModelServiceClient remoteServiceClient;

    @Resource
    private WorkflowConfig workflowConfig;

    @Resource
    private PromptTemplateAppService promptTemplateAppService;

    private static final String CHECK_SCRIPT_PROMPT_TEMPLATE = "CHECK_SCRIPT_PROMPT_TEMPLATE";

    @Value("${workflow.script.enableCheck:false}")
    private Boolean enableLlmCheck;


    /**
     * 处理的节点类型
     *
     * @return
     */
    @Override
    public NodeTypeEnum nodeType() {
        return NodeTypeEnum.SCRIPT;
    }

    /**
     * 执行任务
     *
     * @param param   输入参数
     * @param node    节点数据
     * @param context 上下文
     * @return
     * @throws Exception
     */
    @Override
    public NodeResult<Map<String, Object>> doExecute(Map<String, Object> param, DagNode node, DagContext context) throws Exception {
        BizScriptParam bizParam = node.getBizParam(BizScriptParam.class);
        ScriptParam scriptParam = bizParam.getScriptParam();
        ScriptTypeEnum scriptType = ScriptTypeEnum.getByCode(scriptParam.getScriptType());
        Object result = null;
        try {
            ScriptRequest scriptRequest = checkScript(param, scriptType, scriptParam, context);
            ScriptExecutor scriptExecutor = scriptHolder.getScriptExecutor(scriptType);
            result = scriptExecutor.execute(scriptRequest);
            log.info("执行结果为：" + JSON.toJSONString(result));
            // convert 执行结果
            return convertResult(node, result);
        } catch (Exception e) {
            log.warn("执行脚本失败", e);
            return NodeResult.fail("执行脚本失败" + e.getMessage());
        }
    }


    /**
     * 获取大模型prompt模板
     *
     * @return String
     */
    private String getPromptTemplate() {
        // 优先使用nacos，如果nacos没有配置，则使用数据库配置
        String intentChoosePromptTemplate = workflowConfig.getCheckScriptPromptTemplate();
        PromptTemplateVO promptTemplate = promptTemplateAppService.get(CHECK_SCRIPT_PROMPT_TEMPLATE);
        if (promptTemplate == null && CharSequenceUtil.isBlank(intentChoosePromptTemplate)) {
            // nacos和数据库都不存在prompt配置
            throw new BizException("BA005", "未找到大模型prompt配置");
        }
        if (Objects.nonNull(promptTemplate) && CharSequenceUtil.isNotBlank(promptTemplate.getContent())) {
            // prompt表中存在prompt配置
            intentChoosePromptTemplate = promptTemplate.getContent();
        }
        return intentChoosePromptTemplate;
    }

    /**
     * 将执行结果转换为NodeResult
     *
     * @param node   DagNode
     * @param result Object
     * @return NodeResult<Map < String, Object>>
     */
    private NodeResult<Map<String, Object>> convertResult(DagNode node, Object result) {
        if (result == null) {
            return NodeResult.success(Collections.emptyMap(), result);
        }
        String resultStr = JSON.toJSONString(result);
        Map<String, Object> outputMap = new HashMap<>();
        List<Param> outputParams = node.getOutputParams();
        if (CollectionUtils.isEmpty(outputParams)) {
            return NodeResult.success(outputMap, resultStr);
        }
        if (JSON.isValidArray(resultStr)) {
            //数组类型
            outputMap.put(outputParams.get(0).getName(), result);
        } else if (JSON.isValidObject(resultStr)) {
            //JSON对象类型
            JSONObject jsonObject = JSON.parseObject(resultStr);
            for (Param outputParam : outputParams) {
                outputMap.put(outputParam.getName(), jsonObject.get(outputParam.getName()));
            }
        } else {
            //基础类型
            outputMap.put(outputParams.get(0).getName(), result);
        }
        return NodeResult.success(outputMap, resultStr);
    }

    /**
     * 校验脚本参数
     *
     * @param param       Map<String, Object> 脚本需要的参数
     * @param scriptType  ScriptTypeEnum 脚本类型
     * @param scriptParam ScriptParam
     * @return ScriptRequest
     */
    private ScriptRequest checkScript(Map<String, Object> param, ScriptTypeEnum scriptType, ScriptParam scriptParam, DagContext context) {
        ScriptRequest scriptRequest = new ScriptRequest();
        if (scriptType == null) {
            throw new BizException("BA006", "脚本类型不存在");
        }
        // 校验代码时候存在安全漏洞
        ModelPageListParam modelParam = null;
        // 优先使用nacos配置
        if (CharSequenceUtil.isNotBlank(workflowConfig.getCheckScriptModelCode())) {
            modelParam = remoteServiceClient.queryByModelCode(workflowConfig.getCheckScriptModelCode());
            if (Objects.isNull(modelParam)) {
                // 如果nacos配置模型不存在，则使用默认模型
                modelParam = remoteServiceClient.getDefaultModel(ModelTypeEnum.LLM.getCode());
            }
        } else {
            // 查询默认大模型
            modelParam = remoteServiceClient.getDefaultModel(ModelTypeEnum.LLM.getCode());
        }

        if (Objects.isNull(modelParam)) {
            // 数据库和nacos模型都不存在
            throw new BizException("BA007", "代码检测无模型可用");
        }
        if (Boolean.TRUE.equals(enableLlmCheck)) {
            CheckCodeResult checkCodeResult = llmCheckScript(scriptParam, modelParam);
            if (checkCodeResult.getCheckResult()) {
                throw new BizException("BA008", "代码存在安全问题：" + checkCodeResult.getVulnDetails());
            }
        }
        EnvTypeEnum env = context.getEnv();
        scriptRequest.setIsTest(EnvTypeEnum.TEST.equals(env));
        scriptRequest.setScriptType(scriptType);
        scriptRequest.setScriptCode(scriptParam.getScriptCode());
        scriptRequest.setScriptParam(param);
        return scriptRequest;
    }

    /**
     * 大模型检测代码是否存在安全问题
     *
     * @param scriptParam ScriptParam
     * @param modelParam  ModelPageListParam
     * @return CheckCodeResult
     */
    private CheckCodeResult llmCheckScript(ScriptParam scriptParam, ModelPageListParam modelParam) {
        HashMap<String, Object> promptMap = new HashMap<>();
        promptMap.put("scriptType", scriptParam.getScriptType());
        promptMap.put("script", LlmUtil.removeComments(scriptParam.getScriptCode(), scriptParam.getScriptType()));
        String checkScriptPromptTemplate = getPromptTemplate();
        String llmPrompt = LlmUtil.replaceVariables(checkScriptPromptTemplate, promptMap);
        LLMRequest llmRequest = buildRequest(modelParam, llmPrompt);
        Response<String> llmResponse = streamingChatLanguageModel.syncGenerate(llmRequest);
        String content = llmResponse.content();
        content = LlmUtil.toJson(content);
        CheckCodeResult checkCodeResult = JSON.parseObject(content, CheckCodeResult.class);
        return checkCodeResult;
    }

    /**
     * 构建大模型请求参数
     *
     * @param modelConfig    ModelPageListParam
     * @param promptTemplate String
     * @return LLMRequest
     */
    private LLMRequest buildRequest(ModelPageListParam modelConfig, String promptTemplate) {
        LLMRequest llmRequest = new LLMRequest();
        llmRequest.getLlmModelInfo().setProvider(ModelProviderEnum.from(modelConfig.getModelProvider()));
        llmRequest.getLlmModelInfo().setModelApi(modelConfig.getApiKey());
        llmRequest.getLlmModelInfo().setModelSecret(modelConfig.getModelSecret());
        llmRequest.getLlmModelInfo().setModelName(modelConfig.getModelCallName());
        llmRequest.getLlmModelInfo().setExtraConfig(modelConfig.getExternalModelConfig());
        llmRequest.getLlmModelInfo().setModelUrl(modelConfig.getExternalModelUrl());
        llmRequest.setStreaming(1);
        llmRequest.setTemperature(0.5);
        llmRequest.setText(promptTemplate);
        llmRequest.setUserId(IdUtil.fastSimpleUUID());
        if(Objects.nonNull(modelConfig.getExtraDataVO())){
            llmRequest.getLlmModelInfo().setMaxToken(modelConfig.getExtraDataVO().getMaxInputToken());
            llmRequest.getLlmModelInfo().setTransformerType(modelConfig.getExtraDataVO().getTransformerType());
            llmRequest.getLlmModelInfo().setNativeCall(modelConfig.getExtraDataVO().getNativeCall());
            llmRequest.getLlmModelInfo().setNativeCallUrl(modelConfig.getExtraDataVO().getNativeCallUrl());
        }
        return llmRequest;
    }


    @Getter
    @Setter
    public static class BizScriptParam {
        /**
         * 代码节点参数
         */
        private ScriptParam scriptParam;
    }

    @Getter
    @Setter
    public static class ScriptParam {
        /**
         * 类型
         */
        private String scriptType;
        /**
         * 脚本
         */
        private String scriptCode;
    }

    @Getter
    @Setter
    public static class CheckCodeResult {
        /**
         * 检查时候成功
         */
        private Boolean checkResult;
        /**
         * 安全漏洞描述
         */
        private String vulnDetails;
    }
}
