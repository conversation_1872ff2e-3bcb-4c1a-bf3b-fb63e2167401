package com.chinatelecom.gs.engine.core.manager.controller;

import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.cloud.platform.client.rpc.PlatformUser;
import com.chinatelecom.cloud.platform.client.util.SsoUtil;
import com.chinatelecom.gs.engine.common.constants.Apis;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.core.manager.service.LogEsService;
import com.chinatelecom.gs.engine.core.manager.vo.config.base.ESLogDelParam;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.util.Objects;


/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024年11月28日
 */

@RestController
@Slf4j
@Tag(name = "会话日志埋点管理接口")
@RequestMapping(Apis.BASE_PREFIX + Apis.WEB_API + Apis.CONFIG)
public class EsLogController {

    @Resource
    private LogEsService logEsService;

    @Operation(summary = "删除es埋点会话日志", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "删除es埋点日志", groupName = "会话日志埋点管理")
    @PostMapping("/es/log/delete")
    @AuditLog(businessType = "会话日志埋点管理接口", operType = "删除es埋点日志", operDesc = "删除es埋点日志", objId="null")
    public Result<Boolean> delete(@RequestBody ESLogDelParam param) {
        PlatformUser user = SsoUtil.get();
        if (Boolean.TRUE.equals(!user.getIsSuperAdmin() && Objects.nonNull(user.getIsManager())) && user.getIsManager().equals(0)) {
            throw new BizException("AA090", "非管理员用户不能删除日志");
        }
        return Result.success(logEsService.deleteLogsBySendTimeRange(param.getStartTime(), param.getEndTime()));
    }

//    @Operation( summary = "通过traceId在es查埋点日志", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})} )
//    @PlatformRestApi(name = "通过traceId在es查埋点日志", groupName = "会话日志埋点管理")
//    @GetMapping("/es/search/traceId/{traceId}")
//    public Result<List<LogVO>> searchByTraceId(@PathVariable("traceId") String traceId) {
//        return Result.success(logEsService.searchLogsByTraceId(traceId));
//    }
//
//    @Operation( summary = "通过messageId在es查埋点日志", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})} )
//    @PlatformRestApi(name = "通过messageId在es查埋点日志", groupName = "会话日志埋点管理")
//    @GetMapping("/es/search/messageId/{messageId}")
//    public Result<List<LogVO>> searchByMessageId(@PathVariable("messageId") String messageId) {
//        return Result.success(logEsService.searchLogsByMessageId(messageId));
//    }
}
