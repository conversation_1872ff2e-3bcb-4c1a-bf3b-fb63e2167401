package com.chinatelecom.gs.engine.core.corekit.domain.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Schema(description = "敏感词库请求体")
@Data
public class SensitiveBaseRequest {
    @Schema(description = "敏感词库编码")
    private String code;

    @Schema(description = "敏感词库名称")
    @NotBlank(message = "敏感词库名称不能为空")
    private String name;
}
