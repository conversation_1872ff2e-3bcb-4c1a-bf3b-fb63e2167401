package com.chinatelecom.gs.engine.channel.manage;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinatelecom.gs.engine.channel.api.controller.open.PasswordGenerator;
import com.chinatelecom.gs.engine.channel.api.vo.WebLinkSecretVO;
import com.chinatelecom.gs.engine.channel.common.enums.ChannelTypeEnum;
import com.chinatelecom.gs.engine.channel.common.enums.ConfigKeyEnums;
import com.chinatelecom.gs.engine.channel.dao.po.ChannelApiSecretPO;
import com.chinatelecom.gs.engine.channel.dao.po.ChannelConfigPO;
import com.chinatelecom.gs.engine.channel.dao.repository.ChannelApiSecretRepository;
import com.chinatelecom.gs.engine.channel.dao.repository.ChannelConfigRepository;
import com.chinatelecom.gs.engine.channel.dao.repository.ChannelInfoRepository;
import com.chinatelecom.gs.engine.channel.service.dto.AccessTokenData;
import com.chinatelecom.gs.engine.channel.service.dto.ChannelApiSecretDTO;
import com.chinatelecom.gs.engine.channel.service.dto.ChannelInfoDTO;
import com.chinatelecom.gs.engine.channel.service.dto.WebLinkConfigDTO;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.context.RequestInfo;
import com.chinatelecom.gs.engine.common.utils.JsonUtils;
import com.chinatelecom.gs.engine.core.corekit.common.core.RedisKey;
import com.chinatelecom.gs.engine.robot.sdk.config.enums.ApiSecretType;
import com.chinatelelcom.gs.engine.sdk.common.enums.AppSourceType;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

public class WebLinkConfigServiceTest {

    @InjectMocks
    private WebLinkConfigService webLinkConfigService;

    @Mock
    private ChannelConfigRepository configRepository;

    @Mock
    private ChannelInfoRepository channelInfoRepository;

    @Mock
    private ChannelConfigService channelConfigService;

    @Mock
    private ChannelApiSecretRepository channelApiSecretRepository;

    @Mock
    private RedisKey redisKey;

    @Mock
    private RedissonClient redissonClient;

    @Mock
    private ChannelSecretManagerService channelSecretManagerService;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        RequestInfo requestInfo = new RequestInfo();
        requestInfo.setAppCode("appCode");
        requestInfo.setUserId("userId");
        requestInfo.setUserName("userName");
        requestInfo.setTenantId("tenantId");
        RequestContext.set(requestInfo);
    }

    @AfterEach
    public void tearDown() {
        RequestContext.remove();
    }

    // ------------------ addConfig 测试 ------------------

    @Test
    public void testAddConfig_ChannelNotFound_ThrowsException() {
        String channelId = "123";
        String agentCode = "A001";

        when(channelInfoRepository.getChannelInfo(channelId, agentCode)).thenReturn(null);

        BizException exception = assertThrows(BizException.class, () ->
                webLinkConfigService.addConfig(channelId, agentCode, new WebLinkConfigDTO()));
        assertEquals("A0049", exception.getCode());
    }

    @Test
    public void testAddConfig_ConfigExists_UpdatesSuccessfully() {
        String channelId = "123";
        String agentCode = "A001";
        WebLinkConfigDTO dto = new WebLinkConfigDTO();
        dto.setSecret("abc123");
        dto.setSecretOn(true);

        ChannelInfoDTO info = new ChannelInfoDTO();
        info.setEnable(true);
        when(channelInfoRepository.getChannelInfo(channelId, agentCode)).thenReturn(info);

        ChannelConfigPO existing = new ChannelConfigPO();
        existing.setId(1L);
        when(configRepository.getOne(any(LambdaQueryWrapper.class))).thenReturn(existing);
        when(configRepository.updateById(any(ChannelConfigPO.class))).thenReturn(true);

        Boolean result = webLinkConfigService.addConfig(channelId, agentCode, dto);
        assertTrue(result);
    }

    // ------------------ getWebLinkConfig 测试 ------------------

    @Test
    public void testGetWebLinkConfig_ConfigNotFound_ReturnsNull() {
        String channelId = "123";
        String agentCode = "A001";

        ChannelInfoDTO info = new ChannelInfoDTO();
        info.setEnable(true);
        when(channelInfoRepository.getChannelInfo(channelId, agentCode)).thenReturn(info);

        when(configRepository.getOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        WebLinkConfigDTO result = webLinkConfigService.getWebLinkConfig(channelId, agentCode);
        assertNull(result);
    }

    // ------------------ regenerateWebLinkSecret 测试 ------------------

    @Test
    public void testRegenerateWebLinkSecret_ConfigNotFound_ThrowsException() {
        String channelId = "123";
        String agentCode = "A001";
        ChannelInfoDTO info = new ChannelInfoDTO();
        info.setEnable(true);
        when(channelInfoRepository.getChannelInfo(channelId, agentCode)).thenReturn(info);
        when(webLinkConfigService.getWebLinkConfig(channelId, agentCode)).thenReturn(null);

        BizException exception = assertThrows(BizException.class, () ->
                webLinkConfigService.regenerateWebLinkSecret(channelId, agentCode));
        assertEquals("A0049", exception.getCode());
    }

    // ------------------ setWebLinkSecretOn 测试 ------------------

    @Test
    public void testSetWebLinkSecretOn_SuccessfullyUpdated() {
        String channelId = "123";
        String agentCode = "A001";

        WebLinkConfigDTO dto = new WebLinkConfigDTO();
        dto.setSecret("abc123");
        ChannelInfoDTO info = new ChannelInfoDTO();
        info.setEnable(true);

        ChannelConfigPO channelConfigPO = new ChannelConfigPO();
        channelConfigPO.setChannelId("123");
        channelConfigPO.setConfigKey("123");
        channelConfigPO.setConfigValue("""
                {
                  "id": 0,
                  "tenantId": "123",
                  "yn": 0,
                  "createId": "123",
                  "createName": "123",
                  "updateId": "123",
                  "updateName": "123",
                  "url": "123",
                  "secret": "123",
                  "secretOn": true
                }\
                """);

        when(configRepository.getOne(any())).thenReturn(channelConfigPO);
        when(channelInfoRepository.getChannelInfo(channelId, agentCode)).thenReturn(info);
        ChannelApiSecretPO channelApiSecretPO = new ChannelApiSecretPO();
        channelApiSecretPO.setAppId("123");
        channelApiSecretPO.setChannelId("123");
        channelApiSecretPO.setSecretType(ApiSecretType.API);
        channelApiSecretPO.setSecretId("123");
        channelApiSecretPO.setName("123");
        channelApiSecretPO.setSecret("123");
        channelApiSecretPO.setAppCode("123");
        channelApiSecretPO.setSourceSystem(AppSourceType.AIQC);
        channelApiSecretPO.setTenantId("123");

        when(channelApiSecretRepository.getOne(any())).thenReturn(channelApiSecretPO);
        when(configRepository.updateById(any(ChannelConfigPO.class))).thenReturn(true);

        WebLinkConfigDTO result = webLinkConfigService.setWebLinkSecretOn(channelId, agentCode, true);
        assertNotNull(result);
        assertTrue(result.getSecretOn());
    }

    // ------------------ generateWebLinkConfig 测试 ------------------

    @Test
    public void testGenerateWebLinkConfig_NewSecretGenerated() {
        String channelId = "123";
        String channelName = "testChannel";
        String agentCode = "A001";

        // 模拟 getSecretWithChannelId 返回一个 DTO
        when(channelSecretManagerService.getSecretWithChannelId(channelId)).thenReturn(new ChannelApiSecretDTO());

        // 修复点：将 doNothing 改为 when(...).thenReturn(...)
        when(channelSecretManagerService.removeSecretWithChannelId(channelId)).thenReturn(true);

        // 模拟 RedisKey 的行为
        when(redisKey.getAccessTokenKeyByUserId(anyString())).thenReturn("mocked:token:key");
        ChannelInfoDTO channelInfoDTO = new ChannelInfoDTO();
        channelInfoDTO.setChannelName("123");
        channelInfoDTO.setChannelType(ChannelTypeEnum.TEST_WINDOW);
        channelInfoDTO.setChannelId("123");
        channelInfoDTO.setAgentCode("123");
        channelInfoDTO.setConfigExist(true);
        channelInfoDTO.setEnable(true);

        when(channelInfoRepository.getChannelInfo(channelId, agentCode)).thenReturn(channelInfoDTO);

        // 调用被测方法
        webLinkConfigService.generateWebLinkConfig(channelName, channelId, agentCode);

        // 验证调用次数和参数
        verify(channelSecretManagerService, times(1)).create(eq(agentCode), eq(channelId), anyString(), eq(ApiSecretType.WEB_LINK));
        verify(configRepository, times(1)).save(any(ChannelConfigPO.class));
    }


    // ------------------ webLinkSecretGenerate 测试 ------------------

    @Test
    public void testWebLinkSecretGenerate_ReturnsValidPassword() {
        String password = PasswordGenerator.generateStrongPassword(12);
        assertNotNull(password);
    }

    // ------------------ getApiWithLink 测试 ------------------

    @Test
    public void testGetApiWithLink_SecretDisabled_ThrowsException() {
        String channelId = "123";
        WebLinkSecretVO vo = new WebLinkSecretVO();
        vo.setSecret("wrongPass");

        ChannelInfoDTO info = new ChannelInfoDTO();
        info.setEnable(true);

        WebLinkConfigDTO config = new WebLinkConfigDTO();
        config.setSecret("correctPass");
        config.setSecretOn(false); // 密码关闭

        when(channelInfoRepository.getChannelInfo(channelId, null)).thenReturn(info);
        when(channelConfigService.getChannelConfigValue(channelId, ConfigKeyEnums.WEB_LINK.getCode()))
                .thenReturn(JsonUtils.toJsonString(config));

        BizException exception = assertThrows(BizException.class, () ->
                webLinkConfigService.getApiWithLink(channelId, vo));
        assertEquals("A0056", exception.getCode());
    }

    // ------------------ generateAndStoreAccessToken 测试 ------------------

    @Test
    public void testGenerateAndStoreAccessToken_NewTokenGenerated() {
        AccessTokenData data = new AccessTokenData();
        data.setChannelId("123");
        String userId = "user123";

        // ✅ 模拟 redisKey 返回一个 key 字符串
        String expectedRedisKey = "access_token:user123";
        when(redisKey.getAccessTokenKeyByUserId(userId)).thenReturn(expectedRedisKey);

        // ✅ 模拟 redissonClient 返回一个 mock 的 RBucket
        RBucket<Object> bucket = mock(RBucket.class);
        when(redissonClient.getBucket(expectedRedisKey)).thenReturn(bucket);
        when(bucket.get()).thenReturn(null); // 表示没有旧token

        // 调用被测方法
        String token = webLinkConfigService.generateAndStoreAccessToken(data, userId);

        // 验证结果
        assertNotNull(token);
    }


    // ------------------ validateAndGetTokenData 测试 ------------------

    @Test
    public void testValidateAndGetTokenData_TokenMismatch_ReturnsNull() {
        String accessToken = "abc123";
        String userId = "user123";

        AccessTokenData storedData = new AccessTokenData();
        storedData.setAccessToken("stored_token");

        RBucket<Object> bucket = mock(RBucket.class);
        when(redissonClient.getBucket(anyString())).thenReturn(bucket);
        when(bucket.get()).thenReturn(storedData);

        AccessTokenData result = webLinkConfigService.validateAndGetTokenData(accessToken, userId);
        assertNull(result);
    }
}
