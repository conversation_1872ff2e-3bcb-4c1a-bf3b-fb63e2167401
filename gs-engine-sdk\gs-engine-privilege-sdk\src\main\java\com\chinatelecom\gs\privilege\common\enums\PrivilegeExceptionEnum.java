package com.chinatelecom.gs.privilege.common.enums;

import lombok.Getter;

public enum PrivilegeExceptionEnum {

    NOT_ALLOW_MANUAL_ADMIN("AMDIN_OWNER_NOT_ALLOW", "不允许修改admin或者owner的权限"),
    NO_USER_INFO("PRI401", "缺少用户登录信息"),
    MANAGE_ILLEGAL_PARAM("PRI400", "参数错误"),
    REQUEST_DATA_NOT_EXIST("PRI402", "请求的资源对象不存在"),
    INTERNAL_SERVER_ERROR("INTERNAL_SERVER_ERROR", "服务器内部异常"),
    PARAM_VALUE_NOT_EXISTS("PARAM_VALUE_NOT_EXISTS", "资源id值不存在"),
    NOT_ALLOWED("NOT_ALLOWED", "未授权访问"),
    NO_DATA("NO_DATA", "无数据"),
    PARAM_NOT_EXISTS("PARAM_NOT_EXISTS", "指定的参数在入参中不存在"),
    FIELD_ACCESS_FAILED("FIELD_ACCESS_FAILED", "访问字段参数失败"),
    FIELD_LENGTH_ERROR("FIELD_LENGTH_ERROR", "字段数量标注错误"),
    FIELD_NOT_EXISTS("FIELD_NOT_EXISTS", "权限注解标注字段不存在");

    private PrivilegeExceptionEnum(String code, String cause) {
        this.code = code;
        this.cause = cause;
    }

    @Getter
    private String code;

    @Getter
    private String cause;
}
