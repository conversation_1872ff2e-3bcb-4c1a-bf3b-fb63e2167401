package com.chinatelecom.gs.engine.kms.common.report;

import com.chinatelecom.gs.engine.common.utils.JsonNotNullUtils;
import com.chinatelecom.gs.engine.common.utils.JsonUtils;
import com.chinatelecom.gs.engine.kms.sdk.enums.SseEventType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2025年05月21日
 */
@Getter
@Setter
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class ReportMessage<T> {

    private SseEventType eventType = SseEventType.add;

    private String text;

    private String reasoningContent;

    private T extraData;

    public static ReportMessage ofAdd(String text) {
        return ReportMessage.builder().eventType(SseEventType.add).text(text).build();
    }

    public static <T> ReportMessage ofAdd(String text, T extraData) {
        return ReportMessage.builder().eventType(SseEventType.add).text(text).extraData(extraData).build();
    }

    public static ReportMessage ofCover(String text) {
        return ReportMessage.builder().eventType(SseEventType.cover).text(text).build();
    }

    public static ReportMessage ofFinish(String text) {
        return ReportMessage.builder().eventType(SseEventType.finish).text(text).build();
    }

    public static ReportMessage ofError(String text) {
        return ReportMessage.builder().eventType(SseEventType.error).text(text).build();
    }

    @Override
    public String toString() {
        return JsonNotNullUtils.toJsonString(this);
    }
}
