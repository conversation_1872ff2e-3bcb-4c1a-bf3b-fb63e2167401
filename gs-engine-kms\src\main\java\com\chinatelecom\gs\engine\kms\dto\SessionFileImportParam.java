package com.chinatelecom.gs.engine.kms.dto;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;

/**
 * 会话文件上传参数
 */
@Schema(description = "会话文件上传参数")
@Data
public class SessionFileImportParam extends SessionFileParam{

    /**
     * 文件
     */
    @NotEmpty(message = "文件Key不能为空")
    @Schema(description = "文件Key,fileKey需要先调用直接上传文件接口获取")
    private String fileKey;

    /**
     * 文件
     */
    @NotEmpty(message = "文件名称不能为空")
    @Schema(description = "文件名称")
    private String fileName;
}
