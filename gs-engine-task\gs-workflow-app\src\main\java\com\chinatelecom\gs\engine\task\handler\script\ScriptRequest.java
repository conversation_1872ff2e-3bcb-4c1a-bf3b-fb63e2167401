package com.chinatelecom.gs.engine.task.handler.script;

import com.chinatelecom.gs.workflow.core.workflow.core.enums.ScriptTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * @USER: pengmc1
 * @DATE: 2024/10/9 9:42
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ScriptRequest {
    /**
     * 脚本类型
     */
    private ScriptTypeEnum scriptType;
    /**
     * 脚本代码
     */
    private String scriptCode;
    /**
     * 脚本参数
     */
    private Map<String,Object> scriptParam;

    private Boolean isTest;
}
