package com.chinatelecom.gs.engine.common.config.property;

import com.chinatelecom.gs.engine.common.constants.Constants;
import com.chinatelecom.gs.engine.common.enums.CacheType;
import com.chinatelecom.gs.engine.common.enums.S3Type;
import com.chinatelecom.gs.engine.common.enums.SearchTemplateType;
import com.chinatelecom.gs.engine.kms.sdk.enums.KnowledgeType;
import com.chinatelecom.gs.engine.kms.sdk.enums.RecallType;
import com.chinatelecom.gs.engine.kms.sdk.enums.ResponseType;
import com.chinatelelcom.gs.engine.sdk.common.enums.AppSourceType;
import com.google.common.collect.ImmutableMap;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024年11月29日
 */

@Getter
@Setter
@Component
@RefreshScope
@ConfigurationProperties(prefix = "gs")
@Slf4j
public class GsGlobalConfig {

    /**
     * 系统配置
     */
    private SystemConfig system = new SystemConfig();

    /**
     * 对象存储配置
     */
    private S3Config s3 = new S3Config();

    /**
     * 缓存配置
     */
    private CacheConfig cache = new CacheConfig();

    /**
     * 开发mock请求配置
     */
    private MockRequest mockRequest = new MockRequest();
    /**
     * kms默认索引配置
     */
    private KmsIndexConfig index = new KmsIndexConfig();
    /**
     * kms一些业务配置
     */
    private KmsBizConfig kmsBizConfig = new KmsBizConfig();
    /**
     * 线程池配置
     */
    private PoolConfig pool = new PoolConfig();
    /**
     * kms远程调用的一些配置
     */
    private KmsRpcConfig kmsRpcConfig = new KmsRpcConfig();

    /**
     * 知识库解析默认配置
     */
    private KmsKnowledgeBaseConfig kmsBaseConfig = new KmsKnowledgeBaseConfig();

    private RagConfig rag = new RagConfig();

    private SearchConfig search = new SearchConfig();


    /**
     * 日志追踪分析记录
     */
    private TraceLogProperty traceLogProperty = new TraceLogProperty();

    private ReportRecall reportRecall = new ReportRecall();

    /**
     * OpenAPI配置
     */
    private OpenApiConfig openApi = new OpenApiConfig();

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SystemConfig {
        // 固定前缀格式 ${prefix}${env}****

        // 部署环境: dev、test、prod
        private String env = "prod";
        /**
         * es前缀
         */
        private String esPrefix = "";
        /**
         * mq前缀
         */
        private String mqPrefix = "";
        /**
         * redis前缀
         */
        private String redisPrefix = "";

        // 是否打印拦截日志
        private Boolean logEnabled = true;

        private String rootPath = "/usr/src/app/telecom";

        // 排除不进行appCode拦截的请求路径
//        private String appCodeExcludeUrls = "/kms/web/app/page,/kms/web/app,/kms/web/platform/**";

        private String apiExcludeUrls = "/channel/openapi/message/link/apisecret/**";
        // rpc调用排除不拦截的路径
        private String rpcExcludeUrls = "/kms/rpc/flow/callback";
        private String openApiExcludeUrls = "/**/channel/openapi/**,/**/channel/qywx/**,/**/bot/openapi/agent/v1/**,/**/openapi/open/**";

        /**
         * 来源系统的地址配置
         */
        private Map<AppSourceType, String> sourceUrlType = new HashMap<>();

        /**
         * 账号信息是否使用引擎的app
         */
        private boolean useEngineAppInfo= false;

        /**
         * 是否开启产品来源隔离
         */
        private boolean appSourceIsolation = true;

        /**
         * 默认的AppSourceType，如果不配置则使用KS
         */
        private AppSourceType defaultAppSourceType = AppSourceType.KS;
    }


    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class S3Config {
        // 存储类型： CEPH、MINIO、LOCAL， LOCAL仅用于本地自测依赖使用，生产禁止使用
        private S3Type type = S3Type.CEPH;
        private String endPoint;
        private String accessKey;
        private String secretKey;
        private String bucketName = "gs";
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CacheConfig {
        // 缓存类型
        private CacheType type = CacheType.NONE;

    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MockRequest {
        private Boolean enabled = false;
        private String name = "未知用户";
        private String id = Constants.EMPTY_NODE;
        private String tenantId = Constants.EMPTY_NODE;
        private String emptyName = Constants.EMPTY_NODE;
        private String appCode = Constants.EMPTY_NODE;
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class KmsIndexConfig {
        private Integer maxFrom = 10000;
        private String vectorQueryAnalyzer = "bge-query";
        private String vectorDocAnalyzer = "bge-doc";
        private String textAnalyzer = "中文通用分析器（全停用词）";
        private String spellAnalyzer = "拼音分析器-带中文分词";
    }


    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class KmsBizConfig {


        // 文件名称长度限制
        private int fileNameMaxLength = 512;
        // 文件大小限制  默认100M
        private long fileSizeMaxLength = 200;
        /**
         * 使用单独线程池解析大文件的文件大小下限（单位M）
         */
        private Map<KnowledgeType, Long> largeFileSplit = ImmutableMap.of(
                KnowledgeType.IMAGE, 2L,
                KnowledgeType.TEXT, 5L,
                KnowledgeType.EXCEL, 5L,
                KnowledgeType.FAQ, 5L,
                KnowledgeType.PDF, 10L,
                KnowledgeType.WORD, 10L,
                KnowledgeType.PPT, 10L,
                KnowledgeType.VIDEO, 30L,
                KnowledgeType.AUDIO, 30L
        );
        /**
         * 导读开关，默认关闭
         */
        private Boolean summarySwitch = false;

        /**
         * 默认初始化copy数据的应用编码
         */
        private String defaultInitAppCode;
        /**
         * 默认初始化copy数据的知识库编码
         */
        private String defaultInitKnowledgeBase;

        /**
         * 智能报告 faq相似问 等使用的模型编码，如果不配置，使用默认大模型
         */
        private String fastLlmModelCode;

        /**
         * faq抽取最大分片大小
         */
        private int faqExtractMaxChunkSize = 30;

        /**
         * 推送搜索分片每次推送的最大条数
         */
        private Integer pushSearchMaxBatchSize = 100;

        int tagMaxLevel = 5;

        /**
         * 搜索召回全量数据每页查询的最大条数
         */
        private Integer searchMaxBatchPageSize = 5000;
        /**
         * 游标查询每个ES分片返回的数量
         */
        private Integer searchMaxScrollPageSize = 2000;
        /**
         * 搜索召回全量数据每页查询的最大页数
         */
        private Integer searchMaxBatchPageNum = 10000;

        /**
         * 系统预置模板的ceph的key，如果不配置默认从类路径中取
         */
        private Map<String, String> templateFilePathMap = new HashMap<>();

        /**
         * 是否开启excel文件公式检查
         */
        private boolean checkExcelSafe = true;
        /**
         * 仅检查小于该设置的excel文件
         */
        private Integer checkExcelSize = 5;

        /**
         * 写作正文生成的超时时间（秒）
         */
        private Integer reportSseTimeout = 1800;

    }

    @Getter
    @Setter
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ThreadPoolConfig {
        private int coreSize = 8;
        private int maxSize = 16;
        private int queueSize = -1;
        private String name;
    }

    @Getter
    @Setter
    public static class PoolConfig {
        private ThreadPoolConfig defaultPool = new ThreadPoolConfig(16, 32, -1, "default-pool");
        private ThreadPoolConfig splitPool = new ThreadPoolConfig(4, 8, -1, "split-pool");
        private ThreadPoolConfig largeFileSplitPool = new ThreadPoolConfig(2, 2, -1, "large-file-split-pool");
        private ThreadPoolConfig splitImgPool = new ThreadPoolConfig(4, 4, -1, "split-img-pool");
        /**
         * 聊天窗解析文件的线程池
         */
        private ThreadPoolConfig chatParserPool = new ThreadPoolConfig(8, 16, -1, "chat-parser-pool");
        private ThreadPoolConfig searchFlowPool = new ThreadPoolConfig(16, 16, -1, "search-flow-pool");
        private ThreadPoolConfig extraFlowPool = new ThreadPoolConfig(4, 4, -1, "extra-flow-pool");
        private ThreadPoolConfig apiChatExecutorPool = new ThreadPoolConfig(5, 10, 10, "api-chat-pool");
        private ThreadPoolConfig extractPool = new ThreadPoolConfig(3, 3, -1, "extract-pool");
        private ThreadPoolConfig commonPool = new ThreadPoolConfig(200, 200, -1, "common-pool");
        private ThreadPoolConfig safeCheckThreadPool = new ThreadPoolConfig(50, 200, -1, "safe-check-thread-pool");
        // 专用线程池，勿用
        private ThreadPoolConfig reportSseThreadPool = new ThreadPoolConfig(16, 16, 0, "report-sse-thread-pool");
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class KmsRpcConfig {
        /**
         * 解析服务调用地址
         */
        private String dataParserUrl;
        /**
         * kgd online算法在线服务地址
         */
        private String onlineUrl;
        /**
         * 读取任务结果超时时间 (毫秒)
         */
        private long defaultReadTimeout = 1800000;
        private long convertReadTimeout = 1800000;
        private long parseReadTimeout = 1800000;
        /**
         * 解析超时时间的几倍时间定时任务直接将处理中的置为失败
         */
        private long parseTaskMultiple = 3;
        private long summaryReadTimeout = 600000;

        private String gsUrl = "http://127.0.0.1:8092";
    }


    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class KmsKnowledgeBaseConfig {
        /**
         * 知识库维度默认配置 KnowledgeBaseConfig
         */
        private String defaultConfig = "{\"modelUrlList\":{},\"chunkConfig\":{\"baseChunkFlag\":true,\"semanticChunkFlag\":true,\"customChunkFlag\":false,\"customChunkConfig\":{\"splitChr\":[\"\"],\"maxLen\":512,\"overlapLen\":128,\"splitTime\":300}},\"parserConfig\":{\"ocrFlag\":true,\"cpuFlag\":false,\"layoutFlag\":true,\"lightWeightFlag\":false,\"summaryFlag\":false}}";
        /**
         * 默认pdf解析进度占比，默认10%
         */
        private Integer defaultPdfProcess = 10;
    }

    @Getter
    @Setter
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RagConfig {
        private boolean enableMulti;
        private boolean enableQueryRewrite = true;
        private boolean enableImageModel = true;
        private boolean modelUseRewrite = true;
        private boolean emptyToResp;
        private boolean retryWithOriginalQuery = true;
        private boolean enableRank;
        private int recallSize = 20;
        private int reqSize = 8;
        private int refMaxLength = 40;
        private Map<ResponseType, String> responseText;
        private boolean enableLog;
        private String referencePattern;
        private String referencePrefix;
        private String referenceFormat;
        private int multiRounds;
        private int multiReferenceSize;
        private int bufferLength = 10;
        private Duration timeout = Duration.ofSeconds(60);
    }

    @Getter
    @Setter
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SearchConfig {
        private Map<SearchTemplateType, String> searchTemplate;
        private Map<RecallType, String> rankExpression;
        private int recallMaxSize = 50;
        private int filterKnowledgeChunkSize = 3;
        /** 送入精排的每种粗排类型选用的数量  */
        private int rerankRecallMinSize = 20;

        // 默认精排模型
        private String searchRerankModelCode;
        // 搜索默认分数上限
        private Double searchMinThreshold = 0.1;
        // 猜你想搜最多数量库上限
        private Integer SuggestKbMaxNum = 50;

        /**
         * 分析器租户白名单
         * 如果不为空，则创建索引时检查租户是否在白名单中：
         * - 在白名单中：创建带有同义词的索引
         * - 不在白名单中：普通创建索引
         * 如果为空，则不校验租户
         */
        private List<String> tenantSynonymWhitelist = new ArrayList<>();
    }

    private Map<String, FilterModeConfig> filterModes = new HashMap<>();

    @Data
    public static class FilterModeConfig {
        private String name;
        private String description;
        private Set<String> filteredCodes;
    }

    @Getter
    @Setter
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TraceLogProperty {
        private boolean enabled = false;
        /**
         * 解析分析耗时日志
         */
        private String parseIndexName = "ai-ks-parse";
        private String exportUserId;

        /**
         * 是否开启链路跟踪
         */
        private boolean traceEnabled = true;
        /**
         * 数据库最大保留月数
         */
        private Integer dbMaxMonth = 2;
        /**
         * ES最大保留月数
         */
        private Integer esMaxMonth = 3;
    }

    @Getter
    @Setter
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ReportRecall {
        // 每个大纲参考片段召回数量
        private Integer topN = 5;

        private Double minScore = 0.3;

        private String fileRecallFirstRankExpression = "atan_normalized_bm25(0.01)*0.6+knn_score()*0.4";

        /**
         * 摘要召回数量
         */
        private Integer summaryTopN = 3;
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OpenApiConfig {
        /**
         * OpenAPI文档标题
         */
        private String title = "智能文档知识库V2.0_OpenAPI设计文档";

        /**
         * OpenAPI文档描述
         */
        private String description = "智能文档知识库V2.0_OpenAPI设计文档";

        /**
         * OpenAPI版本
         */
        private String version = "2.x.x";
    }
}