package com.chinatelecom.gs.engine.core.model.controller;

import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.common.auth.KsMenuConfig;
import com.chinatelecom.gs.engine.common.auth.MenuConfig;
import com.chinatelecom.gs.engine.common.auth.PermissionTag;
import com.chinatelecom.gs.engine.common.constants.Apis;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.common.utils.FileUtil;
import com.chinatelecom.gs.engine.core.model.entity.vo.AgentIntentManagerPageQuery;
import com.chinatelecom.gs.engine.core.model.entity.vo.AgentIntentManagerRequest;
import com.chinatelecom.gs.engine.core.model.entity.vo.BatchSwitchParam;
import com.chinatelecom.gs.engine.core.model.entity.vo.ModelIntentUploadRequest;
import com.chinatelecom.gs.engine.core.model.service.AgentIntentManagerAppService;
import com.chinatelecom.gs.engine.core.sdk.request.AgentIntentConfigRequest;
import com.chinatelecom.gs.engine.core.sdk.request.CodeRequest;
import com.chinatelecom.gs.engine.core.sdk.request.IntentQueryRequest;
import com.chinatelecom.gs.engine.core.sdk.vo.AgentIntentConfigResponse;
import com.chinatelecom.gs.engine.core.sdk.vo.AgentIntentResponse;
import com.chinatelecom.gs.engine.core.sdk.vo.IntentIndexData;
import com.chinatelecom.gs.engine.core.sdk.vo.IntentSearchVO;
import com.chinatelecom.gs.engine.kms.sdk.enums.SystemFileType;
import com.chinatelelcom.gs.engine.sdk.common.Page;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import com.google.common.collect.Lists;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;


/**
 * <p>
 * 意图管理表 Controller
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-10
 */
@PermissionTag(code = {MenuConfig.DIALOG_FLOW, MenuConfig.WORKFLOW, KsMenuConfig.WORKFLOW_FLOW})
@RestController
@Tag(name = "意图管理表 Controller")
@RequestMapping(Apis.BASE_PREFIX + Apis.WEB_API + "/intent")
public class AgentIntentManagerController {

    @Resource
    private AgentIntentManagerAppService agentIntentManagerAppService;

    @Operation(summary = "意图管理表分页列表", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "意图管理表分页列表", groupName = "意图管理")
    @PostMapping("/page")
    @AuditLog(businessType = "意图管理", operType = "意图管理表分页列表", operDesc = "意图管理表分页列表", objId="null")
    public Result<Page<AgentIntentResponse>> page(@Validated @RequestBody AgentIntentManagerPageQuery param) {

        return Result.success(agentIntentManagerAppService.pageQuery(param));
    }

    @Operation(summary = "更新意图", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "更新意图", groupName = "意图管理")
    @PutMapping("/{code}")
    @AuditLog(businessType = "意图管理", operType = "更新意图", operDesc = "更新意图", objId="#param.code")
    public Result<Boolean> update(@PathVariable("code") String code, @Validated @RequestBody AgentIntentManagerRequest param) {
        return Result.success(agentIntentManagerAppService.update(code, param));
    }

    @Operation(summary = "意图管理表新增", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "意图管理表新增", groupName = "意图管理")
    @PostMapping
    @AuditLog(businessType = "意图管理", operType = "意图管理表新增", operDesc = "意图管理表新增", objId="null")
    public Result<AgentIntentResponse> add(@Validated @RequestBody AgentIntentManagerRequest createParam) {
        return Result.success(agentIntentManagerAppService.create(createParam, false));
    }

    @Operation(summary = "删除意图", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "删除意图", groupName = "意图管理")
    @PostMapping("/delete")
    @AuditLog(businessType = "意图管理", operType = "删除意图", operDesc = "删除意图", objId="null")
    public Result<Boolean> delete(@Validated @RequestBody CodeRequest codes) {
        return Result.success(agentIntentManagerAppService.delete(codes));
    }

    @Operation(summary = "意图开关", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "意图开关", groupName = "意图管理")
    @PostMapping("/switch")
    @AuditLog(businessType = "意图管理", operType = "意图开关", operDesc = "意图开关", objId="null")
    public Result<Boolean> switchOn(@Validated @RequestBody BatchSwitchParam codes) {
        return Result.success(agentIntentManagerAppService.switchOn(codes));
    }

    @Operation(summary = "例句查询", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "例句查询", groupName = "意图管理")
    @PostMapping("/query")
    @AuditLog(businessType = "意图管理", operType = "例句查询", operDesc = "例句查询", objId="null")
    public IntentSearchVO<IntentIndexData> queryIntent(@RequestBody IntentQueryRequest request) {
        return agentIntentManagerAppService.queryIntent(request, RequestContext.getAppCode());
    }

    @Operation(summary = "获取意图高级配置", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "获取意图高级配置", groupName = "意图管理")
    @GetMapping("/intent-manager")
    @AuditLog(businessType = "意图管理", operType = "获取意图高级配置", operDesc = "获取意图高级配置", objId="null")
    public Result<AgentIntentConfigResponse> getIntentConfig() {
        return Result.success(agentIntentManagerAppService.getIntentConfigOrDefault(RequestContext.getAppCode()));
    }

    @Operation(summary = "更新意图高级配置", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "更新意图高级配置", groupName = "意图管理")
    @PostMapping("/intent-manager")
    @AuditLog(businessType = "意图管理", operType = "更新意图高级配置", operDesc = "更新意图高级配置", objId="null")
    public Result<Boolean> updateIntentConfig(@Validated @RequestBody AgentIntentConfigRequest param) {
        return Result.success(agentIntentManagerAppService.creatOrUpdateConfig(param));
    }

    @Operation(summary = "获取系统文件", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @GetMapping("/system-file")
    @AuditLog(businessType = "意图管理", operType = "获取系统文件", operDesc = "获取系统文件", objId="null")
    @PlatformRestApi(name = "获取系统文件", groupName = "意图管理")
    public void downloadSystemFileApi(@RequestParam SystemFileType systemFileType, HttpServletResponse response) throws IOException {
        agentIntentManagerAppService.getSystemFile(systemFileType, response);
    }

    @GetMapping("/detail")
    @Operation(summary = "意图详情", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "意图详情", groupName = "意图管理")
    @AuditLog(businessType = "意图管理", operType = "意图详情", operDesc = "意图详情", objId="#code")
    public Result<AgentIntentResponse> detail(@RequestParam String code) {
        return Result.success(agentIntentManagerAppService.detail(code));
    }

    @Operation(summary = "导出意图列表", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "导出意图列表", groupName = "意图管理")
    @PostMapping("/export")
    @AuditLog(businessType = "意图管理", operType = "导出意图列表", operDesc = "导出意图列表", objId="null")
    public void export(@Valid @RequestBody IntentQueryRequest param, HttpServletResponse response) throws IOException {
        agentIntentManagerAppService.export(param, response);
    }

    @PostMapping("/upload")
    @Operation(summary = "意图导入", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "意图导入", groupName = "意图管理")
    @AuditLog(businessType = "意图管理", operType = "意图导入", operDesc = "意图导入", objId="null")
    public Result<Boolean> upload(@RequestParam("file") MultipartFile file) throws Exception {
        FileUtil.checkFileType(file, Lists.newArrayList(".xlsx",".xls"));
        String userTip = agentIntentManagerAppService.upload(file);
        Result<Boolean> success = Result.success();
        success.setUserTip(userTip);
        return success;
    }

    @Operation(summary = "模型导入意图", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "模型导入意图", groupName = "意图管理")
    @PostMapping("/model/upload")
    @AuditLog(businessType = "意图管理", operType = "模型导入意图", operDesc = "模型导入意图", objId="#request.modelCode")
    public Result<Boolean> uploadModelIntent(@Validated @RequestBody ModelIntentUploadRequest request) {
        String userTip = agentIntentManagerAppService.uploadModelIntent(request);
        Result<Boolean> success = Result.success();
        success.setUserTip(userTip);
        return success;
    }

    @Operation(summary = "检查是否有打开的意图", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "检查是否有打开的意图", groupName = "意图管理")
    @GetMapping("/check-active")
    @AuditLog(businessType = "意图管理", operType = "检查是否有打开的意图", operDesc = "检查是否有打开的意图", objId="null")
    public Result<Boolean> checkActiveIntents() {
        return Result.success(agentIntentManagerAppService.hasActiveIntents());
    }

}
