package com.chinatelecom.gs.engine.channel.dao.convert;
import com.chinatelecom.gs.engine.channel.dao.po.ChannelApiSecretPO;
import com.chinatelecom.gs.engine.channel.service.dto.ChannelApiSecretDTO;
import org.junit.jupiter.api.BeforeEach; import org.junit.jupiter.api.Test; import static org.junit.jupiter.api.Assertions.*;
class ChannelConvertTest {
    private ChannelConvert channelConvert;

    @BeforeEach
    void setUp() {
        channelConvert = ChannelConvert.INSTANCE;
    }

    @Test
    void testPo2dto() {
        // Arrange
        ChannelApiSecretPO po = new ChannelApiSecretPO();
        po.setId(1L);
        po.setSecretId("secret123");
        po.setChannelId("channel1");

        // Act
        ChannelApiSecretDTO dto = channelConvert.po2dto(po);

        // Assert
        assertNotNull(dto);
        assertEquals(po.getId(), dto.getId());
        assertEquals(po.getSecretId(), dto.getSecretId());
        assertEquals(po.getChannelId(), dto.getChannelId());
    }
}
