//package com.chinatelecom.csbotplatform.channel.api.config;
//
//import com.chinatelecom.csbotplatform.sdk.dto.Result;
//import com.chinatelecom.csbotplatform.sdk.exception.BizException;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.core.annotation.Order;
//import org.springframework.http.HttpStatus;
//import org.springframework.web.bind.annotation.*;
//
///**
// * <AUTHOR>
// * @date 2023/12/26 15:05
// * @description
// */
//@Slf4j
//@Order(1)
//@ControllerAdvice(annotations = {RestController.class})
//public class GlobalExceptionHandler {
//
//    @ExceptionHandler(BizException.class)
//    @ResponseStatus(HttpStatus.OK)
//    @ResponseBody
//    public Result handleBizException(BizException e) {
//        Result response = Result.builder()
//                .message("error")
//                .build();
//        return response;
//    }
//
//    @ExceptionHandler(Exception.class)
//    @ResponseStatus(HttpStatus.OK)
//    @ResponseBody
//    public Result handleException(Exception e) {
//        log.error("系统异常{}", e.getMessage());
//        Result response = Result.builder()
//                .message("系统未知异常")
//                .build();
//        return response;
//    }
//}
