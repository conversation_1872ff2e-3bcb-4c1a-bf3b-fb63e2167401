{"front": [{"uri": "/ais/bot/f/*", "name": "前端/ais/bot/f", "methods": ["GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS", "CONNECT", "TRACE", "PURGE"], "upstream_id": "547719502146569150", "labels": {"name": "AIS"}, "status": 1}, {"uri": "/ais/kms/f/*", "name": "前端/ais/kms/f", "methods": ["GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS", "CONNECT", "TRACE", "PURGE"], "upstream_id": "547719502146569150", "labels": {"name": "AIS"}, "status": 1}, {"uri": "/ais/ks/f/*", "name": "前端/ais/ks/f", "methods": ["GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS", "CONNECT", "TRACE", "PURGE"], "upstream_id": "547719502146569150", "labels": {"name": "AIS"}, "status": 1}, {"uri": "/ais/workflow/f/*", "name": "前端/ais/workflow/f", "methods": ["GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS", "CONNECT", "TRACE", "PURGE"], "upstream_id": "547719502146569150", "labels": {"name": "AIS"}, "status": 1}, {"uri": "/ais/plugin/f/*", "name": "前端/ais/plugin/f", "methods": ["GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS", "CONNECT", "TRACE", "PURGE"], "upstream_id": "547719502146569150", "labels": {"name": "AIS"}, "status": 1}, {"uri": "/ais/im/f/*", "name": "前端/ais/im/f", "methods": ["GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS", "CONNECT", "TRACE", "PURGE"], "upstream_id": "547724367438545854", "labels": {"name": "AIS"}, "status": 1}, {"uri": "/ais/channel/f/*", "name": "前端/ais/channel/f", "methods": ["GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS", "CONNECT", "TRACE", "PURGE"], "upstream_id": "547719502146569150", "labels": {"name": "AIS"}, "status": 1}, {"uri": "/ais/base/f/*", "name": "前端/ais/base/f", "methods": ["GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS", "CONNECT", "TRACE", "PURGE"], "upstream_id": "547719502146569150", "labels": {"name": "AIS"}, "status": 1}, {"uri": "/dtflow/*", "name": "审批流前端/dtflow/f", "methods": ["GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS", "CONNECT", "TRACE", "PURGE"], "upstream_id": "548113554994103261", "labels": {"name": "AIS"}, "status": 1}], "serverWeb": [{"uri": "/ais/bot/web/*", "name": "后端/ais/bot/web", "methods": ["GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS", "CONNECT", "TRACE", "PURGE"], "upstream_id": "547719355027162046", "labels": {"name": "AIS"}, "status": 1}, {"uri": "/ais/kms/web/*", "name": "后端/ais/kms/web", "methods": ["GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS", "CONNECT", "TRACE", "PURGE"], "upstream_id": "547719355027162046", "labels": {"name": "AIS"}, "status": 1}, {"uri": "/ais/ks/web/*", "name": "后端/ais/ks/web", "methods": ["GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS", "CONNECT", "TRACE", "PURGE"], "upstream_id": "547719355027162046", "labels": {"name": "AIS"}, "status": 1}, {"uri": "/ais/workflow/web/*", "name": "后端/ais/workflow/web", "methods": ["GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS", "CONNECT", "TRACE", "PURGE"], "upstream_id": "547719355027162046", "labels": {"name": "AIS"}, "status": 1}, {"uri": "/ais/plugin/web/*", "name": "后端/ais/plugin/web", "methods": ["GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS", "CONNECT", "TRACE", "PURGE"], "upstream_id": "547719355027162046", "labels": {"name": "AIS"}, "status": 1}, {"uri": "/ais/im/web/*", "name": "后端/ais/im/web", "methods": ["GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS", "CONNECT", "TRACE", "PURGE"], "upstream_id": "547719355027162046", "labels": {"name": "AIS"}, "status": 1}, {"uri": "/ais/channel/web/*", "name": "后端/ais/channel/web", "methods": ["GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS", "CONNECT", "TRACE", "PURGE"], "upstream_id": "547719355027162046", "labels": {"name": "AIS"}, "status": 1}, {"uri": "/ais/base/web/*", "name": "后端/ais/base/web", "methods": ["GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS", "CONNECT", "TRACE", "PURGE"], "upstream_id": "547719355027162046", "labels": {"name": "AIS"}, "status": 1}], "serverOpenApi": [{"uri": "/ais/kms/openapi/*", "name": "后端/ais/kms/openapi", "methods": ["GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS", "CONNECT", "TRACE", "PURGE"], "plugins": {"consumer-restriction": {"_meta": {"disable": false}, "whitelist": ["ais_openapi_default_consumer"]}, "hmac-auth": {"_meta": {"disable": false}}, "proxy-rewrite": {"regex_uri": ["^/ais/kms/openapi/(.*)", "/ais/kms/openapi/$1"]}}, "upstream_id": "547719355027162046", "labels": {"name": "AIS"}, "status": 1}], "serverOther": [{"uri": "/ais/platform/*", "name": "后端-登录/ais/platform", "methods": ["GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS", "CONNECT", "TRACE", "PURGE"], "upstream_id": "547719355027162046", "labels": {"name": "AIS"}, "status": 1}, {"uri": "/ais/ks/f/knowledgeBase/platform/*", "name": "后端-登录转发/ais/ks/f/knowledgeBase/platform", "methods": ["GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS", "CONNECT", "TRACE", "PURGE"], "plugins": {"proxy-rewrite": {"regex_uri": ["/ais/ks/f/knowledgeBase/platform/*", "/ais/platform/$1"]}}, "upstream_id": "547719355027162046", "labels": {"name": "AIS"}, "status": 1}, {"uri": "/starlink/dtflow/*", "name": "审批流后端/starlink/dtflow", "methods": ["GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS", "CONNECT", "TRACE", "PURGE"], "upstream_id": "548113633914127325", "labels": {"name": "AIS"}, "status": 1}]}