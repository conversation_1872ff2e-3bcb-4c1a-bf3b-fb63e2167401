package com.chinatelecom.gs.engine.kms.repository;


import com.chinatelecom.gs.engine.common.infra.base.BaseByCodeRepository;
import com.chinatelecom.gs.engine.kms.dto.knowledge.KnowledgeParseResult;
import com.chinatelecom.gs.engine.kms.infra.po.KnowledgePO;
import com.chinatelecom.gs.engine.kms.model.dto.KnowledgeDTO;
import com.chinatelecom.gs.engine.kms.model.dto.KnowledgeDetailDTO;
import com.chinatelecom.gs.engine.kms.sdk.enums.PublishStatus;
import com.chinatelecom.gs.engine.kms.sdk.vo.common.CodeName;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.base.KBPublishStatisticsVO;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 知识表 Repository接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-06
 */


public interface KnowledgeRepository extends BaseByCodeRepository<KnowledgeDTO, KnowledgePO> {

    /**
     * 更新预览视图
     * @param knowledgeCode
     * @param viewFileUrl
     */
    void updateViewFileKey(String knowledgeCode, String viewFileUrl, Integer progress);

    /**
     * 更新解析结果
     * @param knowledgeCode
     * @param knowledgeParseResult
     */
    void updateKnowParseResult(String knowledgeCode, KnowledgeParseResult knowledgeParseResult);

    /**
     * 更新知识解析任务信息
     */
    void updateKnowledgeTaskInfo(String knowledgeCode, String taskId, String imageTaskId, Integer progress);

    /**
     * 查询知识详细信息
     * @param knowledgeCode
     * @return
     */
    KnowledgeDetailDTO selectKnowledgeDetail(String knowledgeCode);

    /**
     * 根据知识编码列表查询知识信息列表
     * @param knowledgeCodes
     * @return
     */
    List<KnowledgeDTO> findByCodes(Collection<String> knowledgeCodes);

    /**
     * 更新发布状态
     * @param knowledgeCodes
     * @param publishStatus
     */
    void updatePublishStatus(Collection<String> knowledgeCodes, PublishStatus publishStatus);

    /**
     * 更新发布状态为修改未发布，只有发布过的才会更新
     * @return
     */
    boolean updatePublishStatus2Update(Collection<String> knowledgeCodes);

    /**
     * 修改状态为未发布,没有发布记录的会使用该方法
     * @param knowledgeCodes
     * @return
     */
    boolean updatePublishStatus2Unpublished(Collection<String> knowledgeCodes);

    /**
     * 修改开关
     * @param knowledgeCodes
     * @param on
     * @return
     */
    boolean updateOn(Collection<String> knowledgeCodes, boolean on);

    /**
     * 查询过滤允许发布的知识列表数据
     * @param knowledgeBaseCode
     * @param knowledgeCode
     * @return
     */
    List<KnowledgeDTO> selectAllowPublish(String knowledgeBaseCode, Collection<String> knowledgeCode);

    /**
     * FAQ查询过滤允许发布的知识列表数据
     * @param knowledgeBaseCode
     * @param knowledgeCode
     * @return
     */
    List<KnowledgeDTO> selectAllowPublishFaq(String knowledgeBaseCode, Collection<String> knowledgeCode);
    /**
     * 根据知识库Code和解析状态、发布状态列表查询知识编码列表
     * @param knowledgeBaseCode
     * @param statusList
     * @param publicStatusList
     * @return
     */
    List<String> filterCodes(String knowledgeBaseCode, List<String> statusList, List<String> publicStatusList);

    /**
     * 取消发布，恢复发布状态
     * @param auditCode
     * @param knowledgeCodes
     */
    void cancelPublish(String auditCode, List<String> knowledgeCodes);

    /**
     * 查询复制的基本信息
     *
     * @param knowledgeBaseCode
     * @param knowledgeCodes
     * @return
     */
    List<KnowledgeDTO> findCopyInfoByCodes(String knowledgeBaseCode, List<String> knowledgeCodes);

    /**
     * 超时时更新知识状态
     * @param localDateTime
     */
    void updateKnowledgeStatusTimeout(LocalDateTime localDateTime);


    /**
     * 查询解析中的知识列表
     * @return
     */
    List<KnowledgeDTO> queryParsePending(LocalDateTime localDateTime);

    /**
     * 根据编码查询名称
     * @param codes
     * @return
     */
    List<CodeName> findNameByCodes(List<String> codes);


    /**
     * 统计知识库文档发布信息
     * @param knowledgeBaseCodes
     * @return
     */
    Map<String, KBPublishStatisticsVO> countPublishInfo(Collection<String> knowledgeBaseCodes);

    /**
     * 查询
     * @param startId
     * @param scrollSize
     * @return
     */
    List<KnowledgeDTO> queryNeedRefreshFileLength(Long startId, Integer scrollSize);

    /**
     * 更新文件大小
     * @param id
     * @param fileLength
     */
    void updateFileLength(Long id, long fileLength);

    /**
     * 刷新文件更新时间
     * @param knowledgeCode
     */
    void updateUpdateTime(String knowledgeCode);
}

