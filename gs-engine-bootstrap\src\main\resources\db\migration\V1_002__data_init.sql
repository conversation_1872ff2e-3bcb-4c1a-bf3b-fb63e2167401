
insert  into `agent_default_config`(`id`,`config_key`,`config_value`,`type`,`category`,`yn`,`create_id`,`create_name`,`update_id`,`update_name`,`create_time`,`update_time`,`version`,`tenant_id`,`app_code`) values
(38,'agentConfig.model','{\"modelName\":\"telechat\",\"modelCode\":\"model_859269637005643776\",\"modelMode\":\"PRECISION_MODE\",\"outputType\":\"txt\",\"modelRandomness\":1,\"modelSamplingRate\":0.0,\"memoryCount\":3, \"enableAdvConf\":true, \"enableSortModel\":true}',1,1,0,'8468034137038716928','test','8468034137038716928','test','2024-07-11 18:42:33.592','2024-07-31 10:46:27.673',1,'zhinengti',NULL),
(39,'agentInfo.reply.template','#角色\r\n你是一位专注于美食领域的资深食评家和美食内容创作者。你擅长运用生动有趣的语言和视角分析各类美食，并为读者推荐优质的美食体验。\r\n#目标\r\n根据你的美食专家身份,为用户生成具有吸引力的美食内容,帮助他们发现并体验优质的美食。\r\n#背景知识\r\n－美食领域存在大量优质内容，能够引导用户发现有价值的美食体验。\r\n－优质的美食内容需要融合专业评判、生动描述和个人见解,才能真正吸引用户。\r\n－美食内容创作需要关注用户痛点和需求，以满足他们对美食的探索和体验欲望。\r\n#限制\r\n－请严格遵守相关法律法规，避免涉及违法或不当内容。\r\n－内容创作应以用户需求为中心,切忌过于自我或主观。\r\n－输出的内容应符合平台规范和用户习惯，注重可读性和实用性。\r\n#技能\r\n1.美食评论分析能力：\r\n－准确把握不同美食的特寺点、口味和适用场景。\r\n－运用专业术语描述美食特征,并提供客观、中肯的评价。\r\n2.内容创作能力：\r\n－擅长运用生动有趣的语言，吸引用户阅读和互动。\r\n－善于挖掘美食背后的故事和文化内涵，增加内容的深度和价值。\r\n－熟悉 SEO 优化技巧，提高内容的曝光和转化率。\r\n3.用户洞察能力：\r\n－了解目标用户的美食偏好和消费习惯，满足他们的需求。\r\n－洞察用户痛点和潜在需求，为其提供有价值的美食体验。',1,1,0,'8468034137038716928','test','8468034137038716928','test','2024-07-11 18:42:33.592','2024-08-01 01:14:17.562',1,'zhinengti',NULL),
(40,'agentConfig.template.category.config','[{\"templateId\":\"1\",\"templateName\":\"智能客服\"},{\"templateId\":\"2\",\"templateName\":\"招聘助手\"}, {\"templateId\":\"3\",\"templateName\":\"文档助手\"},{\"templateId\":\"4\",\"templateName\":\"私人助理\"}]',1,1,0,'8468034137038716928','mockUser','8468034137038716928','mockUser','2024-07-19 11:27:07.406','2024-07-19 17:26:22.059',1,'test',NULL),
(41,'agentConfig.kms.config','{\"searchStrategy\":\"MIX\",\"retriever\":3}',1,1,0,'8468034137038716928','test','8468034137038716928','test','2024-07-11 18:42:33.592','2024-07-29 10:02:04.133',1,'zhinengti',NULL),
(42,'agentConfig.global.policy','{\"globalVarSwitch\":false,\"securityFenceSwitch\":true}',1,1,0,'8468034137038716928','test','8468034137038716928','test','2024-07-11 18:42:33.592','2024-07-31 10:50:18.328',1,'zhinengti',NULL),
(43,'agentConfig.dialog.config','{\"agentPrologue\":\"\",\"noAnswerScript\":\"抱歉，你的问题我无法理解，辛苦换个表述重新询问吧～\",\"agentSuggest\":true,\"suggestCount\":3}',1,1,0,'8468034137038716928','test','8468034137038716928','test','2024-07-11 18:42:33.592','2024-07-31 10:42:21.622',1,'zhinengti',NULL),
(45,'agentConfig.offSiteDeployment.switch','{\"isOpen\":false}',1,2,0,'1','','1','','2024-07-25 10:31:08.492','2024-07-25 16:19:12.956',0,'',NULL),
(46,'botStrategy.llm.qaPrompt','{\"value\":\"基于以下已知信息，简洁、专业的回答问题。\\n 如果无法从中得到答案，请回答\\\"根据已知信息无法回答该问题。\\\"，不允许在答案中添加编造成分，答案请使用中文，保留答案中的样式。\\n\\n 已知内容:\\n#{context}\\n\\n 问题:#{question}\\n\\n 答案:\"}',1,1,0,'1','','1','','2024-07-25 19:55:26.597','2024-07-25 19:55:26.597',1,'',NULL),
(47,'botConfig.qywx.channel.trustedDomain','[\"127.0.0.1\"]',1,2,0,'1','','1','','2024-07-26 14:49:13.257','2024-07-26 14:49:13.257',0,'',NULL),
(48,'botStrategy.llm.weatherGenPrompt','{\"value\":\"基于已下已知内容回答问题，如果已知的内容无法回答问题可以根据自己的理解回答问题，已知内容：#{context}\\n 今天：#{time}\\n 问题:#{question}\\n\"}',1,1,0,'1','','1','','2024-07-26 16:30:12.797','2024-07-26 16:33:37.172',1,'',NULL),
(49,'botStrategy.llm.recommendQuestionPrompt','{\"value\":\"假设你是一个智能助手，根据用户的输入生成#{number}个用户可能会感兴趣的问题，注意是用户向你提问的角度生成，尽可能精简通俗易懂。用户的输入是:#{question}\"}',1,1,0,'1','','1','','2024-07-27 14:07:56.435','2024-08-07 07:44:58.028',1,'',NULL),
(51,'botStrategy.llm.welcomeAndGuidePrompt','{\"value\":\"假设你是一个机器人助手，请根据该机器人的人设和回复逻辑信息，生成一个优雅的开场白和三个用户可能会向你提问的问题,比如:如何做一个蛋炒饭,每个引导问题的长度控制在 20 个字以内, 人设和回复逻辑信息: #{context}\\n 返回结果格式要求:{\\\"开场白\\\":\\\"\\\",\\\"问题\\\":[\\\"\\\",\\\"\\\",\\\"\\\"]}\"}',1,1,0,'1','','1','','2024-08-06 14:53:09.948','2024-08-07 01:23:43.030',1,'',NULL);

insert  into `prompt_template`(`id`,`yn`,`code`,`app_code`,`name`,`description`,`content`,`model_param`,`create_id`,`create_name`,`create_time`,`update_id`,`update_name`,`update_time`) values
(1,0,'FAQ_SIMILAR','0','相似问生成','相似问生成','\"\"\"\n### Task\n根据我输入的功能点名称输出至少20条与其相关的相似说法，只保留相似句子，每行输出一条信息，不输出其他信息\n\n### Instructions\n- 输出要求：只输出一段相似问法的文本内容\n\n### Examples\n- input: 今天天气很不错\n- output: 今天的天气十分宜人\n\n### Question\n输入文本为:\n${content}\n对输入文本根据要求生成相似问法，输出生成后的内容\n\"\"\"',NULL,'_empty_','0','2024-10-11 00:00:00','_empty_','未知用户','2024-10-11 00:00:00'),
(2,0,'OUTLINE','0','大纲生成','全局大纲生成','\"\"\"\n### Task\n根据用户输入的主题，生成大纲\n\n## Instructions\n- 层级要求：大纲生成一级和二级和三级 3个层级，最多生成5个一级标题，最少不少于2个一级标题，每个一级标题最大生成5个二级菜单，以及每个二级标题最大生成5个三级菜单\n- 格式要求：以markdown格式输出，一级标题为以#开头，二级标题以##开头，三级标题以###开头,只输出大纲，不要输出其他内容\n- 标题以#号开始，不能带序号\n### Fewshots\ninput：人工智能的应用\noutput: \n```markdown\n# 人工智能在医疗领域的应用\n## 诊断与治疗支持\n### 医学影像分析\n### 辅助诊断系统\n### 治疗方案推荐\n\n## 个性化医疗\n### 基因组分析 \n### 个性化药物设计\n### 疾病风险预测\n\n## 医疗数据分析\n### 数据挖掘与预测分析\n### 医疗记录自动化\n### 健康监控与预警\n\n# 人工智能在金融行业的应用\n## 风险评估\n### 信用评分模型\n### 反欺诈系统\n### 市场风险预测\n\n## 交易算法\n### 高频交易\n### 量化投资策略\n```\n\n### Question\n用户输入：${content}\n参考示例和指令要求，输出主题大纲\n\"\"\"',NULL,'_empty_','0','2024-10-11 00:00:00','_empty_','未知用户','2024-10-11 00:00:00'),
(3,0,'OUTLINE_REWRITE','0','大纲重写','全局大纲重写','\"\"\"\n### Task\n根据用户主题和之前用户不满意的大纲，重新优化生成大纲\n\n### Instructions \n- 层级要求：大纲生成一级和二级和三级 3个层级，最多生成5个一级标题，最少不少于2个一级标题，每个一级标题最大生成5个二级菜单，以及每个二级标题最大生成5个三级菜单\n- 格式要求：以markdown格式输出，一级标题为以#开头，二级标题以##开头，三级标题以###开头,只输出大纲，不要输出其他内容\n- 标题以#号开始，不能带序号\n### Fewshots\ninput：人工智能的应用\noutput: \n```markdown\n# 人工智能在医疗领域的应用\n## 诊断与治疗支持\n### 医学影像分析\n### 辅助诊断系统\n### 治疗方案推荐\n\n## 个性化医疗\n### 基因组分析 \n### 个性化药物设计\n### 疾病风险预测\n\n## 医疗数据分析\n### 数据挖掘与预测分析\n### 医疗记录自动化\n### 健康监控与预警\n\n# 人工智能在金融行业的应用\n## 风险评估\n### 信用评分模型\n### 反欺诈系统\n### 市场风险预测\n\n## 交易算法\n### 高频交易\n### 量化投资策略\n```\n\n### Question\n用户输入：${content}\n用户不满意的大纲：${outline}\n参考示例和指令要求，对用户不满意的大纲进行优化，重新输出主题大纲\n\"\"\"',NULL,'_empty_','0','2024-10-11 00:00:00','_empty_','未知用户','2024-10-11 00:00:00'),
(4,0,'HEADLINE','0','大纲标题重写','全局大纲标题重写','\"\"\"\n### Task\n对用户输入的主题进行总结，生成一个精简的标题\n\n### Instructions\n- 标题要求：格式应简洁清晰，主题明确，不超过10个字\n- 输出要求：只输出标题内容\n###Fewshots\n#example1\ninput：人工智能在医疗领域的应用有哪些\noutput:人工智能在医疗领域的应用\n\n#example2\ninput：大模型的在当前的发展\noutput:大模型：变革时代\n\n### Question\n用户输入：${content}\n参考示例和指令要求，总结用户输入的内容，生成一个标题\n\"\"\"',NULL,'_empty_','0','2024-10-11 00:00:00','_empty_','未知用户','2024-10-11 00:00:00'),
(5,0,'CONTENT','0','根据大纲编写正文','全局根据大纲编写正文','\"\"\"\n### Task\n根据主题，对大纲内容进行扩展，生成一份详细的Markdown格式的报告。\n\n### Instructions\n- **内容要求**：严格按照给定的大纲格式输出内容，报告应包括每个部分的详细描述，语言应专业、清晰，适合目标读者。请确保逻辑严谨，结构清晰，内容要全面完整。\n- **层级结构**：\n    1. 第一层级下的内容为概括性总结，呈现主要观点或结论，确保信息简明扼要。\n    2. 第二层级的内容为详细的分析，提供具体的数据、案例或理论支持，深入探讨主题的各个方面。\n    3. 第三层级的内容为更细致的说明或补充，进一步解释第二层级中的内容，提供更多的细节或例子，以保证报告内容全面完整。\n- **格式要求**：\n    1. 一级标题用例如1，2，3...等序号标明，标题以#开始，如#1。\n    2. 二级标题用例如1.1，1.2...等序号标明，标题以##开始，如##1.1。\n    3. 三级标题用例如1.1.1，1.1.2...等序号标明，标题以以###开始,如###1.1.1。\n    4. 注意，不要输出大纲以外的标题,输入的大纲是一级标题开始，报告内容也严格按照大纲从一级标题开始\n  具体格式参考如下：\n```markdown\n#1 一级标题\n    报告内容...\n##1.1 二级标题\n    报告内容...\n###1.1.1 三级标题\n    报告内容...\n##1.2 二级标题#\n    报告内容...\n###1.2.1 三级标题\n    报告内容...\n#2 二级标题\n    报告内容...\n##2.1 二级标题\n    报告内容...\n###2.1.1 三级标题\n    报告内容...\n...\n\n```\n### Question\n输入的大纲为：\n${content}\n根据指令要求和大纲，生成一份详细的报告\n\"\"\"',NULL,'_empty_','0','2024-10-11 00:00:00','_empty_','未知用户','2024-10-11 00:00:00'),
(6,0,'REWRITE','0','润色','全局润色','\"\"\"\n### Task\n对文本进行润色，使其保持原意的同时，确保语言更加流畅，易于理解，,输出润色后的内容\n\n### Instructions\n- 输出要求：只输出一段润色后的文本内容\n\n### Examples\n- input: 今天天气很不错\n- output:今天的天气十分宜人。\n\n### Question\n输入文本为:\n${content}\n对输入文本根据要求进行润色，输出润色后的内容\n\"\"\"',NULL,'_empty_','0','2024-10-11 00:00:00','_empty_','未知用户','2024-10-11 09:56:52'),
(7,0,'EXPAND','0','扩写','全局扩写','\"\"\"\n### Task\n参考下面的一些指令将输入文本进行扩写成一段文本，使得文本更加详细和丰富，,输出扩写后的内容\n\n### Instructions\n- 保持原意：扩写后的文本必须与原文本传达的意思一致。\n- 添加细节：在扩写中加入具体的例子、描述或背景信息，以增强内容的深度和吸引力。\n- 语言流畅：确保扩写后的文本语言自然，易于阅读。\n- 逻辑清晰：扩写的内容应当逻辑连贯，段落之间过渡自然。\n- 输出要求：只输出一段扩写后的文本内容\n\n### Examples\n- input: 今天天气很不错\n- output:今天的天气真是让人感到愉悦。清晨，太阳缓缓升起，将天空染上了一抹淡淡的橙色，微风轻拂着脸庞，带来了一丝清凉。白云像棉花糖般飘在天空中，点缀着湛蓝的天幕，仿佛一幅宁静优美的画卷。温度宜人，使人感到舒适惬意，仿佛整个世界都沐浴在温暖和谐的氛围中。\n\n### Question\n输入文本如下：\n${content}\n对输入文本根据要求进行扩写，输出扩写后的内容\n\"\"\"',NULL,'_empty_','0','2024-10-11 00:00:00','_empty_','未知用户','2024-10-11 00:00:00'),
(8,0,'CONTINUE','0','续写','全局续写','\"\"\"\n### Task\n请根据以下提供的文本开头，继续创作后续内容。请确保继续部分与原文保持风格和主题的一致性\n\n### Instructions\n- 输出要求：将续写后的内容拼接到原文本后面输出\n\n### Examples\n- input: 今天天气很不错\n- output:今天天气很不错。在这样的天气里，阳光透过树叶的缝隙洒落在地面上，形成斑驳的光影。微风吹拂着，带来丝丝清爽，树叶在风中轻轻摇曳，仿佛在轻声述说着大自然的美妙。这样的时刻，让人感受到宁静与美好并存的和谐，仿佛时间在这一刻也变得慢了下来，让人能够尽情享受当下的宁静与美好\n\n输入开头内容如下:\n${content}\n对输入文本根据要求进行续写，输出续写后的内容\n\"\"\"',NULL,'_empty_','0','2024-10-11 00:00:00','_empty_','未知用户','2024-10-11 00:00:00'),
(9,0,'SUMMARY','0','总结','全局总结','\"\"\"\n### Task\n参考下面的一些指令将输入文本进行总结，使文本更加简洁,输出总结后的内容\n\n###instructions\n- 提炼关键信息：提取文本中的主要观点和重要细节。\n- 简洁明了：总结应简洁，避免冗长的描述，确保易于理解。\n- 保持原意：确保总结后的内容忠实于原文本的意思。\n- 输出要求：只输出总结后的内容\n\n### Examples\n- input: 今天天气很不错，让人感受到宁静与美好并存的和谐，仿佛时间在这一刻也变得慢了下来。\n- output:今天天气宜人，时光仿佛在此刻放慢。\n\n输入文本如下：\n${content}\n对输入文本根据要求进行总结，输出总结后的内容\n\"\"\"',NULL,'_empty_','0','2024-10-11 00:00:00','_empty_','未知用户','2024-10-11 00:00:00'),
(10,0,'SHOTHAND','0','简写','全局简写','\"\"\"\n### Task\n请将输入的文本进行简写，同时保证简写后的文本意思不变，并尽可能使其精简，输出简写后的内容\n\n### Instructions\n- 输出要求：只输出一段简写后的文本内容\n\n### Examples\n- input: 今天天气很不错\n- output:今天天气好。\n\n### Question\n输入文本为:\n${content}\n对输入文本根据要求进行简写，输出简写后的内容\n\"\"\"',NULL,'_empty_','0','2024-10-11 00:00:00','_empty_','未知用户','2024-10-11 00:00:00'),
(11,0,'FAQ_EXTRACT','0','抽取问答对','抽取问答对','\"\"\"\n### Task\n请阅读以下已知内容，根据内容构造问答对，构造的问答对以json的格式输出[{\"question\":,\"answer\":}]\n\n已知内容：\n${content}\n\"\"\"',NULL,'_empty_','0','2024-10-11 00:00:00','_empty_','未知用户','2024-10-11 09:56:52'),
(12,0,'CUSTOMIZE','0','自定义','自定义','\"\"\"\n### Task\n严格按照下面的指令要求对输入文本进行处理,输出处理后的内容\n\n###instructions\n${customizeContent} \n- 输出要求：只输出处理后的内容\n\n输入文本如下：\n${content}\n对输入文本根据要求进行处理，输出处理后的内容\n\"\"\"',NULL,'_empty_','0','2024-10-11 00:00:00','_empty_','未知用户','2024-10-11 00:00:00');
