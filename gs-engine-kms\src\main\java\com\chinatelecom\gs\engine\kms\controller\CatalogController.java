package com.chinatelecom.gs.engine.kms.controller;


import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.common.auth.KsMenuConfig;
import com.chinatelecom.gs.engine.common.auth.MenuConfig;
import com.chinatelecom.gs.engine.common.auth.PermissionTag;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.kms.base.model.CodeParam;
import com.chinatelecom.gs.engine.kms.sdk.constants.KmsApis;
import com.chinatelecom.gs.engine.kms.sdk.vo.catalog.*;
import com.chinatelecom.gs.engine.kms.service.CatalogService;
import com.chinatelelcom.gs.engine.sdk.common.Page;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.List;


/**
* <p>
    * 目录表 Controller
    * </p>
*
* <AUTHOR>
* @since 2024-12-13
*/
@RestController
@Slf4j
@Tag(name = "目录表 Controller")
@RequestMapping({KmsApis.KMS_API + KmsApis.CATALOG,
        KmsApis.RPC + KmsApis.CATALOG,
        KmsApis.OPENAPI + KmsApis.CATALOG})
public class CatalogController {

    @Resource
    private CatalogService catalogService;

    @Operation(summary = "目录表分页列表", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "目录分页列表查询", groupName = "目录管理")
//    @AuditLog(businessType = "目录管理", operType = "目录分页列表查询", operDesc = "目录分页列表查询", objId="#param.knowledgeBaseCode")
    @PostMapping(KmsApis.PAGE_API)
    @PermissionTag(code = {KsMenuConfig.KNOWLEDGE_BASE, KsMenuConfig.KNOWLEDGE_BASE_1, MenuConfig.WORD_KNOWLEDGE, MenuConfig.QA_KNOWLEDGE,
            KsMenuConfig.KNWL_SEARCH, KsMenuConfig.KNWL_SEARCH_1, MenuConfig.ROBOT, KsMenuConfig.BOT,
            KsMenuConfig.CUSTOM_REPORT, KsMenuConfig.CUSTOM_REPORT_1, MenuConfig.CUSTOM_REPORT,
    })
    public  Result<Page<CatalogVO>> page(@Validated @RequestBody CatalogQueryParam param) {
        return Result.success(catalogService.pageQuery(param));
    }

    @Operation(summary = "目录表详情", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "目录详情查询", groupName = "目录管理")
    @AuditLog(businessType = "目录管理", operType = "目录详情查询", operDesc = "目录详情查询", objId="#code")
    @GetMapping(KmsApis.CODE_PATH)
    @PermissionTag(code = {KsMenuConfig.KNOWLEDGE_BASE, KsMenuConfig.KNOWLEDGE_BASE_1, MenuConfig.WORD_KNOWLEDGE, MenuConfig.QA_KNOWLEDGE})
    public  Result<CatalogVO> get(@PathVariable("code")  String code) {
        return Result.success(catalogService.get(code));
    }

    @Operation(summary = "目录表新增", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "目录新增", groupName = "目录管理")
    @AuditLog(businessType = "目录管理", operType = "目录新增", operDesc = "目录新增", objId="#_RESULT_.data.code")
    @PostMapping
    @PermissionTag(code = {KsMenuConfig.KNOWLEDGE_BASE, KsMenuConfig.KNOWLEDGE_BASE_1, MenuConfig.WORD_KNOWLEDGE, MenuConfig.QA_KNOWLEDGE})
    public Result<CatalogVO> add(@Validated @RequestBody CatalogCreateParam createParam) {
        CatalogVO vo = catalogService.create(createParam);
        return Result.success(vo);
    }


    @Operation(summary = "目录表更新", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "目录更新", groupName = "目录管理")
    @AuditLog(businessType = "目录管理", operType = "目录更新", operDesc = "目录更新", objId="#code")
    @PutMapping(KmsApis.CODE_PATH)
    @PermissionTag(code = {KsMenuConfig.KNOWLEDGE_BASE, KsMenuConfig.KNOWLEDGE_BASE_1, MenuConfig.WORD_KNOWLEDGE, MenuConfig.QA_KNOWLEDGE})
    public Result<Boolean> update(@PathVariable("code") String code, @Validated @RequestBody CatalogUpdateParam param) {
        return Result.success(catalogService.update(code, param));
    }

    @Operation(summary = "目录表删除", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "目录删除", groupName = "目录管理")
    @AuditLog(businessType = "目录管理", operType = "目录删除", operDesc = "目录删除", objId="#codeParam.codes")
    @PostMapping(KmsApis.DELETE_API)
    @PermissionTag(code = {KsMenuConfig.KNOWLEDGE_BASE, KsMenuConfig.KNOWLEDGE_BASE_1, MenuConfig.WORD_KNOWLEDGE, MenuConfig.QA_KNOWLEDGE})
    public Result<Boolean> delete(@Validated @RequestBody CodeParam codeParam) {
        return Result.success(catalogService.delete(codeParam));
    }

    @Operation(summary = "查询到指定节点的树形目录", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "查询到指定节点的树形目录", groupName = "目录管理")
    @AuditLog(businessType = "目录管理", operType = "查询到指定节点的树形目录", operDesc = "查询到指定节点的树形目录", objId="#catalogCode")
    @PostMapping(KmsApis.TREE_PATH_API)
    public Result<List<CatalogTreeVO>> treePath(@RequestParam @Parameter(description = "知识库编码", required = true) String knowledgeBaseCode,
                                                @RequestParam @Parameter(description = "目录编码", required = true) String catalogCode) {
        return Result.success(catalogService.treePath(knowledgeBaseCode, catalogCode));
    }

    @Operation(summary = "移动目录", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "移动目录", groupName = "目录管理")
    @AuditLog(businessType = "目录管理", operType = "移动目录", operDesc = "移动目录", objId="#code")
    @PutMapping(KmsApis.MOVE_API + KmsApis.CODE_PATH)
    @PermissionTag(code = {KsMenuConfig.KNOWLEDGE_BASE, KsMenuConfig.KNOWLEDGE_BASE_1, MenuConfig.WORD_KNOWLEDGE, MenuConfig.QA_KNOWLEDGE})
    public Result<Boolean> move(@PathVariable("code") String code, @Validated @RequestBody CatalogMoveParam moveParam) {
        catalogService.move(code, moveParam);
        return Result.success(true);
    }

    @Operation(summary = "批量移动目录", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "批量移动目录", groupName = "目录管理")
    @AuditLog(businessType = "目录管理", operType = "批量移动目录", operDesc = "批量移动目录", objId="#moveParam.codes")
    @PutMapping(KmsApis.MOVE_API)
    @PermissionTag(code = {KsMenuConfig.KNOWLEDGE_BASE, KsMenuConfig.KNOWLEDGE_BASE_1, MenuConfig.WORD_KNOWLEDGE, MenuConfig.QA_KNOWLEDGE})
    public Result<Boolean> move(@Validated @RequestBody CatalogBatchMoveParam moveParam) {
        for (String code : moveParam.getCodes()) {
            catalogService.move(code, moveParam);
        }
        return Result.success(true);
    }
}


