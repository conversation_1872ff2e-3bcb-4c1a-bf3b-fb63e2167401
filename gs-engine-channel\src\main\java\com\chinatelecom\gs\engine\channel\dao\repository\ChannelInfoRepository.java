package com.chinatelecom.gs.engine.channel.dao.repository;


import com.baomidou.mybatisplus.extension.service.IService;
import com.chinatelecom.gs.engine.channel.common.UidUtils;
import com.chinatelecom.gs.engine.channel.common.enums.ChannelTypeEnum;
import com.chinatelecom.gs.engine.channel.dao.po.ChannelInfoPO;
import com.chinatelecom.gs.engine.channel.dao.po.YN;
import com.chinatelecom.gs.engine.channel.service.dto.ChannelInfoDTO;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.context.RequestInfo;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.data.ChannelInfoVO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/22 14:12
 * @description
 */
public interface ChannelInfoRepository extends IService<ChannelInfoPO> {
    /**
     * 修改渠道配置
     *
     * @param channelInfo ChannelInfoPO
     * @return Boolean
     */
    Boolean updateByChannelId(ChannelInfoPO channelInfo);

    String addChannelInfo(String agentCode, ChannelTypeEnum channelTypeEnum);

    ChannelInfoDTO getChannelInfo(String channelId, String agentCode);

    List<ChannelInfoVO> getAllChannelInfo();

    boolean channelOperateEnable(String agentCode, String channelTypeEnum, boolean enable);

    List<ChannelInfoDTO> getChannelInfoList(String agentCode, List<String> channelTypeList);

    void removeByChannelId(String channelId);

    static ChannelInfoPO getDefaultChannel(String agentCode, ChannelTypeEnum channelTypeEnum) {
        return getDefaultChannel(agentCode, channelTypeEnum, UidUtils.randomString());

    }

    static ChannelInfoPO getDefaultChannelWithNullChannelId(String agentCode, ChannelTypeEnum channelTypeEnum) {
        return getDefaultChannel(agentCode, channelTypeEnum, null);

    }

    static ChannelInfoPO getDefaultChannel(String agentCode, ChannelTypeEnum channelTypeEnum, String channelId) {
        ChannelInfoPO po = new ChannelInfoPO();
        po.setChannelName(channelTypeEnum.getDesc());
        po.setChannelType(channelTypeEnum);
        po.setChannelId(channelId);
        po.setAgentCode(agentCode);
        po.setEnable(false);
        RequestInfo userInfo = RequestContext.get();
        if (userInfo != null){
            po.setTenantId(userInfo.getTenantId());
            po.setYn(YN.YES.getValue());
            po.setCreateId(userInfo.getUserId());
            po.setCreateName(userInfo.getUserName());
            po.setCreateTime(LocalDateTime.now());
            po.setUpdateId(userInfo.getUserId());
            po.setUpdateName(userInfo.getUserName());
            po.setUpdateTime(LocalDateTime.now());
        }
        return po;
    }

}
