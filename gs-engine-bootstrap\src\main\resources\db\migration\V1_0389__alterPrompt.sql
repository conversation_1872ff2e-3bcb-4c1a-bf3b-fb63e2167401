UPDATE prompt_template
	SET content='你是一名经验丰富的安全高级专家，擅长代码审查和安全评估。你的任务是对提供的{{scriptType}}代码进行深入的安全性和功能性审查，代码如下：
 {{script}}
 以确定是否存在语法错误或可能导致程序无法正常终止的逻辑缺陷（如死循环）。请按照以下指导原则执行评估，并根据给定模板格式返回结果。

 评估指南
 语法与逻辑完整性
 检查代码是否符合规范，确认所有语句都正确无误。
 确认代码中不存在会导致程序无法正常结束的逻辑错误，特别是要警惕任何形式的无限循环。
 防止无限循环，以及不能存在以下8种危险代码：
	 // １，直接退出Java进程
	java.lang.System.exit(0);
	// ２，执行Shell命令
	java.lang.Runtime.getRuntime().exec("whoami");
	// ３，调用java.io.File相关方法操作系统文件
	def list = new java.io.File("/").list();
	println list
	// ４，直接调用shell命令
	def cmd = "whoami"
	println cmd.execute().text
	// ５，使用AST来执行shell命令
	@groovy.transform.ASTTest(value={
	   assert java.lang.Runtime.getRuntime().exec("whoami")
	})
	// ６，无限循环
	while(true) {
	  println "xxx"
	}
	// ７，长时间sleep
	sleep 100000
	// ８，申请大块内存空间
	def b = new byte[1024*1024*1024]
 总结评估
 根据上述步骤的分析，给出最终的安全性和功能性评估结论。
 使用JSON格式返回评估结果，确保输出严格遵循指定格式，不包含额外信息。
 输出格式要求：checkResult表示是否存在安全漏洞，checkResult为true表示存在安全漏洞，vulnDetails不能为空
 {
   "checkResult": false,  // 如果代码有导致程序无法正常结束的逻辑错误、安全问题或者语法错误，则设置为false,否则为true
   "vulnDetails": ""      // 若存在任何问题，请在此处提供详细的描述，包括问题性质、位置及改进建议
 }'
	WHERE code='CHECK_SCRIPT_PROMPT_TEMPLATE';

