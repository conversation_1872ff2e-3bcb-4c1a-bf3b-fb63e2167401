package com.chinatelecom.gs.privilege.service.impl;

import com.chinatelecom.cloud.common.pojo.BaseResult;
import com.chinatelecom.cloud.common.pojo.PageResult;
import com.chinatelecom.cloud.common.pojo.Pager;
import com.chinatelecom.cloud.platform.client.rpc.CorpRes;
import com.chinatelecom.cloud.platform.client.rpc.UserInfo;
import com.chinatelecom.cloud.platform.client.util.AuthUtils;
import com.chinatelecom.gs.privilege.service.UserService;
import com.chinatelecom.gs.privilege.service.dto.ObjectCheckInfo;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.Redisson;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.chinatelecom.gs.privilege.service.AuthInfoSyncScheduler.taskPeriod;

@Slf4j
@Service
public class UserServiceImpl implements UserService {
    @Resource
    private Redisson redisson;

    private static final String USER_CACHE_CONSTRUCT_LOCK = "access_privilege_user_construct_lock";

    private static final String USER_REFRESH_TIMESTAMP_KEY = "access_privilege_user_refresh_time";

    private static final String USER_ID_CACHE_KEY = "access_privilege_user_id_checkobjects";

    private static final String USER_CACHE_KEY = "access_privilege_user_list";

    @Override
    public Boolean isUserIdExist(String corpCode, List<ObjectCheckInfo> userObjects) {
        Map<String, Map<String, ObjectCheckInfo>> existUserIds = redisson.getMap(USER_ID_CACHE_KEY);
        if (existUserIds == null || existUserIds.get(corpCode) == null) {
            log.error("【权限模块】团队{}的用户信息不存在", corpCode);
            return false;
        }

        Map<String, ObjectCheckInfo> corpObjects = existUserIds.get(corpCode);

        List<String> objectIds = Optional.ofNullable(userObjects)
                .orElse(Collections.emptyList())
                .stream()
                .map(ObjectCheckInfo::getObjectId)
                .collect(Collectors.toList());
        if (!corpObjects.keySet().containsAll(objectIds)) {
            return false;
        }

        for (ObjectCheckInfo oci : userObjects) {
            ObjectCheckInfo storedOci = corpObjects.get(oci.getObjectId());
            if (!oci.getObjectName().equalsIgnoreCase(storedOci.getObjectName())) {
                return false;
            }
        }

        return true;
    }


    @Override
    public void construct() {
        log.info("【权限模块】开始构建用户列表");

        RLock lock = redisson.getLock(USER_CACHE_CONSTRUCT_LOCK);
        if (lock == null) {
            log.error("【权限模块】构建用户列表，获取分布式锁为空");
            return;
        }

        List<String> corpCodes;
        try {
            boolean lockStatus = lock.tryLock(5, TimeUnit.SECONDS);
            if (!lockStatus) {
                log.error("【权限模块】构建用户列表，分布式锁加锁失败,lockStatus false");
                return;
            }
            long teamStart = System.currentTimeMillis();

            RBucket<Long> lastRefreshTime = redisson.getBucket(USER_REFRESH_TIMESTAMP_KEY);
            if (lastRefreshTime == null || lastRefreshTime.get() == null) {
                log.warn("【权限模块】上次构建用户列表刷新时间不存在，执行本次更新");
            } else if (lastRefreshTime.get() + taskPeriod > teamStart){
                log.warn("【权限模块】上次刷新时间小于时间间隔，已经有其他实例更新用户列表，本次退出");
                return;
            }

            BaseResult<List<CorpRes>> corpResult = AuthUtils.getCorpByAppCode();
            if (!corpResult.ifSuccess() || CollectionUtils.isEmpty(corpResult.getData())) {
                log.error("【权限模块】获取用户下的租户列表失败,baseResult:{}", corpResult);
                return;
            }

            corpCodes = corpResult.getData().stream().map(CorpRes::getCorpCode).collect(Collectors.toList());
            for (String corpCode : corpCodes) {
                try {
                    log.info("【权限模块】开始更新租户{}的用户列表", corpCode);
                    long corpStart = System.currentTimeMillis();
                    List<UserInfo> corpUsers = getCorpUsers(corpCode);
                    if (CollectionUtils.isEmpty(corpUsers)) {
                        log.warn("【权限模块】corp:{}获取用户信息为空", corpCode);
                        continue;
                    }

                    // 用户写入缓存
                    Map<String, List<UserInfo>> corpUserList = redisson.getMap(USER_CACHE_KEY);
                    corpUserList.put(corpCode, corpUsers);

                    log.info("【权限模块】corp:{}写入用户信息到缓存中，size:{}", corpUsers.size());
                    // 用户id写入缓存，用于判断用户是否存在
                    Map<String, ObjectCheckInfo> corpUserIdSet = corpUsers.stream()
                            .map(u -> new ObjectCheckInfo(u.getUserId(), u.getName()))
                            .collect(Collectors.toMap(ObjectCheckInfo::getObjectId, Function.identity()));
                    Map<String, Map<String, ObjectCheckInfo>> hash = redisson.getMap(USER_ID_CACHE_KEY);
                    hash.put(corpCode, corpUserIdSet);

                    log.info("【权限模块】corp：{}的用户列表构建耗时:{}ms", corpCode, (System.currentTimeMillis() - corpStart));
                } catch (Exception e) {
                    log.warn("租户用户列表构造失败：{}", corpCode, e);
                }
            }
            long teamEnd = System.currentTimeMillis();
            log.info("【权限模块】重构用户列表完成,耗时:{}ms", teamEnd - teamStart);
            if (lastRefreshTime != null) {
                lastRefreshTime.set(teamEnd);
            }
        } catch (Exception e) {
            log.error("获取租户下的所有公司corpCode失败", e);
        } finally {
            lock.unlock();
        }
    }

    private List<UserInfo> getCorpUsers(String corpCode) {
        Pager pager = new Pager();

        long pageSize = 2000L;
        long pageIdx = 1L;
        List<UserInfo> userInfoList = new ArrayList<>();
        int queriedCount = 0;

        do {
            pager.setPage(pageIdx);
            pager.setPageSize(pageSize);
            queriedCount = 0;
            try {
                BaseResult<PageResult<UserInfo>> result = AuthUtils.getAllUserList(null, pager, corpCode);
                if (!result.ifSuccess()) {
                    log.error("【权限模块】corp:{}获取用户信息返回结果失败{}", corpCode, result);
                    break;
                }

                if (result.getData() == null) {
                    log.error("【权限模块】corp:{}获取用户信息返回结果为null{}", corpCode, result);
                    break;
                }

                PageResult<UserInfo> pageResult = result.getData();
                List<UserInfo> records = pageResult.getRecords();
                if (CollectionUtils.isEmpty(records)) {
                    log.error("【权限模块】corp:{}获取用户信息返回结果为空{}", corpCode, result);
                    break;
                } else {
                    queriedCount = records.size();
                    userInfoList.addAll(records);
                }
                pageIdx++;
            } catch (Exception e) {
                log.error("【权限模块】corp:{}获取用户信息异常", corpCode, e);
                break;
            }
        } while (queriedCount > 0);

        return userInfoList;
    }
}
