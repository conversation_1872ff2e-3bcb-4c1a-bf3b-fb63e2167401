package com.chinatelecom.gs.engine.core.entity.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.core.entity.domain.po.EntityPO;
import com.chinatelecom.gs.engine.core.entity.domain.query.EntityQuery;
import com.chinatelecom.gs.engine.core.entity.domain.request.EntityDeleteRequest;
import com.chinatelecom.gs.engine.core.entity.mapper.EntityMapper;
import com.chinatelecom.gs.engine.core.entity.service.EntityDataService;
import com.chinatelecom.gs.engine.robot.sdk.v2.enums.EntityTypeEnum;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.entity.EntityDetailVO;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.entity.EntityVO;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * @Author: pengmc1
 * @Date: 2025/4/5
 * @Description: EntityServiceImpl 单元测试
 */
public class EntityServiceImplTest {

    @InjectMocks
    private EntityServiceImpl entityService;

    @Mock
    private EntityMapper entityMapper;

    @Mock
    private EntityDataService entityDataService;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testGetEntity_Success() {
        // Mock RequestContext
        try (MockedStatic<RequestContext> mocked = mockStatic(RequestContext.class)) {
            mocked.when(RequestContext::getTenantId).thenReturn("tenant_001");

            EntityPO po = new EntityPO();
            po.setEntityCode("entity_001");
            po.setEntityType("TYPE1");
            po.setTenantId("tenant_001");

            when(entityMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(po);

            EntityVO vo = entityService.getEntity("entity_001");

            assertNotNull(vo);
            assertEquals("entity_001", vo.getEntityCode());
            assertEquals(EntityTypeEnum.getByCode("TYPE1"), vo.getEntityType());
        }
    }

    @Test
    public void testGetEntityDetail_NotFound() {
        try (MockedStatic<RequestContext> mocked = mockStatic(RequestContext.class)) {
            mocked.when(RequestContext::getTenantId).thenReturn("tenant_001");

            when(entityMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);

            EntityDetailVO vo = entityService.getEntityDetail("entity_001");

            assertNull(vo);
        }
    }


    @Test
    public void testGetEntityList_Success() {
        try (MockedStatic<RequestContext> mocked = mockStatic(RequestContext.class)) {
            mocked.when(RequestContext::getTenantId).thenReturn("tenant_001");

            EntityPO po1 = new EntityPO();
            po1.setEntityCode("entity_001");
            po1.setEntityType("TYPE1");
            po1.setTenantId("tenant_001");

            EntityPO po2 = new EntityPO();
            po2.setEntityCode("entity_002");
            po2.setEntityType("TYPE2");
            po2.setTenantId("tenant_001");

            when(entityMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Arrays.asList(po1, po2));

            List<EntityVO> vos = entityService.getEntityList(Arrays.asList("entity_001", "entity_002"));

            assertFalse(CollectionUtils.isEmpty(vos));
            assertEquals(2, vos.size());
        }
    }

    @Test
    public void testAddEntity_AlreadyExists() {
        try (MockedStatic<RequestContext> mocked = mockStatic(RequestContext.class)) {
            mocked.when(RequestContext::getTenantId).thenReturn("tenant_001");

            EntityDetailVO vo = new EntityDetailVO();
            vo.setEntityCode("entity_001");

            when(entityMapper.selectObjs(any(LambdaQueryWrapper.class))).thenReturn(Collections.singletonList(new Object()));

            BizException exception = assertThrows(BizException.class, () -> {
                entityService.addEntity(vo);
            });

            assertEquals("A0069", exception.getCode());
        }
    }

    @Test
    public void testAddEntity_Success() {
        try (MockedStatic<RequestContext> mocked = mockStatic(RequestContext.class)) {
            mocked.when(RequestContext::getTenantId).thenReturn("tenant_001");

            EntityDetailVO vo = new EntityDetailVO();
            vo.setEntityCode("entity_001");
            vo.setEntityType(EntityTypeEnum.NORMAL_ENTITY);
            vo.setValidatorSwitch(true);

            when(entityMapper.selectObjs(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());
            when(entityMapper.insert(any(EntityPO.class))).thenReturn(1);
            when(entityDataService.saveEntityData(any(EntityDetailVO.class))).thenReturn(true);

            Boolean result = entityService.addEntity(vo);

            assertTrue(result);
        }
    }

    @Test
    public void testUpdateByCode_NotExists() {
        try (MockedStatic<RequestContext> mocked = mockStatic(RequestContext.class)) {
            mocked.when(RequestContext::getTenantId).thenReturn("tenant_001");

            EntityDetailVO vo = new EntityDetailVO();
            vo.setEntityCode("entity_001");

            when(entityMapper.selectObjs(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

            BizException exception = assertThrows(BizException.class, () -> {
                entityService.updateByCode(vo);
            });

            assertEquals("A0004", exception.getCode());
        }
    }

    @Test
    public void testUpdateByCode_Success() {
        try (MockedStatic<RequestContext> mocked = mockStatic(RequestContext.class)) {
            mocked.when(RequestContext::getTenantId).thenReturn("tenant_001");

            EntityDetailVO vo = new EntityDetailVO();
            vo.setEntityCode("entity_001");
            vo.setEntityType(EntityTypeEnum.NORMAL_ENTITY);
            vo.setValidatorSwitch(true);

            when(entityMapper.selectObjs(any(LambdaQueryWrapper.class))).thenReturn(Collections.singletonList(new Object()));
            when(entityMapper.update(any(EntityPO.class), any(LambdaQueryWrapper.class))).thenReturn(1);
            when(entityDataService.saveEntityData(any(EntityDetailVO.class))).thenReturn(true);

            Boolean result = entityService.updateByCode(vo);

            assertTrue(result);
        }
    }

    @Test
    public void testDelete_Success() {
        try (MockedStatic<RequestContext> mocked = mockStatic(RequestContext.class)) {
            mocked.when(RequestContext::getTenantId).thenReturn("tenant_001");

            EntityDeleteRequest request = new EntityDeleteRequest();
            request.setEntityCodes(Arrays.asList("entity_001", "entity_002"));
            when(entityMapper.delete(any(LambdaQueryWrapper.class))).thenReturn(2);
            when(entityDataService.delete(anyList())).thenReturn(true);

            Boolean result = entityService.delete(request);

            assertTrue(result);
        }
    }

    @Test
    public void testQueryEntityDetailList_WithQuery() {
        try (MockedStatic<RequestContext> mocked = mockStatic(RequestContext.class)) {
            mocked.when(RequestContext::getTenantId).thenReturn("tenant_001");

            EntityQuery query = new EntityQuery();
            query.setEntityName("test");
            query.setPageNum(1);
            query.setPageSize(10);

            PageDTO<EntityPO> page = new PageDTO<>();
            EntityPO entityPO = new EntityPO();
            entityPO.setValidatorSwitch(1);
            page.setRecords(Collections.singletonList(entityPO));
            page.setTotal(1L);

            when(entityMapper.selectPage(any(PageDTO.class), any(LambdaQueryWrapper.class))).thenReturn(page);

            var result = entityService.queryEntityDetailList(query);

            assertNotNull(result);
            assertEquals(1, result.getTotal());
        }
    }
}
