//package com.chinatelecom.csbotplatform.channel.api.config;
//
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
//import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
//import org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport;
//
///**
// * <AUTHOR>
// * @date 2024/1/5 11:31
// * @description
// */
//@Configuration
//public class SpringMVCConfig extends WebMvcConfigurationSupport {
//
//    @Autowired
//    private WhiteListWebInterceptor whiteListWebInterceptor;
//
//    @Override
//    protected void addResourceHandlers(ResourceHandlerRegistry registry) {
//        registry.addResourceHandler("/loginpage.html").addResourceLocations("classpath:/templates/loginpage.html");
//        registry.addResourceHandler("/error.html").addResourceLocations("classpath:/templates/error.html");
//        registry.addResourceHandler("/**").addResourceLocations("classpath:/static/");
//        registry.addResourceHandler("doc.html").addResourceLocations("classpath:/META-INF/resources/");
//        registry.addResourceHandler("/webjars/**").addResourceLocations("classpath:/META-INF/resources/webjars/");
//    }
//
//
//    @Override
//    protected void addInterceptors(InterceptorRegistry registry) {
//        registry.addInterceptor(whiteListWebInterceptor).addPathPatterns("/qywx/**");
//    }
//}
