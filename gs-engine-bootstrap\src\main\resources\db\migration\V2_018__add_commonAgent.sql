CREATE TABLE IF NOT EXISTS `common_agent` (
   `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键 ID',
   `agent_code` varchar(100) DEFAULT NULL COMMENT '机器人编码',
   `agent_name` varchar(255) NOT NULL COMMENT '机器人名称',
   `scope` varchar(100) DEFAULT NULL COMMENT '机器人类型，系统机器人、自定义机器人',
   `tag` json DEFAULT NULL COMMENT '标签',
   `tenant_id` varchar(128) DEFAULT NULL COMMENT '租户 ID',
   `create_id` varchar(100) DEFAULT NULL COMMENT '创建用户 ID',
   `create_name` varchar(225) DEFAULT NULL COMMENT '创建用户名称',
   `create_time` datetime DEFAULT NULL COMMENT '创建用户时间',
   `update_id` varchar(255) DEFAULT NULL COMMENT '最后修改用户 ID',
   `update_name` varchar(255) DEFAULT NULL COMMENT '最后修改用户名称',
   `update_time` datetime DEFAULT NULL COMMENT '更新时间',
   `yn` int DEFAULT '0' COMMENT '删除标记，0-未删除，其他表示已删除',
   PRIMARY KEY (`id`),
   KEY `agent_code` (`agent_code`)
 ) ENGINE=InnoDB AUTO_INCREMENT=55 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='公共机器人';