update agent_default_config set config_value = '{"chatLLMSwitch":false,"globalVarSwitch": false,"securityFenceSwitch": false,"securityFenceScript": "检测到您输入的内容可能不太合适哦~请换一种表达方式吧!感谢您的理解!","globalVarList": null,"noAnswerCount": 3,"noAnswerCountSwitch": true,"noAnswerCountType": "CONTINUE_TRIGGER","manuCount": 3,"manuCountSwitch": true,"manuCountType": "CONTINUE_TRIGGER","manuKeyWordList": ["转人工"],"manuScript": "您好，为了更好地帮助您，我将为您转接人工客服，请稍等片刻，感谢您的理解!", "manuInstructCode": "system_to_manual"}' where config_key = "agentConfig.global.policy";

UPDATE agent_config
SET config_value = CASE
                       WHEN config_value NOT LIKE '%manuInstructCode%' THEN REPLACE(config_value, '}', ',"manuInstructCode":"system_to_manual"}')
                       ELSE config_value
    END
WHERE config_value NOT LIKE '%manuInstructCode%' AND config_key = 'agentConfig.global.policy';