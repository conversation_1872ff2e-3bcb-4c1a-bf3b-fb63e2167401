package com.chinatelecom.gs.engine.robot.sdk.v2.spi.plugin;

import com.chinatelecom.gs.engine.robot.sdk.dto.Result;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.plugin.request.PluginMetaRequest;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.plugin.response.PluginMetaResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "gs-engine-robot", url = "${gs.kmsRpcConfig.gsUrl:}", contextId = "PluginAndApiInfoClient", path = "/ais/plugin/rpc/pluginInfo")
public interface PluginAndApiInfoClient {

    @Operation(summary = "创建插件基础信息", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "插件管理")})})
    @PostMapping("/registerPluginMeta")
    Result<PluginMetaResponse> create(@RequestBody PluginMetaRequest pluginMetaRequest);

}
