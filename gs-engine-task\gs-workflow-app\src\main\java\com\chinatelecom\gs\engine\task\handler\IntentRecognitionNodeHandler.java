package com.chinatelecom.gs.engine.task.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson2.JSON;
import com.chinatelecom.gs.engine.common.utils.IdGenerator;
import com.chinatelecom.gs.engine.common.utils.LlmUtil;
import com.chinatelecom.gs.engine.core.model.service.IntentRecognitionService;
import com.chinatelecom.gs.engine.core.model.toolkit.StreamingChatLanguageModel;
import com.chinatelecom.gs.engine.core.model.toolkit.core.dto.LLMRequest;
import com.chinatelecom.gs.engine.core.model.toolkit.core.dto.ModelProviderEnum;
import com.chinatelecom.gs.engine.core.model.toolkit.core.dto.Response;
import com.chinatelecom.gs.engine.core.sdk.rpc.ModelServiceClient;
import com.chinatelecom.gs.engine.core.sdk.vo.ClassificatIntent;
import com.chinatelecom.gs.engine.core.sdk.vo.IntentionRecognitionParam;
import com.chinatelecom.gs.engine.core.sdk.vo.IntentionRecognitionResponse;
import com.chinatelecom.gs.engine.core.sdk.vo.ModelPageListParam;
import com.chinatelecom.gs.engine.kms.sdk.vo.prompt.PromptTemplateVO;
import com.chinatelecom.gs.engine.kms.service.PromptTemplateAppService;
import com.chinatelecom.gs.engine.task.config.WorkflowConfig;
import com.chinatelecom.gs.engine.task.sdk.x6.Node;
import com.chinatelecom.gs.workflow.core.workflow.core.context.DagContext;
import com.chinatelecom.gs.workflow.core.workflow.core.enums.NodeTypeEnum;
import com.chinatelecom.gs.workflow.core.workflow.core.handler.AbstractNodeHandler;
import com.chinatelecom.gs.workflow.core.workflow.core.model.biz.param.IntentRecognitionParam;
import com.chinatelecom.gs.workflow.core.workflow.core.model.node.DagNode;
import com.chinatelecom.gs.workflow.core.workflow.core.model.result.NodeResult;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 大模型意图识别节点
 *
 * @USER: pengmc1
 * @DATE: 2024/8/27 9:45
 */

@Slf4j
@Component
public class IntentRecognitionNodeHandler extends AbstractNodeHandler<Map<String, Object>> {

    @Resource
    private ModelServiceClient remoteServiceClient;

    @Resource
    private StreamingChatLanguageModel streamingChatLanguageModel;

    @Resource
    private WorkflowConfig workflowConfig;

    @Resource
    private PromptTemplateAppService promptTemplateAppService;

    @Resource
    private ModelServiceClient modelServiceClient;

    @Resource
    private IntentRecognitionService intentRecognitionService;

    private static final String INTENT_CHOOSE_PROMPT_TEMPLATE = "INTENT_CHOOSE_PROMPT_TEMPLATE";

    private static final String INTENT_PORTID_KEY = "intentPortId";

    private static final String QUERY = "Query";

    private static final String OTHER_INTENT_KEY = "其它";

    /**
     * 处理的节点类型
     *
     * @return
     */
    @Override
    public NodeTypeEnum nodeType() {
        return NodeTypeEnum.INTENT_RECOGNITION;
    }

    /**
     * 执行任务
     *
     * @param param   输入参数
     * @param node    节点数据
     * @param context 上下文
     * @return
     * @throws Exception
     */
    @Override
    public NodeResult doExecute(Map<String, Object> param, DagNode node, DagContext context) throws Exception {
        IntentRecognitionParam intentRecognitionParam = node.getBizParam(IntentRecognitionParam.class);
        // 全部为关联意图时不进行大模型识别
        IntentionRecognitionResponse intentionRecognitionResponse = null;
        List<String> refIntentList = intentRecognitionParam.getIntents().stream().filter(f -> CharSequenceUtil.isNotBlank(f.getInputType())
                && f.getInputType().equals("ref")).map(ClassificatIntent::getId).collect(Collectors.toList());
        // 输入意图
        List<ClassificatIntent> classificatIntentList = getIntents(intentRecognitionParam);
        IntentResult intentResult = null;
        if (CollectionUtils.isEmpty(refIntentList) && classificatIntentList.size() > 1) {
            // 如果全是输入意图
            ModelPageListParam modelParam = remoteServiceClient.queryByModelCode(intentRecognitionParam.getModelCode());
            HashMap<String, Object> variable = getVariable(param, classificatIntentList, intentRecognitionParam);
            String intentChoosePromptTemplate = getPromptTemplate();
            String promptTemplate = LlmUtil.replaceVariables(intentChoosePromptTemplate, variable);
            LLMRequest llmRequest = buildRequest(modelParam, intentRecognitionParam, promptTemplate);
            Response<String> response = streamingChatLanguageModel.syncGenerate(llmRequest);
            log.info("模型返回信息: {}", response.content());
            String content = response.content();
            content = LlmUtil.toJson(content);
            intentResult = JSON.parseObject(content, IntentResult.class);
        }

        if (CollUtil.isNotEmpty(refIntentList)) {
            // 如果存在关联意图，输入意图和关联意图大模型识别合并
            IntentionRecognitionParam recognitionParam = new IntentionRecognitionParam();
            recognitionParam.setQuery(param.get(QUERY).toString());
            recognitionParam.setIntentList(refIntentList);
            recognitionParam.setModelCode(intentRecognitionParam.getModelCode());
            recognitionParam.setSessionId(context.getSessionId());
            recognitionParam.setIsSystemAgent(context.getDagParam().getIsSystemAgent());
            // 如果既有关联意图，又有输入意图，则合并一起走大模型
            recognitionParam.setClassificatIntents(classificatIntentList);
            intentionRecognitionResponse = intentRecognitionService.intentRecognition(recognitionParam);
        }
        // 匹配意图分支
        return matchIntentBranch(node, context, intentResult, classificatIntentList, intentionRecognitionResponse, intentRecognitionParam);
    }

    /**
     * 全部为关联意图时不进行大模型识别
     *
     * @param node
     * @param context
     * @param intentionRecognitionResponse
     * @param intentRecognitionParam
     * @return
     */
    private NodeResult<?> matchIntentBranch(DagNode node, DagContext context, IntentionRecognitionResponse intentionRecognitionResponse, IntentRecognitionParam intentRecognitionParam) {
        List<String> intentPortList = new ArrayList<>();
        List<String> chooseIntentList = new ArrayList<>();

        if (intentionRecognitionResponse != null) {
            addMatchingBranches(node, intentionRecognitionResponse.getName(), intentPortList, chooseIntentList);
        }

        if (CollUtil.isEmpty(intentPortList)) {
            // 未找到对应的意图分支，返回失败信息
            return NodeResult.fail("未找到对应的分支");
        }

        chooseIntentList = chooseIntentList.stream().distinct().collect(Collectors.toList());
        intentPortList = intentPortList.stream().distinct().collect(Collectors.toList());

        context.getNodeInfo(node.getNodeId()).put(INTENT_PORTID_KEY, intentPortList);

        List<ClassificatIntent> classificatIntentBranchList = intentRecognitionParam.getIntents();
        HashMap<String, Integer> intentIndexMap = buildIntentIndexMap(classificatIntentBranchList, null);
        IntentResult intentResults = new IntentResult();
        for (String intentName : chooseIntentList) {
            Integer intentIndex = intentIndexMap.get(intentName);
            if (intentIndex != null) {
                intentResults.setClassificationId(intentIndex);
                intentResults.setReason(intentName);
            }
        }
        return NodeResult.success(intentResults, chooseIntentList);
    }

    /**
     * 匹配意图分支
     *
     * @param node                         DagNode
     * @param context                      DagContext
     * @param intentResult                 IntentResult
     * @param classificatIntentList                   List<Intent>
     * @param intentionRecognitionResponse IntentionRecognitionResponse
     * @param intentRecognitionParam       IntentRecognitionParam
     * @return NodeResult
     */
    private NodeResult<?> matchIntentBranch(DagNode node, DagContext context, IntentResult intentResult, List<ClassificatIntent> classificatIntentList, IntentionRecognitionResponse intentionRecognitionResponse, IntentRecognitionParam intentRecognitionParam) {
        if (classificatIntentList == null || node == null || context == null || intentRecognitionParam == null) {
            return NodeResult.fail("输入参数不能为空");
        }

        Long classificationId = null;
        if (intentResult != null && intentResult.getClassificationId() != null) {
            classificationId = Optional.ofNullable(intentResult.getClassificationId()).map(Number::longValue).orElse(null);
        }

        Map<Long, ClassificatIntent> intentMap = classificatIntentList.stream()
                .collect(Collectors.toMap(ClassificatIntent::getClassificationId, Function.identity()));
        ClassificatIntent classificatIntent = null;
        if (Objects.nonNull(classificationId)) {
            classificatIntent = intentMap.get(classificationId);
        }

        List<String> intentPortList = new ArrayList<>();
        List<String> chooseIntentList = new ArrayList<>();

        // 关联意图  规则>分类模型>相似问>大模型（关联意图大模型召回和数意图召回同优先级）
        if (intentionRecognitionResponse != null) {
            addMatchingBranches(node, intentionRecognitionResponse.getName(), intentPortList, chooseIntentList);
        }

        if (CollUtil.isEmpty(intentPortList) && Objects.nonNull(classificatIntent)) {
            // 如果管理意图没召回，则在输入意图中召回一个
            addMatchingBranches(node, classificatIntent.getName(), intentPortList, chooseIntentList);
        }

        if (CollUtil.isEmpty(intentPortList) && Objects.isNull(intentionRecognitionResponse)) {
            // 未找到对应的意图分支
            addMatchingBranches(node, "其它", intentPortList, chooseIntentList);
        }

        chooseIntentList = chooseIntentList.stream().distinct().collect(Collectors.toList());
        intentPortList = intentPortList.stream().distinct().collect(Collectors.toList());

        context.getNodeInfo(node.getNodeId()).put(INTENT_PORTID_KEY, intentPortList);

        List<ClassificatIntent> classificatIntentBranchList = intentRecognitionParam.getIntents();
        HashMap<String, Integer> intentIndexMap = buildIntentIndexMap(classificatIntentBranchList, classificatIntentList);
        IntentResult intentResults = new IntentResult();
        for (String intentName : chooseIntentList) {
            Integer intentIndex = intentIndexMap.get(intentName);
            if (intentIndex != null) {
                intentResults.setClassificationId(intentIndex);
                intentResults.setReason(intentName);
            }
        }
        return NodeResult.success(intentResults, chooseIntentList);
    }

    /**
     * 添加匹配的分支
     *
     * @param node             DagNode
     * @param portName         端口名称
     * @param intentPortList   意图端口列表
     * @param chooseIntentList 选择的意图列表
     */
    private void addMatchingBranches(DagNode node, String portName, List<String> intentPortList, List<String> chooseIntentList) {
        if (CharSequenceUtil.isNotBlank(portName)) {
            for (Node.Branch nodeBranch : Optional.ofNullable(node.getBranches()).orElse(Collections.emptyList())) {
                if (CharSequenceUtil.isNotBlank(nodeBranch.getPortName()) && nodeBranch.getPortName().equals(portName)) {
                    intentPortList.add(nodeBranch.getPortId());
                    chooseIntentList.add(nodeBranch.getPortName());
                }
            }
        }
    }

    /**
     * 构建意图索引映射
     *
     * @param classificatIntentBranchList 意图分支列表
     * @param classificatIntentList       意图列表
     * @return 意图索引映射
     */
    private HashMap<String, Integer> buildIntentIndexMap(List<ClassificatIntent> classificatIntentBranchList, List<ClassificatIntent> classificatIntentList) {
        HashMap<String, Integer> intentIndexMap = new HashMap<>();
        for (int i = 0; i < classificatIntentBranchList.size(); i++) {
            ClassificatIntent branchClassificatIntent = classificatIntentBranchList.get(i);
            intentIndexMap.put(branchClassificatIntent.getName(), i);
        }
        intentIndexMap.put(OTHER_INTENT_KEY, classificatIntentBranchList.size());
        return intentIndexMap;
    }

    private HashMap<String, Object> getVariable(Map<String, Object> param, List<ClassificatIntent> classificatIntentList, IntentRecognitionParam intentRecognitionParam) {
        HashMap<String, Object> variable = new HashMap<>();
        variable.put("intents", JSON.toJSONString(classificatIntentList));
        variable.put(QUERY, param.get(QUERY));
        // 处理系统提示词
        String systemPrompt = LlmUtil.replaceVariables(intentRecognitionParam.getSystemPrompt(), param);
        variable.put("systemPrompt", systemPrompt);
        return variable;
    }


    private List<ClassificatIntent> getIntents(IntentRecognitionParam intentRecognitionParam) {
        List<ClassificatIntent> classificatIntentList = intentRecognitionParam.getIntents();
        List<ClassificatIntent> inputClassificatIntentList = new ArrayList<>();
        for (int i = 0; i < classificatIntentList.size(); i++) {
            if ("literal".equals(classificatIntentList.get(i).getInputType())) {
                ClassificatIntent classificatIntent = classificatIntentList.get(i);
                classificatIntent.setClassificationId((long) i);
                inputClassificatIntentList.add(classificatIntent);
            }
        }
        ClassificatIntent otherClassificatIntent = new ClassificatIntent();
        otherClassificatIntent.setName("其它");
        otherClassificatIntent.setClassificationId((long) classificatIntentList.size());
        otherClassificatIntent.setInputType("literal");
        otherClassificatIntent.setId(IdGenerator.id());
        inputClassificatIntentList.add(otherClassificatIntent);
        return inputClassificatIntentList;
    }


    /**
     * 运行后续节点
     *
     * @param node DagNode
     */
    @Override
    protected void runNextNodes(DagNode node, DagContext context) {
        List<String> resultInfo = context.getNodeInfo(node.getNodeId()).getObject(INTENT_PORTID_KEY, List.class);
        if (CollUtil.isEmpty(resultInfo)) {
            return;
        }
        multiPortTrigger(resultInfo,node.getNextNodeMap(), node, context);
    }


    /**
     * 获取大模型prompt模板
     *
     * @return String
     */
    private String getPromptTemplate() {
        // 优先使用nacos，如果nacos没有配置，则使用数据库配置
        String intentChoosePromptTemplate = workflowConfig.getIntentChoosePromptTemplate();
        PromptTemplateVO promptTemplate = promptTemplateAppService.get(INTENT_CHOOSE_PROMPT_TEMPLATE);
        if (promptTemplate == null && CharSequenceUtil.isBlank(intentChoosePromptTemplate)) {
            // nacos和数据库都不存在prompt配置
            throw new BizException("BA010", "未找到大模型配置");
        }
        if (Objects.nonNull(promptTemplate) && CharSequenceUtil.isNotBlank(promptTemplate.getContent())) {
            intentChoosePromptTemplate = promptTemplate.getContent();
        }
        return intentChoosePromptTemplate;
    }


    /**
     * build llm request
     *
     * @param modelConfig    ModelPageConvertParam
     * @param promptTemplate String
     * @return LLMRequest
     */
    private LLMRequest buildRequest(ModelPageListParam modelConfig, IntentRecognitionParam bizParam, String promptTemplate) {
        LLMRequest llmRequest = new LLMRequest();
        llmRequest.getLlmModelInfo().setProvider(ModelProviderEnum.from(modelConfig.getModelProvider()));
        llmRequest.getLlmModelInfo().setModelApi(modelConfig.getApiKey());
        llmRequest.getLlmModelInfo().setModelSecret(modelConfig.getModelSecret());
        llmRequest.getLlmModelInfo().setModelName(modelConfig.getModelCallName());
        llmRequest.getLlmModelInfo().setExtraConfig(modelConfig.getExternalModelConfig());
        llmRequest.getLlmModelInfo().setModelUrl(modelConfig.getExternalModelUrl());
        llmRequest.setStreaming(0);
        llmRequest.setText(promptTemplate);
        llmRequest.setUserId(IdUtil.fastSimpleUUID());
        llmRequest.setTopk((int) Math.round(bizParam.getTopP() * 10));
        llmRequest.setMaxTokens(bizParam.getMaxTokens());
        llmRequest.setTemperature(bizParam.getTemperature());
        if (Objects.nonNull(modelConfig.getExtraDataVO())) {
            llmRequest.getLlmModelInfo().setMaxToken(modelConfig.getExtraDataVO().getMaxInputToken());
            llmRequest.getLlmModelInfo().setTransformerType(modelConfig.getExtraDataVO().getTransformerType());
            llmRequest.getLlmModelInfo().setNativeCall(modelConfig.getExtraDataVO().getNativeCall());
            llmRequest.getLlmModelInfo().setNativeCallUrl(modelConfig.getExtraDataVO().getNativeCallUrl());
        }
        return llmRequest;
    }


    @Getter
    @Setter
    public static class IntentResult {
        /**
         * 意图ID和portId一样
         */
        private Integer classificationId;

        /**
         * 意图描述
         */
        private String reason;
    }
}
