package com.chinatelecom.gs.engine.core.model.service;

import com.chinatelecom.gs.engine.common.infra.base.BaseExtendService;
import com.chinatelecom.gs.engine.core.model.entity.po.AgentIntentManagerPO;
import com.chinatelecom.gs.engine.core.model.entity.vo.AgentIntentManagerPageQuery;
import com.chinatelecom.gs.engine.core.model.entity.vo.AgentIntentManagerRequest;
import com.chinatelecom.gs.engine.core.model.entity.vo.BatchSwitchParam;
import com.chinatelecom.gs.engine.core.model.entity.vo.ModelIntentUploadRequest;
import com.chinatelecom.gs.engine.core.sdk.request.AgentIntentConfigRequest;
import com.chinatelecom.gs.engine.core.sdk.request.CodeRequest;
import com.chinatelecom.gs.engine.core.sdk.request.IntentQueryRequest;
import com.chinatelecom.gs.engine.core.sdk.vo.AgentIntentConfigResponse;
import com.chinatelecom.gs.engine.core.sdk.vo.AgentIntentResponse;
import com.chinatelecom.gs.engine.core.sdk.vo.IntentIndexData;
import com.chinatelecom.gs.engine.core.sdk.vo.IntentSearchVO;
import com.chinatelecom.gs.engine.kms.sdk.enums.SystemFileType;
import com.chinatelelcom.gs.engine.sdk.common.Page;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p>
 * 意图管理表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-10
 */


public interface AgentIntentManagerAppService extends BaseExtendService<AgentIntentManagerPO> {


    Page<AgentIntentResponse> pageQuery(AgentIntentManagerPageQuery param);

    AgentIntentResponse create(AgentIntentManagerRequest createParam, boolean isPreset);

    Boolean update(String code, AgentIntentManagerRequest param);

    Boolean delete(CodeRequest codes);

    Boolean switchOn(BatchSwitchParam codes);

    List<AgentIntentResponse> queryList(CodeRequest codes);

    void getSystemFile(SystemFileType systemFileType, HttpServletResponse response);

    AgentIntentResponse detail(String code);

    void export(IntentQueryRequest param, HttpServletResponse response);

    AgentIntentConfigResponse getIntentConfigOrDefault(String appCode);

    Boolean creatOrUpdateConfig(AgentIntentConfigRequest param);

    String upload(MultipartFile file);

    /**
     * 模型导入意图
     *
     * @param request 模型意图导入请求对象
     * @return 是否导入成功
     */
    String uploadModelIntent(ModelIntentUploadRequest request);

    /**
     * 检查是否有打开的意图
     * @return true如果有一个或多个意图处于打开状态，false如果所有意图都是关闭状态
     */
    Boolean hasActiveIntents();

    /**
     * 意图例句查询
     *
     * @param request 查询请求
     * @return 查询结果
     */
    IntentSearchVO<IntentIndexData> queryIntent(IntentQueryRequest request, String appCode);

    /**
     * 查询打断意图
     *
     * @return AgentIntentResponse
     */
    AgentIntentResponse queryInterruptIntent();
}
