package com.chinatelecom.gs.engine.core.corekit.controller.web;

import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.common.auth.KsMenuConfig;
import com.chinatelecom.gs.engine.common.auth.MenuConfig;
import com.chinatelecom.gs.engine.common.auth.PermissionTag;
import com.chinatelecom.gs.engine.common.constants.Apis;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.core.corekit.service.GlobalVariableAppService;
import com.chinatelecom.gs.engine.core.sdk.request.GlobalVariableAddRequest;
import com.chinatelecom.gs.engine.core.sdk.request.GlobalVariableDelRequest;
import com.chinatelecom.gs.engine.core.sdk.request.GlobalVariableEditRequest;
import com.chinatelecom.gs.engine.core.sdk.request.GlobalVariableQueryRequest;
import com.chinatelecom.gs.engine.core.sdk.vo.GlobalVariableDataDetailVO;
import com.chinatelecom.gs.engine.sdk.common.annotation.ApiType;
import com.chinatelecom.gs.engine.sdk.common.annotation.HideFromApiTypes;
import com.chinatelelcom.gs.engine.sdk.common.Page;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.validation.constraints.NotBlank;


/**
 * <AUTHOR>
 * @description
 * @date 2025/02/06
 */
@RestController
@Slf4j
@Tag(name = "全局变量管理")
@PermissionTag(code = {MenuConfig.ROBOT, KsMenuConfig.BOT})
@PermissionTag(code = {MenuConfig.DIALOG_FLOW, MenuConfig.WORKFLOW, KsMenuConfig.WORKFLOW_FLOW})
@RequestMapping({Apis.BASE_PREFIX + Apis.WEB_API + Apis.VARIABLE_API})
public class GlobalVariableController {

    @Resource
    private GlobalVariableAppService globalVariableAppService;

    @Operation(summary = "全局变量分页列表", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "BOT管理")})})
    @PlatformRestApi(name = "全局变量分页列表", groupName = "全局变量管理")
    @PostMapping("/page")
    @AuditLog(businessType = "全局变量管理", operType = "全局变量分页列表", operDesc = "全局变量分页列表", objId="null")
    @HideFromApiTypes(ApiType.OPENAPI)
    public Result<Page<GlobalVariableDataDetailVO>> page(@Validated @RequestBody GlobalVariableQueryRequest pageRequest) {
        return Result.success(globalVariableAppService.page(pageRequest));
    }

    @Operation(summary = "添加全局变量", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "BOT管理")})})
    @PlatformRestApi(name = "添加全局变量", groupName = "全局变量管理")
    @PostMapping("/add")
    @AuditLog(businessType = "全局变量管理", operType = "添加全局变量", operDesc = "添加全局变量", objId="#addRequest.variableName")
    @HideFromApiTypes(ApiType.OPENAPI)
    public Result<Boolean> add(@Validated @RequestBody GlobalVariableAddRequest addRequest) {
        return globalVariableAppService.add(addRequest);
    }

    @Operation(summary = "更新全局变量", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "BOT管理")})})
    @PlatformRestApi(name = "更新全局变量", groupName = "全局变量管理")
    @PostMapping("/update")
    @HideFromApiTypes(ApiType.OPENAPI)
    @AuditLog(businessType = "全局变量管理", operType = "更新全局变量", operDesc = "更新全局变量", objId="#editRequest.variableCode")
    public Result<Boolean> update(@Validated @RequestBody GlobalVariableEditRequest editRequest) {
        return globalVariableAppService.update(editRequest);
    }

    @Operation(summary = "删除全局变量", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "BOT管理")})})
    @PlatformRestApi(name = "删除全局变量", groupName = "全局变量管理")
    @PostMapping("/delete")
    @HideFromApiTypes(ApiType.OPENAPI)
    @AuditLog(businessType = "全局变量管理", operType = "删除全局变量", operDesc = "删除全局变量", objId="#delRequest.variableType")
    public Result<Boolean> delete(@Validated @RequestBody GlobalVariableDelRequest delRequest) {
        return globalVariableAppService.delete(delRequest);
    }

    @Operation(summary = "查询全局变量详情", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "BOT管理")})})
    @PlatformRestApi(name = "查询全局变量详情", groupName = "全局变量管理")
    @PostMapping("/detail")
    @HideFromApiTypes(ApiType.OPENAPI)
    @AuditLog(businessType = "全局变量管理", operType = "查询全局变量详情", operDesc = "查询全局变量详情", objId="#variableCode")
    public Result<GlobalVariableDataDetailVO> detail(@RequestParam("variableCode") @NotBlank(message = "变量编码不能为空") String variableCode) {
        return Result.success(globalVariableAppService.getDetail(variableCode));
    }
}
