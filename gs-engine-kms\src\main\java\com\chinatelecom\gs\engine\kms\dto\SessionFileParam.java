package com.chinatelecom.gs.engine.kms.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2025年05月29日
 */
@Schema(description = "会话文件上传参数")
@Data
public class SessionFileParam {

    /**
     * 会话ID
     */
    @NotBlank(message = "会话ID不能为空")
    @Schema(description = "会话ID,文件挂载生效的会话ID", required = true)
    private String sessionId;

    /**
     * 文件code
     */
    @NotBlank(message = "文件code不能为空")
    @Schema(description = "文件code,需生成uuid传入 表示上传文件的唯一标识 不能重复", required = true)
    private String fileCode;


    @NotBlank(message = "业务类型不能为空")
    @Schema(description = "业务类型" +
            "agent: 机器人聊天窗" +
            "workflow: 工作流" +
            "plugin: 插件" +
            "robot_bind_plugin: 机器人绑定插件" +
            "report: 智能写作报告")
    private String bizType;


    @Schema(description = " 业务编码" +
            "bizType为agent 传 agentCode" +
            "workflow传 workflowId(工作流ID)" +
            "plugin 传 apiId（工具ID）" +
            "report 传 文章报告code")
    private String bizCode;

    /**
     * 是否进行解析分片,默认为false
     */
    @Schema(description = "是否进行解析分片,默认为false")
    private Boolean parseSplit = Boolean.FALSE;

}
