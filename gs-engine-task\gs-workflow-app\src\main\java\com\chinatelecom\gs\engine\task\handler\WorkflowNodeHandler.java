package com.chinatelecom.gs.engine.task.handler;

import com.alibaba.fastjson.JSON;
import com.chinatelecom.gs.engine.task.sdk.config.WorkflowExecConfig;
import com.chinatelecom.gs.engine.task.sdk.enums.DagStatusEnum;
import com.chinatelecom.gs.engine.task.sdk.enums.NodeStatusEnum;
import com.chinatelecom.gs.engine.task.sdk.enums.WorkflowTypeEnum;
import com.chinatelecom.gs.workflow.core.workflow.core.DagEngine;
import com.chinatelecom.gs.workflow.core.workflow.core.context.DagContext;
import com.chinatelecom.gs.workflow.core.workflow.core.enums.EnvTypeEnum;
import com.chinatelecom.gs.workflow.core.workflow.core.enums.NodeTypeEnum;
import com.chinatelecom.gs.workflow.core.workflow.core.handler.AbstractNodeHandler;
import com.chinatelecom.gs.workflow.core.workflow.core.model.dag.DagParam;
import com.chinatelecom.gs.workflow.core.workflow.core.model.node.DagNode;
import com.chinatelecom.gs.workflow.core.workflow.core.model.node.NodeInfo;
import com.chinatelecom.gs.workflow.core.workflow.core.model.node.NodeParam;
import com.chinatelecom.gs.workflow.core.workflow.core.model.result.DagResult;
import com.chinatelecom.gs.workflow.core.workflow.core.model.result.NodeResult;
import com.chinatelecom.gs.workflow.core.workflow.core.utils.DagParamUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.Deque;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 工作流节点处理器
 *
 * @USER: pengmc1
 * @DATE: 2024/8/21 16:52
 */
@Slf4j
@Component
public class WorkflowNodeHandler extends AbstractNodeHandler<Object> {

    @Resource
    private DagEngine dagEngine;

    @Resource
    private WorkflowExecConfig workflowExecConfig;

    /**
     * 处理的节点类型
     *
     * @return
     */
    @Override
    public NodeTypeEnum nodeType() {
        return NodeTypeEnum.WORKFLOW;
    }

    /**
     * 执行任务
     *
     * @param param   输入参数
     * @param node    节点数据
     * @param context 上下文
     * @return
     * @throws Exception
     */
    @Override
    public NodeResult<Object> doExecute(Map<String, Object> param, DagNode node, DagContext context) throws Exception {
        NodeResult nodeResult = null;
        //获取子流程ID
        DagParam childDagParam = DagParamUtils.buildChildParam(context);
        // 子流程是发布了的，这里固定是线上
        childDagParam.setEnv(EnvTypeEnum.PRODUCT);
        childDagParam.setInputParamMap(param);
        childDagParam.setGlobalVarMap(context.getGlobalVarMap());
        //注意，会引发循环调用
        DagContext childDagContext = new DagContext();
        childDagContext.setMainFlow(false);
        childDagContext.setDialogStatus(context.getDialogStatus());
        childDagContext.setDagParam(childDagParam);
        childDagContext.getNodePaths().addAll(context.getNodePaths());
        childDagContext.getNodePaths().add(node.getNodeId());
        log.info("子流程输入为：{}", JSON.toJSONString(childDagParam));
        WorkflowParam workflowParam = node.getBizParam(WorkflowParam.class);
        log.info("节点参数为：{}", JSON.toJSONString(workflowParam));
        log.info("子流程输入参数：{}", JSON.toJSONString(param));

        DagResult dagResult = dagEngine.execute(workflowParam.getWorkflowId(), childDagContext, workflowExecConfig.getWorkflowExecTimeout());
        //执行完成后，找到结束节点
        if (DagStatusEnum.FINISH.getValue().equals(childDagContext.getDagInfo().getDagStatusEnum().getValue())) {
            //执行成功
            if (WorkflowTypeEnum.DIALOGFLOW.equals(dagResult.getWorkflowType())) {
                nodeResult = NodeResult.success(dagResult.getToolAnswer().getToolAnswer(), dagResult.getToolAnswer());
            } else {
                DagNode endNode = childDagContext.getDag().getEndNode();
                NodeInfo endNodeInfo = childDagContext.getNodeInfo(endNode.getNodeId());
                log.info("工作流节点返回：{}", JSON.toJSONString(endNodeInfo));
                nodeResult = NodeResult.success(endNodeInfo.getExtraOutput(), endNodeInfo.getExtraData());
            }
            //执行完成,如果有等待的节点，需要触发下一个节点
            if (node.getNodeId().equals(context.getDagInfo().getPauseNodeId())) {
                context.getDagInfo().setPauseNodeId(null); //去掉
                Deque<String> waitPauseNodeIds = context.getDagInfo().getWaitPauseNodeIds();
                if (waitPauseNodeIds.size() > 0) {
                    String nodeId = waitPauseNodeIds.pollFirst();
                    if (StringUtils.isNotBlank(nodeId)) {
                        nodeTrigger.triggerNodeRun(null, context.getDag().getNodeMap().get(nodeId), context);
                    }
                }
            }
        } else if (DagStatusEnum.PAUSE.getValue().equals(childDagContext.getDagInfo().getDagStatusEnum().getValue())) {
            if (WorkflowTypeEnum.DIALOGFLOW.getCode().equals(context.getWorkflowType().getCode())) {
                //执行暂停
                if (Objects.nonNull(childDagContext.getDagInfo().getPauseNodeId())) {
                    NodeInfo pauseNodeInfo = childDagContext.getNodeInfo(childDagContext.getDagInfo().getPauseNodeId());
                    nodeResult = NodeResult.pause(pauseNodeInfo.getOutputParamMap());
                } else {
                    nodeResult = NodeResult.pause(null);
                    nodeResult.setNodeStatus(NodeStatusEnum.PAUSE_WAIT);
                }
            } else {
                nodeResult = NodeResult.success(dagResult.getToolAnswer().getToolAnswer(), null); //待处理 todo
            }
        } else {
            //失败状态
            nodeResult = NodeResult.fail("工作流执行失败");
        }
        //将执行结果写入到该节点的结果中
        context.getNodeInfo(node.getNodeId()).setDagInfo(childDagContext.getDagInfo());
        //透传子流程产生的答案
        context.addToolAnswers(childDagContext.getToolAnswers());
        return nodeResult;
    }

    @Getter
    @Setter
    public static class WorkflowParam {
        /**
         * 工作流ID
         */
        private String workflowId;
        /**
         * 输入参数定义
         */
        private List<NodeParam> inputDefs;

        /**
         * 是否上架
         */
        private Boolean shelved;
    }
}
