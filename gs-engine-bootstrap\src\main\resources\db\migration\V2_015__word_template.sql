CREATE TABLE IF NOT EXISTS `word_template` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `yn` bigint unsigned NOT NULL DEFAULT '0' COMMENT '删除标记,0:未删除,其他表示已删除',
  `name` varchar(520) NOT NULL COMMENT '模板名称',
  `code` varchar(64) NOT NULL COMMENT '模板编码',
  `template_type` VARCHAR (255) NOT NULL COMMENT '模板类型',
  `template_desc` varchar(1024) NULL COMMENT '模板描述',
  `template_path` varchar(4096) NOT NULL COMMENT '模板路径',
  `default_param` varchar(4096) NOT NULL COMMENT '默认模板参数',
  `template_flag` varchar(4096) DEFAULT NULL COMMENT '模板标识',
  `create_id` varchar(255) NOT NULL DEFAULT '0' COMMENT '创建用户ID',
  `create_name` varchar(255) NOT NULL DEFAULT '未知用户' COMMENT '创建用户名',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_id` varchar(255) NOT NULL DEFAULT '0' COMMENT '最后修改用户ID',
  `update_name` varchar(255) NOT NULL DEFAULT '未知用户' COMMENT '最后修改用户名',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='word模板表';

INSERT IGNORE  INTO `word_template` (id, `yn`, `name`, `code`, `template_type`, `template_desc`, `template_path`, `default_param`, `template_flag`, `create_id`, `create_name`, `update_id`, `update_name`) VALUES
(1, '0','请示','902891104083513344','REQUEST_FOR_INSTRUCTIONS','适用于向上级机关请求指示、批准','classpath:file/word-tpl/请示.docx','{}','["上行文"]','0','未知用户','0','未知用户'),
(2, '0','报告','902891292101578752','REPORT','适用于向上级机关汇报工作、反映情况，回复上级机关的询问','classpath:file/word-tpl/报告.docx','{}','["上行文"]','0','未知用户','0','未知用户'),
(3, '0','意见','902891519843897344','OPINION','适用于对重要问题提出见解和处理办法','classpath:file/word-tpl/意见.docx','{}','["上行文","平行文","下行文"]','0','未知用户','0','未知用户'),
(4, '0','函','902891616975589376','LETTER','适用于不相隶属机关之间商洽工作、询问和答复问题、请求批准和答复审批事项','classpath:file/word-tpl/函.docx','{}','["平行文"]','0','未知用户','0','未知用户'),
(5, '0','通知','902891706226184192','NOTICE','适用于发布、传达要求下级机关执行和有关单位周知或者执行的事项，批转、转发公文','classpath:file/word-tpl/通知.docx','{}','["平行文","下行文"]','0','未知用户','0','未知用户'),
(6, '0','纪要','902891812514041856','MINUTES','适用于记载会议主要情况和议定事项','classpath:file/word-tpl/纪要.docx','{}','["平行文","下行文"]','0','未知用户','0','未知用户'),
(7, '0','议案','902891903773708288','MOTION','适用于各级人民政府按照法律程序向同级人民代表大会或者人民代表大会常务委员会提请审议事项','classpath:file/word-tpl/议案.docx','{}','["平行文"]','0','未知用户','0','未知用户'),
(8, '0','命令','902892076897800192','ORDER','适用于公布行政法规和规章、宣布施行重大强制性措施、批准授予和晋升衔级、嘉奖有关单位和人员','classpath:file/word-tpl/命令.docx','{}','["下行文"]','0','未知用户','0','未知用户'),
(9, '0','决定','902892152172974080','DECISION','适用于对重要事项作出决策和部署、奖惩有关单位和人员、变更或者撤销下级机关不适当的决定事项','classpath:file/word-tpl/决定.docx','{}','["下行文"]','0','未知用户','0','未知用户'),
(10, '0','公告','902892276798328832','ANNOUNCEMENT','适用于向国内外宣布重要事项或者法定事项','classpath:file/word-tpl/公告.docx','{}','["下行文"]','0','未知用户','0','未知用户'),
(11, '0','通告','902892366623543296','CIRCULAR','适用于在一定范围内公布应当遵守或者周知的事项','classpath:file/word-tpl/通告.docx','{}','["下行文"]','0','未知用户','0','未知用户'),
(12, '0','通报','902892456226459648','NOTIFICATION','适用于表彰先进、批评错误、传达重要精神和告知重要情况','classpath:file/word-tpl/通报.docx','{}','["下行文"]','0','未知用户','0','未知用户'),
(13, '0','批复','902892541253390336','REPLY','适用于答复下级机关请示事项','classpath:file/word-tpl/批复.docx','{}','["下行文"]','0','未知用户','0','未知用户'),
(14, '0','决议','902892639966334976','RESOLUTION','适用于会议讨论通过的重大决策事项','classpath:file/word-tpl/决议.docx','{}','["下行文"]','0','未知用户','0','未知用户'),
(15, '0','公报','902892725324615680','BULLETIN','适用于公布重要决定或者重大事项','classpath:file/word-tpl/公报.docx','{}','["下行文"]','0','未知用户','0','未知用户');


ALTER TABLE `knowledge_report`
ADD COLUMN `report_param` TEXT DEFAULT NULL COMMENT '报告生成参数' AFTER `chose`,
ADD COLUMN `cover_file_key` VARCHAR(1024) DEFAULT NULL COMMENT '报告封面'  AFTER `report_param`,
CHANGE `outline_data` `outline_data` mediumtext COMMENT '大纲结构',
CHANGE `chose` `chose` text DEFAULT NULL COMMENT '智能问答选择项';

update prompt_template set content='"""\n### Task\n根据用户输入的主题，关键词及相关要求，生成大纲及相关内容摘要。尽量不要遗漏关键词的内容。\n\n### Instructions\n- 层级要求：大纲生成一级和二级和三级 3个层级，最多生成3个一级标题，每个一级标题最大生成2个二级菜单，以及每个二级标题最大生成2个三级菜单。\n- 格式要求：以markdown格式输出，一级标题为以#开头，二级标题以##开头，三级标题以###开头,只输出大纲，不要输出其他内容。\n- 标题以#号开始，不能带序号。\n\n### Fewshots\ninput：\n主题：人工智能的应用\n<#if keywords??>\n关键词：医疗，教育，金融\n</#if>\noutput:\n```markdown\n# 医疗健康\n## 疾病诊断\n### 影像识别\n利用深度学习技术分析X光、CT等医学影像，辅助医生发现早期病变。\n### 数据驱动的个性化治疗\n结合患者的基因组数据和电子病历，提供精准的疾病预测和治疗方案。\n## 药物研发\n### 分子筛选与设计\nAI加速新药物分子的筛选过程，通过模拟实验预测其有效性及副作用。\n### 临床试验优化\n利用机器学习算法提高临床试验的设计效率和成功率。\n\n# 教育领域\n## 个性化学习\n### 学习路径规划\n根据学生的学习进度和兴趣点定制个性化的学习路径。\n### 实时反馈系统\n通过自然语言处理技术对学生作业和考试结果进行即时评估和反馈。\n## 智能辅导\n### 自适应学习材料\n提供适合不同学习风格的学生的自适应教材和练习题。\n### 虚拟导师\n使用AI构建虚拟教师，能够24小时解答学生的问题，提供一对一的教学支持。\n\n# 金融服务\n## 风险控制\n### 信用评分模型\n采用机器学习算法建立更精确的信用评分体系，降低信贷风险。\n### 反欺诈检测\n利用AI实时监控交易行为，快速识别潜在的欺诈活动。\n## 自动化交易\n### 投资策略优化\n开发智能算法自动执行复杂的金融市场交易策略。\n### 市场情绪分析\n通过分析社交媒体和新闻报道来预测市场趋势，辅助投资决策。\n```\n\n### Question\n主题：${content}\n<#if keywords??>\n关键词：${keywords}\n</#if>\n<#if additionalKeywords??>\n其他附加要求：${additionalKeywords}\n</#if>\n参考示例和指令要求，输出主题大纲及内容摘要\n"""'
where `code`='OUTLINE' AND app_code="0" limit 1;

update prompt_template set content='"""\n### Task\n根据用户输入的主题，关键词及相关要求，生成大纲及相关内容摘要。尽量不要遗漏关键词的内容。\n\n### Instructions\n- 层级要求：大纲生成一级和二级和三级 3个层级，最多生成3个一级标题，每个一级标题最大生成2个二级菜单，以及每个二级标题最大生成2个三级菜单。\n- 格式要求：以markdown格式输出，一级标题为以#开头，二级标题以##开头，三级标题以###开头,只输出大纲，不要输出其他内容。\n- 标题以#号开始，不能带序号。\n\n### Fewshots\ninput：\n主题：人工智能的应用\n<#if keywords??>\n关键词：医疗，教育，健康\n</#if>\noutput:\n```markdown\n# 医疗健康\n## 疾病诊断\n### 影像识别\n利用深度学习技术分析X光、CT等医学影像，辅助医生发现早期病变。\n### 数据驱动的个性化治疗\n结合患者的基因组数据和电子病历，提供精准的疾病预测和治疗方案。\n## 药物研发\n### 分子筛选与设计\nAI加速新药物分子的筛选过程，通过模拟实验预测其有效性及副作用。\n### 临床试验优化\n利用机器学习算法提高临床试验的设计效率和成功率。\n\n# 教育领域\n## 个性化学习\n### 学习路径规划\n根据学生的学习进度和兴趣点定制个性化的学习路径。\n### 实时反馈系统\n通过自然语言处理技术对学生作业和考试结果进行即时评估和反馈。\n## 智能辅导\n### 自适应学习材料\n提供适合不同学习风格的学生的自适应教材和练习题。\n### 虚拟导师\n使用AI构建虚拟教师，能够24小时解答学生的问题，提供一对一的教学支持。\n\n# 金融服务\n## 风险控制\n### 信用评分模型\n采用机器学习算法建立更精确的信用评分体系，降低信贷风险。\n### 反欺诈检测\n利用AI实时监控交易行为，快速识别潜在的欺诈活动。\n## 自动化交易\n### 投资策略优化\n开发智能算法自动执行复杂的金融市场交易策略。\n### 市场情绪分析\n通过分析社交媒体和新闻报道来预测市场趋势，辅助投资决策。\n```\n\n### Question\n主题：${content}\n<#if keywords??>\n关键词：${keywords}\n</#if>\n<#if additionalKeywords??>\n其他附加要求：${additionalKeywords}\n</#if>\n参考示例和指令要求，输出主题大纲及内容摘要\n用户不满意的大纲：\n```\n${outline!}\n```\n参考示例和指令要求，对用户不满意的大纲进行优化，重新输出主题大纲\n"""'
where `code`='OUTLINE_REWRITE' AND app_code="0" limit 1;

update prompt_template set content='"""\n### Task\n根据主题和关键内容摘要，对大纲内容进行扩展，生成指定大纲内容下的详细文章内容，请输出纯文本，如果有多个方面的信息，请分段输出。\n\n### Instructions\n- **内容要求**：\n1. 严格按照给定的大纲主题和摘要输出内容，如果有参考信息，输出内容应该严格根据参考信息进行生成，生成的文章应该语言应专业、清晰，适合目标读者。请确保逻辑严谨，结构清晰，内容要全面完整。\n<#if outline??>\n2. 当前主题的整体大纲及每级大纲摘要信息如下\n```markdown\n${outline!}\n```\n</#if>\n\n- **格式要求**：\n1. 提供给你的是一个大纲的内容，请根据文章主题及当前大纲主题要求生成响应的文章内容。\n2. 输出内容是当前大纲的具体文章内容，不需要输出对应的大纲标题。\n\n### Question\n用户要求的文章主题是：${content}\n<#if keywords??>\n主题相关的关键字：${keywords}\n</#if>\n<#if additionalKeywords??>\n其他附加要求：${additionalKeywords}\n</#if>\n\n本次要求输出内容的大纲主题是：\n${title}\n<#if introduction??>\n本次要求输出内容的大纲摘要是：\n${introduction}\n</#if>\n\n<#if example??>\n本次要求输出内容的可参考信息如下：\n```\n${example}\n```\n</#if>\n\n根据指令要求和大纲，生成当前大纲下的内容信息\n"""'
where `code`='CONTENT' AND app_code="0" limit 1;


