package com.chinatelecom.gs.engine.core.manager.controller;

import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.common.constants.Apis;
import com.chinatelecom.gs.engine.common.utils.CipherEncodeUtils;
import com.chinatelecom.gs.engine.common.utils.JsonUtils;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;


/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024年11月28日
 */
@RestController
@Slf4j
@Tag(name = "系统接口")
public class SystemController {


    private static final String KEY = "RKS5277S6hHHtRUJ";

    @Operation(summary = "健康检查", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @GetMapping("/ping")
    public String heathCheck() {
        return "OK";
    }


    @Operation(summary = "系统版本信息查询", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @GetMapping(Apis.LICENSE_CHECK)
    @AuditLog(businessType = "系统接口", operType = "系统版本信息查询", operDesc = "系统版本信息查询", objId="null")
    public Result<String> licenseCheck() {
        Map<String, String> data = new HashMap<>();
        data.put("name", "ks");
        data.put("license", "true");
        data.put("timestamp", String.valueOf(System.currentTimeMillis()));
        return Result.success(CipherEncodeUtils.encrypt(JsonUtils.toJsonString(data), KEY));
    }

}
