INSERT INTO plugin_meta_info
(tenant_id, plugin_id, plugin_name, plugin_desc, create_api_type, url, plugin_icon, auth_type, location, service_key, service_token, oauth_info, common_headers, status, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code, plugin_type, source_system)
VALUES('', '1032194778763366400', '智数 Assistant', '智能取数', 2, 'http://ais', '/ais/plugin/web/pluginMall/defaultIcon/bi.png', 0, NULL, NULL, NULL, NULL, '[{"name":"","value":""},{"name":"","value":""}]', 2, 1, '8470678936543756288', '管理员', '8470678936543756288', '管理员', '2025-04-15 15:33:32.511', '2025-04-17 01:19:02.344', 0, 'ks', 2, 'BOT');
INSERT INTO plugin_meta_info
(tenant_id, plugin_id, plugin_name, plugin_desc, create_api_type, url, plugin_icon, auth_type, location, service_key, service_token, oauth_info, common_headers, status, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code, plugin_type, source_system)
VALUES('', '1032194778763366400', '智数 Assistant', '智数 Assistant 是一款智能化的数据查询插件，基于先进的 NL2SQL 技术，能够将用户输入的自然语言问题直接转化为 SQL 查询语句，并在后台执行查询操作，快速返回精确的数据结果。无论是业务分析、报表生成还是数据探索，用户无需掌握复杂的 SQL 语法，即可轻松获取所需信息。', 2, 'http://ais', '/ais/common/f/images/Avatar/Avatar_default.png', 0, NULL, NULL, NULL, NULL, '[{"name":"","value":""},{"name":"","value":""}]', 2, 2, '8470678936543756288', '管理员', '8470678936543756288', '管理员', '2025-04-15 15:33:32.511', '2025-04-17 01:19:02.352', 0, 'ks', 2, 'BOT');
INSERT INTO plugin_meta_info
(tenant_id, plugin_id, plugin_name, plugin_desc, create_api_type, url, plugin_icon, auth_type, location, service_key, service_token, oauth_info, common_headers, status, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code, plugin_type, source_system)
VALUES('', '1032194778763366400', '智数 Assistant', '智数 Assistant 是一款智能化的数据查询插件，基于先进的 NL2SQL 技术，能够将用户输入的自然语言问题直接转化为 SQL 查询语句，并在后台执行查询操作，快速返回精确的数据结果。无论是业务分析、报表生成还是数据探索，用户无需掌握复杂的 SQL 语法，即可轻松获取所需信息。', 2, 'http://ais', '/ais/common/f/images/Avatar/Avatar_default.png', 0, NULL, NULL, NULL, NULL, '[{"name":"","value":""},{"name":"","value":""}]', 2, 3, '8470678936543756288', '管理员', '8470678936543756288', '管理员', '2025-04-15 15:33:32.511', '2025-04-17 01:19:02.358', 0, 'ks', 2, 'BOT');

INSERT INTO plugin_api_meta_info
(tenant_id, api_id, plugin_id, api_name, api_desc, `path`, `method`, service_status, debug_status, quote_number, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES('', '1032195128497016832', '1032194778763366400', 'BI', '取数', '/bi/result', '1', 1, 1, 0, 1, 1, '8470678936543756288', '管理员', '8470678936543756288', '管理员', '2025-04-15 15:34:55.891', '2025-04-15 15:44:38', 0, 'ks');
INSERT INTO plugin_api_meta_info
(tenant_id, api_id, plugin_id, api_name, api_desc, `path`, `method`, service_status, debug_status, quote_number, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES('', '1032195128497016832', '1032194778763366400', 'BI', 'BI 是一款利用 NL2SQL 技术的智能工具，它能够将自然语言问题解析为结构化的 SQL 查询语句，并通过连接数据库进行实时查询，最终以清晰易懂的方式返回查询结果。该工具旨在降低数据分析门槛，帮助用户高效完成数据提取和分析任务。', '/bi/result', '1', 1, 1, 0, 1, 2, '8470678936543756288', '管理员', '8470678936543756288', '管理员', '2025-04-15 15:34:55.891', '2025-04-17 09:18:10', 0, 'ks');
INSERT INTO plugin_api_meta_info
(tenant_id, api_id, plugin_id, api_name, api_desc, `path`, `method`, service_status, debug_status, quote_number, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES('', '1032195128497016832', '1032194778763366400', 'BI', 'BI 是一款利用 NL2SQL 技术的智能工具，它能够将自然语言问题解析为结构化的 SQL 查询语句，并通过连接数据库进行实时查询，最终以清晰易懂的方式返回查询结果。该工具旨在降低数据分析门槛，帮助用户高效完成数据提取和分析任务。', '/bi/result', '1', 1, 1, 0, 1, 3, '8470678936543756288', '管理员', '8470678936543756288', '管理员', '2025-04-15 15:34:55.891', '2025-04-17 09:18:10.432', 0, 'ks');

INSERT INTO plugin_api_param
(tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES('', '1032618080128864257', '1032618080107892736', '1032194778763366400', '1032195128497016832', 'data', '取数最终结果', 2, 4, 0, '', 1, 1, 3, '8470678936543756288', '管理员', '8470678936543756288', '管理员', '2025-04-16 19:35:35.421', '2025-04-17 09:18:10.500', 0, '');
INSERT INTO plugin_api_param
( tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1032618080128864256', '1032618080107892736', '1032194778763366400', '1032195128497016832', 'reasoning', '取数思考结果', 2, 4, 0, '', 1, 1, 3, '8470678936543756288', '管理员', '8470678936543756288', '管理员', '2025-04-16 19:35:35.420', '2025-04-17 09:18:10.499', 0, '');
INSERT INTO plugin_api_param
( tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1032618080107892736', NULL, '1032194778763366400', '1032195128497016832', 'data', '取数结果', 2, 4, 0, '', 5, 1, 3, '8470678936543756288', '管理员', '8470678936543756288', '管理员', '2025-04-16 19:35:35.418', '2025-04-17 09:18:10.499', 0, '');
INSERT INTO plugin_api_param
(tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1032617928337002497', NULL, '1032194778763366400', '1032195128497016832', 'activeBusiness', '业务域code', 1, 1, 1, NULL, 1, 1, 3, '8470678936543756288', '管理员', '8470678936543756288', '管理员', '2025-04-16 19:34:59.232', '2025-04-17 09:18:10.499', 0, '');
INSERT INTO plugin_api_param
( tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1032617928337002496', NULL, '1032194778763366400', '1032195128497016832', 'query', 'query', 1, 1, 1, NULL, 1, 1, 3, '8470678936543756288', '管理员', '8470678936543756288', '管理员', '2025-04-16 19:34:59.231', '2025-04-17 09:18:10.497', 0, '');
INSERT INTO plugin_api_param
( tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1032618080128864257', '1032618080107892736', '1032194778763366400', '1032195128497016832', 'data', '取数最终结果', 2, 4, 0, '', 1, 1, 2, '8470678936543756288', '管理员', '8470678936543756288', '管理员', '2025-04-16 19:35:35.421', '2025-04-16 19:35:35.421', 0, '');
INSERT INTO plugin_api_param
( tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1032618080128864256', '1032618080107892736', '1032194778763366400', '1032195128497016832', 'reasoning', '取数思考结果', 2, 4, 0, '', 1, 1, 2, '8470678936543756288', '管理员', '8470678936543756288', '管理员', '2025-04-16 19:35:35.420', '2025-04-16 19:35:35.420', 0, '');
INSERT INTO plugin_api_param
( tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1032618080107892736', NULL, '1032194778763366400', '1032195128497016832', 'data', '取数结果', 2, 4, 0, '', 5, 1, 2, '8470678936543756288', '管理员', '8470678936543756288', '管理员', '2025-04-16 19:35:35.418', '2025-04-16 19:35:35.418', 0, '');
INSERT INTO plugin_api_param
(tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1032617928337002497', NULL, '1032194778763366400', '1032195128497016832', 'activeBusiness', '业务域code', 1, 1, 1, NULL, 1, 1, 2, '8470678936543756288', '管理员', '8470678936543756288', '管理员', '2025-04-16 19:34:59.232', '2025-04-16 19:34:59.232', 0, '');
INSERT INTO plugin_api_param
(tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1032617928337002496', NULL, '1032194778763366400', '1032195128497016832', 'query', 'query', 1, 1, 1, NULL, 1, 1, 2, '8470678936543756288', '管理员', '8470678936543756288', '管理员', '2025-04-16 19:34:59.231', '2025-04-16 19:34:59.231', 0, '');
INSERT INTO plugin_api_param
( tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1032195808540495874', '1032195808540495872', '1032194778763366400', '1032195128497016832', 'result', '取数最终结果', 2, 4, 0, '', 1, 1, 2, '8470678936543756288', '管理员', '8470678936543756288', '管理员', '2025-04-15 15:37:38.027', '2025-04-16 19:35:35', 68298, '');
INSERT INTO plugin_api_param
( tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1032195808540495873', '1032195808540495872', '1032194778763366400', '1032195128497016832', 'think', '取数思考结果', 2, 4, 0, '', 1, 1, 2, '8470678936543756288', '管理员', '8470678936543756288', '管理员', '2025-04-15 15:37:38.027', '2025-04-16 19:35:35', 68297, '');
INSERT INTO plugin_api_param
( tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1032195808540495872', NULL, '1032194778763366400', '1032195128497016832', 'data', '取数结果', 2, 4, 0, '', 5, 1, 2, '8470678936543756288', '管理员', '8470678936543756288', '管理员', '2025-04-15 15:37:38.026', '2025-04-16 19:35:35', 68296, '');
INSERT INTO plugin_api_param
( tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1032195207882608640', NULL, '1032194778763366400', '1032195128497016832', 'query', 'query', 1, 1, 1, NULL, 1, 1, 2, '8470678936543756288', '管理员', '8470678936543756288', '管理员', '2025-04-15 15:35:14.818', '2025-04-16 19:34:59', 68295, '');
INSERT INTO plugin_api_param
( tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1032195808540495874', '1032195808540495872', '1032194778763366400', '1032195128497016832', 'result', '取数最终结果', 2, 4, 0, '', 1, 1, 1, '8470678936543756288', '管理员', '8470678936543756288', '管理员', '2025-04-15 15:37:38.027', '2025-04-15 15:37:38.027', 0, '');
INSERT INTO plugin_api_param
(tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1032195808540495873', '1032195808540495872', '1032194778763366400', '1032195128497016832', 'think', '取数思考结果', 2, 4, 0, '', 1, 1, 1, '8470678936543756288', '管理员', '8470678936543756288', '管理员', '2025-04-15 15:37:38.027', '2025-04-15 15:37:38.027', 0, '');
INSERT INTO plugin_api_param
(tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1032195808540495872', NULL, '1032194778763366400', '1032195128497016832', 'data', '取数结果', 2, 4, 0, '', 5, 1, 1, '8470678936543756288', '管理员', '8470678936543756288', '管理员', '2025-04-15 15:37:38.026', '2025-04-15 15:37:38.026', 0, '');
INSERT INTO plugin_api_param
( tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1032195207882608640', NULL, '1032194778763366400', '1032195128497016832', 'query', 'query', 1, 1, 1, NULL, 1, 1, 1, '8470678936543756288', '管理员', '8470678936543756288', '管理员', '2025-04-15 15:35:14.818', '2025-04-15 15:35:14.818', 0, '');
INSERT INTO plugin_api_param
(tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES('', '1034670880512806914', NULL, '1032194778763366400', '1032195128497016832', 'pageSize', '每页多少条数据', 1, 1, 0, '10', 1, 1, 2, '8496303708367421440', '管理员', '8496303708367421440', '管理员', '2025-04-22 11:32:41.180', '2025-04-22 11:32:41.180', 0, '');
INSERT INTO plugin_api_param
(tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES('', '1034670880512806913', NULL, '1032194778763366400', '1032195128497016832', 'page', '页码', 1, 1, 0, '1', 1, 1, 2, '8496303708367421440', '管理员', '8496303708367421440', '管理员', '2025-04-22 11:32:41.179', '2025-04-22 11:32:41.179', 0, '');
INSERT INTO plugin_api_param
(tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES('', '1034670880512806914', NULL, '1032194778763366400', '1032195128497016832', 'pageSize', '每页多少条数据', 1, 1, 0, '10', 1, 1, 3, '8496303708367421440', '管理员', '8496303708367421440', '管理员', '2025-04-22 11:32:41.180', '2025-04-22 11:32:41.180', 0, '');
INSERT INTO plugin_api_param
(tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES('', '1034670880512806913', NULL, '1032194778763366400', '1032195128497016832', 'page', '页码', 1, 1, 0, '1', 1, 1, 3, '8496303708367421440', '管理员', '8496303708367421440', '管理员', '2025-04-22 11:32:41.179', '2025-04-22 11:32:41.179', 0, '');
INSERT INTO plugin_api_param
(tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES('', '10326180801288642561', '1032618080107892736', '1032194778763366400', '1032195128497016832', 'summary', '总结', 2, 4, 0, '', 1, 1, 3, '8470678936543756288', '管理员', '8470678936543756288', '管理员', '2025-04-16 19:35:35.420', '2025-04-17 09:18:10.499', 0, '');
INSERT INTO plugin_api_param
(tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES('', '10326180801288642561', '1032618080107892736', '1032194778763366400', '1032195128497016832', 'summary', '总结', 2, 4, 0, '', 1, 1, 2, '8470678936543756288', '管理员', '8470678936543756288', '管理员', '2025-04-16 19:35:35.420', '2025-04-17 09:18:10.499', 0, '');
INSERT INTO plugin_api_param
(tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES('', '10326180801288642561', '1032618080107892736', '1032194778763366400', '1032195128497016832', 'summary', '总结', 2, 4, 0, '', 1, 1, 1, '8470678936543756288', '管理员', '8470678936543756288', '管理员', '2025-04-16 19:35:35.420', '2025-04-17 09:18:10.499', 0, '');


INSERT INTO plugin_version_info
(tenant_id, plugin_id, version, status, runtime_status, version_desc, yn, create_id, create_name, update_id, update_name, create_time, update_time)
VALUES('', '1032194778763366400', 1, 'PUBLISHED', 'OFF_LINE', '', 0, '8470678936543756288', '管理员', '8470678936543756288', '管理员', '2025-04-15 15:33:32.508', '2025-04-17 09:18:10');
INSERT INTO plugin_version_info
(tenant_id, plugin_id, version, status, runtime_status, version_desc, yn, create_id, create_name, update_id, update_name, create_time, update_time)
VALUES('', '1032194778763366400', 2, 'PUBLISHED', 'RUNNING_PROD', '', 0, '8470678936543756288', '管理员', '8470678936543756288', '管理员', '2025-04-15 15:44:38.285', '2025-04-17 09:18:10');
INSERT INTO plugin_version_info
(tenant_id, plugin_id, version, status, runtime_status, version_desc, yn, create_id, create_name, update_id, update_name, create_time, update_time)
VALUES('', '1032194778763366400', 3, 'EDITING', 'OFF_LINE', '', 0, '8470678936543756288', '管理员', '8470678936543756288', '管理员', '2025-04-17 09:18:10.382', '2025-04-17 09:18:10.382');

INSERT INTO plugin_mall_info
(plugin_id, category_code, category_name, plugin_source, create_id, create_name, update_id, update_name, create_time, update_time, yn, plugin_name)
VALUES('1032194778763366400', '1', '公共插件', 1, '1', 'admin', '1', 'admin', '2025-04-16 11:43:45.677', '2025-04-16 11:43:45.677', 0, '智数 Assistant');

INSERT INTO gs_base_config
(yn, code, name, dimension, config_type, business_no, config_data, obj_class, description, create_id, create_name, create_time, update_id, update_name, update_time)
VALUES(0, 'system-agentUploadConfig', 'system-agentUploadConfig', 'SYSTEM', 'agentUploadConfig', 'SYSTEM', '{"supportKnowledgeType":["PDF","WORD","PPT","TEXT","AUDIO","VIDEO","IMAGE","FAQ","EXCEL"],"chatSupportKnowledgeType":["PDF","WORD","PPT","TEXT","AUDIO","VIDEO","IMAGE", "EXCEL"],"supportFileSize":{"DOC":100,"EXCEL":50,"MD":100,"IMG":10,"VIDEO":100,"AUDIO":100, "FAQ": 50}}', '', '内置的功能配置', '_empty_', '未知用户', '2024-10-11 00:00:00', '_empty_', '未知用户', '2025-04-21 05:53:36');

update gs_base_config
set config_data = '{"supportKnowledgeType":["PDF","WORD","PPT","TEXT","AUDIO","VIDEO","IMAGE","FAQ","EXCEL"],"chatSupportKnowledgeType":["PDF","WORD","PPT","TEXT","AUDIO","VIDEO","IMAGE", "EXCEL"],"supportFileSize":{"DOC":200,"EXCEL":50,"MD":200,"IMG":10,"VIDEO":200,"AUDIO":200, "FAQ": 50},"chatSupportFileSize":{"DOC":100,"EXCEL":100,"MD":100,"IMG":100,"VIDEO":100,"AUDIO":100, "FAQ": 100},"supportKnowledgeFileSize":{"DOC":200,"EXCEL":50,"MD":200,"IMG":10,"VIDEO":2048,"AUDIO":2048,"FAQ":50}}'
where code = 'system-internalConfig' and yn = 0;

