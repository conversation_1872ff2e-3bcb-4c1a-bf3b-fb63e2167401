package com.chinatelecom.gs.engine.kms.controller;

import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.common.config.aspect.annotation.DebugLog;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.context.RequestInfo;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.common.utils.BizAssert;
import com.chinatelecom.gs.engine.common.utils.UserInfoUtils;
import com.chinatelecom.gs.engine.kms.common.CustomContext;
import com.chinatelecom.gs.engine.kms.model.dto.KnowledgeAuditDTO;
import com.chinatelecom.gs.engine.kms.repository.KnowledgeAuditRepository;
import com.chinatelecom.gs.engine.kms.sdk.constants.KmsApis;
import com.chinatelecom.gs.engine.kms.sdk.vo.process.AuditFlowCallbackParam;
import com.chinatelecom.gs.engine.kms.service.KnowledgeAuditAppService;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import com.chinatelelcom.gs.engine.sdk.common.enums.AppSourceType;
import com.chinatelelcom.gs.engine.sdk.common.enums.RequestSourceType;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.util.Objects;


/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2025年01月07日
 */
@Slf4j
@Tag(name = "流程系统回调入口")
@RestController
@RequestMapping({KmsApis.RPC + KmsApis.FLOW})
public class AuditFlowCallbackController {


    @Resource
    private KnowledgeAuditAppService knowledgeAuditAppService;

    @Resource
    private KnowledgeAuditRepository knowledgeAuditRepository;


    @Resource
    private CustomContext customContext;

    @Operation(summary = "审批结果回调", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "审批")})})
    @DebugLog(operation = "审批结果回调")
    @PlatformRestApi(name = "审批结果回调", groupName = "知识审核")
    @AuditLog(businessType = "知识审核", operType = "审批结果回调", operDesc = "审批结果回调", objId="#param.flowProcessInstanceId")
    @PostMapping(KmsApis.CALLBACK)
    public Result<String> callback(@Validated @RequestBody AuditFlowCallbackParam param) {
        try {
            setContext(param);

            knowledgeAuditAppService.endFlow(param);
            Result<String> result = new Result<>();
            // 流程系统固定成功编码为1
            result.setCode("1");
            result.setData("success");
            return result;
        } finally {
            customContext.clear();
        }
    }

    public void setContext(AuditFlowCallbackParam param) {
        customContext.setContext(param.getAppCode());
        String auditCode = param.getAuditCode();
        BizAssert.notEmpty(auditCode, "A0003", "审核编码不能为空");
        KnowledgeAuditDTO auditDTO = knowledgeAuditRepository.selectByCode(auditCode);
        BizAssert.notNull(auditDTO, "A0003", "审核记录不存在：{}", auditCode);
        RequestInfo requestInfo = RequestContext.get();
        requestInfo.setUserId(auditDTO.getCreateId());
        requestInfo.setUserName(auditDTO.getCreateName());
        requestInfo.setRequestSourceType(RequestSourceType.RPC);

        //打印UserInfoUtils入参
        log.info("审批UserInfoUtils入参：{},{},{}", requestInfo.getTenantId(), auditDTO.getCreateId(), requestInfo.getAppSourceType());
        requestInfo.setIsAdmin(false);
        try {
            Boolean userAdmin = UserInfoUtils.isAdmin(requestInfo.getTenantId(), auditDTO.getCreateId(), requestInfo.getAppSourceType());
            if (userAdmin != null) {
                requestInfo.setIsAdmin(userAdmin);
            }
        } catch (Exception ignored) {
            log.warn("UserInfoUtils.checkUserAdmin 异常，使用默认值 false");
        }
        requestInfo.setTeam(UserInfoUtils.getUserTeam());
    }


}
