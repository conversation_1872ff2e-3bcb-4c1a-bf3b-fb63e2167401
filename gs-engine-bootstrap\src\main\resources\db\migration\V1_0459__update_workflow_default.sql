alter table bot_workflow add column `is_default` VARCHAR(10) DEFAULT '0' NOT NULL comment '是否默认';

update bot_workflow set is_default = '1' where workflow_id = 'ab1070c5-d83e-4a0d-aaa7-d7c0deb99883';

INSERT INTO workflow_version_info
( tenant_id, workflow_id, version, status, runtime_status, version_desc, yn, app_code, create_id, create_name, update_id, update_name, create_time, update_time)
VALUES( '', 'ab1070c5-d83e-4a0d-aaa7-d7c0deb98564', 1, 'PUBLISHED', 'RUNNING_PROD', '', 0, 'ks', '8470678936543756288', '管理员', '8470678936543756288', '管理员', '2025-02-24 17:11:36.160', '2025-02-24 17:22:09');
INSERT INTO workflow_version_info
( tenant_id, workflow_id, version, status, runtime_status, version_desc, yn, app_code, create_id, create_name, update_id, update_name, create_time, update_time)
VALUES( '', 'ab1070c5-d83e-4a0d-aaa7-d7c0deb98564', 2, 'EDITING', 'OFF_LINE', '', 0, 'ks', '8470678936543756288', '管理员', '8470678936543756288', '管理员', '2025-02-24 17:22:09.167', '2025-02-24 17:22:09.167');

INSERT INTO bot_workflow
(workflow_id, workflow_name, workflow_cn_name, icon_url, scene, canvas, version, description, debug_status, status, bot_ref_count, yn, app_code, tenant_id, create_id, create_name, create_time, update_id, update_name, update_time, bot_type, workflow_type, main_workflow_id, is_default)
VALUES('ab1070c5-d83e-4a0d-aaa7-d7c0deb98564', '', '呼出bot内置策略模板', '/ais/common/f/images/defaultAvatar.png', NULL, '{"nodes":[{"position":{"x":1190,"y":70},"size":{"width":360,"height":128},"view":"vue-shape-view","shape":"end","ports":{"groups":{"right":{"attrs":{"circle":{"r":6,"magnet":true,"strokeWidth":2,"stroke":"#fff","fill":"#9197F1"}},"position":"right"},"left":{"attrs":{"circle":{"r":6,"magnet":true,"strokeWidth":2,"stroke":"#fff","fill":"#9197F1"}},"position":"left"},"pos":{"attrs":{"circle":{"r":6,"magnet":true,"strokeWidth":2,"stroke":"#fff","fill":"#9197F1"}},"position":{"name":"absolute"}}},"items":[{"id":"561e13f7-a0e7-4762-83ec-3626e36705df","group":"left","options":{},"attrs":{"circle":{"fill":"#4D53E8"}}}]},"id":"c28fe6e5-7d17-4ddb-b837-186cf859a1dd","data":{"shape":"end","title":"结束","hideDel":false,"initNode":false,"description":"工作流的最终节点，用于返回工作流运行后的结果信息。","initPos":{"x":1200,"y":100},"inputs":{"inputParameters":[],"settingOnError":{},"bizParam":{}},"branches":[{"position":"left","branchParam":{},"portId":"561e13f7-a0e7-4762-83ec-3626e36705df"}],"outputs":[{"name":"output","paramType":1,"valueRefName":"$.output","valueRefNodeId":"dfbffd79-3fdd-40f4-8523-3be3f645fc7f","valueRefNodeName":"融合策略","valueRefSource":"output","subParameters":[],"value":"","valueRefType":"ref","refVariable":null}],"bizParam":{"answerType":"returnByVariable","answerContent":{"type":"string","value":""}}},"zIndex":1},{"position":{"x":70,"y":88},"size":{"width":360,"height":92},"view":"vue-shape-view","shape":"start","ports":{"groups":{"right":{"attrs":{"circle":{"r":6,"magnet":true,"strokeWidth":2,"stroke":"#fff","fill":"#9197F1"}},"position":"right"},"left":{"attrs":{"circle":{"r":6,"magnet":true,"strokeWidth":2,"stroke":"#fff","fill":"#9197F1"}},"position":"left"},"pos":{"attrs":{"circle":{"r":6,"magnet":true,"strokeWidth":2,"stroke":"#fff","fill":"#9197F1"}},"position":{"name":"absolute"}}},"items":[{"id":"4d44a576-d2df-4da5-9d2f-bba127707339","group":"right","options":{},"attrs":{"circle":{"fill":"#4D53E8"}}}]},"id":"c31eb8dd-3246-4780-b73d-853d1eedda4e","data":{"shape":"start","hideDel":true,"title":"开始","description":"工作流的起始节点，用于设定启动工作流需要的信息。","initNode":true,"requestParams":[{"name":"BOT_USER_INPUT","paramDesc":"用户本轮对话输入内容","paramType":1,"location":1,"required":true,"defaultValue":"","enabled":true}],"settingOnError":{},"bizParam":{},"outputs":[],"branches":[{"position":"right","branchParam":{},"portId":"4d44a576-d2df-4da5-9d2f-bba127707339"}],"initPos":{"x":100,"y":100}},"zIndex":2},{"position":{"x":660,"y":70},"size":{"width":360,"height":140},"view":"vue-shape-view","shape":"dialogStrategy","ports":{"groups":{"right":{"attrs":{"circle":{"r":6,"magnet":true,"strokeWidth":2,"stroke":"#fff","fill":"#9197F1"}},"position":"right"},"left":{"attrs":{"circle":{"r":6,"magnet":true,"strokeWidth":2,"stroke":"#fff","fill":"#9197F1"}},"position":"left"},"pos":{"attrs":{"circle":{"r":6,"magnet":true,"strokeWidth":2,"stroke":"#fff","fill":"#9197F1"}},"position":{"name":"absolute"}}},"items":[{"id":"23f9d4da-4025-4c52-907a-298c3dee2292","group":"left","options":{},"attrs":{"circle":{"fill":"#4D53E8"}}},{"id":"28a9dd7e-ae96-4a52-b32d-e7eff2ea1b83","group":"right","options":{"output":true},"attrs":{"circle":{"fill":"#4D53E8"}}}]},"id":"dfbffd79-3fdd-40f4-8523-3be3f645fc7f","name":"融合策略","nodeType":"dialogStrategy","component":{"__v_isVNode":true,"__v_skip":true,"type":{"__name":"MenuNode","props":{"icon":{},"name":{}},"emits":["customEvent"],"__cssModules":{"Css":{"container":"_container_4aag0_1","info":"_info_4aag0_19","infoItem":"_infoItem_4aag0_26","icon":"_icon_4aag0_31","name":"_name_4aag0_39","addicon":"_addicon_4aag0_42"}}},"props":{"name":"融合策略"},"key":null,"ref":null,"scopeId":null,"slotScopeIds":null,"children":null,"component":null,"suspense":null,"ssContent":null,"ssFallback":null,"dirs":null,"transition":null,"el":null,"anchor":null,"target":null,"targetAnchor":null,"staticCount":0,"shapeFlag":4,"patchFlag":0,"dynamicProps":null,"dynamicChildren":null,"appContext":null,"ctx":null},"data":{"shape":"dialogStrategy","title":"融合策略","groupName":"大模型","description":"用户输入一个命题，从互联网上检索信息，判断用户的命题是否正确。","requestParams":[{"name":"query","paramType":1,"location":1,"required":false,"defaultValue":"","enabled":true,"valueRefName":"$.BOT_USER_INPUT","valueRefNodeId":"c31eb8dd-3246-4780-b73d-853d1eedda4e","valueRefNodeName":"开始","valueRefSource":"input","subParameters":[],"value":"","valueRefType":"ref","refVariable":null}],"settingOnError":{},"bizParam":{"skillParam":{"enableBotSkills":true,"skillList":[]},"kmsParam":{"retriever":3,"searchStrategy":"MIX","faq":{"enableSortModel":true,"sortCode":"model_992668744299715591","llmAccept":0.5,"topn":3,"faqAccept":0.8},"doc":{"enableSortModel":true,"docAccept":0.5,"topn":3}},"prompt":"{{query}}","systemPrompt":"","modelCode":"model_901039658069659653","modelName":"TeleChat","temperature":0.1,"topP":0.7,"maxTokens":1024},"outputs":[{"name":"output","paramDesc":"","paramType":1}],"branches":[{"position":"left","input":true,"branchParam":{},"portId":"23f9d4da-4025-4c52-907a-298c3dee2292"},{"output":true,"position":"right","branchParam":{},"portId":"28a9dd7e-ae96-4a52-b32d-e7eff2ea1b83"}]},"zIndex":3}],"edges":[{"shape":"edge","attrs":{"line":{"stroke":"#4D53E8"}},"id":"190bf85e-51b6-4bdf-9a5a-1c023e6d4e66","source":{"cell":"c31eb8dd-3246-4780-b73d-853d1eedda4e","port":"4d44a576-d2df-4da5-9d2f-bba127707339"},"target":{"cell":"dfbffd79-3fdd-40f4-8523-3be3f645fc7f","port":"23f9d4da-4025-4c52-907a-298c3dee2292"},"zIndex":4,"tools":{"items":[],"name":null}},{"shape":"edge","attrs":{"line":{"stroke":"#4D53E8"}},"id":"77559207-4f9a-4919-a42d-7aa1436f1934","source":{"cell":"dfbffd79-3fdd-40f4-8523-3be3f645fc7f","port":"28a9dd7e-ae96-4a52-b32d-e7eff2ea1b83"},"target":{"cell":"c28fe6e5-7d17-4ddb-b837-186cf859a1dd","port":"561e13f7-a0e7-4762-83ec-3626e36705df"},"zIndex":5,"tools":{"items":[],"name":null}}]}', 1, '呼出bot内置策略模板', 2, 2, 0, 0, 'ks', 'ks', '8470678936543756288', '管理员', '2025-02-24 17:11:36.172', '8470678936543756288', '管理员', '2025-02-24 17:22:09', '3', 'strategy_template', NULL, '1');

INSERT INTO bot_workflow
( workflow_id, workflow_name, workflow_cn_name, icon_url, scene, canvas, version, description, debug_status, status, bot_ref_count, yn, app_code, tenant_id, create_id, create_name, create_time, update_id, update_name, update_time, bot_type, workflow_type, main_workflow_id, is_default)
VALUES('ab1070c5-d83e-4a0d-aaa7-d7c0deb98564', '', '呼出bot内置策略模板', '/ais/common/f/images/defaultAvatar.png', NULL, '{"nodes":[{"position":{"x":1190,"y":70},"size":{"width":360,"height":128},"view":"vue-shape-view","shape":"end","ports":{"groups":{"right":{"attrs":{"circle":{"r":6,"magnet":true,"strokeWidth":2,"stroke":"#fff","fill":"#9197F1"}},"position":"right"},"left":{"attrs":{"circle":{"r":6,"magnet":true,"strokeWidth":2,"stroke":"#fff","fill":"#9197F1"}},"position":"left"},"pos":{"attrs":{"circle":{"r":6,"magnet":true,"strokeWidth":2,"stroke":"#fff","fill":"#9197F1"}},"position":{"name":"absolute"}}},"items":[{"id":"561e13f7-a0e7-4762-83ec-3626e36705df","group":"left","options":{},"attrs":{"circle":{"fill":"#4D53E8"}}}]},"id":"c28fe6e5-7d17-4ddb-b837-186cf859a1dd","data":{"shape":"end","title":"结束","hideDel":false,"initNode":false,"description":"工作流的最终节点，用于返回工作流运行后的结果信息。","initPos":{"x":1200,"y":100},"inputs":{"inputParameters":[],"settingOnError":{},"bizParam":{}},"branches":[{"position":"left","branchParam":{},"portId":"561e13f7-a0e7-4762-83ec-3626e36705df"}],"outputs":[{"name":"output","paramType":1,"valueRefName":"$.output","valueRefNodeId":"dfbffd79-3fdd-40f4-8523-3be3f645fc7f","valueRefNodeName":"融合策略","valueRefSource":"output","subParameters":[],"value":"","valueRefType":"ref","refVariable":null}],"bizParam":{"answerType":"returnByVariable","answerContent":{"type":"string","value":""}}},"zIndex":1},{"position":{"x":70,"y":88},"size":{"width":360,"height":92.390625},"view":"vue-shape-view","shape":"start","ports":{"groups":{"right":{"attrs":{"circle":{"r":6,"magnet":true,"strokeWidth":2,"stroke":"#fff","fill":"#9197F1"}},"position":"right"},"left":{"attrs":{"circle":{"r":6,"magnet":true,"strokeWidth":2,"stroke":"#fff","fill":"#9197F1"}},"position":"left"},"pos":{"attrs":{"circle":{"r":6,"magnet":true,"strokeWidth":2,"stroke":"#fff","fill":"#9197F1"}},"position":{"name":"absolute"}}},"items":[{"id":"4d44a576-d2df-4da5-9d2f-bba127707339","group":"right","options":{},"attrs":{"circle":{"fill":"#4D53E8"}}}]},"id":"c31eb8dd-3246-4780-b73d-853d1eedda4e","data":{"shape":"start","hideDel":true,"title":"开始","description":"工作流的起始节点，用于设定启动工作流需要的信息。","initNode":true,"requestParams":[{"name":"BOT_USER_INPUT","paramDesc":"用户本轮对话输入内容","paramType":1,"location":1,"required":true,"defaultValue":"","enabled":true}],"settingOnError":{},"bizParam":{},"outputs":[],"branches":[{"position":"right","branchParam":{},"portId":"4d44a576-d2df-4da5-9d2f-bba127707339"}],"initPos":{"x":100,"y":100}},"zIndex":2},{"position":{"x":660,"y":70},"size":{"width":360,"height":140},"view":"vue-shape-view","shape":"dialogStrategy","ports":{"groups":{"right":{"attrs":{"circle":{"r":6,"magnet":true,"strokeWidth":2,"stroke":"#fff","fill":"#9197F1"}},"position":"right"},"left":{"attrs":{"circle":{"r":6,"magnet":true,"strokeWidth":2,"stroke":"#fff","fill":"#9197F1"}},"position":"left"},"pos":{"attrs":{"circle":{"r":6,"magnet":true,"strokeWidth":2,"stroke":"#fff","fill":"#9197F1"}},"position":{"name":"absolute"}}},"items":[{"id":"23f9d4da-4025-4c52-907a-298c3dee2292","group":"left","options":{},"attrs":{"circle":{"fill":"#4D53E8"}}},{"id":"28a9dd7e-ae96-4a52-b32d-e7eff2ea1b83","group":"right","options":{"output":true},"attrs":{"circle":{"fill":"#4D53E8"}}}]},"id":"dfbffd79-3fdd-40f4-8523-3be3f645fc7f","name":"融合策略","nodeType":"dialogStrategy","component":{"__v_isVNode":true,"__v_skip":true,"type":{"__name":"MenuNode","props":{"icon":{},"name":{}},"emits":["customEvent"],"__cssModules":{"Css":{"container":"_container_4aag0_1","info":"_info_4aag0_19","infoItem":"_infoItem_4aag0_26","icon":"_icon_4aag0_31","name":"_name_4aag0_39","addicon":"_addicon_4aag0_42"}}},"props":{"name":"融合策略"},"key":null,"ref":null,"scopeId":null,"slotScopeIds":null,"children":null,"component":null,"suspense":null,"ssContent":null,"ssFallback":null,"dirs":null,"transition":null,"el":null,"anchor":null,"target":null,"targetAnchor":null,"staticCount":0,"shapeFlag":4,"patchFlag":0,"dynamicProps":null,"dynamicChildren":null,"appContext":null,"ctx":null},"data":{"shape":"dialogStrategy","title":"融合策略","groupName":"大模型","description":"用户输入一个命题，从互联网上检索信息，判断用户的命题是否正确。","requestParams":[{"name":"query","paramType":1,"location":1,"required":false,"defaultValue":"","enabled":true,"valueRefName":"$.BOT_USER_INPUT","valueRefNodeId":"c31eb8dd-3246-4780-b73d-853d1eedda4e","valueRefNodeName":"开始","valueRefSource":"input","subParameters":[],"value":"","valueRefType":"ref","refVariable":null}],"settingOnError":{},"bizParam":{"skillParam":{"enableBotSkills":true,"skillList":[]},"kmsParam":{"retriever":3,"searchStrategy":"MIX","faq":{"enableSortModel":true,"sortCode":"model_992668744299715591","llmAccept":0.5,"topn":3,"faqAccept":0.8},"doc":{"enableSortModel":true,"sortCode":"model_992668744299715591","docAccept":0.5,"topn":3}},"prompt":"{{query}}","systemPrompt":"","modelCode":"model_901039658069659653","modelName":"TeleChat","temperature":0.1,"topP":0.7,"maxTokens":1024},"outputs":[{"name":"output","paramDesc":"","paramType":1}],"branches":[{"position":"left","input":true,"branchParam":{},"portId":"23f9d4da-4025-4c52-907a-298c3dee2292"},{"output":true,"position":"right","branchParam":{},"portId":"28a9dd7e-ae96-4a52-b32d-e7eff2ea1b83"}]},"zIndex":3}],"edges":[{"shape":"edge","attrs":{"line":{"stroke":"#4D53E8"}},"id":"190bf85e-51b6-4bdf-9a5a-1c023e6d4e66","zIndex":4,"source":{"cell":"c31eb8dd-3246-4780-b73d-853d1eedda4e","port":"4d44a576-d2df-4da5-9d2f-bba127707339"},"target":{"cell":"dfbffd79-3fdd-40f4-8523-3be3f645fc7f","port":"23f9d4da-4025-4c52-907a-298c3dee2292"},"tools":{"items":[],"name":null}},{"shape":"edge","attrs":{"line":{"stroke":"#4D53E8"}},"id":"77559207-4f9a-4919-a42d-7aa1436f1934","zIndex":5,"source":{"cell":"dfbffd79-3fdd-40f4-8523-3be3f645fc7f","port":"28a9dd7e-ae96-4a52-b32d-e7eff2ea1b83"},"target":{"cell":"c28fe6e5-7d17-4ddb-b837-186cf859a1dd","port":"561e13f7-a0e7-4762-83ec-3626e36705df"},"tools":{"items":[],"name":null}}]}', 2, '呼出bot内置策略模板', 1, 2, 0, 0, 'ks', 'ks', '8470678936543756288', '管理员', '2025-02-24 17:11:36.172', '8470678936543756288', '管理员', '2025-03-17 15:46:49', '3', 'strategy_template', NULL, '1');


INSERT INTO bot_workflow_node
(workflow_id, node_id, node_type, `data`, version, yn, create_id, create_name, create_time, update_id, update_name, update_time, app_code)
VALUES( 'ab1070c5-d83e-4a0d-aaa7-d7c0deb98564', 'c28fe6e5-7d17-4ddb-b837-186cf859a1dd', 'END', '{"inputs":{"bizParam":{"answerType":"returnByVariable","answerContent":{"type":"string","value":""}},"branches":[{"branchParam":{},"portId":"561e13f7-a0e7-4762-83ec-3626e36705df"}]},"nodeMeta":{"description":"","imageflow":false,"subTitle":"","title":"结束"},"outputs":[{"name":"output","paramType":1,"subParameters":[],"value":"","valueRefName":"$.output","valueRefNodeId":"dfbffd79-3fdd-40f4-8523-3be3f645fc7f","valueRefNodeName":"融合策略","valueRefSource":"output","valueRefType":"ref","varDataType":1,"variableRef":false}]}', 1, 0, '8470678936543756288', '管理员', '2025-02-24 17:13:36.220', '8470678936543756288', '管理员', '2025-02-24 17:13:36.220', 'ks');
INSERT INTO bot_workflow_node
(workflow_id, node_id, node_type, `data`, version, yn, create_id, create_name, create_time, update_id, update_name, update_time, app_code)
VALUES('ab1070c5-d83e-4a0d-aaa7-d7c0deb98564', 'c28fe6e5-7d17-4ddb-b837-186cf859a1dd', 'END', '{"inputs":{"bizParam":{"answerType":"returnByVariable","answerContent":{"type":"string","value":""}},"branches":[{"branchParam":{},"portId":"561e13f7-a0e7-4762-83ec-3626e36705df"}]},"nodeMeta":{"description":"","imageflow":false,"subTitle":"","title":"结束"},"outputs":[{"name":"output","paramType":1,"subParameters":[],"value":"","valueRefName":"$.output","valueRefNodeId":"dfbffd79-3fdd-40f4-8523-3be3f645fc7f","valueRefNodeName":"融合策略","valueRefSource":"output","valueRefType":"ref","varDataType":1,"variableRef":false}]}', 2, 0, '8470678936543756288', '管理员', '2025-03-17 15:46:49.404', '8470678936543756288', '管理员', '2025-03-17 15:46:49.404', 'ks');
INSERT INTO bot_workflow_node
(workflow_id, node_id, node_type, `data`, version, yn, create_id, create_name, create_time, update_id, update_name, update_time, app_code)
VALUES('ab1070c5-d83e-4a0d-aaa7-d7c0deb98564', 'c31eb8dd-3246-4780-b73d-853d1eedda4e', 'START', '{"inputs":{"bizParam":{},"branches":[{"branchParam":{},"portId":"4d44a576-d2df-4da5-9d2f-bba127707339"}],"requestParams":[{"enabled":true,"name":"BOT_USER_INPUT","paramDesc":"用户本轮对话输入内容","paramType":1,"required":true,"varDataType":1,"variableRef":false}],"settingOnError":{}},"nodeMeta":{"description":"","imageflow":false,"subTitle":"","title":"开始"},"outputs":[]}', 1, 0, '8470678936543756288', '管理员', '2025-02-24 17:13:36.220', '8470678936543756288', '管理员', '2025-02-24 17:13:36.220', 'ks');
INSERT INTO bot_workflow_node
(workflow_id, node_id, node_type, `data`, version, yn, create_id, create_name, create_time, update_id, update_name, update_time, app_code)
VALUES( 'ab1070c5-d83e-4a0d-aaa7-d7c0deb98564', 'c31eb8dd-3246-4780-b73d-853d1eedda4e', 'START', '{"inputs":{"bizParam":{},"branches":[{"branchParam":{},"portId":"4d44a576-d2df-4da5-9d2f-bba127707339"}],"requestParams":[{"enabled":true,"name":"BOT_USER_INPUT","paramDesc":"用户本轮对话输入内容","paramType":1,"required":true,"varDataType":1,"variableRef":false}],"settingOnError":{}},"nodeMeta":{"description":"","imageflow":false,"subTitle":"","title":"开始"},"outputs":[]}', 2, 0, '8470678936543756288', '管理员', '2025-03-17 15:46:49.404', '8470678936543756288', '管理员', '2025-03-17 15:46:49.404', 'ks');
INSERT INTO bot_workflow_node
(workflow_id, node_id, node_type, `data`, version, yn, create_id, create_name, create_time, update_id, update_name, update_time, app_code)
VALUES( 'ab1070c5-d83e-4a0d-aaa7-d7c0deb98564', 'dfbffd79-3fdd-40f4-8523-3be3f645fc7f', 'DIALOG_STRATEGY', '{"inputs":{"bizParam":{"skillParam":{"enableBotSkills":true,"skillList":[]},"kmsParam":{"retriever":3,"searchStrategy":"MIX","faq":{"enableSortModel":true,"sortCode":"model_992668744299715591","llmAccept":0.5,"topn":3,"faqAccept":0.8},"doc":{"enableSortModel":true,"docAccept":0.5,"topn":3}},"prompt":"{{query}}","systemPrompt":"","modelCode":"model_901039658069659653","modelName":"TeleChat","temperature":0.1,"topP":0.7,"maxTokens":1024},"branches":[{"branchParam":{},"portId":"23f9d4da-4025-4c52-907a-298c3dee2292"},{"branchParam":{},"portId":"28a9dd7e-ae96-4a52-b32d-e7eff2ea1b83"}],"requestParams":[{"enabled":true,"name":"query","paramType":1,"required":false,"subParameters":[],"value":"","valueRefName":"$.BOT_USER_INPUT","valueRefNodeId":"c31eb8dd-3246-4780-b73d-853d1eedda4e","valueRefNodeName":"开始","valueRefSource":"input","valueRefType":"ref","varDataType":1,"variableRef":false}],"settingOnError":{}},"nodeMeta":{"description":"","imageflow":false,"subTitle":"","title":"融合策略"},"outputs":[{"name":"output","paramDesc":"","paramType":1,"varDataType":1,"variableRef":false}]}', 1, 0, '8470678936543756288', '管理员', '2025-02-24 17:13:36.220', '8470678936543756288', '管理员', '2025-02-24 17:13:36.220', 'ks');
INSERT INTO bot_workflow_node
(workflow_id, node_id, node_type, `data`, version, yn, create_id, create_name, create_time, update_id, update_name, update_time, app_code)
VALUES('ab1070c5-d83e-4a0d-aaa7-d7c0deb98564', 'dfbffd79-3fdd-40f4-8523-3be3f645fc7f', 'DIALOG_STRATEGY', '{"inputs":{"bizParam":{"skillParam":{"enableBotSkills":true,"skillList":[]},"kmsParam":{"retriever":3,"searchStrategy":"MIX","faq":{"enableSortModel":true,"sortCode":"model_992668744299715591","llmAccept":0.5,"topn":3,"faqAccept":0.8},"doc":{"enableSortModel":true,"sortCode":"model_992668744299715591","docAccept":0.5,"topn":3}},"prompt":"{{query}}","systemPrompt":"","modelCode":"model_901039658069659653","modelName":"TeleChat","temperature":0.1,"topP":0.7,"maxTokens":1024},"branches":[{"branchParam":{},"portId":"23f9d4da-4025-4c52-907a-298c3dee2292"},{"branchParam":{},"portId":"28a9dd7e-ae96-4a52-b32d-e7eff2ea1b83"}],"requestParams":[{"enabled":true,"name":"query","paramType":1,"required":false,"subParameters":[],"value":"","valueRefName":"$.BOT_USER_INPUT","valueRefNodeId":"c31eb8dd-3246-4780-b73d-853d1eedda4e","valueRefNodeName":"开始","valueRefSource":"input","valueRefType":"ref","varDataType":1,"variableRef":false}],"settingOnError":{}},"nodeMeta":{"description":"","imageflow":false,"subTitle":"","title":"融合策略"},"outputs":[{"name":"output","paramDesc":"","paramType":1,"varDataType":1,"variableRef":false}]}', 2, 0, '8470678936543756288', '管理员', '2025-03-17 15:46:49.406', '8470678936543756288', '管理员', '2025-03-17 15:46:49.406', 'ks');

INSERT INTO bot_workflow_edge
(workflow_id, source_node_id, source_port_id, target_node_id, target_port_id, version, yn, create_id, create_name, create_time, update_id, update_name, update_time, app_code)
VALUES('ab1070c5-d83e-4a0d-aaa7-d7c0deb98564', 'c31eb8dd-3246-4780-b73d-853d1eedda4e', '4d44a576-d2df-4da5-9d2f-bba127707339', 'dfbffd79-3fdd-40f4-8523-3be3f645fc7f', '23f9d4da-4025-4c52-907a-298c3dee2292', 1, 0, '8470678936543756288', '管理员', '2025-02-24 17:13:36.226', '8470678936543756288', '管理员', '2025-02-24 17:13:36.226', 'ks');
INSERT INTO bot_workflow_edge
(workflow_id, source_node_id, source_port_id, target_node_id, target_port_id, version, yn, create_id, create_name, create_time, update_id, update_name, update_time, app_code)
VALUES('ab1070c5-d83e-4a0d-aaa7-d7c0deb98564', 'dfbffd79-3fdd-40f4-8523-3be3f645fc7f', '28a9dd7e-ae96-4a52-b32d-e7eff2ea1b83', 'c28fe6e5-7d17-4ddb-b837-186cf859a1dd', '561e13f7-a0e7-4762-83ec-3626e36705df', 1, 0, '8470678936543756288', '管理员', '2025-02-24 17:13:36.226', '8470678936543756288', '管理员', '2025-02-24 17:13:36.226', 'ks');
INSERT INTO bot_workflow_edge
(workflow_id, source_node_id, source_port_id, target_node_id, target_port_id, version, yn, create_id, create_name, create_time, update_id, update_name, update_time, app_code)
VALUES('ab1070c5-d83e-4a0d-aaa7-d7c0deb98564', 'c31eb8dd-3246-4780-b73d-853d1eedda4e', '4d44a576-d2df-4da5-9d2f-bba127707339', 'dfbffd79-3fdd-40f4-8523-3be3f645fc7f', '23f9d4da-4025-4c52-907a-298c3dee2292', 2, 0, '8470678936543756288', '管理员', '2025-03-17 15:46:49.418', '8470678936543756288', '管理员', '2025-03-17 15:46:49.418', 'ks');
INSERT INTO bot_workflow_edge
( workflow_id, source_node_id, source_port_id, target_node_id, target_port_id, version, yn, create_id, create_name, create_time, update_id, update_name, update_time, app_code)
VALUES('ab1070c5-d83e-4a0d-aaa7-d7c0deb98564', 'dfbffd79-3fdd-40f4-8523-3be3f645fc7f', '28a9dd7e-ae96-4a52-b32d-e7eff2ea1b83', 'c28fe6e5-7d17-4ddb-b837-186cf859a1dd', '561e13f7-a0e7-4762-83ec-3626e36705df', 2, 0, '8470678936543756288', '管理员', '2025-03-17 15:46:49.418', '8470678936543756288', '管理员', '2025-03-17 15:46:49.418', 'ks');


INSERT INTO workflow_version_info
( tenant_id, workflow_id, version, status, runtime_status, version_desc, yn, app_code, create_id, create_name, update_id, update_name, create_time, update_time)
VALUES( '', 'ab1070c5-d83e-4a0d-aaa7-d7c0deb84629', 1, 'PUBLISHED', 'RUNNING_PROD', '', 0, 'ks', '8470678936543756288', '管理员', '8470678936543756288', '管理员', '2025-02-24 17:11:36.160', '2025-02-24 17:22:09');
INSERT INTO workflow_version_info
( tenant_id, workflow_id, version, status, runtime_status, version_desc, yn, app_code, create_id, create_name, update_id, update_name, create_time, update_time)
VALUES( '', 'ab1070c5-d83e-4a0d-aaa7-d7c0deb84629', 2, 'EDITING', 'OFF_LINE', '', 0, 'ks', '8470678936543756288', '管理员', '8470678936543756288', '管理员', '2025-02-24 17:22:09.167', '2025-02-24 17:22:09.167');

INSERT INTO bot_workflow
(workflow_id, workflow_name, workflow_cn_name, icon_url, scene, canvas, version, description, debug_status, status, bot_ref_count, yn, app_code, tenant_id, create_id, create_name, create_time, update_id, update_name, update_time, bot_type, workflow_type, main_workflow_id, is_default)
VALUES('ab1070c5-d83e-4a0d-aaa7-d7c0deb84629', '', '呼入bot内置策略模板', '/ais/common/f/images/defaultAvatar.png', NULL, '{"nodes":[{"position":{"x":1190,"y":70},"size":{"width":360,"height":128},"view":"vue-shape-view","shape":"end","ports":{"groups":{"right":{"attrs":{"circle":{"r":6,"magnet":true,"strokeWidth":2,"stroke":"#fff","fill":"#9197F1"}},"position":"right"},"left":{"attrs":{"circle":{"r":6,"magnet":true,"strokeWidth":2,"stroke":"#fff","fill":"#9197F1"}},"position":"left"},"pos":{"attrs":{"circle":{"r":6,"magnet":true,"strokeWidth":2,"stroke":"#fff","fill":"#9197F1"}},"position":{"name":"absolute"}}},"items":[{"id":"561e13f7-a0e7-4762-83ec-3626e36705df","group":"left","options":{},"attrs":{"circle":{"fill":"#4D53E8"}}}]},"id":"c28fe6e5-7d17-4ddb-b837-186cf859a1dd","data":{"shape":"end","title":"结束","hideDel":false,"initNode":false,"description":"工作流的最终节点，用于返回工作流运行后的结果信息。","initPos":{"x":1200,"y":100},"inputs":{"inputParameters":[],"settingOnError":{},"bizParam":{}},"branches":[{"position":"left","branchParam":{},"portId":"561e13f7-a0e7-4762-83ec-3626e36705df"}],"outputs":[{"name":"output","paramType":1,"valueRefName":"$.output","valueRefNodeId":"dfbffd79-3fdd-40f4-8523-3be3f645fc7f","valueRefNodeName":"融合策略","valueRefSource":"output","subParameters":[],"value":"","valueRefType":"ref","refVariable":null}],"bizParam":{"answerType":"returnByVariable","answerContent":{"type":"string","value":""}}},"zIndex":1},{"position":{"x":70,"y":88},"size":{"width":360,"height":92},"view":"vue-shape-view","shape":"start","ports":{"groups":{"right":{"attrs":{"circle":{"r":6,"magnet":true,"strokeWidth":2,"stroke":"#fff","fill":"#9197F1"}},"position":"right"},"left":{"attrs":{"circle":{"r":6,"magnet":true,"strokeWidth":2,"stroke":"#fff","fill":"#9197F1"}},"position":"left"},"pos":{"attrs":{"circle":{"r":6,"magnet":true,"strokeWidth":2,"stroke":"#fff","fill":"#9197F1"}},"position":{"name":"absolute"}}},"items":[{"id":"4d44a576-d2df-4da5-9d2f-bba127707339","group":"right","options":{},"attrs":{"circle":{"fill":"#4D53E8"}}}]},"id":"c31eb8dd-3246-4780-b73d-853d1eedda4e","data":{"shape":"start","hideDel":true,"title":"开始","description":"工作流的起始节点，用于设定启动工作流需要的信息。","initNode":true,"requestParams":[{"name":"BOT_USER_INPUT","paramDesc":"用户本轮对话输入内容","paramType":1,"location":1,"required":true,"defaultValue":"","enabled":true}],"settingOnError":{},"bizParam":{},"outputs":[],"branches":[{"position":"right","branchParam":{},"portId":"4d44a576-d2df-4da5-9d2f-bba127707339"}],"initPos":{"x":100,"y":100}},"zIndex":2},{"position":{"x":660,"y":70},"size":{"width":360,"height":140},"view":"vue-shape-view","shape":"dialogStrategy","ports":{"groups":{"right":{"attrs":{"circle":{"r":6,"magnet":true,"strokeWidth":2,"stroke":"#fff","fill":"#9197F1"}},"position":"right"},"left":{"attrs":{"circle":{"r":6,"magnet":true,"strokeWidth":2,"stroke":"#fff","fill":"#9197F1"}},"position":"left"},"pos":{"attrs":{"circle":{"r":6,"magnet":true,"strokeWidth":2,"stroke":"#fff","fill":"#9197F1"}},"position":{"name":"absolute"}}},"items":[{"id":"23f9d4da-4025-4c52-907a-298c3dee2292","group":"left","options":{},"attrs":{"circle":{"fill":"#4D53E8"}}},{"id":"28a9dd7e-ae96-4a52-b32d-e7eff2ea1b83","group":"right","options":{"output":true},"attrs":{"circle":{"fill":"#4D53E8"}}}]},"id":"dfbffd79-3fdd-40f4-8523-3be3f645fc7f","name":"融合策略","nodeType":"dialogStrategy","component":{"__v_isVNode":true,"__v_skip":true,"type":{"__name":"MenuNode","props":{"icon":{},"name":{}},"emits":["customEvent"],"__cssModules":{"Css":{"container":"_container_4aag0_1","info":"_info_4aag0_19","infoItem":"_infoItem_4aag0_26","icon":"_icon_4aag0_31","name":"_name_4aag0_39","addicon":"_addicon_4aag0_42"}}},"props":{"name":"融合策略"},"key":null,"ref":null,"scopeId":null,"slotScopeIds":null,"children":null,"component":null,"suspense":null,"ssContent":null,"ssFallback":null,"dirs":null,"transition":null,"el":null,"anchor":null,"target":null,"targetAnchor":null,"staticCount":0,"shapeFlag":4,"patchFlag":0,"dynamicProps":null,"dynamicChildren":null,"appContext":null,"ctx":null},"data":{"shape":"dialogStrategy","title":"融合策略","groupName":"大模型","description":"用户输入一个命题，从互联网上检索信息，判断用户的命题是否正确。","requestParams":[{"name":"query","paramType":1,"location":1,"required":false,"defaultValue":"","enabled":true,"valueRefName":"$.BOT_USER_INPUT","valueRefNodeId":"c31eb8dd-3246-4780-b73d-853d1eedda4e","valueRefNodeName":"开始","valueRefSource":"input","subParameters":[],"value":"","valueRefType":"ref","refVariable":null}],"settingOnError":{},"bizParam":{"skillParam":{"enableBotSkills":true,"skillList":[]},"kmsParam":{"retriever":3,"searchStrategy":"MIX","faq":{"enableSortModel":true,"sortCode":"model_992668744299715591","llmAccept":0.5,"topn":3,"faqAccept":0.8},"doc":{"enableSortModel":true,"docAccept":0.5,"topn":3}},"prompt":"{{query}}","systemPrompt":"","modelCode":"model_901039658069659653","modelName":"TeleChat","temperature":0.1,"topP":0.7,"maxTokens":1024},"outputs":[{"name":"output","paramDesc":"","paramType":1}],"branches":[{"position":"left","input":true,"branchParam":{},"portId":"23f9d4da-4025-4c52-907a-298c3dee2292"},{"output":true,"position":"right","branchParam":{},"portId":"28a9dd7e-ae96-4a52-b32d-e7eff2ea1b83"}]},"zIndex":3}],"edges":[{"shape":"edge","attrs":{"line":{"stroke":"#4D53E8"}},"id":"190bf85e-51b6-4bdf-9a5a-1c023e6d4e66","source":{"cell":"c31eb8dd-3246-4780-b73d-853d1eedda4e","port":"4d44a576-d2df-4da5-9d2f-bba127707339"},"target":{"cell":"dfbffd79-3fdd-40f4-8523-3be3f645fc7f","port":"23f9d4da-4025-4c52-907a-298c3dee2292"},"zIndex":4,"tools":{"items":[],"name":null}},{"shape":"edge","attrs":{"line":{"stroke":"#4D53E8"}},"id":"77559207-4f9a-4919-a42d-7aa1436f1934","source":{"cell":"dfbffd79-3fdd-40f4-8523-3be3f645fc7f","port":"28a9dd7e-ae96-4a52-b32d-e7eff2ea1b83"},"target":{"cell":"c28fe6e5-7d17-4ddb-b837-186cf859a1dd","port":"561e13f7-a0e7-4762-83ec-3626e36705df"},"zIndex":5,"tools":{"items":[],"name":null}}]}', 1, '呼入bot内置策略模板', 2, 2, 0, 0, 'ks', 'ks', '8470678936543756288', '管理员', '2025-02-24 17:11:36.172', '8470678936543756288', '管理员', '2025-02-24 17:22:09', '2', 'strategy_template', NULL, '1');

INSERT INTO bot_workflow
( workflow_id, workflow_name, workflow_cn_name, icon_url, scene, canvas, version, description, debug_status, status, bot_ref_count, yn, app_code, tenant_id, create_id, create_name, create_time, update_id, update_name, update_time, bot_type, workflow_type, main_workflow_id, is_default)
VALUES('ab1070c5-d83e-4a0d-aaa7-d7c0deb84629', '', '呼入bot内置策略模板', '/ais/common/f/images/defaultAvatar.png', NULL, '{"nodes":[{"position":{"x":1190,"y":70},"size":{"width":360,"height":128},"view":"vue-shape-view","shape":"end","ports":{"groups":{"right":{"attrs":{"circle":{"r":6,"magnet":true,"strokeWidth":2,"stroke":"#fff","fill":"#9197F1"}},"position":"right"},"left":{"attrs":{"circle":{"r":6,"magnet":true,"strokeWidth":2,"stroke":"#fff","fill":"#9197F1"}},"position":"left"},"pos":{"attrs":{"circle":{"r":6,"magnet":true,"strokeWidth":2,"stroke":"#fff","fill":"#9197F1"}},"position":{"name":"absolute"}}},"items":[{"id":"561e13f7-a0e7-4762-83ec-3626e36705df","group":"left","options":{},"attrs":{"circle":{"fill":"#4D53E8"}}}]},"id":"c28fe6e5-7d17-4ddb-b837-186cf859a1dd","data":{"shape":"end","title":"结束","hideDel":false,"initNode":false,"description":"工作流的最终节点，用于返回工作流运行后的结果信息。","initPos":{"x":1200,"y":100},"inputs":{"inputParameters":[],"settingOnError":{},"bizParam":{}},"branches":[{"position":"left","branchParam":{},"portId":"561e13f7-a0e7-4762-83ec-3626e36705df"}],"outputs":[{"name":"output","paramType":1,"valueRefName":"$.output","valueRefNodeId":"dfbffd79-3fdd-40f4-8523-3be3f645fc7f","valueRefNodeName":"融合策略","valueRefSource":"output","subParameters":[],"value":"","valueRefType":"ref","refVariable":null}],"bizParam":{"answerType":"returnByVariable","answerContent":{"type":"string","value":""}}},"zIndex":1},{"position":{"x":70,"y":88},"size":{"width":360,"height":92.390625},"view":"vue-shape-view","shape":"start","ports":{"groups":{"right":{"attrs":{"circle":{"r":6,"magnet":true,"strokeWidth":2,"stroke":"#fff","fill":"#9197F1"}},"position":"right"},"left":{"attrs":{"circle":{"r":6,"magnet":true,"strokeWidth":2,"stroke":"#fff","fill":"#9197F1"}},"position":"left"},"pos":{"attrs":{"circle":{"r":6,"magnet":true,"strokeWidth":2,"stroke":"#fff","fill":"#9197F1"}},"position":{"name":"absolute"}}},"items":[{"id":"4d44a576-d2df-4da5-9d2f-bba127707339","group":"right","options":{},"attrs":{"circle":{"fill":"#4D53E8"}}}]},"id":"c31eb8dd-3246-4780-b73d-853d1eedda4e","data":{"shape":"start","hideDel":true,"title":"开始","description":"工作流的起始节点，用于设定启动工作流需要的信息。","initNode":true,"requestParams":[{"name":"BOT_USER_INPUT","paramDesc":"用户本轮对话输入内容","paramType":1,"location":1,"required":true,"defaultValue":"","enabled":true}],"settingOnError":{},"bizParam":{},"outputs":[],"branches":[{"position":"right","branchParam":{},"portId":"4d44a576-d2df-4da5-9d2f-bba127707339"}],"initPos":{"x":100,"y":100}},"zIndex":2},{"position":{"x":660,"y":70},"size":{"width":360,"height":140},"view":"vue-shape-view","shape":"dialogStrategy","ports":{"groups":{"right":{"attrs":{"circle":{"r":6,"magnet":true,"strokeWidth":2,"stroke":"#fff","fill":"#9197F1"}},"position":"right"},"left":{"attrs":{"circle":{"r":6,"magnet":true,"strokeWidth":2,"stroke":"#fff","fill":"#9197F1"}},"position":"left"},"pos":{"attrs":{"circle":{"r":6,"magnet":true,"strokeWidth":2,"stroke":"#fff","fill":"#9197F1"}},"position":{"name":"absolute"}}},"items":[{"id":"23f9d4da-4025-4c52-907a-298c3dee2292","group":"left","options":{},"attrs":{"circle":{"fill":"#4D53E8"}}},{"id":"28a9dd7e-ae96-4a52-b32d-e7eff2ea1b83","group":"right","options":{"output":true},"attrs":{"circle":{"fill":"#4D53E8"}}}]},"id":"dfbffd79-3fdd-40f4-8523-3be3f645fc7f","name":"融合策略","nodeType":"dialogStrategy","component":{"__v_isVNode":true,"__v_skip":true,"type":{"__name":"MenuNode","props":{"icon":{},"name":{}},"emits":["customEvent"],"__cssModules":{"Css":{"container":"_container_4aag0_1","info":"_info_4aag0_19","infoItem":"_infoItem_4aag0_26","icon":"_icon_4aag0_31","name":"_name_4aag0_39","addicon":"_addicon_4aag0_42"}}},"props":{"name":"融合策略"},"key":null,"ref":null,"scopeId":null,"slotScopeIds":null,"children":null,"component":null,"suspense":null,"ssContent":null,"ssFallback":null,"dirs":null,"transition":null,"el":null,"anchor":null,"target":null,"targetAnchor":null,"staticCount":0,"shapeFlag":4,"patchFlag":0,"dynamicProps":null,"dynamicChildren":null,"appContext":null,"ctx":null},"data":{"shape":"dialogStrategy","title":"融合策略","groupName":"大模型","description":"用户输入一个命题，从互联网上检索信息，判断用户的命题是否正确。","requestParams":[{"name":"query","paramType":1,"location":1,"required":false,"defaultValue":"","enabled":true,"valueRefName":"$.BOT_USER_INPUT","valueRefNodeId":"c31eb8dd-3246-4780-b73d-853d1eedda4e","valueRefNodeName":"开始","valueRefSource":"input","subParameters":[],"value":"","valueRefType":"ref","refVariable":null}],"settingOnError":{},"bizParam":{"skillParam":{"enableBotSkills":true,"skillList":[]},"kmsParam":{"retriever":3,"searchStrategy":"MIX","faq":{"enableSortModel":true,"sortCode":"model_992668744299715591","llmAccept":0.5,"topn":3,"faqAccept":0.8},"doc":{"enableSortModel":true,"sortCode":"model_992668744299715591","docAccept":0.5,"topn":3}},"prompt":"{{query}}","systemPrompt":"","modelCode":"model_901039658069659653","modelName":"TeleChat","temperature":0.1,"topP":0.7,"maxTokens":1024},"outputs":[{"name":"output","paramDesc":"","paramType":1}],"branches":[{"position":"left","input":true,"branchParam":{},"portId":"23f9d4da-4025-4c52-907a-298c3dee2292"},{"output":true,"position":"right","branchParam":{},"portId":"28a9dd7e-ae96-4a52-b32d-e7eff2ea1b83"}]},"zIndex":3}],"edges":[{"shape":"edge","attrs":{"line":{"stroke":"#4D53E8"}},"id":"190bf85e-51b6-4bdf-9a5a-1c023e6d4e66","zIndex":4,"source":{"cell":"c31eb8dd-3246-4780-b73d-853d1eedda4e","port":"4d44a576-d2df-4da5-9d2f-bba127707339"},"target":{"cell":"dfbffd79-3fdd-40f4-8523-3be3f645fc7f","port":"23f9d4da-4025-4c52-907a-298c3dee2292"},"tools":{"items":[],"name":null}},{"shape":"edge","attrs":{"line":{"stroke":"#4D53E8"}},"id":"77559207-4f9a-4919-a42d-7aa1436f1934","zIndex":5,"source":{"cell":"dfbffd79-3fdd-40f4-8523-3be3f645fc7f","port":"28a9dd7e-ae96-4a52-b32d-e7eff2ea1b83"},"target":{"cell":"c28fe6e5-7d17-4ddb-b837-186cf859a1dd","port":"561e13f7-a0e7-4762-83ec-3626e36705df"},"tools":{"items":[],"name":null}}]}', 2, '呼入bot内置策略模板', 1, 2, 0, 0, 'ks', 'ks', '8470678936543756288', '管理员', '2025-02-24 17:11:36.172', '8470678936543756288', '管理员', '2025-03-17 15:46:49', '2', 'strategy_template', NULL, '1');


INSERT INTO bot_workflow_node
(workflow_id, node_id, node_type, `data`, version, yn, create_id, create_name, create_time, update_id, update_name, update_time, app_code)
VALUES( 'ab1070c5-d83e-4a0d-aaa7-d7c0deb84629', 'c28fe6e5-7d17-4ddb-b837-186cf859a1dd', 'END', '{"inputs":{"bizParam":{"answerType":"returnByVariable","answerContent":{"type":"string","value":""}},"branches":[{"branchParam":{},"portId":"561e13f7-a0e7-4762-83ec-3626e36705df"}]},"nodeMeta":{"description":"","imageflow":false,"subTitle":"","title":"结束"},"outputs":[{"name":"output","paramType":1,"subParameters":[],"value":"","valueRefName":"$.output","valueRefNodeId":"dfbffd79-3fdd-40f4-8523-3be3f645fc7f","valueRefNodeName":"融合策略","valueRefSource":"output","valueRefType":"ref","varDataType":1,"variableRef":false}]}', 1, 0, '8470678936543756288', '管理员', '2025-02-24 17:13:36.220', '8470678936543756288', '管理员', '2025-02-24 17:13:36.220', 'ks');
INSERT INTO bot_workflow_node
(workflow_id, node_id, node_type, `data`, version, yn, create_id, create_name, create_time, update_id, update_name, update_time, app_code)
VALUES('ab1070c5-d83e-4a0d-aaa7-d7c0deb84629', 'c28fe6e5-7d17-4ddb-b837-186cf859a1dd', 'END', '{"inputs":{"bizParam":{"answerType":"returnByVariable","answerContent":{"type":"string","value":""}},"branches":[{"branchParam":{},"portId":"561e13f7-a0e7-4762-83ec-3626e36705df"}]},"nodeMeta":{"description":"","imageflow":false,"subTitle":"","title":"结束"},"outputs":[{"name":"output","paramType":1,"subParameters":[],"value":"","valueRefName":"$.output","valueRefNodeId":"dfbffd79-3fdd-40f4-8523-3be3f645fc7f","valueRefNodeName":"融合策略","valueRefSource":"output","valueRefType":"ref","varDataType":1,"variableRef":false}]}', 2, 0, '8470678936543756288', '管理员', '2025-03-17 15:46:49.404', '8470678936543756288', '管理员', '2025-03-17 15:46:49.404', 'ks');
INSERT INTO bot_workflow_node
(workflow_id, node_id, node_type, `data`, version, yn, create_id, create_name, create_time, update_id, update_name, update_time, app_code)
VALUES('ab1070c5-d83e-4a0d-aaa7-d7c0deb84629', 'c31eb8dd-3246-4780-b73d-853d1eedda4e', 'START', '{"inputs":{"bizParam":{},"branches":[{"branchParam":{},"portId":"4d44a576-d2df-4da5-9d2f-bba127707339"}],"requestParams":[{"enabled":true,"name":"BOT_USER_INPUT","paramDesc":"用户本轮对话输入内容","paramType":1,"required":true,"varDataType":1,"variableRef":false}],"settingOnError":{}},"nodeMeta":{"description":"","imageflow":false,"subTitle":"","title":"开始"},"outputs":[]}', 1, 0, '8470678936543756288', '管理员', '2025-02-24 17:13:36.220', '8470678936543756288', '管理员', '2025-02-24 17:13:36.220', 'ks');
INSERT INTO bot_workflow_node
(workflow_id, node_id, node_type, `data`, version, yn, create_id, create_name, create_time, update_id, update_name, update_time, app_code)
VALUES( 'ab1070c5-d83e-4a0d-aaa7-d7c0deb84629', 'c31eb8dd-3246-4780-b73d-853d1eedda4e', 'START', '{"inputs":{"bizParam":{},"branches":[{"branchParam":{},"portId":"4d44a576-d2df-4da5-9d2f-bba127707339"}],"requestParams":[{"enabled":true,"name":"BOT_USER_INPUT","paramDesc":"用户本轮对话输入内容","paramType":1,"required":true,"varDataType":1,"variableRef":false}],"settingOnError":{}},"nodeMeta":{"description":"","imageflow":false,"subTitle":"","title":"开始"},"outputs":[]}', 2, 0, '8470678936543756288', '管理员', '2025-03-17 15:46:49.404', '8470678936543756288', '管理员', '2025-03-17 15:46:49.404', 'ks');
INSERT INTO bot_workflow_node
(workflow_id, node_id, node_type, `data`, version, yn, create_id, create_name, create_time, update_id, update_name, update_time, app_code)
VALUES( 'ab1070c5-d83e-4a0d-aaa7-d7c0deb84629', 'dfbffd79-3fdd-40f4-8523-3be3f645fc7f', 'DIALOG_STRATEGY', '{"inputs":{"bizParam":{"skillParam":{"enableBotSkills":true,"skillList":[]},"kmsParam":{"retriever":3,"searchStrategy":"MIX","faq":{"enableSortModel":true,"sortCode":"model_992668744299715591","llmAccept":0.5,"topn":3,"faqAccept":0.8},"doc":{"enableSortModel":true,"docAccept":0.5,"topn":3}},"prompt":"{{query}}","systemPrompt":"","modelCode":"model_901039658069659653","modelName":"TeleChat","temperature":0.1,"topP":0.7,"maxTokens":1024},"branches":[{"branchParam":{},"portId":"23f9d4da-4025-4c52-907a-298c3dee2292"},{"branchParam":{},"portId":"28a9dd7e-ae96-4a52-b32d-e7eff2ea1b83"}],"requestParams":[{"enabled":true,"name":"query","paramType":1,"required":false,"subParameters":[],"value":"","valueRefName":"$.BOT_USER_INPUT","valueRefNodeId":"c31eb8dd-3246-4780-b73d-853d1eedda4e","valueRefNodeName":"开始","valueRefSource":"input","valueRefType":"ref","varDataType":1,"variableRef":false}],"settingOnError":{}},"nodeMeta":{"description":"","imageflow":false,"subTitle":"","title":"融合策略"},"outputs":[{"name":"output","paramDesc":"","paramType":1,"varDataType":1,"variableRef":false}]}', 1, 0, '8470678936543756288', '管理员', '2025-02-24 17:13:36.220', '8470678936543756288', '管理员', '2025-02-24 17:13:36.220', 'ks');
INSERT INTO bot_workflow_node
(workflow_id, node_id, node_type, `data`, version, yn, create_id, create_name, create_time, update_id, update_name, update_time, app_code)
VALUES('ab1070c5-d83e-4a0d-aaa7-d7c0deb84629', 'dfbffd79-3fdd-40f4-8523-3be3f645fc7f', 'DIALOG_STRATEGY', '{"inputs":{"bizParam":{"skillParam":{"enableBotSkills":true,"skillList":[]},"kmsParam":{"retriever":3,"searchStrategy":"MIX","faq":{"enableSortModel":true,"sortCode":"model_992668744299715591","llmAccept":0.5,"topn":3,"faqAccept":0.8},"doc":{"enableSortModel":true,"sortCode":"model_992668744299715591","docAccept":0.5,"topn":3}},"prompt":"{{query}}","systemPrompt":"","modelCode":"model_901039658069659653","modelName":"TeleChat","temperature":0.1,"topP":0.7,"maxTokens":1024},"branches":[{"branchParam":{},"portId":"23f9d4da-4025-4c52-907a-298c3dee2292"},{"branchParam":{},"portId":"28a9dd7e-ae96-4a52-b32d-e7eff2ea1b83"}],"requestParams":[{"enabled":true,"name":"query","paramType":1,"required":false,"subParameters":[],"value":"","valueRefName":"$.BOT_USER_INPUT","valueRefNodeId":"c31eb8dd-3246-4780-b73d-853d1eedda4e","valueRefNodeName":"开始","valueRefSource":"input","valueRefType":"ref","varDataType":1,"variableRef":false}],"settingOnError":{}},"nodeMeta":{"description":"","imageflow":false,"subTitle":"","title":"融合策略"},"outputs":[{"name":"output","paramDesc":"","paramType":1,"varDataType":1,"variableRef":false}]}', 2, 0, '8470678936543756288', '管理员', '2025-03-17 15:46:49.406', '8470678936543756288', '管理员', '2025-03-17 15:46:49.406', 'ks');

INSERT INTO bot_workflow_edge
(workflow_id, source_node_id, source_port_id, target_node_id, target_port_id, version, yn, create_id, create_name, create_time, update_id, update_name, update_time, app_code)
VALUES('ab1070c5-d83e-4a0d-aaa7-d7c0deb84629', 'c31eb8dd-3246-4780-b73d-853d1eedda4e', '4d44a576-d2df-4da5-9d2f-bba127707339', 'dfbffd79-3fdd-40f4-8523-3be3f645fc7f', '23f9d4da-4025-4c52-907a-298c3dee2292', 1, 0, '8470678936543756288', '管理员', '2025-02-24 17:13:36.226', '8470678936543756288', '管理员', '2025-02-24 17:13:36.226', 'ks');
INSERT INTO bot_workflow_edge
(workflow_id, source_node_id, source_port_id, target_node_id, target_port_id, version, yn, create_id, create_name, create_time, update_id, update_name, update_time, app_code)
VALUES('ab1070c5-d83e-4a0d-aaa7-d7c0deb84629', 'dfbffd79-3fdd-40f4-8523-3be3f645fc7f', '28a9dd7e-ae96-4a52-b32d-e7eff2ea1b83', 'c28fe6e5-7d17-4ddb-b837-186cf859a1dd', '561e13f7-a0e7-4762-83ec-3626e36705df', 1, 0, '8470678936543756288', '管理员', '2025-02-24 17:13:36.226', '8470678936543756288', '管理员', '2025-02-24 17:13:36.226', 'ks');
INSERT INTO bot_workflow_edge
(workflow_id, source_node_id, source_port_id, target_node_id, target_port_id, version, yn, create_id, create_name, create_time, update_id, update_name, update_time, app_code)
VALUES('ab1070c5-d83e-4a0d-aaa7-d7c0deb84629', 'c31eb8dd-3246-4780-b73d-853d1eedda4e', '4d44a576-d2df-4da5-9d2f-bba127707339', 'dfbffd79-3fdd-40f4-8523-3be3f645fc7f', '23f9d4da-4025-4c52-907a-298c3dee2292', 2, 0, '8470678936543756288', '管理员', '2025-03-17 15:46:49.418', '8470678936543756288', '管理员', '2025-03-17 15:46:49.418', 'ks');
INSERT INTO bot_workflow_edge
( workflow_id, source_node_id, source_port_id, target_node_id, target_port_id, version, yn, create_id, create_name, create_time, update_id, update_name, update_time, app_code)
VALUES('ab1070c5-d83e-4a0d-aaa7-d7c0deb84629', 'dfbffd79-3fdd-40f4-8523-3be3f645fc7f', '28a9dd7e-ae96-4a52-b32d-e7eff2ea1b83', 'c28fe6e5-7d17-4ddb-b837-186cf859a1dd', '561e13f7-a0e7-4762-83ec-3626e36705df', 2, 0, '8470678936543756288', '管理员', '2025-03-17 15:46:49.418', '8470678936543756288', '管理员', '2025-03-17 15:46:49.418', 'ks');
