package com.chinatelecom.gs.engine.common.config.swagger;


import com.chinatelecom.gs.engine.common.config.property.GsGlobalConfig;
import com.chinatelecom.gs.engine.sdk.common.annotation.ApiType;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import jakarta.annotation.Resource;
import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * SwaggerConfig
 * 传统swagger访问地址： http://localhost:8092/ais/swagger-ui/index.html
 * knife4j风格地址： http://localhost:8092/ais/doc.html
 *
 * <AUTHOR>
 * @date 2022-10-15 20:32
 */
@Configuration
public class OpenApiConfig {

    @Resource
    private GsGlobalConfig gsGlobalConfig;

    public static final String[] GS_PACKAGES = {"com.chinatelecom.gs.engine", "com.chinatelecom.gs.workflow", "com.chinatelecom.gs.plugin", "com.chinatelelcom.gs.engine.sdk", "com.telecom.ais.telephone"};

    /**
     * 定义全局的 API 信息
     *
     * @return
     */
    @Bean
    public OpenAPI openAPI() {
        Map<String, Object> extensions = new LinkedHashMap<>();
        //此处设置公共属性 productCode和serverCode，优先读取接口上的注解属性，接口级别没配置则默认取这里的全局配置
        extensions.put("x-productCode", "ais-ks");
        extensions.put("x-serverCode", "gs-engine");

        return new OpenAPI()
                .info(new Info()
                        .title(gsGlobalConfig.getOpenApi().getTitle())
                        .description(gsGlobalConfig.getOpenApi().getDescription())
                        .version(gsGlobalConfig.getOpenApi().getVersion())
                        )
                        .extensions(extensions);


    }


    @Bean
    public GroupedOpenApi webApi() {
        return GroupedOpenApi.builder()
                .group("1.Web端接口")
                .pathsToMatch("/**/web/**")
                .packagesToScan(GS_PACKAGES)
                // Web端接口暴露所有参数，不使用任何定制器
                .build();
    }


    @Bean
    public GroupedOpenApi openApi() {
        return GroupedOpenApi.builder()
                .group("2.对外开放接口 (OpenAPI)")
                .pathsToMatch("/**/openapi/**")
                .packagesToScan(GS_PACKAGES)
                // 添加OpenAPI类型的定制器，用于隐藏标记的字段、接口和参数
                .addOpenApiCustomizer(new SmartApiTypeSchemaCustomizer(ApiType.OPENAPI))
                .addOperationCustomizer(new ApiTypeOperationCustomizer(ApiType.OPENAPI))
                .addOperationCustomizer(new ApiTypeParameterCustomizer(ApiType.OPENAPI))
                .addOperationCustomizer(new OpenApiHeaderParameterCustomizer(ApiType.OPENAPI))
                .build();
    }

    @Bean
    public GroupedOpenApi rpcApi() {
        return GroupedOpenApi.builder()
                .group("3.内部服务接口 (RPC)")
                .pathsToMatch("/**/rpc/**")
                .packagesToScan(GS_PACKAGES)
                // 添加RPC类型的定制器，用于隐藏标记的字段、接口和参数
                .addOpenApiCustomizer(new SmartApiTypeSchemaCustomizer(ApiType.RPC))
                .addOperationCustomizer(new ApiTypeOperationCustomizer(ApiType.RPC))
                .addOperationCustomizer(new ApiTypeParameterCustomizer(ApiType.RPC))
                .build();
    }

    /**
     * 其他接口分组
     * 匹配所有不属于其他分组的接口
     */
    @Bean
    public GroupedOpenApi otherApi() {
        return GroupedOpenApi.builder()
                .group("4.其他接口 (Other API)")
                .pathsToMatch("/**")
                .pathsToExclude("/**/rpc/**","/**/web/**","/**/openapi/**")
                .packagesToScan(GS_PACKAGES)
                // 其他接口不使用定制器，保持原样
                .build();
    }
}