package com.chinatelecom.gs.engine.core.entity.service.impl;

import com.alibaba.fastjson2.JSON;
import com.chinatelecom.gs.engine.core.entity.ability.recognition.EntityRecognitionAbilityHolder;
import com.chinatelecom.gs.engine.core.entity.ability.recognition.EntityRecognitionAbilityService;
import com.chinatelecom.gs.engine.core.entity.ability.validator.EntityValidatorAbilityHolder;
import com.chinatelecom.gs.engine.core.entity.ability.validator.EntityValidatorAbilityService;
import com.chinatelecom.gs.engine.core.entity.common.utils.ParallelUtils;
import com.chinatelecom.gs.engine.core.entity.domain.request.EntityValidatorRequest;
import com.chinatelecom.gs.engine.core.entity.service.EntityRecognitionService;
import com.chinatelecom.gs.engine.core.entity.service.EntityService;
import com.chinatelecom.gs.engine.robot.sdk.dto.Entity;
import com.chinatelecom.gs.engine.robot.sdk.v2.enums.EntityAbilityEnum;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.entity.EntityRecognitionRequest;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.entity.EntityRecognitionResponse;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.entity.EntityVO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @USER: pengmc1
 * @DATE: 2025/1/16 19:26
 */

@Slf4j
@Service
public class EntityRecognitionServiceImpl implements EntityRecognitionService {

    @Resource
    private EntityService entityService;

    @Resource
    private EntityRecognitionAbilityHolder entityRecognitionAbilityHolder;

    @Resource
    private EntityValidatorAbilityHolder entityValidatorAbilityHolder;

    @Resource
    private ExecutorService commonExecutorService;

    /**
     * 实体识别
     *
     * @param request
     * @return
     */
    @Override
    public EntityRecognitionResponse predict(EntityRecognitionRequest request) {
        List<EntityVO> entityVOS = entityService.getEntityList(request.getEntityCodes());
        //并发进行实体识别
        List<Entity> entityList = parallelRecognition(request, entityVOS);
        //对识别进行进行校验
        List<Entity> finalEntityList = entityValidator(request, entityVOS, entityList);
        EntityRecognitionResponse recognitionResponse = new EntityRecognitionResponse();
        recognitionResponse.setEntities(finalEntityList);
        return recognitionResponse;
    }

    /**
     * 并发进行实体识别
     *
     * @param request
     * @return
     */
    protected List<Entity> parallelRecognition(EntityRecognitionRequest request, List<EntityVO> entityVOS) {
        return ParallelUtils.parallelExecute(commonExecutorService, entityVOS, 60000L, entity -> {
            Set<String> abilityCodes = entity.getAbilityConfigMap().keySet();
            for (String ability : abilityCodes) {
                if (EntityAbilityEnum.MODEL_RECOGNITION.getCode().equals(ability)) {
                    //模型识别
                    ability = EntityAbilityEnum.MODEL_RECOGNITION.getCode() + ":" + entity.getAbilityConfigMap().get(ability).getModelCode();
                }
                EntityRecognitionAbilityService entityRecognitionAbilityService = entityRecognitionAbilityHolder.getAbilityService(ability);
                EntityRecognitionRequest recognitionRequest = new EntityRecognitionRequest();
                recognitionRequest.setSessionId(request.getSessionId());
                recognitionRequest.setMessageId(request.getMessageId());
                recognitionRequest.setTenantId(request.getTenantId());
                recognitionRequest.setQuery(request.getQuery());
                List<String> entityCodes = Collections.singletonList(entity.getEntityCode());
                recognitionRequest.setEntityCodes(entityCodes);
                List<Entity> predictRes = entityRecognitionAbilityService.predict(recognitionRequest);
                if (CollectionUtils.isNotEmpty(predictRes)) {
                    log.info("【实体识别】能力: {}, 实体编码: {}, 最终识别到结果：{}", ability, entity.getEntityCode(), JSON.toJSONString(predictRes));
                    return predictRes;
                }
            }
            return null;
        });
    }

    /**
     * 进行实体校验
     *
     * @param entityVOS
     * @param entityList
     * @return
     */
    protected List<Entity> entityValidator(EntityRecognitionRequest request, List<EntityVO> entityVOS, List<Entity> entityList) {
        if (CollectionUtils.isEmpty(entityList)) {
            return entityList;
        }
        Map<String, EntityVO> entityVOMap = entityVOS.stream().collect(Collectors.toMap(EntityVO::getEntityCode, Function.identity(), (k1, k2) -> k1));
        return ParallelUtils.parallelExecute(commonExecutorService, entityList, 60000L, entity -> {
            EntityVO entityVO = entityVOMap.get(entity.getEntityCode());
            //开启校验
            if (Boolean.TRUE.equals(entityVO.getValidatorSwitch())) {
                EntityValidatorAbilityService entityValidatorAbilityService = entityValidatorAbilityHolder.getValidatorAbilityService(entityVO.getValidatorType());
                EntityValidatorRequest validatorRequest = new EntityValidatorRequest();
                validatorRequest.setSessionId(request.getSessionId());
                validatorRequest.setMessageId(request.getMessageId());
                validatorRequest.setQuery(request.getQuery());
                validatorRequest.setEntityVO(entityVO);
                validatorRequest.setEntity(entity);
                Boolean validator = entityValidatorAbilityService.validator(validatorRequest);
                if (Boolean.TRUE.equals(validator)) {
                    return Collections.singletonList(entity);
                }
            } else {
                return Collections.singletonList(entity);
            }
            return null;
        });
    }
}
