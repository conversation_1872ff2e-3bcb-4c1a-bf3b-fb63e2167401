package com.chinatelecom.gs.engine.common.cache.config;


import com.chinatelecom.gs.engine.common.cache.lock.impl.JavaDistributedLock;
import org.redisson.spring.starter.RedissonAutoConfiguration;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.support.NoOpCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * NoneCacheConfig
 * <AUTHOR>
 * @date 2023-05-29 10:05
 */
@Configuration
@EnableCaching(order = 10)
@ConditionalOnProperty(prefix = "gs.cache", name = "type", havingValue = "NONE")
@EnableAutoConfiguration(exclude = {RedissonAutoConfiguration.class, RedisAutoConfiguration.class})
public class NoneCacheConfig {

    @Bean
    @Primary
    NoOpCacheManager NoneCacheManager() {
        return new NoOpCacheManager();
    }

    @Bean
    @Primary
    JavaDistributedLock caffeineCache() {
        return new JavaDistributedLock();
    }

}
