package com.chinatelecom.gs.engine.kms.controller;

import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.common.config.aspect.annotation.DebugLog;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.common.utils.FileUtil;
import com.chinatelecom.gs.engine.kms.sdk.constants.KmsApis;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.KnowledgeDocImportParam;
import com.chinatelecom.gs.engine.kms.service.KnowledgeBatchService;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import com.google.common.collect.Lists;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2025年01月20日
 */

@RestController
@Tag(name = "知识批量导入管理")
@RequestMapping({KmsApis.KMS_API + KmsApis.KNOWLEDGE + KmsApis.BATCH})
public class KnowledgeBatchController {

    @Resource
    private KnowledgeBatchService knowledgeBatchService;


    @Operation(summary = "zip包中批量导入文档", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "zip包中批量导入文档", groupName = "知识批量操作管理")
    @AuditLog(businessType = "知识批量操作管理", operType = "zip包中批量导入文档", operDesc = "zip包中批量导入文档", objId="null")
    @DebugLog(operation = "zip包中批量导入文档")
    @PostMapping(value = KmsApis.FILE)
    public Result<Map<String, String>> batchUpload(@RequestPart @RequestParam("file") MultipartFile file) {
        FileUtil.checkFileType(file, Lists.newArrayList(".zip"));
        Map<String, String> result =  knowledgeBatchService.batchImport(file);
        return Result.success(result);
    }

    @Operation(summary = "单个文档导入文档", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "单个文档导入文档", groupName = "知识批量操作管理")
    @AuditLog(businessType = "知识批量操作管理", operType = "单个文档导入文档", operDesc = "单个文档导入文档", objId="#param.path")
    @DebugLog(operation = "单个文档导入文档")
    @Parameters({
            @Parameter(name = "path", description = "文档路径", required = true, schema = @Schema(type = "string")),
            @Parameter(name = "file", description = "文件", required = true, schema = @Schema(type = "string", format = "binary"))
    })
    @PostMapping(value = KmsApis.DOC, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Result<Map<String, String>> docUpload(KnowledgeDocImportParam param) {
        Map<String, String> result =  knowledgeBatchService.docUpload(param);
        return Result.success(result);
    }


    @Operation(summary = "zip包中元数据及封面导入", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "zip包中元数据及封面导入", groupName = "知识批量操作管理")
    @AuditLog(businessType = "知识批量操作管理", operType = "zip包中元数据及封面导入", operDesc = "zip包中元数据及封面导入", objId="null")
    @DebugLog(operation = "zip包中元数据及封面导入")
    @PostMapping(value = KmsApis.METADATA)
    public Result<Map<String, String>> metadataBatchUpload(@RequestPart @RequestParam("file") MultipartFile file) {
        FileUtil.checkFileType(file, Lists.newArrayList(".zip"));
        Map<String, String> result =  knowledgeBatchService.metadataBatchUpload(file);
        return Result.success(result);
    }


}
