package com.chinatelecom.gs.engine.channel.api.controller.open;

import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;

import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Arrays;
import java.util.Random;

public class PasswordGenerator {

    private PasswordGenerator() {
        throw new IllegalStateException("Utility class");
    }

    // 定义可能的字符集
    private static final String LOWER_CASE = "abcdefghijklmnopqrstuvwxyz";
    private static final String UPPER_CASE = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    private static final String NUMBERS = "0123456789";
    private static final String SPECIAL_CHARACTERS = "!@#$%^&*()-_=+[{]}\\|;:'\",<.>/?";

    private static final Random random;

    static {
        try {
            random = SecureRandom.getInstanceStrong();
        } catch (NoSuchAlgorithmException e) {
            throw new BizException("A0003", e.getMessage());
        }
    }

    public static String generateStrongPassword(int length) {
        if (length < 4) {
            throw new IllegalArgumentException("Length must be at least 4");
        }

        StringBuilder password = new StringBuilder(length);

        // 确保密码包含各种类型的字符
        password.append(randomCharacter(LOWER_CASE));
        password.append(randomCharacter(UPPER_CASE));
        password.append(randomCharacter(NUMBERS));
        password.append(randomCharacter(SPECIAL_CHARACTERS));

        // 剩余的字符随机选择
        for (int i = 4; i < length; i++) {
            String characterSet = combineAllCharacterSets();
            password.append(randomCharacter(characterSet));
        }

        // 打乱顺序
        char[] pwArray = password.toString().toCharArray();
        for (int i = 0; i < length; i++) {
            int randomPosition = random.nextInt(length);
            char temp = pwArray[i];
            pwArray[i] = pwArray[randomPosition];
            pwArray[randomPosition] = temp;
        }


        String returnPw= new String(pwArray);
        Arrays.fill(pwArray, ' ');
        return returnPw;
    }

    private static char randomCharacter(String characterSet) {
        int randomIndex = random.nextInt(characterSet.length());
        return characterSet.charAt(randomIndex);
    }

    private static String combineAllCharacterSets() {
        return LOWER_CASE + UPPER_CASE + NUMBERS + SPECIAL_CHARACTERS;
    }
}
