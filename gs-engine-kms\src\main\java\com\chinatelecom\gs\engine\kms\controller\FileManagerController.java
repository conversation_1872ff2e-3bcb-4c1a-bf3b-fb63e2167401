package com.chinatelecom.gs.engine.kms.controller;

import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.common.constants.Apis;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.common.platform.StatOpenApi;
import com.chinatelecom.gs.engine.common.utils.HttpFileUtils;
import com.chinatelecom.gs.engine.core.manager.param.DeleteFileParam;
import com.chinatelecom.gs.engine.core.manager.param.FileManagerUploadParam;
import com.chinatelecom.gs.engine.core.manager.vo.FileManagerUploadVO;
import com.chinatelecom.gs.engine.kms.service.FileManagerAppService;
import com.chinatelecom.gs.engine.sdk.common.annotation.ApiType;
import com.chinatelecom.gs.engine.sdk.common.annotation.HideFromApiTypes;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ResourceLoader;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;


/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024年12月26日
 */


@RestController
@Slf4j
@Tag(name = "默认文件管理")
@RequestMapping({Apis.BASE_PREFIX + Apis.WEB_API + Apis.FILE_API,
        Apis.BASE_PREFIX + Apis.RPC_PREFIX + Apis.FILE_API,
        Apis.BASE_PREFIX + Apis.OPENAPI + Apis.FILE_API})
public class FileManagerController {

    @Resource
    private FileManagerAppService fileManagerAppService;

    @Resource
    private ResourceLoader resourceLoader;

    @Parameters({
            @Parameter(name = "file", description = "文件", required = true, schema = @Schema(type = "string", format = "binary")),
            @Parameter(name = "param", hidden = true)
    })
    @Operation(summary = "直接上传", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "直接上传", groupName = "默认文件管理")
    @StatOpenApi(name = "直接上传", groupName = "默认文件管理")
    @PostMapping(value = Apis.UPLOAD, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
//    @AuditLog(businessType = "默认文件管理", operType = "直接上传", operDesc = "直接上传", objId="null")
    public Result<FileManagerUploadVO> upload(@Validated FileManagerUploadParam param) throws IOException {
        FileManagerUploadVO upload = fileManagerAppService.upload(param);

        // 检查文件是否存在xss攻击
        fileManagerAppService.checkFile(upload, param.getFile().getOriginalFilename());
        return Result.success(upload);
    }


    @Operation(summary = "获取文件", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "获取文件", groupName = "默认文件管理")
    @StatOpenApi(name = "获取文件", groupName = "默认文件管理")
    @GetMapping(Apis.DOWNLOAD_API)
//    @AuditLog(businessType = "默认文件管理", operType = "获取文件", operDesc = "获取文件", objId="#fileKey")
    public void download(@RequestParam @Parameter(description = "文件唯一标识", required = true) String fileKey,
                         @RequestParam(required = false) @Parameter(description = "下载指定的文件名称", required = false) String fileName,
                         @RequestParam(required = false) @Parameter(description = "inline设置", required = false) boolean inline,
                         @HideFromApiTypes(ApiType.OPENAPI) @RequestParam(name = "range", required = false, defaultValue =  "false") boolean range,
                         HttpServletResponse response) throws IOException {
        fileManagerAppService.download(fileKey, fileName, inline, range, response);
    }

    @Operation(summary = "删除指定文件", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "删除指定文件", groupName = "默认文件管理")
    @StatOpenApi(name = "删除指定文件", groupName = "默认文件管理")
    @PostMapping(Apis.DELETE_API)
    @AuditLog(businessType = "默认文件管理", operType = "删除指定文件", operDesc = "删除指定文件", objId="null")
    public Result<Boolean> delete(@Validated @RequestBody DeleteFileParam param) throws IOException {
        fileManagerAppService.delete(param.getFileKeys());
        return Result.success(true);
    }

    @HideFromApiTypes(ApiType.OPENAPI)
    @Operation(summary = "敏感词示例文档下载", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "敏感词示例文档下载", groupName = "默认文件管理")
    @StatOpenApi(name = "敏感词示例文档下载", groupName = "默认文件管理")
    @GetMapping("download/sensitiveWordDemo")
    @AuditLog(businessType = "默认文件管理", operType = "敏感词示例文档下载", operDesc = "敏感词示例文档下载", objId="null")
    public void sensitiveWordDemo(HttpServletResponse response) throws IOException {
        try {
            String fileName = "示例文档.txt";
            String filePath = "classpath:/file/" + fileName;
            final var resource = resourceLoader.getResource(filePath);
            InputStream inputStream = resource.getInputStream();
            HttpFileUtils.download(inputStream, fileName, true, response);
        } catch (IOException e) {
            log.error("下载文件异常", e);
            throw new BizException(e, "AA017", "下载文件异常");
        }
    }

    /**
     * API使用指南下载接口
     */
    @Operation(summary = "API接入手册下载", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @HideFromApiTypes(ApiType.OPENAPI)
    @PlatformRestApi(name = "API接入手册下载", groupName = "默认文件管理")
    @StatOpenApi(name = "API接入手册下载", groupName = "默认文件管理")
    @GetMapping("/download/apiInvokeFile")
    @AuditLog(businessType = "默认文件管理", operType = "API接入手册下载", operDesc = "API接入手册下载", objId="null")
    public void usageGuidelines(HttpServletResponse response) {
        try {
            String fileName = "机器人API接入手册.pdf";
            String filePath = "classpath:/file/" + fileName;
            final var resource = resourceLoader.getResource(filePath);
            InputStream inputStream = resource.getInputStream();
            HttpFileUtils.download(inputStream, fileName, true, response);
        } catch (IOException e) {
            log.error("下载文件异常", e);
            throw new BizException(e, "AA017", "下载文件异常");
        }
    }
}
