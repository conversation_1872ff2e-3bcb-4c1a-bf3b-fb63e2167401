<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.chinatelecom</groupId>
    <packaging>pom</packaging>
    <artifactId>gs-engine</artifactId>
    <version>2.0.0-SNAPSHOT</version>

    <modules>
        <module>gs-engine-sdk-common</module>
        <module>gs-engine-sdk</module>
        <module>gs-engine-common</module>
        <module>gs-engine-core</module>
        <module>gs-engine-robot</module>
        <module>gs-engine-task</module>
        <module>gs-engine-kms</module>
        <module>gs-engine-plugin</module>
        <module>gs-engine-channel</module>
        <module>gs-engine-bootstrap</module>
    </modules>

    <properties>
        <gs-engine.version>2.0.0-SNAPSHOT</gs-engine.version>
        <robot-sdk.version>2.0.1-SNAPSHOT</robot-sdk.version>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <maven.compiler.encoding>UTF-8</maven.compiler.encoding>

        <java.version>17</java.version>
        <druid.version>1.1.20</druid.version>
        <mapstruct.version>1.6.3</mapstruct.version>
        <misc-generator.version>1.0.5</misc-generator.version>
        <shardingsphere.version>5.5.2</shardingsphere.version>
        <nacos.version>2023.0.3.3</nacos.version>
<!--        <elasticsearch.version>7.17.25</elasticsearch.version>-->
        <sftp.version>0.1.55</sftp.version>

        <chinatelecom.root.version>1.0.0-SNAPSHOT</chinatelecom.root.version>
        <spring.cloud-version>2025.0.0</spring.cloud-version>
        <mybatis-plus.version>3.5.14</mybatis-plus.version>
        <redisson.version>3.51.0</redisson.version>
        <testable.version>0.7.9</testable.version>
        <jakarta-json.version>2.1.1</jakarta-json.version>
        <jacoco.version>0.8.10</jacoco.version>
        <easyexcel-version>3.3.2</easyexcel-version>
        <poi-tl-version>1.12.2</poi-tl-version>
        <spring-kafka.version>3.3.9</spring-kafka.version>
        <cloud-platform.version>2.7.4-jdk17-SNAPSHOT</cloud-platform.version>
        <fastjson2.version>2.0.53</fastjson2.version>
        <gs-workflow.version>1.0.0-SNAPSHOT</gs-workflow.version>
        <jackson.version>2.17.2</jackson.version>
        <mysql.verion>8.0.33</mysql.verion>
        <guava.version>33.4.5-jre</guava.version>
        <jacoco.version>0.8.10</jacoco.version>
        <retrofit.version>3.0.0</retrofit.version>
        <mvel.version>2.5.2.Final</mvel.version>
        <jython.version>2.7.3</jython.version>
        <netty.version>4.2.4.Final</netty.version>

    </properties>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.5.5</version>
    </parent>


    <distributionManagement>
        <repository>
            <id>central</id>
            <name>ct_aics-release-maven-local</name>
            <url>https://artifact.srdcloud.cn/artifactory/ct_aics-release-maven-local</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <name>ct_aics-snapshot-maven-local</name>
            <url>https://artifact.srdcloud.cn/artifactory/ct_aics-snapshot-maven-local</url>
        </snapshotRepository>
    </distributionManagement>


    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-bom</artifactId>
                <version>1.12.788</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-bom</artifactId>
                <version>${mybatis-plus.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>


            <dependency>
                <groupId>com.chinatelecom.gs</groupId>
                <artifactId>gs-workflow-core</artifactId>
                <version>${gs-engine.version}</version>
            </dependency>

            <dependency>
                <groupId>com.chinatelecom</groupId>
                <artifactId>gs-engine-common</artifactId>
                <version>${gs-engine.version}</version>
            </dependency>
            <dependency>
                <groupId>com.chinatelecom</groupId>
                <artifactId>gs-engine-sdk-common</artifactId>
                <version>${gs-engine.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.security</groupId>
                        <artifactId>spring-security-crypto</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.chinatelecom</groupId>
                <artifactId>gs-engine-privilege-sdk</artifactId>
                <version>${gs-engine.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.security</groupId>
                <artifactId>spring-security-crypto</artifactId>
                <version>6.2.8</version>
            </dependency>
            <dependency>
                <groupId>com.chinatelecom</groupId>
                <artifactId>gs-engine-corekit</artifactId>
                <version>${gs-engine.version}</version>
            </dependency>
            <dependency>
                <groupId>com.chinatelecom</groupId>
                <artifactId>gs-engine-entity</artifactId>
                <version>${gs-engine.version}</version>
            </dependency>
            <dependency>
                <groupId>com.chinatelecom</groupId>
                <artifactId>gs-engine-model</artifactId>
                <version>${gs-engine.version}</version>
            </dependency>
            <dependency>
                <groupId>com.chinatelecom</groupId>
                <artifactId>gs-robot-dialog</artifactId>
                <version>${gs-engine.version}</version>
            </dependency>
            <dependency>
                <groupId>com.chinatelecom</groupId>
                <artifactId>gs-robot-manage</artifactId>
                <version>${gs-engine.version}</version>
            </dependency>
            <dependency>
                <groupId>com.chinatelecom</groupId>
                <artifactId>gs-engine-manager</artifactId>
                <version>${gs-engine.version}</version>
            </dependency>
            <dependency>
                <groupId>com.chinatelecom</groupId>
                <artifactId>gs-engine-core-sdk</artifactId>
                <version>${gs-engine.version}</version>
            </dependency>

            <dependency>
                <groupId>com.chinatelecom</groupId>
                <artifactId>gs-engine-kms-sdk</artifactId>
                <version>${gs-engine.version}</version>
            </dependency>
            <dependency>
                <groupId>com.chinatelecom</groupId>
                <artifactId>gs-engine-task-sdk</artifactId>
                <version>${gs-engine.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-webmvc</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-webmvc</artifactId>
                <version>6.2.10</version>
            </dependency>
            <dependency>
                <groupId>com.chinatelecom</groupId>
                <artifactId>gs-engine-robot-sdk</artifactId>
                <version>${robot-sdk.version}</version>
            </dependency>
            <dependency>
                <groupId>com.chinatelecom</groupId>
                <artifactId>gs-engine-plugin-sdk</artifactId>
                <version>${gs-engine.version}</version>
            </dependency>
            <dependency>
                <groupId>com.chinatelecom</groupId>
                <artifactId>gs-engine-kms</artifactId>
                <version>${gs-engine.version}</version>
            </dependency>

            <dependency>
                <groupId>com.chinatelecom</groupId>
                <artifactId>gs-workflow-app</artifactId>
                <version>${gs-engine.version}</version>
            </dependency>

            <dependency>
                <groupId>com.chinatelecom</groupId>
                <artifactId>gs-engine-plugin</artifactId>
                <version>${gs-engine.version}</version>
            </dependency>
            <dependency>
                <groupId>com.chinatelecom</groupId>
                <artifactId>gs-engine-channel</artifactId>
                <version>${gs-engine.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>io.netty</groupId>
                        <artifactId>netty-codec-http</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.xmlunit</groupId>
                <artifactId>xmlunit-core</artifactId>
                <version>2.10.0</version>
            </dependency>
            <dependency>
                <groupId>com.deepoove</groupId>
                <artifactId>poi-tl</artifactId>
                <version>${poi-tl-version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>3.18.0</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>5.2.2</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>5.2.2</version>
            </dependency>
            <dependency>
                <groupId>commons-logging</groupId>
                <artifactId>commons-logging</artifactId>
                <version>1.3.5</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring.cloud-version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>4.5.0</version>
            </dependency>

            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-openapi3-jakarta-spring-boot-starter</artifactId>
                <version>4.4.0</version>
            </dependency>
            <dependency>
                <groupId>io.swagger.core.v3</groupId>
                <artifactId>swagger-annotations</artifactId>
                <version>2.2.32</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>2.15.1</version>
            </dependency>
            <dependency>
                <groupId>commons-fileupload</groupId>
                <artifactId>commons-fileupload</artifactId>
                <version>1.6.0</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-starter-openfeign</artifactId>
                <version>4.3.0</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-expression</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-security-crypto</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
<!--            <dependency>-->
<!--                <groupId>org.springframework</groupId>-->
<!--                <artifactId>spring-security-crypto</artifactId>-->
<!--                <version>5.7.14</version>-->
<!--            </dependency>-->
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-expression</artifactId>
                <version>6.2.10</version>
            </dependency>
<!--            <dependency>-->
<!--                <groupId>io.github.openfeign</groupId>-->
<!--                <artifactId>feign-httpclient</artifactId>-->
<!--                <version>10.1.0</version>-->
<!--            </dependency>-->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId>
                <version>2.14.1</version>
            </dependency>
            <dependency>
                <groupId>org.flywaydb</groupId>
                <artifactId>flyway-core</artifactId>
                <version>10.10.0</version>
            </dependency>
            <dependency>
                <groupId>org.flywaydb</groupId>
                <artifactId>flyway-mysql</artifactId>
                <version>10.10.0</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>5.8.29</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-spring-ui</artifactId>
                <version>3.0.3</version>
            </dependency>

            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson.version}</version>
            </dependency>
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson</artifactId>
                <version>${redisson.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-handler</artifactId>
                <version>${netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-common</artifactId>
                <version>${netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-codec-http</artifactId>
                <version>${netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-buffer</artifactId>
                <version>${netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-codec</artifactId>
                <version>${netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-resolver</artifactId>
                <version>${netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-resolver-dns</artifactId>
                <version>${netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-transport</artifactId>
                <version>${netty.version}</version>
            </dependency>

            <!-- nacos -->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
                <version>${nacos.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.kafka</groupId>
                <artifactId>spring-kafka</artifactId>
                <version>${spring-kafka.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-text</artifactId>
                <version>1.13.0</version>
            </dependency>
            <dependency>
                <groupId>io.minio</groupId>
                <artifactId>minio</artifactId>
                <version>8.5.17</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.commons</groupId>
                        <artifactId>commons-compress</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.squareup.okio</groupId>
                        <artifactId>okio</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-compress</artifactId>
                <version>1.26.0</version>
            </dependency>
            <dependency>
                <groupId>com.jayway.jsonpath</groupId>
                <artifactId>json-path</artifactId>
                <version>2.9.0</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.okio</groupId>
                <artifactId>okio</artifactId>
                <version>3.4.0</version>
            </dependency>
            <dependency>
                <groupId>org.jsoup</groupId>
                <artifactId>jsoup</artifactId>
                <version>1.16.1</version>
            </dependency>
            <dependency>
                <groupId>org.yaml</groupId>
                <artifactId>snakeyaml</artifactId>
                <version>2.0</version>
            </dependency>

            <dependency>
                <groupId>com.dameng</groupId>
                <artifactId>DmJdbcDriver18</artifactId>
                <version>8.1.3.62</version>
            </dependency>
            <dependency>
                <groupId>com.github.mengweijin</groupId>
                <artifactId>db-migration</artifactId>
                <version>2.1.0</version>
            </dependency>
            <!-- okhttp3客户端 -->

<!--            <dependency>-->
<!--                <groupId>ch.qos.logback</groupId>-->
<!--                <artifactId>logback-classic</artifactId>-->
<!--                <version>1.2.13</version>-->
<!--            </dependency>-->
<!--            <dependency>-->
<!--                <groupId>ch.qos.logback</groupId>-->
<!--                <artifactId>logback-core</artifactId>-->
<!--                <version>1.2.13</version>-->
<!--            </dependency>-->

            <dependency>
                <groupId>org.xerial.snappy</groupId>
                <artifactId>snappy-java</artifactId>
                <version>1.1.10.5</version>
            </dependency>

            <dependency>
                <groupId>com.amazon.ion</groupId>
                <artifactId>ion-java</artifactId>
                <version>1.10.5</version>
            </dependency>

<!--            <dependency>-->
<!--                <groupId>com.google.protobuf</groupId>-->
<!--                <artifactId>protobuf-java</artifactId>-->
<!--                <version>3.25.8</version>-->
<!--            </dependency>-->
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>kafka-clients</artifactId>
                <version>${kafka.version}</version>
            </dependency>


            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcprov-jdk18on</artifactId>
                <version>1.81</version>
            </dependency>
            <dependency>
                <groupId>io.github.classgraph</groupId>
                <artifactId>classgraph</artifactId>
                <version>4.8.112</version>
            </dependency>
            <dependency>
                <groupId>com.github.ulisesbocchio</groupId>
                <artifactId>jasypt-spring-boot-starter</artifactId>
                <version>3.0.3</version>
            </dependency>

            <dependency>
                <groupId>com.itextpdf</groupId>
                <artifactId>html2pdf</artifactId>
                <version>5.0.5</version>
            </dependency>

            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-core</artifactId>
                <version>${tomcat.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-el</artifactId>
                <version>${tomcat.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-websocket</artifactId>
                <version>${tomcat.version}</version>
            </dependency>

            <dependency>
                <groupId>com.chinatelecom.cloud</groupId>
                <artifactId>cloud-platform-client-jdk17</artifactId>
                <version>${cloud-platform.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>swagger-annotations</artifactId>
                        <groupId>io.swagger</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>swagger-annotations</artifactId>
                        <groupId>io.swagger</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.chinatelecom</groupId>
                <artifactId>telecom-ai-spring-boot-search</artifactId>
                <version>1.3.3-SNAPSHOT</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.chinatelecom</groupId>
                        <artifactId>telecom-ai-search-open-sdk</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.chinatelecom</groupId>
                <artifactId>telecom-ai-search-open-sdk</artifactId>
                <version>1.3.3-ais-SNAPSHOT</version>
            </dependency>


            <dependency>
                <groupId>com.yomahub</groupId>
                <artifactId>liteflow-spring-boot-starter</artifactId>
                <version>2.15.0</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-beanutils</groupId>
                        <artifactId>commons-beanutils</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>commons-beanutils</groupId>
                <artifactId>commons-beanutils</artifactId>
                <version>1.11.0</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel-version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.poi</groupId>
                        <artifactId>poi-ooxml-schemas</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.xmlbeans</groupId>
                <artifactId>xmlbeans</artifactId>
                <version>5.0.3</version>
            </dependency>

            <dependency>
                <groupId>com.chinatelecom</groupId>
                <artifactId>tele-flowable-api</artifactId>
                <version>3.9.0</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>fastjson</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework.cloud</groupId>
                        <artifactId>spring-cloud-starter-openfeign</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.commons</groupId>
                        <artifactId>commons-lang3</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>cn.hutool</groupId>
                        <artifactId>hutool-all</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>swagger-annotations</artifactId>
                        <groupId>io.swagger</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>io.opentelemetry.instrumentation</groupId>
                <artifactId>opentelemetry-logback-mdc-1.0</artifactId>
                <version>1.26.0-alpha</version>
            </dependency>
            <!-- html转markdown-->
            <dependency>
                <groupId>com.vladsch.flexmark</groupId>
                <artifactId>flexmark-all</artifactId>
                <version>0.62.2</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.pdfbox</groupId>
                        <artifactId>pdfbox</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.pdfbox</groupId>
                <artifactId>pdfbox</artifactId>
                <version>2.0.24</version>
            </dependency>

            <!-- Mockito -->
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-inline</artifactId>
                <version>5.2.0</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-junit-jupiter</artifactId>
                <version>5.19.0</version>
                <scope>test</scope>
            </dependency>

            <!-- AssertJ (更强大的断言库) -->
            <dependency>
                <groupId>org.assertj</groupId>
                <artifactId>assertj-core</artifactId>
                <version>4.0.0-M1</version>
            </dependency>

            <!-- TestContainers (集成测试用) -->
            <dependency>
                <groupId>org.testcontainers</groupId>
                <artifactId>junit-jupiter</artifactId>
                <version>1.17.3</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-module-junit4</artifactId>
                <version>2.0.9</version> <!-- 请根据实际需求选择版本 -->
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-api-mockito2</artifactId>
                <version>2.0.9</version> <!-- 请根据实际需求选择版本 -->
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson2.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>${fastjson2.version}</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.retrofit2</groupId>
                <artifactId>retrofit</artifactId>
                <version>${retrofit.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.squareup.okio</groupId>
                        <artifactId>okio</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.squareup.retrofit2</groupId>
                <artifactId>converter-gson</artifactId>
                <version>${retrofit.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.google.code.gson</groupId>
                        <artifactId>gson</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.azure</groupId>
                <artifactId>azure-ai-openai</artifactId>
                <version>1.0.0-beta.6</version>
                <exclusions>
                    <exclusion>
                        <artifactId>jackson-annotations</artifactId>
                        <groupId>com.fasterxml.jackson.core</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jackson-core</artifactId>
                        <groupId>com.fasterxml.jackson.core</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jackson-databind</artifactId>
                        <groupId>com.fasterxml.jackson.core</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jackson-datatype-jsr310</artifactId>
                        <groupId>com.fasterxml.jackson.datatype</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.mvel</groupId>
                <artifactId>mvel2</artifactId>
                <version>${mvel.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>5.19.0</version>
            </dependency>

            <dependency>
                <groupId>org.python</groupId>
                <artifactId>jython-standalone</artifactId>
                <version>${jython.version}</version>
            </dependency>
            <dependency>
                <groupId>black.ninia</groupId>
                <artifactId>jep</artifactId>
                <version>4.2.2</version>
            </dependency>
            <dependency>
                <groupId>com.bladejava</groupId>
                <artifactId>blade-kit</artifactId>
                <version>1.3.4</version>
            </dependency>
            <dependency>
                <groupId>org.craftercms</groupId>
                <artifactId>groovy-sandbox</artifactId>
                <version>4.0.2</version>
            </dependency>
            <dependency>
                <groupId>io.swagger.parser.v3</groupId>
                <artifactId>swagger-parser-v3</artifactId>
                <version>2.1.22</version>
            </dependency>
            <dependency>
                <groupId>jakarta.servlet</groupId>
                <artifactId>jakarta.servlet-api</artifactId>
                <version>6.0.0</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents.client5</groupId>
                <artifactId>httpclient5</artifactId>
                <version>5.4.3</version>
            </dependency>
            <dependency>
                <groupId>org.testng</groupId>
                <artifactId>testng</artifactId>
                <version>7.5.1</version>
            </dependency>
            <dependency>
                <groupId>org.eclipse.parsson</groupId>
                <artifactId>parsson</artifactId>
                <version>1.1.5</version>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-reflect</artifactId>
                <version>2.0.9</version>
            </dependency>

            <dependency>
                <groupId>com.chinatelecom</groupId>
                <artifactId>yuqing-common-monitor</artifactId>
                <version>0.0.1</version>
            </dependency>
            <dependency>
                <groupId>com.chinatelecom</groupId>
                <artifactId>yuqing-common-pojo</artifactId>
                <version>0.0.2-SNAPSHOT</version>
            </dependency>


        </dependencies>
    </dependencyManagement>


    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.14.0</version>
                    <configuration>
                        <encoding>UTF-8</encoding>
                        <source>${java.version}</source>
                        <target>${java.version}</target>
                        <annotationProcessorPaths>
                            <path>
                                <groupId>org.mapstruct</groupId>
                                <artifactId>mapstruct-processor</artifactId>
                                <version>${mapstruct.version}</version>
                            </path>
                            <path>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok</artifactId>
                                <version>${lombok.version}</version>
                            </path>
                            <path>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok-mapstruct-binding</artifactId>
                                <version>0.2.0</version>
                            </path>
                        </annotationProcessorPaths>
                        <compilerArgs>
                            <!-- <arg>-Amapstruct.defaultComponentModel=spring</arg>-->
                            <arg>-Amapstruct.defaultInjectionStrategy=constructor</arg>
                            <arg>-Amapstruct.processing.defaultConversionService=true</arg>
                            <arg>-Amapstruct.options.jakarta=true</arg>
                        </compilerArgs>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                </plugin>
                <plugin>
                    <groupId>com.github.ulisesbocchio</groupId>
                    <artifactId>jasypt-maven-plugin</artifactId>
                    <version>3.0.3</version>
                </plugin>

            </plugins>
        </pluginManagement>

        <plugins>
            <plugin>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <skip>false</skip>
                    <forkCount>1</forkCount>
                    <reuseForks>false</reuseForks>

                    <argLine>
                        -ea
                        -javaagent:${settings.localRepository}/org/jacoco/org.jacoco.agent/${jacoco.version}/org.jacoco.agent-${jacoco.version}-runtime.jar=destfile=${project.build.directory}/coverage-reports/jacoco-unit.exec
                    </argLine>
                    <includes>
                        <include>**/*Test.java</include>
                    </includes>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>${jacoco.version}</version>
                <configuration>
                    <!--指定生成.exec文件的存放位置-->
                    <destFile>${project.build.directory}/coverage-reports/jacoco-unit.exec</destFile>
                    <!--Jacoco是根据.exec文件生成最终的报告，所以需指定.exec的存放路径-->
                    <dataFile>${project.build.directory}/coverage-reports/jacoco-unit.exec</dataFile>

                    <!--Jacoco生成的xml报告位置，本项配置默认情况下不开启，对应使用的扫描指令是mvn sonar或mvn verify sonar时；而当扫描指令是sonar-scanner时,本项配置需启用-->
                    <!--<outputDirectory>${project.build.directory}/coverage-reports/jacoco-ut</outputDirectory>-->

                    <!-- rules里面指定覆盖规则 -->
                    <rules>
                        <rule implementation="org.jacoco.maven.RuleConfiguration">
                            <element>BUNDLE</element>
                        </rule>
                    </rules>
                </configuration>
                <executions>
                    <execution>
                        <id>pre-test</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>post-test</id>
                        <phase>test</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>com.github.ulisesbocchio</groupId>
                <artifactId>jasypt-maven-plugin</artifactId>
                <configuration>
                    <path>file:src/main/resources/*.yaml</path>
                </configuration>
            </plugin>
        </plugins>
    </build>


</project>
