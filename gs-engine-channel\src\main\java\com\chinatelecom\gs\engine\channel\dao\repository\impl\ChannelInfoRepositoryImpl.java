package com.chinatelecom.gs.engine.channel.dao.repository.impl;


import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chinatelecom.gs.engine.channel.common.UidUtils;
import com.chinatelecom.gs.engine.channel.common.enums.ChannelTypeEnum;
import com.chinatelecom.gs.engine.channel.dao.mapper.ChannelInfoMapper;
import com.chinatelecom.gs.engine.channel.dao.po.ChannelInfoPO;
import com.chinatelecom.gs.engine.channel.dao.po.YN;
import com.chinatelecom.gs.engine.channel.dao.repository.ChannelInfoRepository;
import com.chinatelecom.gs.engine.channel.manage.convertor.DTOBeanConvertor;
import com.chinatelecom.gs.engine.channel.service.dto.ChannelInfoDTO;
import com.chinatelecom.gs.engine.robot.sdk.enums.ResultCode;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.data.ChannelInfoVO;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/12/22 14:13
 * @description
 */
@Service
@Slf4j
public class ChannelInfoRepositoryImpl extends ServiceImpl<ChannelInfoMapper, ChannelInfoPO>
        implements ChannelInfoRepository {

    @Override
    public String addChannelInfo(String agentCode, ChannelTypeEnum channelTypeEnum) {
        // 查询或创建渠道信息
        ChannelInfoPO channelInfo = Optional.ofNullable(getChannelInfoByAgentAndChannelType(agentCode, channelTypeEnum))
                .orElseGet(() -> ChannelInfoRepository.getDefaultChannelWithNullChannelId(agentCode, channelTypeEnum));

        // 设置新的渠道ID
        channelInfo.setChannelId(UidUtils.randomString());
        channelInfo.setId(null);

        // 保存渠道信息
        try {
            super.save(channelInfo);
            return channelInfo.getChannelId();
        } catch (DuplicateKeyException e) {
            throw new BizException("A0052","渠道信息主键重复，请检查渠道名是否重复");
        }
    }

    /**
     * 修改渠道配置
     *
     * @param channelInfo ChannelInfoPO
     * @return Boolean
     */
    @Override
    public Boolean updateByChannelId(ChannelInfoPO channelInfo) {
        if (CharSequenceUtil.isBlank(channelInfo.getChannelId()) || CharSequenceUtil.isBlank(channelInfo.getAgentCode())) {
            log.warn("修改渠道，参数错误:{}", channelInfo);
            throw new BizException("A0001", "参数错误");
        }
        LambdaUpdateWrapper<ChannelInfoPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ChannelInfoPO::getChannelId, channelInfo.getChannelId());
        updateWrapper.eq(ChannelInfoPO::getAgentCode, channelInfo.getAgentCode());
        return this.update(channelInfo, updateWrapper);
    }

    @Override
    public ChannelInfoDTO getChannelInfo(String channelId, String agentCode) {
        if (!StringUtils.hasLength(channelId)) {
            throw new BizException("A0053", "渠道id不能为空");
        }
        LambdaQueryWrapper<ChannelInfoPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ChannelInfoPO::getChannelId, channelId);
        if (StringUtils.hasLength(agentCode)) {
            queryWrapper.eq(ChannelInfoPO::getAgentCode, agentCode);
        }
        ChannelInfoPO channelInfo = super.getOne(queryWrapper);
        return DTOBeanConvertor.INSTANCE.convert(channelInfo);
    }

    @Override
    public List<ChannelInfoVO> getAllChannelInfo() {
        LambdaQueryWrapper<ChannelInfoPO> queryWrapper = new LambdaQueryWrapper<>();
        List<ChannelInfoPO> channelInfo = super.list(queryWrapper);
        return channelInfo.stream().map(DTOBeanConvertor.INSTANCE::convertVo).collect(Collectors.toList());
    }

    @Override
    public boolean channelOperateEnable(String agentCode, String channelTypeEnum, boolean enable) {
        LambdaUpdateWrapper<ChannelInfoPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ChannelInfoPO::getAgentCode, agentCode);
        updateWrapper.eq(ChannelInfoPO::getChannelType, channelTypeEnum);

        updateWrapper.set(ChannelInfoPO::isEnable, enable);
        return super.update(updateWrapper);
    }

    @Override
    public List<ChannelInfoDTO> getChannelInfoList(String agentCode, List<String> channelTypeList) {
        LambdaQueryWrapper<ChannelInfoPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ChannelInfoPO::getAgentCode, agentCode);
        if (!CollectionUtils.isEmpty(channelTypeList)) {
            // 查询指定类型的渠道
            queryWrapper.in(ChannelInfoPO::getChannelType, channelTypeList);
        }
        List<ChannelInfoPO> list = super.list(queryWrapper);
        return list.stream().map(DTOBeanConvertor.INSTANCE::convert).collect(Collectors.toList());
    }

    @Override
    public void removeByChannelId(String channelId) {
        LambdaQueryWrapper<ChannelInfoPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ChannelInfoPO::getChannelId, channelId);
        queryWrapper.eq(ChannelInfoPO::getYn, YN.YES.getValue());
        super.remove(queryWrapper);
    }

    private ChannelInfoPO getChannelInfoByAgentAndChannelType(String agentCode, ChannelTypeEnum channelTypeEnum) {
        LambdaQueryWrapper<ChannelInfoPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ChannelInfoPO::getChannelType, channelTypeEnum.getCode())
                .eq(ChannelInfoPO::getAgentCode, agentCode).last("LIMIT 1");
        return super.getOne(queryWrapper);
    }
}
