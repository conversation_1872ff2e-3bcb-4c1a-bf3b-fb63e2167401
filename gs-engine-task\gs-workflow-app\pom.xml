<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.chinatelecom</groupId>
        <artifactId>gs-engine-task</artifactId>
        <version>2.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>gs-workflow-app</artifactId>
    <version>2.0.0-SNAPSHOT</version>
    <packaging>jar</packaging>


    <dependencies>
        <dependency>
            <groupId>black.ninia</groupId>
            <artifactId>jep</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chinatelecom.gs</groupId>
            <artifactId>gs-workflow-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.ulisesbocchio</groupId>
            <artifactId>jasypt-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-loadbalancer</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chinatelecom</groupId>
            <artifactId>gs-engine-sdk-common</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.security</groupId>
                    <artifactId>spring-security-crypto</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-crypto</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chinatelecom</groupId>
            <artifactId>gs-engine-kms</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chinatelecom</groupId>
            <artifactId>gs-robot-manage</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chinatelecom</groupId>
            <artifactId>gs-robot-dialog</artifactId>
        </dependency>
        <dependency>
            <groupId>org.craftercms</groupId>
            <artifactId>groovy-sandbox</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chinatelecom</groupId>
            <artifactId>gs-engine-plugin</artifactId>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.chinatelecom</groupId>
            <artifactId>gs-engine-entity</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chinatelecom</groupId>
            <artifactId>gs-engine-common</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.flywaydb</groupId>
                    <artifactId>flyway-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.chinatelecom</groupId>
            <artifactId>gs-engine-model</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>netty-codec-http</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
        </dependency>
    </dependencies>

</project>
