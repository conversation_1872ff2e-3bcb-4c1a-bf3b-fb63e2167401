CREATE TABLE `tag_relation`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `yn`          bigint unsigned NOT NULL DEFAULT '0' COMMENT '删除标记,0:未删除,其他表示已删除',
    `code`        varchar(64)  NOT NULL COMMENT 'Code',
    `app_code`    varchar(64)  NOT NULL COMMENT '应用Code',
    `tag_code`    varchar(64)  NOT NULL COMMENT '标签code',
    `target_code` varchar(64)  NOT NULL COMMENT '关联知识或知识库code',
    `target_type` varchar(20)  NOT NULL COMMENT '关联类型',
    `create_id`   varchar(255) NOT NULL DEFAULT '0' COMMENT '创建用户ID',
    `create_name` varchar(255) NOT NULL DEFAULT '未知用户' COMMENT '创建用户名',
    `create_time` datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_id`   varchar(255) NOT NULL DEFAULT '0' COMMENT '最后修改用户ID',
    `update_name` varchar(255) NOT NULL DEFAULT '未知用户' COMMENT '最后修改用户名',
    `update_time` datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY           `idx_tag_relation_target` (`yn`,`target_code`,`target_type`),
    KEY           `idx_tag_relation_code` (`app_code`,`tag_code`),
    KEY           `idx_tag_relation_app_target` (`app_code`,`target_code`,`target_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='标签关联表';
