//package com.chinatelecom.gs.engine.channel.common.cache.redis;
//
//import lombok.Data;
//import org.springframework.boot.context.properties.ConfigurationProperties;
//
///**
// * <AUTHOR>
// * @date 2023/12/25 13:55
// * @description
// */
//@ConfigurationProperties(prefix = "spring.redis", ignoreUnknownFields = false)
//@Data
//public class RedisProperties {
//
//    private int database;
//
//    /**
//     * 等待节点回复命令的时间。该时间从命令发送成功时开始计时
//     */
//    private int timeout;
//
//    private String username;
//
//    private String password;
//
//    private String mode;
//
//
//    /**
//     * 池配置
//     */
//    private RedisPoolProperties pool;
//
//
//    /**
//     * 单机信息配置
//     */
//    private RedisSingleProperties single;
//
//
//    /**
//     * 集群 信息配置
//     */
//    private RedisClusterProperties cluster;
//
//}
