package com.chinatelecom.gs.engine.core.model.toolkit.adapter.deepseek;

import com.chinatelecom.gs.engine.core.sdk.vo.llm.BaseLLMRequest;
import com.chinatelecom.gs.engine.core.sdk.vo.llm.ChatTemplateKwargs;
import com.chinatelecom.gs.engine.core.sdk.vo.llm.Tool;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

@Data
public class DeepSeekRequest implements BaseLLMRequest {

    private String model;

    private List<DeepSeekMessage> messages;

    private boolean stream = true;

    /**
     * qwen 2.5 top_p
     * 1.5  private Integer topk = 5;
     */
    private Float top_p = 0.5f;


    private double temperature = 0.3;

    private double repetition_penalty = 1.03;

    private List<Tool> tools;


    private ChatTemplateKwargs chat_template_kwargs;

    /**
     * 获取输入内容
     *
     * @return
     */
    @Override
    public String inputContent() {
        StringBuilder inputBuilder = new StringBuilder();
        if(CollectionUtils.isNotEmpty(messages)){
            for(DeepSeekMessage message : messages){
                inputBuilder.append(message.getContent());
            }
        }
        return inputBuilder.toString();
    }
}
