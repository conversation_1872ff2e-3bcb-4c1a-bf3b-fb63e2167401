create index bot_workflow_id_index using btree on bot_workflow (workflow_id, version);
create index bot_workflow_app_code_index using btree on bot_workflow (app_code);
create index bot_workflow_node_index using btree on bot_workflow_node (workflow_id, node_id, app_code, version);
create index bot_workflow_edge_id_index using btree on bot_workflow_edge (workflow_id, app_code, version);
create index bot_workflow_version_id_index using btree on workflow_version_info (workflow_id, version);
create index workflow_task_index using btree on workflow_task (workflow_id, app_code);
create index workflow_test_log_index using btree on workflow_test_log (workflow_id, message_id);