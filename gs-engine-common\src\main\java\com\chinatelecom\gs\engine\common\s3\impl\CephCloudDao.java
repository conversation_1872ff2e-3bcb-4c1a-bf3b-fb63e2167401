package com.chinatelecom.gs.engine.common.s3.impl;

import com.chinatelecom.gs.engine.common.config.property.GsGlobalConfig;
import com.chinatelecom.gs.engine.common.s3.CloudStorageDao;
import com.chinatelecom.gs.engine.common.s3.FileMetadataRes;
import com.chinatelecom.gs.engine.common.s3.ObjectMetadataReq;
import com.chinatelecom.gs.engine.common.s3.ObjectMetadataRes;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.core.ResponseInputStream;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.*;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URI;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class CephCloudDao implements CloudStorageDao {

    @Resource
    private GsGlobalConfig gsGlobalConfig;

    private String bucketName;

    private S3Client s3Client;

    @PostConstruct
    public void init() {
        GsGlobalConfig.S3Config s3 = gsGlobalConfig.getS3();
        bucketName = s3.getBucketName();
        AwsBasicCredentials awsCredentials = AwsBasicCredentials.create(s3.getAccessKey(), s3.getSecretKey());
        try {
            this.s3Client = S3Client.builder()
                    .credentialsProvider(StaticCredentialsProvider.create(awsCredentials))
                    .endpointOverride(URI.create(s3.getEndPoint()))
                    .region(Region.US_EAST_1) // 对于自定义端点，region可以是任意值
                    .build();
        } catch (Exception e) {
            log.error("S3 Client init error", e);
        }

        createBucket(bucketName);
    }

    public void createBucket(String bucketName) {
        if (existBucket(bucketName)) {
            return;
        }
        try {
            CreateBucketRequest createBucketRequest = CreateBucketRequest.builder()
                    .bucket(bucketName)
                    .build();
            getS3Client().createBucket(createBucketRequest);

            // 设置生命周期配置
            LifecycleRule rule = LifecycleRule.builder()
                    .id("multipart-upload-rule")
                    .status(ExpirationStatus.ENABLED)
                    .abortIncompleteMultipartUpload(AbortIncompleteMultipartUpload.builder()
                            .daysAfterInitiation(7)
                            .build())
                    .build();

            BucketLifecycleConfiguration lifecycleConfiguration = BucketLifecycleConfiguration.builder()
                    .rules(rule)
                    .build();

            PutBucketLifecycleConfigurationRequest lifecycleRequest = PutBucketLifecycleConfigurationRequest.builder()
                    .bucket(bucketName)
                    .lifecycleConfiguration(lifecycleConfiguration)
                    .build();

            getS3Client().putBucketLifecycleConfiguration(lifecycleRequest);
        } catch (Exception e) {
            log.error("创建bucket后置处理异常，可忽略，bucketName：{}", bucketName, e);
        }
    }

    public boolean existBucket(String bucketName) {
        try {
            HeadBucketRequest headBucketRequest = HeadBucketRequest.builder()
                    .bucket(bucketName)
                    .build();
            getS3Client().headBucket(headBucketRequest);
            return true;
        } catch (NoSuchBucketException e) {
            return false;
        } catch (Exception e) {
            log.error("检查bucket存在性时出错", e);
            return false;
        }
    }

    @Override
    public String upload(String uploadKey, InputStream fileInputStream, long len, String contentType) {
        try {
            PutObjectRequest putObjectRequest = PutObjectRequest.builder()
                    .bucket(bucketName)
                    .key(uploadKey)
                    .contentType(contentType)
                    .contentLength(len)
                    .build();

            s3Client.putObject(putObjectRequest, RequestBody.fromInputStream(fileInputStream, len));
            return uploadKey;
        } catch (Exception e) {
            log.error("上传文件失败: {}", uploadKey, e);
            throw new BizException("A0082", "文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 新增方法：从本地文件系统上传到Ceph
     *
     * @param localFilePath 本地文件路径
     * @param cephKey       Ceph存储键
     */
    public String uploadFromLocalFile(String localFilePath, String cephKey) {
        File file = new File(localFilePath);
        String contentType = null;
        try {
            contentType = Files.probeContentType(Path.of(localFilePath));
        } catch (IOException e) {
            log.error("Failed to determine content type for file: {}", localFilePath, e);
            throw new RuntimeException(e);
        }
        try (InputStream is = Files.newInputStream(file.toPath())) {
            return this.upload(cephKey, is, file.length(), contentType);
        } catch (IOException e) {
            throw new BizException("A0083", "本地文件读取失败: " + e.getMessage());
        }
    }


    @Override
    public void remove(String fileKey) {
        try {
            DeleteObjectRequest deleteObjectRequest = DeleteObjectRequest.builder()
                    .bucket(bucketName)
                    .key(fileKey)
                    .build();
            s3Client.deleteObject(deleteObjectRequest);
        } catch (Exception e) {
            log.error("删除文件失败: {}", fileKey, e);
            throw new BizException("A0084", "文件删除失败: " + e.getMessage());
        }
    }

    @Override
    public void removeByPrefix(String prefix) {
        if (!StringUtils.endsWith(prefix, "/")) {
            prefix = prefix + "/";
        }

        try {
            ListObjectsV2Request listRequest = ListObjectsV2Request.builder()
                    .bucket(bucketName)
                    .prefix(prefix)
                    .delimiter("/")
                    .maxKeys(1000)
                    .build();

            ListObjectsV2Response listResponse = s3Client.listObjectsV2(listRequest);
            List<S3Object> s3Objects = listResponse.contents();

            if (CollectionUtils.isNotEmpty(s3Objects)) {
                List<ObjectIdentifier> objectsToDelete = s3Objects.stream()
                        .map(obj -> ObjectIdentifier.builder().key(obj.key()).build())
                        .collect(Collectors.toList());

                Delete delete = Delete.builder()
                        .objects(objectsToDelete)
                        .quiet(true)
                        .build();

                DeleteObjectsRequest deleteRequest = DeleteObjectsRequest.builder()
                        .bucket(bucketName)
                        .delete(delete)
                        .build();

                s3Client.deleteObjects(deleteRequest);
            }
        } catch (Exception e) {
            log.error("按前缀删除文件失败: {}", prefix, e);
            throw new BizException("A0085", "批量删除文件失败: " + e.getMessage());
        }
    }

    @Override
    public InputStream download(String fileKey) {
        try {
            GetObjectRequest getObjectRequest = GetObjectRequest.builder()
                    .bucket(bucketName)
                    .key(fileKey)
                    .build();

            ResponseInputStream<GetObjectResponse> s3Object = s3Client.getObject(getObjectRequest);
            return s3Object;
        } catch (NoSuchKeyException e) {
            throw new BizException("A0080", "文件不存在");
        } catch (Exception e) {
            log.error("下载文件失败: {}", fileKey, e);
            throw new BizException(e, "A0081", "下载异常");
        }
    }

    @Override
    public void copy(String sourceKey, String targetKey) {
        try {
            CopyObjectRequest copyObjectRequest = CopyObjectRequest.builder()
                    .sourceBucket(bucketName)
                    .sourceKey(sourceKey)
                    .destinationBucket(bucketName)
                    .destinationKey(targetKey)
                    .build();
            s3Client.copyObject(copyObjectRequest);
        } catch (Exception e) {
            log.error("复制文件失败: {} -> {}", sourceKey, targetKey, e);
            throw new BizException("A0086", "文件复制失败: " + e.getMessage());
        }
    }

    @Override
    public Boolean bucketExists(String bucketName) {
        return existBucket(bucketName);
    }

    @Override
    public void makeBucket(String bucketName) {
        createBucket(bucketName);
    }

    @Override
    public void removeBucket(String bucketName) {
        try {
            DeleteBucketRequest deleteBucketRequest = DeleteBucketRequest.builder()
                    .bucket(bucketName)
                    .build();
            s3Client.deleteBucket(deleteBucketRequest);
        } catch (Exception e) {
            log.error("删除bucket失败: {}", bucketName, e);
            throw new BizException("A0087", "删除bucket失败: " + e.getMessage());
        }
    }

    @Override
    public String upload(String bucketName, String uploadKey, InputStream fileInputStream, String orgFileName, long len,
                         String contentType) {
        try {
            PutObjectRequest putObjectRequest = PutObjectRequest.builder()
                    .bucket(bucketName)
                    .key(uploadKey)
                    .contentType(contentType)
                    .contentLength(len)
                    .build();

            s3Client.putObject(putObjectRequest, RequestBody.fromInputStream(fileInputStream, len));
            return uploadKey + "?fileName=" + orgFileName;
        } catch (Exception e) {
            log.error("上传文件失败: {}", uploadKey, e);
            throw new BizException("A0099", "文件上传失败: " + e.getMessage());
        }
    }

    @Override
    public String startPartUpload(String uploadKey) {
        try {
            CreateMultipartUploadRequest createMultipartUploadRequest = CreateMultipartUploadRequest.builder()
                    .bucket(bucketName)
                    .key(uploadKey)
                    .build();
            CreateMultipartUploadResponse response = s3Client.createMultipartUpload(createMultipartUploadRequest);
            return response.uploadId();
        } catch (Exception e) {
            log.error("开始分片上传失败: {}", uploadKey, e);
            throw new BizException("A0088", "开始分片上传失败: " + e.getMessage());
        }
    }

    @Override
    public String uploadPart(String uploadKey, String uploadId, InputStream fileInputStream, long partSize, int partNum) {
        try {
            UploadPartRequest uploadPartRequest = UploadPartRequest.builder()
                    .bucket(bucketName)
                    .key(uploadKey)
                    .uploadId(uploadId)
                    .partNumber(partNum)
                    .contentLength(partSize)
                    .build();

            UploadPartResponse uploadPartResponse = s3Client.uploadPart(uploadPartRequest,
                    RequestBody.fromInputStream(fileInputStream, partSize));
            return uploadPartResponse.eTag();
        } catch (Exception e) {
            log.error("上传分片失败: {} part: {}", uploadKey, partNum, e);
            throw new BizException("A0089", "上传分片失败: " + e.getMessage());
        }
    }

    @Override
    public String completePartUpload(String uploadId, String uploadKey, List<CompletedPart> partETags) {
        try {
            CompletedMultipartUpload completedMultipartUpload = CompletedMultipartUpload.builder()
                    .parts(partETags)
                    .build();

            CompleteMultipartUploadRequest completeMultipartUploadRequest = CompleteMultipartUploadRequest.builder()
                    .bucket(bucketName)
                    .key(uploadKey)
                    .uploadId(uploadId)
                    .multipartUpload(completedMultipartUpload)
                    .build();

            CompleteMultipartUploadResponse response = s3Client.completeMultipartUpload(completeMultipartUploadRequest);
            return response.key();
        } catch (Exception e) {
            log.error("合并文件分片失败", e);
            throw new BizException("A0090", "合并文件分片失败: " + e.getMessage());
        }
    }

    @Override
    public List<CompletedPart> listParts(String uploadId, String uploadKey) {
        try {
            ListPartsRequest listPartsRequest = ListPartsRequest.builder()
                    .bucket(bucketName)
                    .key(uploadKey)
                    .uploadId(uploadId)
                    .build();

            ListPartsResponse listPartsResponse = s3Client.listParts(listPartsRequest);
            return listPartsResponse.parts().stream()
                    .map(part -> CompletedPart.builder()
                            .partNumber(part.partNumber())
                            .eTag(part.eTag())
                            .build())
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取文件分片列表失败", e);
            return Collections.emptyList();
        }
    }

    @Override
    public HeadObjectResponse getObjectMetadata(String uploadKey) {
        try {
            HeadObjectRequest headObjectRequest = HeadObjectRequest.builder()
                    .bucket(bucketName)
                    .key(uploadKey)
                    .build();
            return s3Client.headObject(headObjectRequest);
        } catch (Exception e) {
            log.error("获取对象元数据失败: {}", uploadKey, e);
            throw new BizException("A0091", "获取对象元数据失败: " + e.getMessage());
        }
    }

    @Override
    public void abortMultipartUpload(String uploadId, String fileKey) {
        try {
            AbortMultipartUploadRequest abortMultipartUploadRequest = AbortMultipartUploadRequest.builder()
                    .bucket(bucketName)
                    .key(fileKey)
                    .uploadId(uploadId)
                    .build();
            s3Client.abortMultipartUpload(abortMultipartUploadRequest);
        } catch (Exception e) {
            log.error("取消分片上传失败: {}", fileKey, e);
            throw new BizException("AA070", "取消上传失败，文件分片不存在");
        }
    }

    @Override
    public void remove(List<String> fileKeys) {
        if (CollectionUtils.isEmpty(fileKeys)) {
            return;
        }
        try {
            List<ObjectIdentifier> objectsToDelete = fileKeys.stream()
                    .map(key -> ObjectIdentifier.builder().key(key).build())
                    .collect(Collectors.toList());

            Delete delete = Delete.builder()
                    .objects(objectsToDelete)
                    .quiet(true)
                    .build();

            DeleteObjectsRequest deleteObjectsRequest = DeleteObjectsRequest.builder()
                    .bucket(bucketName)
                    .delete(delete)
                    .build();

            s3Client.deleteObjects(deleteObjectsRequest);
        } catch (Exception e) {
            log.error("批量删除文件失败", e);
            throw new BizException("A0092", "批量删除文件失败: " + e.getMessage());
        }
    }

    @Override
    public String uploadByBytes(String bucketName, String uploadKey, byte[] fileBytes) {
        try {
            PutObjectRequest putObjectRequest = PutObjectRequest.builder()
                    .bucket(bucketName)
                    .key(uploadKey)
                    .contentLength((long) fileBytes.length)
                    .build();

            s3Client.putObject(putObjectRequest, RequestBody.fromBytes(fileBytes));
            return uploadKey;
        } catch (Exception e) {
            log.error("字节数组上传失败: {}", uploadKey, e);
            throw new BizException("A0093", "字节数组上传失败: " + e.getMessage());
        }
    }

    @Override
    public void remove(String bucketName, String fileKey) {
        try {
            DeleteObjectRequest deleteObjectRequest = DeleteObjectRequest.builder()
                    .bucket(bucketName)
                    .key(fileKey)
                    .build();
            s3Client.deleteObject(deleteObjectRequest);
        } catch (Exception e) {
            log.error("删除文件失败: {}", fileKey, e);
            throw new BizException("A0094", "删除文件失败: " + e.getMessage());
        }
    }

    @Override
    public InputStream download(String bucketName, String fileKey) {
        try {
            GetObjectRequest getObjectRequest = GetObjectRequest.builder()
                    .bucket(bucketName)
                    .key(fileKey)
                    .build();

            ResponseInputStream<GetObjectResponse> s3Object = s3Client.getObject(getObjectRequest);
            return s3Object;
        } catch (Exception e) {
            log.error("下载文件失败: {}", fileKey, e);
            throw new BizException("A0095", "下载文件失败: " + e.getMessage());
        }
    }

    private String getUrl(String bucketName, String keyName) {
        try {
            // 对于自定义端点（如Ceph），直接构造URL
            // 在生产环境中，您可能需要使用S3Presigner来生成预签名URL
            String endpoint = gsGlobalConfig.getS3().getEndPoint();
            if (endpoint.endsWith("/")) {
                endpoint = endpoint.substring(0, endpoint.length() - 1);
            }
            return String.format("%s/%s/%s", endpoint, bucketName, keyName);
        } catch (Exception e) {
            log.error("生成URL失败: {}", keyName, e);
            throw new BizException("A0096", "生成URL失败: " + e.getMessage());
        }
    }

    @Override
    public void move(String sourceKey, String targetKey) {
        copy(sourceKey, targetKey);
        remove(sourceKey);
    }

    @Override
    public ObjectMetadataRes download(ObjectMetadataReq req) {
        try {
            GetObjectRequest.Builder getObjectRequestBuilder = GetObjectRequest.builder()
                    .bucket(bucketName)
                    .key(req.getFileKey());

            if (req.getEnd() == null) {
                getObjectRequestBuilder.range("bytes=" + req.getStart() + "-");
            } else {
                getObjectRequestBuilder.range("bytes=" + req.getStart() + "-" + req.getEnd());
            }

            GetObjectRequest getObjectRequest = getObjectRequestBuilder.build();
            ResponseInputStream<GetObjectResponse> s3Object = s3Client.getObject(getObjectRequest);

            ObjectMetadataRes res = new ObjectMetadataRes();
            res.setInputStream(s3Object);

            // 从响应头中获取内容范围信息
            String contentRange = s3Object.response().contentRange();
            if (contentRange != null) {
                // 解析 "bytes start-end/total" 格式
                String[] parts = contentRange.replace("bytes ", "").split("/");
                String[] range = parts[0].split("-");
                res.setFirstBytePos(Long.parseLong(range[0]));
                res.setLastBytePos(Long.parseLong(range[1]));
                res.setEntityLength(Long.parseLong(parts[1]));
            }

            return res;
        } catch (Exception e) {
            log.error("分块下载失败: {}", req.getFileKey(), e);
            throw new BizException("A0097", "分块下载失败: " + e.getMessage());
        }
    }

    @Override
    public FileMetadataRes queryMetadata(String fileKey) {
        try {
            HeadObjectRequest headObjectRequest = HeadObjectRequest.builder()
                    .bucket(bucketName)
                    .key(fileKey)
                    .build();

            HeadObjectResponse headObjectResponse = s3Client.headObject(headObjectRequest);
            long fileSize = headObjectResponse.contentLength();
            return new FileMetadataRes(fileSize);
        } catch (Exception e) {
            log.error("查询文件元数据失败: {}", fileKey, e);
            throw new BizException("A0098", "查询文件元数据失败: " + e.getMessage());
        }
    }

    public S3Client getS3Client() {
        return s3Client;
    }
}
