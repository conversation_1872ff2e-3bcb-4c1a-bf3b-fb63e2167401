<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.chinatelecom</groupId>
        <artifactId>gs-engine</artifactId>
        <version>2.0.0-SNAPSHOT</version>
    </parent>

    <groupId>com.chinatelecom</groupId>
    <artifactId>gs-engine-kms</artifactId>
    <version>2.0.0-SNAPSHOT</version>

    <dependencies>
        <dependency>
            <groupId>com.chinatelecom</groupId>
            <artifactId>gs-engine-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chinatelecom</groupId>
            <artifactId>gs-engine-model</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>netty-codec-http</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.chinatelecom</groupId>
            <artifactId>gs-engine-privilege-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-codec-http</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chinatelecom</groupId>
            <artifactId>gs-engine-manager</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chinatelecom</groupId>
            <artifactId>gs-engine-corekit</artifactId>
        </dependency>

        <dependency>
            <groupId>com.chinatelecom</groupId>
            <artifactId>telecom-ai-spring-boot-search</artifactId>
        </dependency>

        <dependency>
            <groupId>com.chinatelecom</groupId>
            <artifactId>telecom-ai-search-open-sdk</artifactId>
        </dependency>


        <!--mybatis plus 代码生成-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-generator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-freemarker</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yomahub</groupId>
            <artifactId>liteflow-spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>commons-beanutils</groupId>
                    <artifactId>commons-beanutils</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>commons-beanutils</groupId>
            <artifactId>commons-beanutils</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.fastjson2</groupId>
            <artifactId>fastjson2</artifactId>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>org.freemarker</groupId>
            <artifactId>freemarker</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>html2pdf</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chinatelecom</groupId>
            <artifactId>tele-flowable-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chinatelecom</groupId>
            <artifactId>gs-engine-kms-sdk</artifactId>
        </dependency>

        <dependency>
            <groupId>com.deepoove</groupId>
            <artifactId>poi-tl</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox</artifactId>
        </dependency>
    </dependencies>
</project>
