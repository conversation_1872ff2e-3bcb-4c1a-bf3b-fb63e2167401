package com.chinatelecom.gs.engine.channel.api.controller.web;

import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.channel.application.listener.ChannelOffSiteSwitchOpenEvent;
import com.chinatelecom.gs.engine.channel.manage.OffSiteChannelSwitchCacheService;
import com.chinatelecom.gs.engine.channel.manage.WebLinkConfigService;
import com.chinatelecom.gs.engine.channel.service.dto.WebLinkConfigDTO;
import com.chinatelecom.gs.engine.common.auth.KsMenuConfig;
import com.chinatelecom.gs.engine.common.auth.MenuConfig;
import com.chinatelecom.gs.engine.common.auth.PermissionTag;
import com.chinatelecom.gs.engine.common.context.Constants;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.robot.manage.info.dao.service.impl.AgentConfigServiceImpl;
import com.chinatelecom.gs.engine.robot.manage.info.service.AgentBasicConfigService;
import com.chinatelecom.gs.engine.robot.sdk.dto.Result;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;


/**
 * @description: 渠道配置
 * @author: xktang
 * @date: 2024/7/16 下午4:17
 * @version: 1.0
 */
@Slf4j
@Tag(name = "机器人渠道配置")
@PermissionTag(code = {MenuConfig.ROBOT, KsMenuConfig.BOT})
@RestController
@RequestMapping(Constants.ROBOT_PREFIX + Constants.WEB_PREFIX + "/channel")
public class ChannelConfigController {

    @Autowired
    private AgentConfigServiceImpl agentConfigService;

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private WebLinkConfigService webLinkConfigService;

    @Autowired
    AgentBasicConfigService agentBasicConfigService;

    @Autowired
    private OffSiteChannelSwitchCacheService offSiteChannelSwitchCacheService;

    @Operation(summary = "查询机器人场外部署总开关", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "查询机器人场外部署总开关", groupName = "机器人渠道配置")
    @Parameters({
            @Parameter(name = "botCode", description = "机器人编码", schema = @Schema(implementation = String.class))
    })
    @GetMapping("/offsite/switch/{agentCode}")
    @AuditLog(businessType = "机器人渠道配置", operType = "查询机器人场外部署总开关", operDesc = "查询机器人场外部署总开关", objId="#agentCode")
    public Result<Boolean> getOffSiteSwitch(@PathVariable(name = "agentCode") String agentCode) {

        Long agentEditVersion = agentBasicConfigService.getAgentEditVersion(agentCode);
        //如果agentEditVersion等于1则直接返回false
        if (agentEditVersion == 1) {
            return Result.success(false);
        }
        return Result.success(offSiteChannelSwitchCacheService.offsiteOffSwitch(agentCode, agentEditVersion));
    }

    @Operation(summary = "打开站外部署开关", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "打开站外部署开关", groupName = "机器人渠道配置")
    @PostMapping("/offsite/switch/{agentCode}/open")
    @AuditLog(businessType = "机器人渠道配置", operType = "打开站外部署开关", operDesc = "打开站外部署开关", objId="#agentCode")
    public Result<Boolean> openOffSiteSwitch(@PathVariable(name = "agentCode") String agentCode) {
        //校验机器人是否已经上线
        Long agentEditVersion = agentBasicConfigService.getAgentEditVersion(agentCode);
        //如果agentEditVersion等于1则直接返回false
        if (agentEditVersion == 1) {
            throw new BizException("AD010", "当前bot尚未发布上线");
        }
        boolean updateResult = offSiteChannelSwitchCacheService.updateOffSiteSwitch(agentCode, true, agentEditVersion);
        applicationContext.publishEvent(new ChannelOffSiteSwitchOpenEvent(agentCode));
        return Result.success(updateResult);
    }

    @Operation(summary = "关闭站外部署开关", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "关闭站外部署开关", groupName = "机器人渠道配置")
    @PostMapping("/offsite/switch/{agentCode}/close")
    @AuditLog(businessType = "机器人渠道配置", operType = "关闭站外部署开关", operDesc = "关闭站外部署开关", objId="#agentCode")
    public Result<Boolean> closeOffSiteSwitch(@PathVariable(name = "agentCode") String agentCode) {
        Long agentEditVersion = agentBasicConfigService.getAgentEditVersion(agentCode);
        boolean updateResult = offSiteChannelSwitchCacheService.updateOffSiteSwitch(agentCode, false, agentEditVersion);
        return Result.success(updateResult);
    }

    @Operation(summary = "刷新共享链接密码", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "刷新共享链接密码", groupName = "机器人渠道配置")
    @PostMapping(value = "/link/{channelId}/{agentCode}/regenerate", produces = MediaType.APPLICATION_JSON_VALUE)
    @AuditLog(businessType = "机器人渠道配置", operType = "刷新共享链接密码", operDesc = "刷新共享链接密码", objId="#agentCode")
    public Result<String> regenerateLinkPassword(@PathVariable(name = "channelId") String channelId, @PathVariable(name = "agentCode") String agentCode) {
        WebLinkConfigDTO newLinkConfig = webLinkConfigService.regenerateWebLinkSecret(channelId, agentCode);
        return Result.success(newLinkConfig.getSecret());
    }
}
