package com.chinatelecom.gs.engine.channel.service.dto;

import com.chinatelecom.gs.engine.channel.common.enums.MessageDirectionEnum;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2023/12/22 10:53
 * @description 会话记录表
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChannelMessageRecordDTO extends BaseDTO {
    private String channelId;

    private String robotCode;

    private String sessionId;

    private String userId;

    private String messageId;

    private String message;

    private String messageType;

    private MessageDirectionEnum msgDirection;
}
