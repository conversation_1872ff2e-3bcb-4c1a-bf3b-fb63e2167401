package com.chinatelecom.gs.engine.kms.controller;

import cn.hutool.core.net.NetUtil;
import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.common.config.aspect.annotation.DebugLog;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.kms.model.vo.*;
import com.chinatelecom.gs.engine.kms.sdk.constants.KmsApis;
import com.chinatelecom.gs.engine.kms.service.VisitsAppService;
import com.chinatelelcom.gs.engine.sdk.common.Page;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import com.chinatelelcom.gs.engine.sdk.common.enums.AppSourceType;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;


/**
 * <p>
 * 操作访问记录表 Controller
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
@RestController
@Slf4j
@Tag(name = "操作访问记录表 Controller")
@RequestMapping({KmsApis.KMS_API + KmsApis.VISITS_API})
public class VisitsController {

    @Autowired
    private VisitsAppService visitsAppService;

    @Operation(summary = "操作访问记录表新增", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @DebugLog(operation = "操作访问记录表新增")
    @PlatformRestApi(name = "操作访问记录表新增", groupName = "操作访问管理")
    @AuditLog(businessType = "操作访问管理", operType = "操作访问记录表新增", operDesc = "操作访问记录表新增", objId="#_RESULT_.data.code")
    @PostMapping
    public Result<VisitsVO> add(@Validated @RequestBody VisitsCreateParam createParam) {
        return Result.success(visitsAppService.create(createParam));
    }

    @Operation(summary = "操作访问记录表分页列表", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @DebugLog(operation = "操作访问记录表分页列表")
    @PlatformRestApi(name = "操作访问记录表分页列表", groupName = "操作访问管理")
    @AuditLog(businessType = "操作访问管理", operType = "操作访问记录表分页列表", operDesc = "操作访问记录表分页列表", objId="null")
    @PostMapping(KmsApis.PAGE_API)
    public Result<Page<VisitsVO>> page(@Validated @RequestBody VisitsQueryParam param) {
        param.setSourceSystem(AppSourceType.KS);
        return Result.success(visitsAppService.pageQuery(param));
    }

    @Operation(summary = "操作记录总数统计", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @DebugLog(operation = "操作访问记录表新增")
    @PlatformRestApi(name = "操作访问记录表新增", groupName = "操作访问管理")
    @AuditLog(businessType = "操作访问管理", operType = "操作访问记录表新增", operDesc = "操作访问记录表新增", objId="null")
    @GetMapping(KmsApis.GET_API)
    public Result<VisitsQueryVO> visitsStatistics() {
        return Result.success(visitsAppService.visitsStatistics());
    }

    @Operation(summary = "系统信息记录", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @DebugLog(operation = "操作访问记录表新增")
    @PlatformRestApi(name = "操作访问记录表新增", groupName = "操作访问管理")
    @AuditLog(businessType = "操作访问管理", operType = "操作访问记录表新增", operDesc = "操作访问记录表新增", objId="null")
    @GetMapping(KmsApis.VERIFY_API)
    public Result<VisitsVerifyInfoVO> verify() {
        VisitsVerifyInfoVO vo = new VisitsVerifyInfoVO();
        vo.setUsername(RequestContext.getUserName());
        vo.setUserId(RequestContext.getUserId());
        vo.setMac(NetUtil.getLocalMacAddress());
        vo.setDateTime(LocalDateTime.now());
        return Result.success(vo);
    }
}


