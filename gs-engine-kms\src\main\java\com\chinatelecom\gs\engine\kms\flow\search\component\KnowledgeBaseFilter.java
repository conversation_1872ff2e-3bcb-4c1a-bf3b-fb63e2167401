package com.chinatelecom.gs.engine.kms.flow.search.component;

import com.chinatelecom.gs.engine.common.utils.BizAssert;
import com.chinatelecom.gs.engine.kms.dto.KnowledgeBaseDTO;
import com.chinatelecom.gs.engine.kms.flow.search.SearchContext;
import com.chinatelecom.gs.engine.kms.sdk.enums.IndexType;
import com.chinatelecom.gs.engine.kms.sdk.enums.KnowledgeBaseType;
import com.chinatelecom.gs.engine.kms.sdk.enums.SearchRange;
import com.chinatelecom.gs.engine.kms.sdk.vo.search.SearchParam;
import com.chinatelecom.gs.engine.kms.service.RecallService;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@LiteflowComponent(id = "KnowledgeBaseFilter", name = "知识库过滤")
public class KnowledgeBaseFilter extends BaseRecall {

    @Resource
    private RecallService recallService;

    @Override
    public void process() throws Exception {
        SearchParam searchParam = this.getRequestData();
        SearchContext searchContext = this.getContextBean(SearchContext.class);

        SearchParam.Filter filter = searchParam.getFilter();
        if (filter == null) {
            filter = new SearchParam.Filter();
            searchParam.setFilter(filter);
        }
        if (filter.isKnowledgeBaseChoseAll()) {
            filter.setKnowledgeBaseChose(null);
            filter.setKnowledgeFilters(null);
        }

        // 处理挂载参数
        if (CollectionUtils.isNotEmpty(filter.getKnowledgeBaseChose())) {
            List<SearchParam.KnowledgeFilter> knowledgeFilters = recallService.convertSearchFilter(filter.getKnowledgeBaseChose());
            if (CollectionUtils.isEmpty(knowledgeFilters)) {
                log.warn("挂载视图范围下未找到符合条件的知识库数据");
                super.setIsEnd(true);
                return;
            }
            filter.setKnowledgeFilters(knowledgeFilters);
        }

        var knowledgeFilters = filter.getKnowledgeFilters();
        SearchRange range = searchParam.getRange();
        Map<String, KnowledgeBaseDTO> searchKnowledgeBaseMap = findSearchKnowledgeBaseMap(filter, range);
        if (MapUtils.isEmpty(searchKnowledgeBaseMap)) {
            log.warn("未查询到任何符合条件的知识库数据，本次搜索完成");
            super.setIsEnd(true);
            return;
        }

        // 分离普通知识库和外部知识库
        List<SearchParam.KnowledgeFilter> internalFilters = new ArrayList<>();
        List<SearchParam.KnowledgeFilter> externalFilters = new ArrayList<>();

        if (CollectionUtils.isEmpty(knowledgeFilters)) {
            knowledgeFilters = searchKnowledgeBaseMap.keySet().stream().map(kbCode -> {
                SearchParam.KnowledgeFilter knowledgeFilter = new SearchParam.KnowledgeFilter();
                knowledgeFilter.setKnowledgeBaseCode(kbCode);
                return knowledgeFilter;
            }).collect(Collectors.toList());
        }

        // 过滤知识库并分类
        Map<String, KnowledgeBaseDTO> filteredKnowledgeBaseMap = new HashMap<>();
        Set<IndexType> types = getAllTypes(searchParam);
        for (SearchParam.KnowledgeFilter filterItem : knowledgeFilters) {
            KnowledgeBaseDTO kbDto = searchKnowledgeBaseMap.get(filterItem.getKnowledgeBaseCode());

            // 过滤掉不存在的知识库
            if (kbDto == null) {
                continue;
            }

            // 过滤掉关闭状态的知识库
            if (!kbDto.onState() && filter.isOnlyOnState()) {
                continue;
            }

            // 过滤掉不符合类型的知识库
            IndexType knowledgeIndexType = getKnowledgeIndexType(kbDto);
            if (CollectionUtils.isNotEmpty(types) && !types.contains(knowledgeIndexType)) {
                continue;
            }

            // 添加到过滤后的知识库Map
            filteredKnowledgeBaseMap.put(filterItem.getKnowledgeBaseCode(), kbDto);

            // 分类为内部或外部知识库
            if (KnowledgeBaseType.EXTERNAL == kbDto.getType()) {
                externalFilters.add(filterItem);
            } else {
                internalFilters.add(filterItem);
            }
        }

        // 设置到过滤器和上下文中
        filter.setKnowledgeFilters(internalFilters);
        searchContext.setKnowledgeBaseDTOMap(filteredKnowledgeBaseMap);
        searchContext.setExternalKnowledgeFilters(externalFilters);

        // 记录过滤后的知识库数量
        log.info("过滤后的内部知识库数量: {}, 外部知识库数量: {}", internalFilters.size(), externalFilters.size());
    }

    private Set<IndexType> getAllTypes(SearchParam searchParam) {
        Set<IndexType> result = new HashSet<>();
        if (CollectionUtils.isNotEmpty(searchParam.getTypes())) {
            result.addAll(searchParam.getTypes());
        }
        if (searchParam.getType() != null) {
            result.add(searchParam.getType());
        }
        return result;
    }
}
