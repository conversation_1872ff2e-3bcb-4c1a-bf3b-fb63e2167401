package com.chinatelecom.gs.engine.channel.api.controller.rpc;

import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.channel.dao.repository.ChannelInfoRepository;
import com.chinatelecom.gs.engine.common.context.Constants;
import com.chinatelecom.gs.engine.robot.sdk.dto.Result;
import com.chinatelecom.gs.engine.robot.sdk.v2.spi.channel.ChannelRpcApi;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.data.ChannelInfoVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.util.List;

@Slf4j
@Tag(name = "机器人渠道配置")
@RestController
@RequestMapping(Constants.CHANNEL_PREFIX + Constants.RPC_PREFIX)
public class ChannelConfigRpcController implements ChannelRpcApi {

    @Resource
    private ChannelInfoRepository channelInfoRepository;

    @Operation(summary = "查询机器人场外部署总开关", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @GetMapping("/queryAllChannelInfo")
    @AuditLog(businessType = "机器人渠道配置", operType = "查询机器人场外部署总开关", operDesc = "查询机器人场外部署总开关", objId="null")
    public Result<List<ChannelInfoVO>> queryAllChannelInfo() {
        List<ChannelInfoVO> channelInfo = channelInfoRepository.getAllChannelInfo();
        return Result.success(channelInfo);
    }
}
