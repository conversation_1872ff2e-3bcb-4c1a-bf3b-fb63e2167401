package com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.faq;

import com.chinatelecom.gs.engine.kms.sdk.enums.AnswerType;
import com.chinatelecom.gs.engine.kms.sdk.enums.PublishStatus;
import com.chinatelecom.gs.engine.kms.sdk.vo.base.BaseCodeVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 问答表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-05
 */
@Getter
@Setter
@Schema(description = "问答表")
public class KnowledgeFaqVO extends BaseCodeVO {

    @Schema(description = "关联知识编码")
    @NotNull(message = "关联知识code不能为空")
    private String knowledgeCode;
    @Schema(description = "知识库编码")
    private String knowledgeBaseCode;
    @Schema(description = "开关")
    @NotNull(message = "开关不能为空")
    private Boolean on;
    @Schema(description = "问题")
    @NotEmpty(message = "问题不能为空")
    private String question;
    @Schema(description = "回答类型")
    private AnswerType answerType;
    @Schema(description = "回答")
    @NotEmpty(message = "回答不能为空")
    private String answer;
    @Schema(description = "相似问")
    private List<String> corpus;
    @Schema(description = "相似问个数")
    private Integer corpusCount;
    @Schema(description = "是否自动抽取")
    private Boolean isAuto;

    @Schema(description = "发布状态")
    private PublishStatus publishStatus;

    @Schema(description = "发布时间")
    private LocalDateTime publishTime;

    @Schema(description = "发布次数")
    private Integer publishCount;
}
