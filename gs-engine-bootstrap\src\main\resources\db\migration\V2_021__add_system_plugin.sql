
-- 插件版本信息表
INSERT INTO plugin_version_info
( tenant_id, plugin_id, version, status, runtime_status, version_desc, yn, create_id, create_name, update_id, update_name, create_time, update_time)
VALUES('', '1047410391340814336', 1, 'PUBLISHED', 'OFF_LINE', '', 0, '8578282513128292352', 'admin', '8578282513128292352', 'admin', '2025-05-27 15:14:57.240', '2025-06-05 15:00:14');
INSERT INTO plugin_version_info
(tenant_id, plugin_id, version, status, runtime_status, version_desc, yn, create_id, create_name, update_id, update_name, create_time, update_time)
VALUES('', '1047410391340814336', 2, 'PUBLISHED', 'RUNNING_PROD', '', 0, '8578282513128292352', 'admin', '8578282513128292352', 'admin', '2025-05-28 16:25:31.478', '2025-06-05 15:00:14');
INSERT INTO plugin_version_info
(tenant_id, plugin_id, version, status, runtime_status, version_desc, yn, create_id, create_name, update_id, update_name, create_time, update_time)
VALUES('', '1047410391340814336', 3, 'EDITING', 'OFF_LINE', '', 0, '8578282513128292352', 'admin', '8578282513128292352', 'admin', '2025-06-05 15:00:14.891', '2025-06-05 15:00:14.891');

-- 插件基本信息表
INSERT INTO plugin_meta_info
(tenant_id, plugin_id, plugin_name, plugin_desc, create_api_type, url, plugin_icon, auth_type, location, service_key, service_token, oauth_info, common_headers, status, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code, plugin_type, source_system)
VALUES('', '1047410391340814336', 'md转换ppt插件', '能够将markdown文件转换为ppt，并生成一个文件下载链接, 用于下载转换后的文件。', 2, 'http://aics', '/ais/common/f/images/Avatar/Avatar_default.png', 0, NULL, NULL, NULL, NULL, '[]', 2, 1, '8578282513128292352', 'admin', '8578282513128292352', 'admin', '2025-05-27 15:14:57.242', '2025-06-06 14:02:53.907', 0, 'ais', 1, 'GOVERNMENT');
INSERT INTO plugin_meta_info
(tenant_id, plugin_id, plugin_name, plugin_desc, create_api_type, url, plugin_icon, auth_type, location, service_key, service_token, oauth_info, common_headers, status, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code, plugin_type, source_system)
VALUES('', '1047410391340814336', 'md转换ppt插件', '能够将markdown文件转换为ppt，并生成一个文件下载链接, 用于下载转换后的文件。', 2, 'http://aics', '/ais/common/f/images/Avatar/Avatar_default.png', 0, NULL, NULL, NULL, NULL, '[]', 2, 2, '8578282513128292352', 'admin', '8578282513128292352', 'admin', '2025-05-27 15:14:57.242', '2025-06-06 14:02:53.918', 0, 'ais', 1, 'GOVERNMENT');
INSERT INTO plugin_meta_info
(tenant_id, plugin_id, plugin_name, plugin_desc, create_api_type, url, plugin_icon, auth_type, location, service_key, service_token, oauth_info, common_headers, status, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code, plugin_type, source_system)
VALUES('', '1047410391340814336', 'md转换ppt插件', '能够将markdown文件转换为ppt，并生成一个文件下载链接, 用于下载转换后的文件。', 2, 'http://aics', '/ais/common/f/images/Avatar/Avatar_default.png', 0, NULL, NULL, NULL, NULL, '[]', 2, 3, '8578282513128292352', 'admin', '8578282513128292352', 'admin', '2025-05-27 15:14:57.242', '2025-06-06 14:02:53.927', 0, 'ais', 1, 'GOVERNMENT');

-- 插件工具表
INSERT INTO plugin_api_meta_info
(tenant_id, api_id, plugin_id, api_name, api_desc, `path`, `method`, service_status, debug_status, quote_number, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES('', '1047411619793735680', '1047410391340814336', 'md_convert_ppt', '能够将markdown文件转换为ppt，并生成一个文件下载链接, 用于下载转换后的文件。', '/plugin/convert_md', '2', 1, 1, 0, 1, 1, '8578282513128292352', 'admin', '8578282513128292352', 'admin', '2025-05-27 15:19:50.125', '2025-06-06 14:34:51.170', 0, 'ais');
INSERT INTO plugin_api_meta_info
(tenant_id, api_id, plugin_id, api_name, api_desc, `path`, `method`, service_status, debug_status, quote_number, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES('', '1047411619793735680', '1047410391340814336', 'md_convert_ppt', '能够将markdown文件转换为ppt，并生成一个文件下载链接, 用于下载转换后的文件。', '/plugin/convert_md', '2', 1, 1, 0, 1, 2, '8578282513128292352', 'admin', '8578282513128292352', 'admin', '2025-05-27 15:19:50.125', '2025-06-06 14:34:51.181', 0, 'ais');
INSERT INTO plugin_api_meta_info
(tenant_id, api_id, plugin_id, api_name, api_desc, `path`, `method`, service_status, debug_status, quote_number, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES('', '1047411619793735680', '1047410391340814336', 'md_convert_ppt', '能够将markdown文件转换为ppt，并生成一个文件下载链接, 用于下载转换后的文件。', '/plugin/convert_md', '2', 1, 1, 0, 1, 3, '8578282513128292352', 'admin', '8578282513128292352', 'admin', '2025-05-27 15:19:50.125', '2025-06-06 14:34:51.191', 0, 'ais');

-- 插件参数表
INSERT INTO plugin_api_param
(tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES('', '1047778956523343872', NULL, '1047410391340814336', '1047411619793735680', 'url', 'file', 1, 4, 1, '[]', 11, 1, 1, '8578282513128292352', 'admin', '8578282513128292352', 'admin', '2025-05-28 15:39:30.030', '2025-05-28 15:39:30.030', 0, '');
INSERT INTO plugin_api_param
(tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES('', '1047778956523343873', NULL, '1047410391340814336', '1047411619793735680', 'format', 'format', 1, 4, 1, NULL, 1, 1, 1, '8578282513128292352', 'admin', '8578282513128292352', 'admin', '2025-05-28 15:39:30.031', '2025-05-28 15:39:30.031', 0, '');
INSERT INTO plugin_api_param
(tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES('', '1047778961300656128', NULL, '1047410391340814336', '1047411619793735680', 'download_url', 'download_url', 2, 4, 0, NULL, 1, 1, 1, '8578282513128292352', 'admin', '8578282513128292352', 'admin', '2025-05-28 15:39:31.166', '2025-05-28 15:39:31.166', 0, '');
INSERT INTO plugin_api_param
(tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES('', '1047778961300656129', NULL, '1047410391340814336', '1047411619793735680', 'expires_in', 'expires_in', 2, 4, 0, '', 3, 1, 1, '8578282513128292352', 'admin', '8578282513128292352', 'admin', '2025-05-28 15:39:31.168', '2025-05-28 15:39:31.168', 0, '');
INSERT INTO plugin_api_param
(tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES('', '1047778961300656130', NULL, '1047410391340814336', '1047411619793735680', 'status', 'status', 2, 4, 0, '', 1, 1, 1, '8578282513128292352', 'admin', '8578282513128292352', 'admin', '2025-05-28 15:39:31.169', '2025-05-28 15:39:31.169', 0, '');
INSERT INTO plugin_api_param
(tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES('', '1048146253691752448', NULL, '1047410391340814336', '1047411619793735680', 'url', 'file', 1, 4, 1, '[]', 11, 1, 2, '8578282513128292352', 'admin', '8578282513128292352', 'admin', '2025-05-29 15:59:00.496', '2025-05-29 15:59:00.496', 0, '');
INSERT INTO plugin_api_param
(tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES('', '1048146287392985088', NULL, '1047410391340814336', '1047411619793735680', 'data', 'download_url', 2, 4, 0, NULL, 1, 1, 2, '8578282513128292352', 'admin', '8578282513128292352', 'admin', '2025-05-29 15:59:08.531', '2025-05-29 15:59:08.531', 0, '');
INSERT INTO plugin_api_param
(tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES('', '1048146253691752448', NULL, '1047410391340814336', '1047411619793735680', 'url', 'file', 1, 4, 1, '[]', 11, 1, 3, '8578282513128292352', 'admin', '8578282513128292352', 'admin', '2025-05-29 15:59:00.496', '2025-06-05 15:00:15.109', 0, '');
INSERT INTO plugin_api_param
(tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES('', '1048146287392985088', NULL, '1047410391340814336', '1047411619793735680', 'data', 'download_url', 2, 4, 0, NULL, 1, 1, 3, '8578282513128292352', 'admin', '8578282513128292352', 'admin', '2025-05-29 15:59:08.531', '2025-06-05 15:00:15.110', 0, '');

-- 插件市场表
INSERT INTO plugin_mall_info
(plugin_id, category_code, category_name, plugin_source, create_id, create_name, update_id, update_name, create_time, update_time, yn, plugin_name, tenant_id, app_code, source_system)
VALUES('1047410391340814336', '', '', 1, '8578282513128292352', 'admin', '8578282513128292352', 'admin', '2025-06-05 15:01:02.105', '2025-06-05 15:03:05.999', 0, 'md转换ppt插件', NULL, NULL, NULL);

-- 插件版本信息表
INSERT INTO plugin_version_info
(tenant_id, plugin_id, version, status, runtime_status, version_desc, yn, create_id, create_name, update_id, update_name, create_time, update_time)
VALUES('', '1048143748303949824', 1, 'PUBLISHED', 'RUNNING_PROD', '', 0, '8578282513128292352', 'admin', '8578282513128292352', 'admin', '2025-05-29 15:49:03.166', '2025-06-05 15:00:23');
INSERT INTO plugin_version_info
(tenant_id, plugin_id, version, status, runtime_status, version_desc, yn, create_id, create_name, update_id, update_name, create_time, update_time)
VALUES('', '1048143748303949824', 2, 'EDITING', 'OFF_LINE', '', 0, '8578282513128292352', 'admin', '8578282513128292352', 'admin', '2025-06-05 15:00:23.939', '2025-06-05 15:00:23.939');


-- 插件基本信息表
INSERT INTO plugin_meta_info
(tenant_id, plugin_id, plugin_name, plugin_desc, create_api_type, url, plugin_icon, auth_type, location, service_key, service_token, oauth_info, common_headers, status, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code, plugin_type, source_system)
VALUES('', '1048143748303949824', 'md转换word插件', '能够将markdown文件转换为word文档，并生成一个文件下载链接, 用于下载转换后的文件。', 2, 'http://aics', '/ais/common/f/images/Avatar/Avatar_default.png', 0, NULL, NULL, NULL, NULL, '[]', 2, 1, '8578282513128292352', 'admin', '8578282513128292352', 'admin', '2025-05-29 15:49:03.176', '2025-06-06 14:21:15.535', 0, 'ais', 1, 'GOVERNMENT');
INSERT INTO plugin_meta_info
(tenant_id, plugin_id, plugin_name, plugin_desc, create_api_type, url, plugin_icon, auth_type, location, service_key, service_token, oauth_info, common_headers, status, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code, plugin_type, source_system)
VALUES('', '1048143748303949824', 'md转换word插件', '能够将markdown文件转换为word文档，并生成一个文件下载链接, 用于下载转换后的文件。', 2, 'http://aics', '/ais/common/f/images/Avatar/Avatar_default.png', 0, NULL, NULL, NULL, NULL, '[]', 2, 2, '8578282513128292352', 'admin', '8578282513128292352', 'admin', '2025-05-29 15:49:03.176', '2025-06-06 14:21:15.545', 0, 'ais', 1, 'GOVERNMENT');

-- 插件工具表
INSERT INTO plugin_api_meta_info
(tenant_id, api_id, plugin_id, api_name, api_desc, `path`, `method`, service_status, debug_status, quote_number, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES('', '1048144004991160320', '1048143748303949824', 'md_convert_word', '能够将markdown文件转换为word文档，并生成一个文件下载链接, 用于下载转换后的文件。', '/plugin/convert_md', '2', 1, 1, 0, 1, 1, '8578282513128292352', 'admin', '8578282513128292352', 'admin', '2025-05-29 15:50:04.363', '2025-06-05 15:00:23', 0, 'ais');
INSERT INTO plugin_api_meta_info
(tenant_id, api_id, plugin_id, api_name, api_desc, `path`, `method`, service_status, debug_status, quote_number, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES('', '1048144004991160320', '1048143748303949824', 'md_convert_word', '能够将markdown文件转换为word文档，并生成一个文件下载链接, 用于下载转换后的文件。', '/plugin/convert_md', '2', 1, 1, 0, 1, 2, '8578282513128292352', 'admin', '8578282513128292352', 'admin', '2025-05-29 15:50:04.363', '2025-06-05 15:00:23.988', 0, 'ais');

-- 插件参数表
INSERT INTO plugin_api_param
(tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES('', '1048144082359291904', NULL, '1048143748303949824', '1048144004991160320', 'url', 'url', 1, 1, 1, '[]', 11, 1, 1, '8578282513128292352', 'admin', '8578282513128292352', 'admin', '2025-05-29 15:50:22.813', '2025-05-29 15:50:22.813', 0, '');
INSERT INTO plugin_api_param
(tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES('', '1048144122171625472', NULL, '1048143748303949824', '1048144004991160320', 'data', 'data', 2, 4, 0, NULL, 1, 1, 1, '8578282513128292352', 'admin', '8578282513128292352', 'admin', '2025-05-29 15:50:32.305', '2025-05-29 15:50:32.305', 0, '');
INSERT INTO plugin_api_param
(tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES('', '1048144122171625472', NULL, '1048143748303949824', '1048144004991160320', 'data', 'data', 2, 4, 0, NULL, 1, 1, 2, '8578282513128292352', 'admin', '8578282513128292352', 'admin', '2025-05-29 15:50:32.305', '2025-06-05 15:00:24.153', 0, '');
INSERT INTO plugin_api_param
(tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES('', '1051035984359854080', NULL, '1048143748303949824', '1048144004991160320', 'url', 'url', 1, 4, 1, '[]', 11, 1, 2, '8578282513128292352', 'admin', '8578282513128292352', 'admin', '2025-06-06 15:21:45.978', '2025-06-06 15:21:45.978', 0, '');

-- 插件市场表
INSERT INTO plugin_mall_info
(plugin_id, category_code, category_name, plugin_source, create_id, create_name, update_id, update_name, create_time, update_time, yn, plugin_name, tenant_id, app_code, source_system)
VALUES('1048143748303949824', '', '', 1, '8578282513128292352', 'admin', '8578282513128292352', 'admin', '2025-06-05 15:01:08.059', '2025-06-05 15:03:05.987', 0, 'md转换word插件', NULL, NULL, NULL);

-- 插件版本信息表
INSERT INTO plugin_version_info
(tenant_id, plugin_id, version, status, runtime_status, version_desc, yn, create_id, create_name, update_id, update_name, create_time, update_time)
VALUES('', '1047407832391749632', 1, 'PUBLISHED', 'OFF_LINE', '', 0, '8578282513128292352', 'admin', '8578282513128292352', 'admin', '2025-05-27 15:04:47.140', '2025-05-30 14:45:43');
INSERT INTO plugin_version_info
(tenant_id, plugin_id, version, status, runtime_status, version_desc, yn, create_id, create_name, update_id, update_name, create_time, update_time)
VALUES('', '1047407832391749632', 2, 'PUBLISHED', 'RUNNING_PROD', '', 0, '8578282513128292352', 'admin', '8578282513128292352', 'admin', '2025-05-27 15:55:24.224', '2025-05-30 14:45:43');
INSERT INTO plugin_version_info
(tenant_id, plugin_id, version, status, runtime_status, version_desc, yn, create_id, create_name, update_id, update_name, create_time, update_time)
VALUES('', '1047407832391749632', 3, 'EDITING', 'OFF_LINE', '', 0, '8578282513128292352', 'admin', '8578282513128292352', 'admin', '2025-05-30 14:45:43.313', '2025-05-30 14:45:43.313');

-- 插件基本信息表
INSERT INTO plugin_meta_info
(tenant_id, plugin_id, plugin_name, plugin_desc, create_api_type, url, plugin_icon, auth_type, location, service_key, service_token, oauth_info, common_headers, status, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code, plugin_type, source_system)
VALUES('', '1047407832391749632', 'nl2sql 插件', '将自然语言查询转换为SQL代码，简化数据库查询，使非技术用户也能轻松交互。', 1, 'http://aics', '/ais/common/f/images/Avatar/Avatar_default.png', 0, NULL, NULL, NULL, NULL, '[]', 2, 1, '8578282513128292352', 'admin', '8578282513128292352', 'admin', '2025-05-27 15:04:47.148', '2025-06-06 14:52:18.404', 0, 'ais', 1, 'GOVERNMENT');
INSERT INTO plugin_meta_info
(tenant_id, plugin_id, plugin_name, plugin_desc, create_api_type, url, plugin_icon, auth_type, location, service_key, service_token, oauth_info, common_headers, status, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code, plugin_type, source_system)
VALUES('', '1047407832391749632', 'nl2sql 插件', '将自然语言查询转换为SQL代码，简化数据库查询，使非技术用户也能轻松交互。', 1, 'http://aics', '/ais/common/f/images/Avatar/Avatar_default.png', 0, NULL, NULL, NULL, NULL, '[]', 2, 2, '8578282513128292352', 'admin', '8578282513128292352', 'admin', '2025-05-27 15:04:47.148', '2025-06-06 14:52:18.415', 0, 'ais', 1, 'GOVERNMENT');
INSERT INTO plugin_meta_info
(tenant_id, plugin_id, plugin_name, plugin_desc, create_api_type, url, plugin_icon, auth_type, location, service_key, service_token, oauth_info, common_headers, status, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code, plugin_type, source_system)
VALUES('', '1047407832391749632', 'nl2sql 插件', '将自然语言查询转换为SQL代码，简化数据库查询，使非技术用户也能轻松交互。', 1, 'http://aics', '/ais/common/f/images/Avatar/Avatar_default.png', 0, NULL, NULL, NULL, NULL, '[]', 2, 3, '8578282513128292352', 'admin', '8578282513128292352', 'admin', '2025-05-27 15:04:47.148', '2025-06-06 14:52:18.425', 0, 'ais', 1, 'GOVERNMENT');

-- 插件工具表
INSERT INTO plugin_api_meta_info
(tenant_id, api_id, plugin_id, api_name, api_desc, `path`, `method`, service_status, debug_status, quote_number, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES('', '1047407972066267136', '1047407832391749632', 'nl_sql', 'nl_sql', '/member2/ks-plugin-nl2sql/nl_sql', '2', 1, 1, 0, 1, 1, '8578282513128292352', 'admin', '8578282513128292352', 'admin', '2025-05-27 15:05:20.439', '2025-05-27 15:55:24', 0, 'ais');
INSERT INTO plugin_api_meta_info
(tenant_id, api_id, plugin_id, api_name, api_desc, `path`, `method`, service_status, debug_status, quote_number, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES('', '1047407972066267136', '1047407832391749632', 'nl_sql', 'nl_sql', '/member2/ks-plugin-nl2sql/nl_sql', '2', 1, 1, 0, 1, 2, '8578282513128292352', 'admin', '8578282513128292352', 'admin', '2025-05-27 15:05:20.439', '2025-05-30 14:45:43', 0, 'ais');
INSERT INTO plugin_api_meta_info
(tenant_id, api_id, plugin_id, api_name, api_desc, `path`, `method`, service_status, debug_status, quote_number, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES('', '1047407972066267136', '1047407832391749632', 'nl_sql', 'nl_sql', '/member2/ks-plugin-nl2sql/nl_sql', '2', 1, 1, 0, 1, 3, '8578282513128292352', 'admin', '8578282513128292352', 'admin', '2025-05-27 15:05:20.439', '2025-05-30 14:45:43.372', 0, 'ais');

-- 插件参数表
INSERT INTO plugin_api_param
(tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES('', '1047408566638219264', NULL, '1047407832391749632', '1047407972066267136', 'inputs', 'inputs', 1, 4, 1, '{}', 5, 1, 1, '8578282513128292352', 'admin', '8578282513128292352', 'admin', '2025-05-27 15:07:42.203', '2025-05-27 15:07:42.203', 0, '');
INSERT INTO plugin_api_param
(tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES('', '1047408566638219265', '1047408566638219264', '1047407832391749632', '1047407972066267136', 'query', 'query', 1, 4, 1, '', 1, 1, 1, '8578282513128292352', 'admin', '8578282513128292352', 'admin', '2025-05-27 15:07:42.205', '2025-05-27 15:07:42.205', 0, '');
INSERT INTO plugin_api_param
(tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES('', '1047408566638219266', '1047408566638219264', '1047407832391749632', '1047407972066267136', 'database_type', 'database_type', 1, 4, 1, '', 1, 1, 1, '8578282513128292352', 'admin', '8578282513128292352', 'admin', '2025-05-27 15:07:42.206', '2025-05-27 15:07:42.206', 0, '');
INSERT INTO plugin_api_param
(tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES('', '1047408566638219267', '1047408566638219264', '1047407832391749632', '1047407972066267136', 'extra_knowledge', 'extra_knowledge', 1, 4, 0, '', 1, 1, 1, '8578282513128292352', 'admin', '8578282513128292352', 'admin', '2025-05-27 15:07:42.206', '2025-05-27 15:07:42.206', 0, '');
INSERT INTO plugin_api_param
(tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES('', '1047408566642413568', '1047408566638219264', '1047407832391749632', '1047407972066267136', 'req-id', 'req-id', 1, 4, 0, '', 1, 1, 1, '8578282513128292352', 'admin', '8578282513128292352', 'admin', '2025-05-27 15:07:42.206', '2025-05-27 15:07:42.206', 0, '');
INSERT INTO plugin_api_param
(tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES('', '1047408566642413569', '1047408566638219264', '1047407832391749632', '1047407972066267136', 'table', 'table', 1, 4, 1, '', 1, 1, 1, '8578282513128292352', 'admin', '8578282513128292352', 'admin', '2025-05-27 15:07:42.206', '2025-05-27 15:07:42.206', 0, '');
INSERT INTO plugin_api_param
(tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES('', '1047408929072222208', NULL, '1047407832391749632', '1047407972066267136', 'status', 'status', 2, 4, 0, '', 2, 1, 1, '8578282513128292352', 'admin', '8578282513128292352', 'admin', '2025-05-27 15:09:08.608', '2025-05-27 15:09:08.608', 0, '');
INSERT INTO plugin_api_param
(tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES('', '1047408929072222209', NULL, '1047407832391749632', '1047407972066267136', 'message', 'message', 2, 4, 0, NULL, 1, 1, 1, '8578282513128292352', 'admin', '8578282513128292352', 'admin', '2025-05-27 15:09:08.609', '2025-05-27 15:09:08.609', 0, '');
INSERT INTO plugin_api_param
(tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES('', '1047408929072222210', NULL, '1047407832391749632', '1047407972066267136', 'result', 'result', 2, 4, 0, '', 5, 1, 1, '8578282513128292352', 'admin', '8578282513128292352', 'admin', '2025-05-27 15:09:08.610', '2025-05-27 15:09:08.610', 0, '');
INSERT INTO plugin_api_param
(tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES('', '1047408929072222211', '1047408929072222210', '1047407832391749632', '1047407972066267136', 'text', 'text', 2, 4, 0, '', 1, 1, 1, '8578282513128292352', 'admin', '8578282513128292352', 'admin', '2025-05-27 15:09:08.610', '2025-05-27 15:09:08.610', 0, '');
INSERT INTO plugin_api_param
(tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES('', '1047408566638219264', NULL, '1047407832391749632', '1047407972066267136', 'inputs', 'inputs', 1, 4, 1, '{}', 5, 1, 2, '8578282513128292352', 'admin', '8578282513128292352', 'admin', '2025-05-27 15:07:42.203', '2025-05-27 15:55:24.346', 0, '');
INSERT INTO plugin_api_param
(tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES('', '1047408566638219265', '1047408566638219264', '1047407832391749632', '1047407972066267136', 'query', 'query', 1, 4, 1, '', 1, 1, 2, '8578282513128292352', 'admin', '8578282513128292352', 'admin', '2025-05-27 15:07:42.205', '2025-05-27 15:55:24.348', 0, '');
INSERT INTO plugin_api_param
(tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES('', '1047408566638219266', '1047408566638219264', '1047407832391749632', '1047407972066267136', 'database_type', 'database_type', 1, 4, 1, '', 1, 1, 2, '8578282513128292352', 'admin', '8578282513128292352', 'admin', '2025-05-27 15:07:42.206', '2025-05-27 15:55:24.350', 0, '');
INSERT INTO plugin_api_param
(tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES('', '1047408566638219267', '1047408566638219264', '1047407832391749632', '1047407972066267136', 'extra_knowledge', 'extra_knowledge', 1, 4, 0, '', 1, 1, 2, '8578282513128292352', 'admin', '8578282513128292352', 'admin', '2025-05-27 15:07:42.206', '2025-05-27 15:55:24.350', 0, '');
INSERT INTO plugin_api_param
(tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES('', '1047408566642413568', '1047408566638219264', '1047407832391749632', '1047407972066267136', 'req-id', 'req-id', 1, 4, 0, '', 1, 1, 2, '8578282513128292352', 'admin', '8578282513128292352', 'admin', '2025-05-27 15:07:42.206', '2025-05-27 15:55:24.351', 0, '');
INSERT INTO plugin_api_param
(tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES('', '1047408566642413569', '1047408566638219264', '1047407832391749632', '1047407972066267136', 'table', 'table', 1, 4, 1, '', 1, 1, 2, '8578282513128292352', 'admin', '8578282513128292352', 'admin', '2025-05-27 15:07:42.206', '2025-05-27 15:55:24.351', 0, '');
INSERT INTO plugin_api_param
(tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES('', '1047408929072222208', NULL, '1047407832391749632', '1047407972066267136', 'status', 'status', 2, 4, 0, '', 2, 1, 2, '8578282513128292352', 'admin', '8578282513128292352', 'admin', '2025-05-27 15:09:08.608', '2025-05-27 15:55:24.351', 0, '');
INSERT INTO plugin_api_param
(tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES('', '1047408929072222209', NULL, '1047407832391749632', '1047407972066267136', 'message', 'message', 2, 4, 0, NULL, 1, 1, 2, '8578282513128292352', 'admin', '8578282513128292352', 'admin', '2025-05-27 15:09:08.609', '2025-05-27 15:55:24.353', 0, '');
INSERT INTO plugin_api_param
(tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES('', '1047408929072222210', NULL, '1047407832391749632', '1047407972066267136', 'result', 'result', 2, 4, 0, '', 5, 1, 2, '8578282513128292352', 'admin', '8578282513128292352', 'admin', '2025-05-27 15:09:08.610', '2025-05-27 15:55:24.354', 0, '');
INSERT INTO plugin_api_param
(tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES('', '1047408929072222211', '1047408929072222210', '1047407832391749632', '1047407972066267136', 'text', 'text', 2, 4, 0, '', 1, 1, 2, '8578282513128292352', 'admin', '8578282513128292352', 'admin', '2025-05-27 15:09:08.610', '2025-05-27 15:55:24.355', 0, '');
INSERT INTO plugin_api_param
(tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES('', '1047408566638219264', NULL, '1047407832391749632', '1047407972066267136', 'inputs', 'inputs', 1, 4, 1, '{}', 5, 1, 3, '8578282513128292352', 'admin', '8578282513128292352', 'admin', '2025-05-27 15:07:42.203', '2025-05-30 14:45:43.440', 0, '');
INSERT INTO plugin_api_param
(tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES('', '1047408566638219265', '1047408566638219264', '1047407832391749632', '1047407972066267136', 'query', 'query', 1, 4, 1, '', 1, 1, 3, '8578282513128292352', 'admin', '8578282513128292352', 'admin', '2025-05-27 15:07:42.205', '2025-05-30 14:45:43.442', 0, '');
INSERT INTO plugin_api_param
(tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES('', '1047408566638219266', '1047408566638219264', '1047407832391749632', '1047407972066267136', 'database_type', 'database_type', 1, 4, 1, '', 1, 1, 3, '8578282513128292352', 'admin', '8578282513128292352', 'admin', '2025-05-27 15:07:42.206', '2025-05-30 14:45:43.444', 0, '');
INSERT INTO plugin_api_param
(tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES('', '1047408566638219267', '1047408566638219264', '1047407832391749632', '1047407972066267136', 'extra_knowledge', 'extra_knowledge', 1, 4, 0, '', 1, 1, 3, '8578282513128292352', 'admin', '8578282513128292352', 'admin', '2025-05-27 15:07:42.206', '2025-05-30 14:45:43.445', 0, '');
INSERT INTO plugin_api_param
(tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES('', '1047408566642413568', '1047408566638219264', '1047407832391749632', '1047407972066267136', 'req-id', 'req-id', 1, 4, 0, '', 1, 1, 3, '8578282513128292352', 'admin', '8578282513128292352', 'admin', '2025-05-27 15:07:42.206', '2025-05-30 14:45:43.446', 0, '');
INSERT INTO plugin_api_param
(tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES('', '1047408566642413569', '1047408566638219264', '1047407832391749632', '1047407972066267136', 'table', 'table', 1, 4, 1, '', 1, 1, 3, '8578282513128292352', 'admin', '8578282513128292352', 'admin', '2025-05-27 15:07:42.206', '2025-05-30 14:45:43.451', 0, '');
INSERT INTO plugin_api_param
(tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES('', '1047408929072222208', NULL, '1047407832391749632', '1047407972066267136', 'status', 'status', 2, 4, 0, '', 2, 1, 3, '8578282513128292352', 'admin', '8578282513128292352', 'admin', '2025-05-27 15:09:08.608', '2025-05-30 14:45:43.451', 0, '');
INSERT INTO plugin_api_param
(tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES('', '1047408929072222209', NULL, '1047407832391749632', '1047407972066267136', 'message', 'message', 2, 4, 0, NULL, 1, 1, 3, '8578282513128292352', 'admin', '8578282513128292352', 'admin', '2025-05-27 15:09:08.609', '2025-05-30 14:45:43.452', 0, '');
INSERT INTO plugin_api_param
(tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES('', '1047408929072222210', NULL, '1047407832391749632', '1047407972066267136', 'result', 'result', 2, 4, 0, '', 5, 1, 3, '8578282513128292352', 'admin', '8578282513128292352', 'admin', '2025-05-27 15:09:08.610', '2025-05-30 14:45:43.455', 0, '');
INSERT INTO plugin_api_param
(tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES('', '1047408929072222211', '1047408929072222210', '1047407832391749632', '1047407972066267136', 'text', 'text', 2, 4, 0, '', 1, 1, 3, '8578282513128292352', 'admin', '8578282513128292352', 'admin', '2025-05-27 15:09:08.610', '2025-05-30 14:45:43.457', 0, '');

-- 插件市场表
INSERT INTO plugin_mall_info
(plugin_id, category_code, category_name, plugin_source, create_id, create_name, update_id, update_name, create_time, update_time, yn, plugin_name, tenant_id, app_code, source_system)
VALUES('1047407832391749632', '', '', 1, '8578282513128292352', 'admin', '8578282513128292352', 'admin', '2025-05-27 15:55:41.049', '2025-05-27 16:49:21.978', 0, 'nl2sql 插件', NULL, NULL, NULL);
