package com.chinatelecom.gs.engine.channel.dao.repository;


import com.baomidou.mybatisplus.extension.service.IService;
import com.chinatelecom.gs.engine.channel.dao.po.ChannelApiSecretPO;
import com.chinatelecom.gs.engine.channel.service.dto.ChannelApiSecretDTO;
import com.chinatelecom.gs.engine.robot.sdk.config.enums.ApiSecretType;

import java.util.List;

/**
 * API秘钥管理 服务类
 *
 * <AUTHOR>
 * @since 2023-08-16
 */
public interface ChannelApiSecretRepository extends IService<ChannelApiSecretPO> {
    ChannelApiSecretDTO findBySecretId(String secretId, String appId);

    List<ChannelApiSecretDTO> channelApiSecretListByAppId(String appId, ApiSecretType apiSecretType, int limit);
}