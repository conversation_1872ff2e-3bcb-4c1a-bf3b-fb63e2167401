package com.chinatelecom.gs.engine.kms.controller;

import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.common.constants.Apis;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.kms.dto.PluginFileUploadParam;
import com.chinatelecom.gs.engine.kms.dto.SessionFileImportParam;
import com.chinatelecom.gs.engine.kms.dto.SessionFileUploadParam;
import com.chinatelecom.gs.engine.kms.sdk.constants.KmsApis;
import com.chinatelecom.gs.engine.kms.sdk.vo.file.PluginFileQueryParam;
import com.chinatelecom.gs.engine.kms.sdk.vo.file.SessionFileQueryParam;
import com.chinatelecom.gs.engine.kms.sdk.vo.file.SessionFileUploadVO;
import com.chinatelecom.gs.engine.kms.sdk.vo.file.SessionFileVO;
import com.chinatelecom.gs.engine.kms.service.SessionFileService;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024年12月26日
 */


@RestController
@Slf4j
@Tag(name = "会话文件")
@RequestMapping({KmsApis.KMS_API + Apis.FILE_API + "/session", KmsApis.OPENAPI + Apis.FILE_API + "/session"})
public class SessionFileManagerController {

    @Resource
    private SessionFileService sessionFileService;

    @Operation(summary = "会话文件上传",description = "对话框挂载文件 2.5版本以上请使用 会话文件分开上传接口 进行对话框文件挂载,接口需要发送二进制文件和文件关联信息", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "会话文件上传", groupName = "对话框挂载文件")
    @AuditLog(businessType = "对话框挂载文件", operType = "会话文件上传", operDesc = "会话文件上传", objId="null")
    @PostMapping(value = Apis.UPLOAD, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Result<SessionFileUploadVO> upload(@Valid SessionFileUploadParam param) {
        return Result.success(sessionFileService.upload(param));
    }


    @Operation(summary = "会话文件分开上传", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "会话文件分开上传", groupName = "对话框挂载文件")
    @AuditLog(businessType = "对话框挂载文件", operType = "会话文件分开上传", operDesc = "会话文件分开上传", objId="#param.fileCode")
    @PostMapping(value = Apis.IMPORT)
    public Result<SessionFileUploadVO> importFile(@Valid @RequestBody SessionFileImportParam param) {
        return Result.success(sessionFileService.importFile(param));
    }

    @Operation(summary = "会话文件分开批量上传", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "会话文件分开批量上传", groupName = "对话框挂载文件")
    @AuditLog(businessType = "对话框挂载文件", operType = "会话文件分开批量上传", operDesc = "会话文件分开批量上传", objId="null")
    @PostMapping(value = Apis.IMPORT + Apis.BATCH)
    public Result<List<SessionFileUploadVO>> importFileBatch(@Valid @RequestBody List<SessionFileImportParam> param) {
        List<SessionFileUploadVO> result = new ArrayList<>();
        for (SessionFileImportParam sessionFileImportParam : param) {
            result.add(sessionFileService.importFile(sessionFileImportParam));
        }
        return Result.success(result);
    }

    @Operation(summary = "机器绑定插件-文件上传", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "机器绑定插件-文件上传", groupName = "对话框挂载文件")
    @AuditLog(businessType = "对话框挂载文件", operType = "机器绑定插件-文件上传", operDesc = "机器绑定插件-文件上传", objId="null")
    @PostMapping(value = "/plugin" + Apis.UPLOAD, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Result<SessionFileUploadVO> pluginUpload(@Valid PluginFileUploadParam param) {
        return Result.success(sessionFileService.pluginUpload(param));
    }

    @Operation(summary = "会话文件删除", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "会话文件删除", groupName = "对话框挂载文件")
    @AuditLog(businessType = "对话框挂载文件", operType = "会话文件删除", operDesc = "会话文件删除", objId="#param.fileCodes")
    @PostMapping(value = "/delFiles")
    public Result<Boolean> delFiles(@Valid @RequestBody SessionFileQueryParam param) {
        return Result.success(sessionFileService.deleteFile(param));
    }

    @Operation(summary = "文件查询", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "文件查询", groupName = "对话框挂载文件")
    @AuditLog(businessType = "对话框挂载文件", operType = "文件查询", operDesc = "文件查询", objId="#param.fileCodes")
    @PostMapping("/files")
    public Result<List<SessionFileVO>> files(@Valid @RequestBody PluginFileQueryParam param) {
        return Result.success(sessionFileService.queryFileCode(param));
    }

}
