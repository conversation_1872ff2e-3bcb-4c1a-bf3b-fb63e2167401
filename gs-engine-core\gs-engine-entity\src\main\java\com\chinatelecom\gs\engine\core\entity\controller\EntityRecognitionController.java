package com.chinatelecom.gs.engine.core.entity.controller;

import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.common.constants.Apis;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.core.entity.domain.request.PluginRecognitionRequest;
import com.chinatelecom.gs.engine.core.entity.service.EntityRecognitionService;
import com.chinatelecom.gs.engine.robot.sdk.dto.Entity;
import com.chinatelecom.gs.engine.robot.sdk.dto.EntityContent;
import com.chinatelecom.gs.engine.robot.sdk.dto.Result;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.entity.EntityDetailVO;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.entity.EntityRecognitionRequest;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.entity.EntityRecognitionResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.util.Arrays;
import java.util.List;


/**
 * @USER: pengmc1
 * @DATE: 2025/2/11 16:08
 */

@RestController
@Slf4j
@Tag(name = "实体识别")
@RequestMapping({Apis.BASE_PREFIX + Apis.WEB_API + "/entityRecognition"})
public class EntityRecognitionController {

    @Resource
    private EntityRecognitionService entityRecognitionService;

    @PostMapping("/predict")
    @Operation(summary = "实体识别", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "BOT管理")})})
    @PlatformRestApi(name = "实体识别", groupName = "实体识别")
    @AuditLog(businessType = "识别", operType = "实体识别", operDesc = "实体识别", objId="#request.query")
    public Result<EntityRecognitionResponse> predict(@Validated @RequestBody EntityRecognitionRequest request){
        return Result.success(entityRecognitionService.predict(request));
    }

    /**
     * 用于插件识别的接口
     * @param request
     * @return
     */
    @PostMapping("/pluginPredict")
    @PlatformRestApi(name = "插件识别", groupName = "实体识别")
    @AuditLog(businessType = "识别", operType = "插件识别", operDesc = "插件识别", objId="#request.query")
    public Result<List<Entity>> pluginPredict(@Validated @RequestBody PluginRecognitionRequest request) {
        EntityDetailVO entityDetailVO = request.getEntityDetailVOs().get(0);
        List<Entity> entityList = Lists.newArrayList();
        Entity entity = new Entity();
        entity.setEntityCode(entityDetailVO.getEntityCode());
        entity.setEntityName(entityDetailVO.getEntityName());
        entity.setEntityType(entityDetailVO.getEntityType());
        EntityContent entityContent = new EntityContent();
        entityContent.setValue("实体值");
        entityContent.setRawValue("实体值");
        entity.setEntityContents(Arrays.asList(entityContent));
        entityList.add(entity);
        return Result.success(entityList);
    }
}
