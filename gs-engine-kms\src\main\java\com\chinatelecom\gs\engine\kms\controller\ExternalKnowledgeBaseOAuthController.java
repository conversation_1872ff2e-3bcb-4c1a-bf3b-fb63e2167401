package com.chinatelecom.gs.engine.kms.controller;

import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.common.utils.JsonUtils;
import com.chinatelecom.gs.engine.kms.dto.KnowledgeBaseDTO;
import com.chinatelecom.gs.engine.kms.infra.dto.ExternalKnowledgeBaseOAuthStatusDTO;
import com.chinatelecom.gs.engine.kms.infra.repository.ExternalKnowledgeBaseOAuthStatusRepository;
import com.chinatelecom.gs.engine.kms.model.ExternalKnowledgeBaseOAuthRequest;
import com.chinatelecom.gs.engine.kms.model.ExternalKnowledgeBaseToken;
import com.chinatelecom.gs.engine.kms.repository.KnowledgeBaseRepository;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.base.ExternalKnowledgeBaseConfig;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.base.ExternalKnowledgeBaseOAuthInfo;
import com.chinatelecom.gs.engine.kms.service.ExternalKnowledgeBaseOAuthService;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 外部知识库OAuth授权控制器
 */
@RestController
//@RequestMapping("/kms/web/external/oauth")
//@Tag(name = "外部知识库OAuth授权接口")
@Slf4j
public class ExternalKnowledgeBaseOAuthController {

    @Autowired
    private ExternalKnowledgeBaseOAuthService oauthService;

    @Autowired
    private KnowledgeBaseRepository knowledgeBaseRepository;

    @Autowired
    private ExternalKnowledgeBaseOAuthStatusRepository oauthStatusRepository;

    /**
     * OAuth授权回调接口
     *
     * @param code  授权码
     * @param state 状态，包含知识库编码
     * @return 授权结果
     */
    @GetMapping("/callback")
    @Operation(summary = "OAuth授权回调", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "OAuth授权回调", groupName = "外部知识库")
    @AuditLog(businessType = "外部知识库", operType = "OAuth授权回调", operDesc = "OAuth授权回调", objId="#code")
    public ResponseEntity<Map<String, Object>> oauthCallback(@RequestParam("code") String code, @RequestParam("state") String state) {
        log.info("收到OAuth授权回调: code={}, state={}", code, state);

        Map<String, Object> result = new HashMap<>();
        try {
            // 解析state参数，获取知识库编码
            String knowledgeBaseCode = parseState(state);
            if (StringUtils.isEmpty(knowledgeBaseCode)) {
                throw new BizException("无效的state参数");
            }

            // 获取知识库信息
            KnowledgeBaseDTO knowledgeBaseDTO = knowledgeBaseRepository.selectByCode(knowledgeBaseCode);
            if (knowledgeBaseDTO == null || knowledgeBaseDTO.getConfig() == null || knowledgeBaseDTO.getConfig().getExternalConfig() == null) {
                throw new BizException("知识库不存在或未配置外部知识库");
            }

            // 获取外部知识库配置
            ExternalKnowledgeBaseConfig config = knowledgeBaseDTO.getConfig().getExternalConfig();
            if (config.getAuthType() == null || config.getAuthType() != 2) {
                throw new BizException("知识库未配置OAuth授权");
            }

            // 获取OAuth信息
            ExternalKnowledgeBaseOAuthInfo oauthInfo = config.getOauthInfo();
            if (oauthInfo == null) {
                throw new BizException("知识库OAuth信息为空");
            }

            // 创建OAuth请求
            ExternalKnowledgeBaseOAuthRequest request = new ExternalKnowledgeBaseOAuthRequest();
            request.setClientId(oauthInfo.getClientId());
            request.setClientSecret(oauthInfo.getClientSecret());
            request.setRedirectUri(oauthInfo.getClientUrl());
            request.setCode(code);

            // 获取OAuth令牌
            ExternalKnowledgeBaseToken token = oauthService.getToken(request, oauthInfo.getAuthorizationUrl());

            // 保存OAuth状态
            saveOAuthStatus(knowledgeBaseCode, token);

            result.put("success", true);
            result.put("message", "授权成功");
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("OAuth授权回调处理异常", e);
            result.put("success", false);
            result.put("message", "授权失败: " + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }

    /**
     * 获取OAuth授权状态
     *
     * @param knowledgeBaseCode 知识库编码
     * @return OAuth授权状态
     */
    @GetMapping("/status")
    @Operation(summary = "获取OAuth授权状态", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "获取OAuth授权状态", groupName = "外部知识库")
    @AuditLog(businessType = "外部知识库", operType = "获取OAuth授权状态", operDesc = "获取OAuth授权状态", objId="#knowledgeBaseCode")
    public ResponseEntity<Map<String, Object>> getOAuthStatus(@RequestParam("knowledgeBaseCode") String knowledgeBaseCode) {
        Map<String, Object> result = new HashMap<>();
        try {
            ExternalKnowledgeBaseOAuthStatusDTO statusDTO = oauthStatusRepository.getAuthStatus(knowledgeBaseCode);
            if (statusDTO == null) {
                result.put("authorized", false);
                result.put("message", "未授权");
            } else {
                result.put("authorized", statusDTO.getStatus() == 1);
                result.put("message", statusDTO.getStatus() == 1 ? "已授权" : "未授权");
                result.put("tokenType", statusDTO.getTokenType());
                result.put("expiresIn", statusDTO.getExpiresIn());
                result.put("scope", statusDTO.getScope());
                result.put("createdAt", statusDTO.getCreatedAt());
            }
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取OAuth授权状态异常: knowledgeBaseCode={}", knowledgeBaseCode, e);
            result.put("authorized", false);
            result.put("message", "获取授权状态失败: " + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }

    /**
     * 解析state参数
     *
     * @param state state参数
     * @return 知识库编码
     */
    private String parseState(String state) {
        try {
            if (StringUtils.isEmpty(state)) {
                return null;
            }
            Map<String, Object> stateMap = JsonUtils.parseObject(state, HashMap.class);
            return (String) stateMap.get("knowledgeBaseCode");
        } catch (Exception e) {
            log.error("解析state参数异常: state={}", state, e);
            return null;
        }
    }

    /**
     * 保存OAuth状态
     *
     * @param knowledgeBaseCode 知识库编码
     * @param token             OAuth令牌
     */
    private void saveOAuthStatus(String knowledgeBaseCode, ExternalKnowledgeBaseToken token) {
        // 删除旧的OAuth状态
        oauthStatusRepository.deleteByKnowledgeBaseCode(knowledgeBaseCode);

        // 创建新的OAuth状态
        ExternalKnowledgeBaseOAuthStatusDTO statusDTO = new ExternalKnowledgeBaseOAuthStatusDTO();
        statusDTO.setKnowledgeBaseCode(knowledgeBaseCode);
        statusDTO.setStatus(1);
        statusDTO.setToken(token.getAccessToken());
        statusDTO.setRefreshToken(token.getRefreshToken());
        statusDTO.setTokenType(token.getTokenType());
        statusDTO.setExpiresIn(token.getExpiresIn());
        statusDTO.setScope(token.getScope());
        statusDTO.setCreatedAt(System.currentTimeMillis() / 1000);

        // 保存OAuth状态
        oauthStatusRepository.save(statusDTO);
    }
}
