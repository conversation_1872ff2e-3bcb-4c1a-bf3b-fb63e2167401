package com.chinatelecom.gs.engine.channel.manage;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinatelecom.gs.engine.channel.dao.po.ChannelConfigPO;
import com.chinatelecom.gs.engine.channel.dao.repository.ChannelConfigRepository;
import com.chinatelecom.gs.engine.channel.dao.repository.ChannelInfoRepository;
import com.chinatelecom.gs.engine.channel.service.dto.ChannelInfoDTO;
import com.chinatelecom.gs.engine.channel.service.dto.QywxConfigDTO;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.context.RequestInfo;
import com.chinatelecom.gs.engine.common.utils.JsonUtils;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;

class QywxConfigServiceTest {

    @InjectMocks
    private QywxConfigService qywxConfigService;

    @Mock
    private ChannelConfigRepository configRepository;

    @Mock
    private ChannelInfoRepository channelInfoRepository;

    @Mock
    private RequestInfo requestInfo;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        RequestInfo requestInfo = new RequestInfo();
        requestInfo.setUserName("testUser");
        RequestContext.set(requestInfo);
    }

    @AfterEach
    void tearDown() {
        RequestContext.remove();
    }

    @Test
    void testAddConfig_GenerateTokenAndAesKey() {
        String channelId = "1";
        String robotCode = "robot123";
        QywxConfigDTO dto = new QywxConfigDTO();
        dto.setSecret("secret");
        dto.setCorpId("corp123");

        ChannelInfoDTO channelInfo = new ChannelInfoDTO();
        channelInfo.setId(1L);

        when(channelInfoRepository.getChannelInfo(channelId, robotCode)).thenReturn(channelInfo);
        when(configRepository.getOne(any(LambdaQueryWrapper.class))).thenReturn(null);
        when(configRepository.save(any(ChannelConfigPO.class))).thenReturn(true);

        Boolean result = qywxConfigService.addConfig(channelId, robotCode, dto);

        assertNotNull(dto.getToken());
        assertNotNull(dto.getEncodingAESKey());
        assertTrue(result);
    }

    @Test
    void testAddConfig_ChannelInfoNotFound_ThrowsException() {
        String channelId = "1";
        String robotCode = "robot123";
        QywxConfigDTO dto = new QywxConfigDTO();

        when(channelInfoRepository.getChannelInfo(channelId, robotCode)).thenReturn(null);

        BizException exception = assertThrows(BizException.class, () ->
                qywxConfigService.addConfig(channelId, robotCode, dto));
        assertEquals("A0049", exception.getCode());
    }

    @Test
    void testAddConfig_UpdateExistingConfig() {
        String channelId = "1";
        String robotCode = "robot123";
        QywxConfigDTO dto = new QywxConfigDTO();
        dto.setSecret("secret");
        dto.setCorpId("corp123");

        ChannelInfoDTO channelInfo = new ChannelInfoDTO();
        channelInfo.setId(1L);

        ChannelConfigPO existingConfig = new ChannelConfigPO();
        existingConfig.setId(1L);

        when(channelInfoRepository.getChannelInfo(channelId, robotCode)).thenReturn(channelInfo);
        when(configRepository.getOne(any(LambdaQueryWrapper.class))).thenReturn(existingConfig);
        when(configRepository.updateById(any(ChannelConfigPO.class))).thenReturn(true);

        Boolean result = qywxConfigService.addConfig(channelId, robotCode, dto);

        assertTrue(result);
    }

    @Test
    void testGetQywxConfig_ChannelInfoNotFound_ThrowsException() {
        String channelId = "1";
        String robotCode = "robot123";

        when(channelInfoRepository.getChannelInfo(channelId, robotCode)).thenReturn(null);

        BizException exception = assertThrows(BizException.class, () ->
                qywxConfigService.getQywxConfig(channelId, robotCode));
        assertEquals("A0049", exception.getCode());
    }

    @Test
    void testGetQywxConfig_ConfigNotFound_ReturnsNull() {
        String channelId = "1";
        String robotCode = "robot123";

        ChannelInfoDTO channelInfo = new ChannelInfoDTO();
        channelInfo.setId(1L);

        when(channelInfoRepository.getChannelInfo(channelId, robotCode)).thenReturn(channelInfo);
        when(configRepository.getOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        QywxConfigDTO result = qywxConfigService.getQywxConfig(channelId, robotCode);

        assertNull(result);
    }

    @Test
    void testGetQywxConfig_ConfigExists_ReturnsDTO() {
        String channelId = "1";
        String robotCode = "robot123";

        ChannelInfoDTO channelInfo = new ChannelInfoDTO();
        channelInfo.setId(1L);

        ChannelConfigPO configPO = new ChannelConfigPO();
        configPO.setConfigValue(JsonUtils.toJsonString(new QywxConfigDTO()));

        when(channelInfoRepository.getChannelInfo(channelId, robotCode)).thenReturn(channelInfo);
        when(configRepository.getOne(any(LambdaQueryWrapper.class))).thenReturn(configPO);

        QywxConfigDTO result = qywxConfigService.getQywxConfig(channelId, robotCode);

        assertNotNull(result);
    }
}
