package com.chinatelecom.gs.engine.core.corekit.domain.request;

import com.chinatelelcom.gs.engine.sdk.common.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * @author: Wei
 * @date: 2025-02-05 17:52
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "敏感词查询分页参数")
public class SensitivePageRequest extends PageParam {

    /**
     * 关键字
     */
    @Schema(description = "关键字")
    private String keyword;

    /**
     * 敏感词库编码
     */
    @Schema(description = "敏感词库编码")
    @NotBlank(message = "敏感词库编码不能为空")
    private String sensitiveBaseCode;

}
