CREATE TABLE `gs_session_file` (
                                   `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键 ID',
                                   `biz_type` varchar(32) DEFAULT NULL COMMENT '业务类型',
                                   `biz_code` varchar(255) DEFAULT NULL COMMENT '业务编码',
                                   `session_id` varchar(255) DEFAULT NULL COMMENT '会话ID',
                                   `file_code` varchar(255) DEFAULT NULL COMMENT '文件编码',
                                   `file_type` varchar(32) DEFAULT NULL COMMENT '文件类型',
                                   `file_name` varchar(255) DEFAULT NULL COMMENT '文件名称',
                                   `file_oss_url` varchar(255) DEFAULT NULL COMMENT 'oss地址',
                                   `extra_data` text COMMENT '扩展配置',
                                   `parse_split` varchar(32) DEFAULT NULL COMMENT '是否解析分片 0-否 1-是',
                                   `tenant_id` varchar(128) DEFAULT NULL COMMENT '租户 ID',
                                   `create_id` varchar(100) DEFAULT NULL COMMENT '创建用户 ID',
                                   `create_name` varchar(225) DEFAULT NULL COMMENT '创建用户名称',
                                   `create_time` datetime DEFAULT NULL COMMENT '创建用户时间',
                                   `update_id` varchar(255) DEFAULT NULL COMMENT '最后修改用户 ID',
                                   `update_name` varchar(255) DEFAULT NULL COMMENT '最后修改用户名称',
                                   `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                   `yn` int DEFAULT '0' COMMENT '删除标记，0-未删除，其他表示已删除',
                                   PRIMARY KEY (`id`),
                                   KEY `file_code_index` (`file_code`,`session_id`),
                                   KEY `session_id_index` (`session_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='会话文件';