CREATE TABLE `gs_log_tpl` (
        `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键 ID',
        `p_log_id` varchar(255) DEFAULT NULL COMMENT '父级日志ID',
        `log_id` varchar(255) DEFAULT NULL COMMENT '日志ID',
		`session_id` varchar(255) DEFAULT NULL COMMENT '会话ID',
		`message_id` varchar(255) DEFAULT NULL COMMENT '消息ID',
		`tenant_id` varchar(255) DEFAULT NULL COMMENT '租户ID',
		`trace_id` varchar(255) DEFAULT NULL COMMENT '天眼TraceId',
		`user_id` varchar(255) DEFAULT NULL COMMENT '用户ID',
		`agent_code` varchar(255) DEFAULT NULL COMMENT '机器人编码',
		`workflow_id` varchar(255) DEFAULT NULL COMMENT '工作流ID',
		`node_id` varchar(255) DEFAULT NULL COMMENT '节点ID',
        `name` varchar(255) DEFAULT NULL COMMENT '名称',
        `log_type` varchar(128) DEFAULT NULL COMMENT '日志类型',
		`url` varchar(512) DEFAULT NULL COMMENT '请求地址',
        `config` text COMMENT '配置信息',
		`input_data` text COMMENT '请求参数JSON',
		`output_data` text COMMENT '返回结果JSON',
		`extra_data` text COMMENT '扩展数据',
        `log_level` varchar(255) DEFAULT NULL COMMENT '日志级别',
        `status` varchar(32) DEFAULT NULL COMMENT '状态',
        `message` varchar(255) DEFAULT NULL COMMENT '消息',
        `env` varchar(255) DEFAULT NULL COMMENT '环境',
        `start_time` datetime(3) DEFAULT NULL COMMENT '创建用户时间',
		`end_time` datetime(3) DEFAULT NULL COMMENT '创建用户时间',
		`send_time` datetime(3) DEFAULT NULL COMMENT '创建用户时间',
        `add_time` datetime(3) DEFAULT NULL COMMENT '更新时间',
		`first_token_time` datetime(3) DEFAULT NULL COMMENT '更新时间',
		`cost_time` varchar(128) DEFAULT NULL COMMENT '日志类型',
		`first_token_cost_time` varchar(128) DEFAULT NULL COMMENT '日志类型',
        `yn` int DEFAULT '0' COMMENT '删除标记，0-未删除，其他表示已删除',
        PRIMARY KEY (`id`),
        KEY `session_id_index` (`session_id`),
        KEY `message_id_index` (`message_id`),
        KEY `start_time_index` (`start_time`),
		KEY `add_time_index` (`add_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='日志模板表';