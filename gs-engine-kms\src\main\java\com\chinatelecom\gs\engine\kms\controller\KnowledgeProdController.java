package com.chinatelecom.gs.engine.kms.controller;


import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.kms.sdk.constants.KmsApis;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.prod.*;
import com.chinatelecom.gs.engine.kms.service.KnowledgeProdAppService;
import com.chinatelecom.gs.engine.sdk.common.annotation.ApiType;
import com.chinatelecom.gs.engine.sdk.common.annotation.HideFromApiTypes;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * <p>
 * 知识线上表 Controller
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-16
 */
@RestController
@Slf4j
@Tag(name = "知识发布管理")
@RequestMapping({KmsApis.KMS_API + KmsApis.KNOWLEDGE + KmsApis.PROD,
        KmsApis.RPC + KmsApis.KNOWLEDGE + KmsApis.PROD,
        KmsApis.OPENAPI + KmsApis.KNOWLEDGE + KmsApis.PROD})
public class KnowledgeProdController {

    @Autowired
    private KnowledgeProdAppService knowledgeProdAppService;

    @Operation(summary = "发布知识文档", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "发布知识文档", groupName = "知识发布管理")
    @AuditLog(businessType = "知识发布管理", operType = "发布知识文档", operDesc = "发布知识文档", objId="#param.knowledgeCodes")
    @PostMapping(KmsApis.PUBLISH)
    public Result<Boolean> publish(@Validated @RequestBody  KnowledgePublishParam param) {
        knowledgeProdAppService.triggerPublish(param, false);
        return Result.success(true);
    }

    @Operation(summary = "批量发布知识文档", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "批量发布知识文档", groupName = "知识发布管理")
    @AuditLog(businessType = "知识发布管理", operType = "批量发布知识文档", operDesc = "批量发布知识文档", objId="#param.knowledgeBaseCode")
    @PostMapping(KmsApis.PUBLISH + KmsApis.BATCH)
    public Result<Boolean> publishBatch(@Validated @RequestBody KnowledgePublishBatchParam param) {
        knowledgeProdAppService.triggerPublishBatch(param);
        return Result.success(true);
    }

    @Operation(summary = "重置知识文档", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "重置知识文档", groupName = "知识发布管理")
    @AuditLog(businessType = "知识发布管理", operType = "重置知识文档", operDesc = "重置知识文档", objId="#param.knowledgeCodes")
    @PostMapping(KmsApis.RESET)
    public Result<Boolean> reset(@Validated @RequestBody KnowledgeResetParam param) {
        knowledgeProdAppService.reset(param);
        return Result.success(true);
    }

    @HideFromApiTypes(ApiType.OPENAPI)
    @Operation(summary = "文档维度内容对比", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "文档维度内容对比", groupName = "知识发布管理")
    @AuditLog(businessType = "知识发布管理", operType = "文档维度内容对比", operDesc = "文档维度内容对比", objId="#param.knowledgeCode")
    @PostMapping(KmsApis.COMPARE)
    public Result<KnowledgeCompareVO> compare(@Validated @RequestBody KnowledgeCompareParam param) {
        KnowledgeCompareVO result = knowledgeProdAppService.compare(param);
        return Result.success(result);
    }


//
//    @ApiOperation(value = "知识线上表分页列表")
//    @PlatformRestApi(name = "知识线上表分页列表", groupName = "知识发布管理")
//    @AuditLog(businessType = "知识发布管理", operType = "知识线上表分页列表", operDesc = "知识线上表分页列表", objId="#param")
//    @PostMapping(KmsApis.PAGE_API)
//    public Result<Page<KnowledgeProdVO>> page(@Validated @RequestBody KnowledgeProdQueryParam param) {
//
//        return Result.success(knowledgeProdAppService.pageQuery(param));
//    }
//
//    @ApiOperation(value = "知识线上表详情")
//    @PlatformRestApi(name = "知识线上表详情", groupName = "知识发布管理")
//    @AuditLog(businessType = "知识发布管理", operType = "知识线上表详情", operDesc = "知识线上表详情", objId="#param")
//    @GetMapping(KmsApis.CODE_PATH)
//    public Result<KnowledgeProdVO> get(@PathVariable("code") String code) {
//        return Result.success(knowledgeProdAppService.get(code));
//    }
//
//    @ApiOperation(value = "知识线上表新增")
//    @PlatformRestApi(name = "知识线上表新增", groupName = "知识发布管理")
//    @AuditLog(businessType = "知识发布管理", operType = "知识线上表新增", operDesc = "知识线上表新增", objId="#param")
//    @PostMapping
//    public Result<KnowledgeProdVO> add(@Validated @RequestBody KnowledgeProdCreateParam createParam) {
//        return Result.success(knowledgeProdAppService.create(createParam));
//    }
//
//
//    @ApiOperation(value = "知识线上表更新")
//    @PlatformRestApi(name = "知识线上表更新", groupName = "知识发布管理")
//    @AuditLog(businessType = "知识发布管理", operType = "知识线上表更新", operDesc = "知识线上表更新", objId="#param")
//    @PutMapping(KmsApis.CODE_PATH)
//    public Result<Boolean> update(@PathVariable("code") String code, @Validated @RequestBody KnowledgeProdUpdateParam param) {
//        return Result.success(knowledgeProdAppService.update(code, param));
//    }
//
//
//    @ApiOperation(value = "知识线上表删除")
//    @PlatformRestApi(name = "知识线上表删除", groupName = "知识发布管理")
//    @AuditLog(businessType = "知识发布管理", operType = "知识线上表删除", operDesc = "知识线上表删除", objId="#param")
//    @PostMapping(KmsApis.DELETE_API)
//    public Result<Boolean> delete(@Validated @RequestBody CodeParam codes) {
//        return Result.success(knowledgeProdAppService.delete(codes));
//    }
}


