package com.chinatelecom.gs.engine.kms.service.impl;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.excel.util.StringUtils;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.chinatelecom.gs.engine.common.config.property.GsGlobalConfig;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.context.RequestInfo;
import com.chinatelecom.gs.engine.common.utils.BizAssert;
import com.chinatelecom.gs.engine.common.utils.IPageUtils;
import com.chinatelecom.gs.engine.common.utils.JsonUtils;
import com.chinatelecom.gs.engine.core.manager.service.BaseConfigAppService;
import com.chinatelecom.gs.engine.core.model.enums.ModelConfigTypeEnum;
import com.chinatelecom.gs.engine.core.model.enums.ModelTypeEnum;
import com.chinatelecom.gs.engine.core.sdk.vo.ModelPageListParam;
import com.chinatelecom.gs.engine.kms.base.converter.BaseVoConverter;
import com.chinatelecom.gs.engine.kms.base.model.CodeParam;
import com.chinatelecom.gs.engine.kms.base.service.impl.BaseAppServiceByCodeImpl;
import com.chinatelecom.gs.engine.kms.convert.common.CommonConverter;
import com.chinatelecom.gs.engine.kms.convert.vo.KnowledgeBaseVoConverter;
import com.chinatelecom.gs.engine.kms.dto.EmbeddingInfo;
import com.chinatelecom.gs.engine.kms.dto.KnowledgeBaseDTO;
import com.chinatelecom.gs.engine.kms.enums.CatalogType;
import com.chinatelecom.gs.engine.kms.infra.po.KnowledgeBasePO;
import com.chinatelecom.gs.engine.kms.model.dto.CatalogDTO;
import com.chinatelecom.gs.engine.kms.repository.CatalogRepository;
import com.chinatelecom.gs.engine.kms.repository.KnowledgeBaseRepository;
import com.chinatelecom.gs.engine.kms.repository.KnowledgeRepository;
import com.chinatelecom.gs.engine.kms.sdk.enums.CheckStatus;
import com.chinatelecom.gs.engine.kms.sdk.enums.ExternalKnowledgeBaseFailureType;
import com.chinatelecom.gs.engine.kms.sdk.enums.KnowledgeBaseType;
import com.chinatelecom.gs.engine.kms.sdk.enums.KnowledgeRoleType;
import com.chinatelecom.gs.engine.kms.sdk.enums.PermissionEnum;
import com.chinatelecom.gs.engine.kms.sdk.enums.TargetType;
import com.chinatelecom.gs.engine.kms.sdk.vo.app.AppVO;
import com.chinatelecom.gs.engine.kms.sdk.vo.base.BaseCodeVO;
import com.chinatelecom.gs.engine.kms.sdk.vo.base.BaseNameVO;
import com.chinatelecom.gs.engine.kms.sdk.vo.common.CodeBatchParam;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.base.ExternalKnowledgeBaseConfig;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.base.KBPublishStatisticsVO;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.base.KnowledgeBaseCreateParam;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.base.KnowledgeBaseParam;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.base.KnowledgeBaseQueryParam;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.base.KnowledgeBaseUpdateParam;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.base.KnowledgeBaseVO;
import com.chinatelecom.gs.engine.kms.search.SearchService;
import com.chinatelecom.gs.engine.kms.search.model.KmsIndexCreateConfig;
import com.chinatelecom.gs.engine.kms.service.AppService;
import com.chinatelecom.gs.engine.kms.service.CollectionAppService;
import com.chinatelecom.gs.engine.kms.service.KnowledgeBaseService;
import com.chinatelecom.gs.engine.kms.service.ModelAppService;
import com.chinatelecom.gs.engine.kms.util.PrivilegeEnumUtils;
import com.chinatelecom.gs.engine.robot.sdk.dto.Result;
import com.chinatelecom.gs.engine.robot.sdk.v2.spi.kms.AgentKmsApi;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.kms.request.DeleteKnowledgeBaseRequest;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.kms.request.KmsRelAgentRequest;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.kms.request.SubscribeKnowledgeRequest;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.kms.response.KmsRelAgentResponse;
import com.chinatelecom.gs.privilege.common.dto.GrantObjectDTO;
import com.chinatelecom.gs.privilege.common.dto.ResourceDTO;
import com.chinatelecom.gs.privilege.common.enums.PrivilegeEnum;
import com.chinatelecom.gs.privilege.common.enums.ResourceTypeEnum;
import com.chinatelecom.gs.privilege.util.PrivilegeUtil;
import com.chinatelecom.gs.privilege.util.dto.PrivilegeResources;
import com.chinatelelcom.gs.engine.sdk.common.Page;
import com.chinatelelcom.gs.engine.sdk.common.PageImpl;
import com.chinatelelcom.gs.engine.sdk.common.enums.AppSourceType;
import com.chinatelelcom.gs.engine.sdk.common.enums.RequestSourceType;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.chinatelecom.gs.engine.common.constants.Constants.EMPTY_NODE;

/**
 * @Author: gaoxianjun
 * @CreateTime: 2024-12-12
 * @Description:
 * @Version: 1.0
 */
@Service
@Validated
@Slf4j
public class KnowledgeBaseServiceImpl extends BaseAppServiceByCodeImpl<KnowledgeBaseRepository, KnowledgeBaseQueryParam, KnowledgeBaseVoConverter, KnowledgeBasePO, KnowledgeBaseDTO, KnowledgeBaseVO, KnowledgeBaseCreateParam, KnowledgeBaseUpdateParam> implements KnowledgeBaseService {

    @Resource
    private SearchService searchService;

    @Resource
    private KnowledgeBaseRepository knowledgeBaseRepository;

    @Resource
    private CatalogRepository catalogRepository;

    @Resource
    private CollectionAppService collectionAppService;

    @Resource
    private AgentKmsApi agentKmsApi;

    @Resource
    @Lazy
    private AppService appService;

    @Resource
    private GsGlobalConfig gsGlobalConfig;

    @Resource
    private ModelAppService modelAppService;

    @Resource
    private PrivilegeUtil privilegeUtil;

    @Resource
    private KnowledgeRepository knowledgeRepository;

    @Resource
    private BaseConfigAppService configService;


    @Override
    protected BaseVoConverter<KnowledgeBaseDTO, KnowledgeBaseVO, KnowledgeBaseCreateParam, KnowledgeBaseUpdateParam> converter() {
        return KnowledgeBaseVoConverter.INSTANCE;
    }

    @Override
    public Page<KnowledgeBaseVO> pageQuery(KnowledgeBaseQueryParam knowledgeBaseQueryParam) {
        Pair<Wrapper<KnowledgeBasePO>, List<PrivilegeResources>> pair = pageQueryWrapperWithPrivilege(knowledgeBaseQueryParam);
        if (pair == null || pair.getLeft() == null) {
            return PageImpl.of(0, 0);
        }
        Wrapper<KnowledgeBasePO> queryWrapper = pair.getLeft();
        IPage<KnowledgeBaseDTO> page = new PageDTO<>(knowledgeBaseQueryParam.getPageNum(), knowledgeBaseQueryParam.getPageSize());
        page = repository.page(page, queryWrapper);
        Page<KnowledgeBaseVO> knowledgeBaseVOPage = IPageUtils.convert(page, dto -> converter().convertVO(dto));
        List<KnowledgeBaseVO> records = knowledgeBaseVOPage.getRecords();
        if (CollectionUtils.isNotEmpty(records)) {
            collectionAppService.fetchCollectionVO(TargetType.KnowledgeBase, records);
            //增加知识点数量统计
            appendKnowledgeCount(knowledgeBaseQueryParam, records, pair.getRight());
        }
        return knowledgeBaseVOPage;
    }

    protected Pair<Wrapper<KnowledgeBasePO>, List<PrivilegeResources>> pageQueryWrapperWithPrivilege(KnowledgeBaseQueryParam query) {
        Pair<Set<String>, List<PrivilegeResources>> pair = filterAuthKb(query.isCheckRole(), query.getKnowledgeBaseCodes(), query.getPermission());
        if (pair == null || pair.getLeft() == null) {
            return null;
        }
        Set<KnowledgeBaseType> types = new HashSet<>();
        if (query.getType() != null) {
            types.add(query.getType());
        }
        if (CollectionUtils.isNotEmpty(query.getTypes())) {
            types.addAll(query.getTypes());
        }

        Set<String> allSetCode = pair.getLeft();
        AppSourceType sourceSystem = query.getSourceSystem();
        boolean appSourceIsolation = gsGlobalConfig.getSystem().isAppSourceIsolation();
        LambdaQueryWrapper<KnowledgeBasePO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.like(StringUtils.isNotBlank(query.getName()), KnowledgeBasePO::getName, query.getName())
                .in(CollectionUtils.isNotEmpty(types), KnowledgeBasePO::getType, types)
                .eq(query.getOn() != null, KnowledgeBasePO::getOn, query.getOn())
                .eq(sourceSystem != null && appSourceIsolation, KnowledgeBasePO::getSourceSystem, sourceSystem)
                .in(CollectionUtils.isNotEmpty(allSetCode), KnowledgeBasePO::getCode, allSetCode)
                .eq(query.getCreatedByMe() != null && Boolean.TRUE.equals(query.getCreatedByMe()), KnowledgeBasePO::getCreateId, RequestContext.getUserId())
                .ne(query.getCreatedByMe() != null && Boolean.FALSE.equals(query.getCreatedByMe()), KnowledgeBasePO::getCreateId, RequestContext.getUserId())
                .orderByDesc(KnowledgeBasePO::getCreateTime);
        return Pair.of(lambdaQueryWrapper, pair.getRight());
    }


    /**
     * 构造分页查询条件, 有额外条件覆盖该方法
     *
     * @param query
     * @return
     */
    protected Wrapper<KnowledgeBasePO> pageQueryWrapper(KnowledgeBaseQueryParam query) {
        Pair<Wrapper<KnowledgeBasePO>, List<PrivilegeResources>> result = pageQueryWrapperWithPrivilege(query);
        if (result != null) {
            return result.getLeft();
        }
        return null;
    }

    private void appendKnowledgeCount(KnowledgeBaseQueryParam knowledgeBaseQueryParam, List<KnowledgeBaseVO> records, List<PrivilegeResources> privilegeResources) {
        if (CollectionUtils.isEmpty(records)) {
            return;
        }
        List<String> baseCodes = records.stream().map(BaseCodeVO::getCode).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(privilegeResources)) {
            privilegeResources = privilegeUtil.checkResourcePrivileges(baseCodes, ResourceTypeEnum.KNOWLEDGE, PrivilegeEnum.view);
        }
        final Map<String, PrivilegeResources> privilegeMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(privilegeResources)) {
            privilegeMap.putAll(privilegeResources.stream().collect(Collectors.toMap(PrivilegeResources::getResourceId, t -> t, (v1, v2) -> v1)));
        }
        Set<String> hasPrivilegeBaseCode = privilegeMap.keySet();
        Collection<String> hasNotPrivilegeBaseCode = CollectionUtils.subtract(baseCodes, hasPrivilegeBaseCode);
        if (CollectionUtils.isNotEmpty(hasNotPrivilegeBaseCode)) {
            List<ResourceDTO> resourceList = privilegeUtil.queryResource(ResourceTypeEnum.KNOWLEDGE, hasNotPrivilegeBaseCode);
            if (CollectionUtils.isNotEmpty(resourceList)) {
                resourceList.forEach(resource -> {
                    PrivilegeResources pr = new PrivilegeResources();
                    pr.setResourceId(resource.getResourceId());
                    pr.setIsPublic(resource.getIsPublic());
                    if (resource.getIsPublic() != null && resource.getIsPublic()) {
                        pr.setPrivilege(PrivilegeEnum.view);
                    } else {
                        pr.setPrivilege(PrivilegeEnum.no_permission);
                    }
                    privilegeMap.put(resource.getResourceId(), pr);
                });
            }
        }

        Map<String, KBPublishStatisticsVO> publishCountMap = knowledgeRepository.countPublishInfo(baseCodes);
        records.forEach(record -> {
            KBPublishStatisticsVO kbVO = publishCountMap.get(record.getCode());
            if (kbVO != null) {
                record.setPublishInfo(kbVO);
                record.setKnowledgeCount(kbVO.countAll());
            }

            // 设置知识库权限信息
            PrivilegeResources pr = privilegeMap.get(record.getCode());
            if (pr != null) {
                record.setPermission(PrivilegeEnumUtils.convertPermission(pr.getPrivilege()));
                record.setRoleType(KnowledgeRoleType.fromValue(pr.getIsPublic()));
            } else {
                record.setPermission(PermissionEnum.no_permission);
            }
        });

        try {
            KmsRelAgentRequest req = new KmsRelAgentRequest();
            req.setKnowledgeBaseCodes(baseCodes);
            Result<HashMap<String, KmsRelAgentResponse>> result = agentKmsApi.getKmsRelAgent(req);
            if (result.isSuccess() && result.getData() != null) {
                HashMap<String, KmsRelAgentResponse> infoMap = result.getData();
                records.forEach(record -> {
                    KmsRelAgentResponse agentInfo = infoMap.get(record.getCode());
                    if (agentInfo != null) {
                        record.setAgentInfo(agentInfo);
                    }
                });
            }
        } catch (Exception e) {
            log.warn("查询知识库业务流信息失败", e);
        }

        try {
            List<String> kbCodes = records.stream().map(KnowledgeBaseVO::getCode).collect(Collectors.toList());
            Map<String, Integer> titleCountMap = catalogRepository.countByTypeAndKbCodes(kbCodes,
                    EMPTY_NODE, CatalogType.TITLE);
            Map<String, Integer> knowledgeCountMap = catalogRepository.countByTypeAndKbCodes(kbCodes,
                    EMPTY_NODE, CatalogType.KNOWLEDGE);
            records.forEach(vo -> {
                // 获取下一级目录数量
                vo.setNextLevelCatalogCount(titleCountMap.getOrDefault(vo.getCode(), 0));
                // 获取下一级知识数量
                vo.setNextLevelKnowledgeCount(knowledgeCountMap.getOrDefault(vo.getCode(), 0));
            });
        } catch (Exception e) {
            log.warn("统计下属知识目录数量异常", e);
        }

    }

    @Transactional
    @Override
    public KnowledgeBaseVO rename(String code, String name) {
        KnowledgeBaseDTO knowledgeBaseDTO = repository.selectByCode(code);
        if (knowledgeBaseDTO == null) {
            return null;
        }
        checkKnowledgeBaseAuth(code, PrivilegeEnum.edit);

        checkNameUnique(code, name);
        knowledgeBaseDTO.setName(name);
        repository.updateByCode(knowledgeBaseDTO);
        return converter().convertVO(knowledgeBaseDTO);
    }

    private void checkNameUnique(String code, String name) {
        KnowledgeBaseDTO baseDTO = knowledgeBaseRepository.findByName(name);
        if (baseDTO != null) {
            if (!baseDTO.getCode().equals(code)) {
                BizAssert.throwBizException("AA060", "存在同名知识库");
            }
        }
    }

    @Override
    public KnowledgeBaseVO updateConfig(String code, KnowledgeBaseParam config) {
        KnowledgeBaseDTO knowledgeBaseDTO = repository.selectByCode(code);
        if (knowledgeBaseDTO == null) {
            return null;
        }
        KnowledgeBaseParam oldConfig = knowledgeBaseDTO.getConfig();
        // 判断外部知识库类型
        boolean isExternal = KnowledgeBaseType.EXTERNAL.equals(knowledgeBaseDTO.getType());
        if (!isExternal) {
            // todo 判断表征模型是否变动,索引迁移期间不允许变更操作。
            boolean result = changeIndex(code, config, oldConfig);
            if (result) {
                Map<String, KnowledgeBaseParam.ModelConfig> updateModelConfigMap = config.getModelConfig();
                KnowledgeBaseParam.ModelConfig updateModelConfig = updateModelConfigMap.get(ModelTypeEnum.EMBEDDING.getCode());
                if (updateModelConfig != null) {
                    Map<String, KnowledgeBaseParam.ModelConfig> oldModelConfig = oldConfig.getModelConfig();
                    oldModelConfig.put(ModelTypeEnum.EMBEDDING.getCode(), updateModelConfig);
                }
            }

            if (config.getBaseConfig() != null) {
                oldConfig.setBaseConfig(config.getBaseConfig());
            }
            knowledgeBaseDTO.setConfig(oldConfig);
            repository.updateKnowledgeBaseConfig(code, config);
        } else {
            updateExternalKnowledgeBaseConfig(knowledgeBaseDTO, config, oldConfig);
        }
        return converter().convertVO(knowledgeBaseDTO);
    }


    private void updateExternalKnowledgeBaseConfig(KnowledgeBaseDTO knowledgeBaseDTO, KnowledgeBaseParam newConfig, KnowledgeBaseParam oldConfig) {
        // 更新外部知识库配置
        if (newConfig.getExternalConfig() != null) {
            oldConfig.setExternalConfig(newConfig.getExternalConfig());
            knowledgeBaseDTO.setConfig(oldConfig);
            repository.updateByCode(knowledgeBaseDTO);
            log.info("更新外部知识库配置成功, code: {}", knowledgeBaseDTO.getCode());
        }
    }

    private boolean changeIndex(String code, KnowledgeBaseParam config, KnowledgeBaseParam oldConfig) {
        if (oldConfig != null && config != null) {
            KmsIndexCreateConfig oldCreateIndex = setAndGetModelDefault(oldConfig);
            KmsIndexCreateConfig createIndex = setAndGetModelDefault(config);

            if (oldCreateIndex != null && createIndex != null) {
                boolean queryChanged = !StringUtils.equals(oldCreateIndex.getVectorQueryAnalyzer(), createIndex.getVectorQueryAnalyzer());
                boolean docChanged = !StringUtils.equals(oldCreateIndex.getVectorDocAnalyzer(), createIndex.getVectorDocAnalyzer());
                if (queryChanged || docChanged) {
                    searchService.reindexAndMigrate(code, createIndex);
                    return true;
                }
            }
        }
        return false;
    }

    @Override
    public void switchOn(Collection<String> knowledgeBaseCodes, boolean on) {
        List<KnowledgeBaseDTO> knowledgeBaseDTOS = repository.listByCodes(knowledgeBaseCodes);
        if (CollectionUtils.isEmpty(knowledgeBaseDTOS)) {
            return;
        }
        knowledgeBaseDTOS.forEach(dto -> {
                    dto.setOn(on);
                    checkKnowledgeBaseAuth(dto.getCode(), PrivilegeEnum.edit);
                }
        );
        repository.saveOrUpdateBatch(knowledgeBaseDTOS);
    }

    @Transactional
    @Override
    public KnowledgeBaseVO create(KnowledgeBaseCreateParam createParam) {
        String knowledgeBaseCode = knowledgeBaseRepository.generateCode(StringUtils.EMPTY);
        checkNameUnique(knowledgeBaseCode, createParam.getName());
        if (createParam.getConfig() == null) {
            createParam.setConfig(new KnowledgeBaseParam());
        }

        KnowledgeBaseDTO knowledgeBaseDTO = converter().convertCreate(createParam);
        knowledgeBaseDTO.setCode(knowledgeBaseCode);

        if (createParam.isCheckRoleType()
                && Objects.equals(RequestContext.getRequestSourceType(), RequestSourceType.WEB)
                && KnowledgeRoleType.PUB == createParam.getRoleType()) {
            RequestInfo requestInfo = RequestContext.getAndCheck();
            BizAssert.assertTrue(requestInfo.getIsAdmin(), "AA110", "操作无权限，仅限管理员公开知识库");
        }

        // 只有非外部知识库才需要创建索引
        if (!KnowledgeBaseType.EXTERNAL.equals(createParam.getType())) {
            KmsIndexCreateConfig config = setAndGetModelDefault(createParam.getConfig());
            searchService.createOrUpdateIndex(knowledgeBaseCode, config);
        } else {
            // 外部知识库设置检查状态为失败
            knowledgeBaseDTO.setCheckStatus(CheckStatus.FAIL);
            // 设置失败原因为未进行配置校验状态
            ExternalKnowledgeBaseConfig externalConfig = new ExternalKnowledgeBaseConfig();
            externalConfig.setFailReason(ExternalKnowledgeBaseFailureType.CONFIG_NOT_VALIDATED);
            knowledgeBaseDTO.getConfig().setExternalConfig(externalConfig);
        }
        knowledgeBaseRepository.save(knowledgeBaseDTO);


        // 注册知识库到云平台
        updateAuth(knowledgeBaseDTO, createParam.getRoleType());
        return converter().convertVO(knowledgeBaseDTO);
    }

    private void updateAuth(KnowledgeBaseDTO dto,  KnowledgeRoleType roleType) {
        ResourceDTO resourceDTO = buildResource(dto, roleType);
        privilegeUtil.addResource(resourceDTO);
    }


    private ResourceDTO buildResource(KnowledgeBaseDTO dto, KnowledgeRoleType roleType) {
        ResourceDTO resourceDTO = new ResourceDTO();
        resourceDTO.setResourceId(dto.getCode());
        resourceDTO.setResourceType(ResourceTypeEnum.KNOWLEDGE);
        if (roleType != null) {
            resourceDTO.setIsPublic(KnowledgeRoleType.PUB == roleType);
        }
        return resourceDTO;
    }

    private KmsIndexCreateConfig setAndGetModelDefault(KnowledgeBaseParam param) {
        if (param == null) {
            param = new KnowledgeBaseParam();
        }
        Map<String, KnowledgeBaseParam.ModelConfig> modelConfig = param.getModelConfig();
        if (modelConfig == null) {
            modelConfig = Maps.newHashMap();
            param.setModelConfig(modelConfig);
        }

        String modelCode = null;
        KnowledgeBaseParam.ModelConfig embeddingModel = modelConfig.get(ModelTypeEnum.EMBEDDING.getCode());
        if (embeddingModel != null) {
            modelCode = embeddingModel.getModelCode();
        } else {
            embeddingModel = new KnowledgeBaseParam.ModelConfig();
            modelConfig.put(ModelTypeEnum.EMBEDDING.getCode(), embeddingModel);
        }

        EmbeddingInfo embeddingInfo = modelAppService.queryDefaultEmbedding(modelCode);
        KmsIndexCreateConfig config = buildDefaultConfig();
        if (embeddingInfo != null) {
            embeddingModel.setModelCode(embeddingInfo.getModelCode());
            embeddingModel.setModelFlag(true);

            String embeddingOfflineName = embeddingInfo.getEmbeddingName(ModelConfigTypeEnum.OFFLINEDOCUMENTSMODELCONFIG);
            if (StringUtils.isNotBlank(embeddingOfflineName)) {
                config.setVectorDocAnalyzer(embeddingOfflineName);
            }

            String embeddingQueryName = embeddingInfo.getEmbeddingName(ModelConfigTypeEnum.USERREQUEST);
            if (StringUtils.isNotBlank(embeddingQueryName)) {
                config.setVectorQueryAnalyzer(embeddingQueryName);
            }
        }

        // 设置其他模型地址
        Map<ModelTypeEnum, ModelPageListParam> modelMap = modelAppService.getDefaultModel();
        // 防EMBEDDING配置被覆盖
        if (modelConfig.containsKey(ModelTypeEnum.EMBEDDING.getCode())) {
            modelMap.remove(ModelTypeEnum.EMBEDDING);
        }
        if (MapUtils.isNotEmpty(modelMap)) {
            for (Map.Entry<ModelTypeEnum, ModelPageListParam> entry : modelMap.entrySet()) {
                ModelPageListParam modelPageListParam = entry.getValue();
                if (modelPageListParam != null) {
                    KnowledgeBaseParam.ModelConfig modelDefaultConfig = new KnowledgeBaseParam.ModelConfig();
                    modelDefaultConfig.setModelFlag(true);
                    modelDefaultConfig.setModelCode(modelPageListParam.getModelCode());
                    modelConfig.put(entry.getKey().getCode(), modelDefaultConfig);
                }
            }
        }
        return config;
    }

    private KmsIndexCreateConfig buildDefaultConfig() {
        GsGlobalConfig.KmsIndexConfig indexConfig = gsGlobalConfig.getIndex();
        KmsIndexCreateConfig config = CommonConverter.INSTANCE.convertConfig(indexConfig);
        return config;
    }


    @Override
    public boolean update(String code, KnowledgeBaseUpdateParam updateParam) {
        if (StringUtils.isNotBlank(updateParam.getName())) {
            checkNameUnique(code, updateParam.getName());
        }
        checkKnowledgeBaseAuth(code, PrivilegeEnum.edit);

        KnowledgeBaseDTO knowledgeBaseDTO = repository.findByCode(code);
        BizAssert.notNull(knowledgeBaseDTO, "AA011", "知识库不存在");
        return repository.update(code, updateParam);
    }

    private void subscribeRobot(String knowledgeBaseCode, KnowledgeBaseCreateParam createParam) {
        AppVO appVO = appService.get(RequestContext.getAppCode());
        BizAssert.notNull(appVO, "AA035", "未查询到应用信息");
        if (StringUtils.isNotBlank(appVO.getBotCode())) {
            SubscribeKnowledgeRequest sub = new SubscribeKnowledgeRequest();
            sub.setAgentCode(appVO.getBotCode());
            sub.setKnowledgeBaseCode(knowledgeBaseCode);
            sub.setType(createParam.getType().name());
            Result<Boolean> result = agentKmsApi.subscribe(sub);
            if (result == null || !result.isSuccess()) {
                log.error("默认机器人订阅失败, request:{}", JsonUtils.toJsonString(sub));
                BizAssert.throwBizException("AA031", "默认机器人订阅知识库失败");
            }
        } else {
            log.warn("空间未关联机器人信息，无法挂载知识库， appCode:{}", appVO.getCode());
        }
    }

    @Override
    public KnowledgeBaseVO get(String code) {
//        checkKnowledgeBaseAuth(code);
        KnowledgeBaseVO vo = super.get(code);
        PrivilegeEnum hasPrivilege = checkKnowledgeBaseAuth(code, PrivilegeEnum.view);
        GrantObjectDTO grantObject = queryGrantObject(code);

        // 设置用户权限
        vo.setPermission(PrivilegeEnumUtils.convertPermission(hasPrivilege));
        if (grantObject != null && grantObject.getIsPublic() != null) {
            vo.setRoleType(KnowledgeRoleType.fromValue(grantObject.getIsPublic()));
        }

        // 获取下一级目录数量
        Integer nextLevelCatalogCount = catalogRepository.countByTypeAndParentCode(code, EMPTY_NODE, CatalogType.TITLE);
        vo.setNextLevelCatalogCount(nextLevelCatalogCount);
        // 获取下一级知识数量
        Integer nextLevelKnowledgeCount = catalogRepository.countByTypeAndParentCode(code, EMPTY_NODE, CatalogType.KNOWLEDGE);
        vo.setNextLevelKnowledgeCount(nextLevelKnowledgeCount);
        vo.setCollected(collectionAppService.isCollected(TargetType.KnowledgeBase, vo.getCode()));

        Map<String, KBPublishStatisticsVO> publishCountMap = knowledgeRepository.countPublishInfo(ImmutableList.of(code));
        KBPublishStatisticsVO kbVO = publishCountMap.get(code);
        if (kbVO != null) {
            vo.setPublishInfo(kbVO);
            vo.setKnowledgeCount(kbVO.countAll());
        }

        // 设置审批流程信息
        KnowledgeBaseParam config = vo.getConfig();
        if (config != null) {
            KnowledgeBaseParam.BaseConfig baseConfig = config.getBaseConfig();
            if (baseConfig == null) {
                baseConfig = new KnowledgeBaseParam.BaseConfig();
                config.setBaseConfig(baseConfig);
            }
//            if (StringUtils.isBlank(baseConfig.getProcessCode())) {
//                // 设置全局的配置
//                PublishConfig publicConfig = configService.getConfigOrDefault(ConfigConvert.PUBLISH_CONFIG, RequestContext.getTenantId());
//                baseConfig.setProcessCode(publicConfig.getProcessCode());
//            }
        }

        return vo;
    }

    @Override
    public List<KnowledgeBaseVO> list(Collection<String> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return ImmutableList.of();
        }

        List<KnowledgeBaseDTO> knowledgeBaseDTOS = repository.listByCodes(codes);

        List<KnowledgeBaseVO> vos = knowledgeBaseDTOS.stream()
                .map(converter()::convertVO)
                .collect(Collectors.toList());

        Map<String, Integer> titleCountMap = catalogRepository.countByTypeAndKbCodes(codes,
                EMPTY_NODE, CatalogType.TITLE);
        Map<String, Integer> knowledgeCountMap = catalogRepository.countByTypeAndKbCodes(codes,
                EMPTY_NODE, CatalogType.KNOWLEDGE);

        vos.forEach(vo -> {
            // 获取下一级目录数量
            vo.setNextLevelCatalogCount(titleCountMap.getOrDefault(vo.getCode(), 0));
            // 获取下一级知识数量
            vo.setNextLevelKnowledgeCount(knowledgeCountMap.getOrDefault(vo.getCode(), 0));
        });
        return vos;
    }


    /**
     * 返回所有符合条件的知识库编码
     *
     * @param checkRole
     * @param filterKbCodes
     * @param permission
     * @return null表示没有符合条件的数据， 空列表表示所有的都符合条件， 非空列表表示符合条件的数据
     */
    private Pair<Set<String>, List<PrivilegeResources>> filterAuthKb(boolean checkRole, List<String> filterKbCodes, PermissionEnum permission) {
        Set<String> filterKnCode = new HashSet<>();
        List<PrivilegeResources> priList = null;
        if (checkRole) {
            if (RequestContext.getAndCheck().getIsAdmin()) {
                if (CollectionUtils.isNotEmpty(filterKbCodes)) {
                    filterKnCode.addAll(filterKbCodes);
                }
            } else {
                PrivilegeEnum privilege = PrivilegeEnumUtils.convertPrivilege(permission);
                if (privilege == null) {
                    privilege = PrivilegeEnum.view;
                }

                List<String> filterPriKbCodes = filterKbCodes;
                if (filterPriKbCodes == null) {
                    filterPriKbCodes = new ArrayList<>();
                }

                priList = privilegeUtil.checkResourcePrivileges(filterPriKbCodes, ResourceTypeEnum.KNOWLEDGE, privilege);
                if (CollectionUtils.isEmpty(priList)) {
                    return null;
                }
                Set<String> kbPriSet = priList.stream()
                        .map(PrivilegeResources::getResourceId)
                        .collect(Collectors.toSet());
                if (CollectionUtils.isEmpty(kbPriSet)) {
                    return null;
                } else {
                    if (CollectionUtils.isNotEmpty(filterKbCodes)) {
                        filterKnCode.addAll(CollectionUtils.intersection(kbPriSet, filterKbCodes));
                    } else {
                        filterKnCode.addAll(kbPriSet);
                    }
                }
            }
        } else {
            if (CollectionUtils.isNotEmpty(filterKbCodes)) {
                filterKnCode.addAll(filterKbCodes);
            }
        }
        return Pair.of(filterKnCode, priList);
    }

    @Override
    public List<KnowledgeBaseDTO> queryAllAuthedKB(KnowledgeBaseQueryParam param) {
        Wrapper<KnowledgeBasePO> queryWrapper = pageQueryWrapper(param);
        if (queryWrapper == null) {
            return Collections.EMPTY_LIST;
        }

        IPage<KnowledgeBaseDTO> page = new PageDTO<>(1, 1000);
        page = repository.page(page, queryWrapper);

        return page.getRecords();
    }

    @Override
    public PrivilegeEnum checkKnowledgeBaseAuth(String knowledgeBaseCode, PrivilegeEnum privilegeType) {
        KnowledgeBaseDTO knowledgeBaseDTO = repository.findByCode(knowledgeBaseCode);
        BizAssert.notNull(knowledgeBaseDTO, "AA011", "知识库不存在");

        boolean checkRole = RequestContext.checkRole();
        PrivilegeEnum hasPrivilege;
        if (checkRole) {
            GrantObjectDTO grantObjectDTO = queryGrantObject(knowledgeBaseCode);
            if (grantObjectDTO == null || grantObjectDTO.getPrivilege() == null) {
                BizAssert.throwBizException("A0013", "当前暂无该文档访问权限，需授权后才可进行预览，感谢您的理解！");
            }
            hasPrivilege = grantObjectDTO.getPrivilege();
        } else {
            // 可看默认授予读权限
            hasPrivilege = PrivilegeEnum.view;
        }

        if (hasPrivilege == null || hasPrivilege == PrivilegeEnum.no_permission) {
            BizAssert.throwBizException("A0013", "无知识库权限");
        } else if (hasPrivilege.getPriority() < privilegeType.getPriority()) {
            BizAssert.throwBizException("AA109", "无足够的操作权限，需要的权限类型：{}，实际的权限类型：{}", privilegeType, hasPrivilege);
        }
        return hasPrivilege;
    }

    @Override
    public GrantObjectDTO queryGrantObject(String knowledgeBaseCode) {
        try {
            return privilegeUtil.hasPrivilege(knowledgeBaseCode, ResourceTypeEnum.KNOWLEDGE);
        } catch (Exception e) {
            throw new BizException(e, "AA111", "知识库权限查询失败，code:" + knowledgeBaseCode);
        }
    }

    @Override
    public Page<BaseNameVO> allAuth(KnowledgeBaseQueryParam knowledgeBaseQueryParam) {
        LambdaQueryWrapper<KnowledgeBasePO> queryWrapper = (LambdaQueryWrapper<KnowledgeBasePO>) pageQueryWrapper(knowledgeBaseQueryParam);
        if (queryWrapper == null) {
            return PageImpl.of(0, 0);
        }
        queryWrapper.select(KnowledgeBasePO::getCode, KnowledgeBasePO::getName, KnowledgeBasePO::getType);

        IPage<KnowledgeBaseDTO> page = new PageDTO<>(knowledgeBaseQueryParam.getPageNum(), knowledgeBaseQueryParam.getPageSize());
        page = repository.page(page, queryWrapper);
        Page<BaseNameVO> knowledgeBaseVOPage = IPageUtils.convert(page, dto -> {
            BaseNameVO vo = new BaseNameVO();
            vo.setCode(dto.getCode());
            vo.setName(dto.getName());
            vo.setKnowledgeBaseType(dto.getType());
            return vo;
        });
        return knowledgeBaseVOPage;
    }

    /**
     * 根据code删除
     *
     * @param codes
     * @return
     */
    @Override
    @Transactional
    public boolean delete(CodeParam codes) {
        for (String code : codes.getCodes()) {
            SpringUtil.getBean(this.getClass()).delete(code);
        }
        return true;
    }

    public void delete(String code) {
        log.info("开始删除知识库，code:{}", code);
        KnowledgeBaseDTO knowledgeBaseDTO = repository.selectByCode(code);
        BizAssert.notNull(knowledgeBaseDTO, "AA011", "知识库不存在");
        checkKnowledgeBaseAuth(code, PrivilegeEnum.manage);
        CatalogDTO catalogDTO = catalogRepository.findOneByKnowledgeBaseCode(code);
        BizAssert.isNull(catalogDTO, "AA033", "知识库下存在文档或目录，禁止删除");
        repository.removeByCode(code);

        // 删除索引
        try {
            if (knowledgeBaseDTO.getType() != KnowledgeBaseType.EXTERNAL) {
                searchService.deleteIndex(code);
            }
        } catch (Exception e) {
            log.error("删除索引失败, 异常将忽略", e);
        }

        //删除收藏
        try {
            collectionAppService.deleteByTargetAllUser(code, TargetType.KnowledgeBase);
        } catch (Exception e) {
            log.error("删除收藏失败, 异常将忽略", e);
        }

        // 取消知识库挂载
        try {
            unsubscribeRobot(code);
        } catch (Exception e) {
            log.error("取消知识库挂载失败, 异常将忽略", e);
        }

        // 删除知识库权限资源
        try {
            ResourceDTO resourceDTO = buildResource(knowledgeBaseDTO, null);
            privilegeUtil.deleteResource(resourceDTO);
        } catch (Exception e) {
            log.error("删除知识库资源失败, 异常将忽略", e);
        }
    }

    private void unsubscribeRobot(String knowledgeBaseCode) {
        AppVO appVO = appService.get(RequestContext.getAppCode());
        BizAssert.notNull(appVO, "AA035", "未查询到应用信息");
        DeleteKnowledgeBaseRequest sub = new DeleteKnowledgeBaseRequest();
//        sub.setAgentCode(appVO.getBotCode()); 删除知识库同时移除所有关联bot
        sub.setKnowledgeBaseCodes(ImmutableList.of(knowledgeBaseCode));
        Result<Boolean> result = agentKmsApi.delete(sub);
        if (result == null || !result.isSuccess()) {
            log.error("删除默认机器人订阅失败, request:{}", JsonUtils.toJsonString(sub));
            BizAssert.throwBizException("AA031", "删除默认机器人订阅知识库失败");
        }
    }

    @Override
    public boolean updateExternalKnowledgeBaseCheckStatus(String knowledgeBaseCode, boolean success) {
        KnowledgeBaseDTO knowledgeBaseDTO = repository.selectByCode(knowledgeBaseCode);
        if (knowledgeBaseDTO == null) {
            log.warn("知识库不存在: knowledgeBaseCode={}", knowledgeBaseCode);
            return false;
        }

        if (!KnowledgeBaseType.EXTERNAL.equals(knowledgeBaseDTO.getType())) {
            log.info("非外部知识库，不需要更新检查状态: knowledgeBaseCode={}, type={}",
                    knowledgeBaseCode, knowledgeBaseDTO.getType());
            return false;
        }

        // 获取外部知识库配置

        CheckStatus checkStatus = success ? CheckStatus.SUCCESS : CheckStatus.FAIL;
        knowledgeBaseDTO.setCheckStatus(checkStatus);

        // 如果成功，清除失败信息
        if (success) {
            ExternalKnowledgeBaseConfig config = knowledgeBaseDTO.getConfig().getExternalConfig();
            config.setFailReason(null);
        }

        repository.updateByCode(knowledgeBaseDTO);

        log.info("更新外部知识库检查状态成功: knowledgeBaseCode={}, checkStatus={}",
                knowledgeBaseCode, checkStatus);
        return true;
    }

    @Override
    public List<String> findAllCodesWithoutTenant() {
        return repository.findAllCodesWithoutTenant();

    }

    /**
     * 查询知识库数量
     *
     * @param scope KnowledgeRoleType
     * @return Integer
     */
    @Override
    public Integer queryCommonKmsCount(KnowledgeRoleType scope) {
        if (Objects.isNull(scope)) {
            return 0;
        }
        Integer count = repository.queryCommonKmsCount();
        return count;
    }

    /**
     * 查询知识库数量
     *
     * @return Integer
     */
    @Override
    public Integer queryKmsCount() {
        LambdaQueryWrapper<KnowledgeBasePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(KnowledgeBasePO::getYn, 0);
        queryWrapper.eq(KnowledgeBasePO::getAppCode, RequestContext.getAppCode());
        return Math.toIntExact(repository.count(queryWrapper));
    }

    @Override
    public Collection<String> findCodesByTenantIds(Collection<String> tenantIds) {
        return repository.findCodesByTenantIds(tenantIds);

    }

    @Override
    public List<KBPublishStatisticsVO> statisticsPublish(CodeBatchParam codeParam) {
        Map<String, KBPublishStatisticsVO> result = knowledgeRepository.countPublishInfo(codeParam.getCodes());
        if(MapUtils.isNotEmpty(result)) {
            return new ArrayList<>(result.values());
        }
        return Collections.emptyList();
    }

    @Override
    public void refreshFileLength(String knowledgeBaseCode) {
        knowledgeBaseRepository.refreshFileLength(knowledgeBaseCode);
    }

    @Override
    public void refreshFileLength(Collection<String> knowledgeBaseCodes) {
        if (CollectionUtils.isEmpty(knowledgeBaseCodes)) {
            return;
        }
        for (String code : knowledgeBaseCodes) {
            knowledgeBaseRepository.refreshFileLength(code);
        }
    }


}
