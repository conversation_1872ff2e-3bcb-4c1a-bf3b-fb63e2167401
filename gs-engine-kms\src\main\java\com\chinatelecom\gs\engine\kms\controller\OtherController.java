package com.chinatelecom.gs.engine.kms.controller;

import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.common.utils.IdGenerator;
import com.chinatelecom.gs.engine.kms.sdk.constants.KmsApis;
import com.chinatelecom.gs.engine.sdk.common.annotation.ApiType;
import com.chinatelecom.gs.engine.sdk.common.annotation.HideFromApiTypes;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024年12月24日
 */

@RestController
@Tag(name = "其他接口")
@RequestMapping({KmsApis.KMS_API + KmsApis.OTHER})
public class OtherController {

    @HideFromApiTypes(ApiType.OPENAPI)
    @Operation(summary = "生成一个编码", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "生成一个编码", groupName = "其他接口")
    @AuditLog(businessType = "其他接口", operType = "生成一个编码", operDesc = "生成一个编码", objId="null")
    @GetMapping(value = KmsApis.ID_GENERATOR)
    public Result<String> idGenerator() {
        return Result.success(IdGenerator.id());
    }

}
