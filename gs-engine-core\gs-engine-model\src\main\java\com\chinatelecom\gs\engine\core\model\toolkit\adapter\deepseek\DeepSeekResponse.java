package com.chinatelecom.gs.engine.core.model.toolkit.adapter.deepseek;

import com.chinatelecom.gs.engine.core.sdk.vo.llm.BaseLLMResponse;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

@Data
public class DeepSeekResponse implements BaseLLMResponse {

    private String model;

    private String object;

    private Long created;

    private List<Choice> choices;

    private Usage usage;

    @Data
    public static class Choice implements Serializable{
        private Integer index;

        private String finish_reason;

        private DeepSeekMessage message;

        private DeepSeekMessage delta;
    }

    @Data
    public static class Usage implements Serializable {
        private Integer prompt_tokens;
        private Integer total_tokens;
        private Integer completion_tokens;
    }

    /**
     * 获取输出内容
     *
     * @return
     */
    @Override
    public String outputContent() {
        StringBuilder outputBuilder = new StringBuilder();
        if(CollectionUtils.isNotEmpty(choices)){
            for(Choice choice:choices){
                if(Objects.nonNull(choice.getMessage())){
                    outputBuilder.append(choice.getMessage().getContent());
                }
                if(Objects.nonNull(choice.getDelta())){
                    outputBuilder.append(choice.getDelta().getContent());
                }
            }
        }
        return outputBuilder.toString();
    }

    /**
     * 获取输入token数
     *
     * @return
     */
    @Override
    public Integer promptTokens() {
        if(this.usage != null){
            return this.usage.prompt_tokens;
        }
        return null;
    }

    /**
     * 获取输出token数
     *
     * @return
     */
    @Override
    public Integer completionTokens() {
        if(this.usage != null){
            return this.usage.completion_tokens;
        }
        return null;
    }
}
