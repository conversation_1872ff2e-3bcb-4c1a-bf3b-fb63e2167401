//package com.chinatelecom.gs.engine.core.manager.service.impl;
//
//import com.chinatelecom.gs.engine.common.log.track.LogEsPO;
//import com.chinatelecom.gs.engine.common.log.track.LogMessage;
//import com.chinatelecom.gs.engine.common.utils.EsPrefixUtils;
//import com.chinatelecom.gs.engine.robot.sdk.constant.CommonConstant;
//import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.extension.ExtendWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.MockedStatic;
//import org.mockito.MockitoAnnotations;
//import org.mockito.junit.jupiter.MockitoExtension;
//import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
//import org.springframework.data.elasticsearch.core.convert.ElasticsearchConverter;
//import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
//
//import java.time.LocalDateTime;
//
//import static org.junit.jupiter.api.Assertions.assertEquals;
//import static org.junit.jupiter.api.Assertions.assertThrows;
//import static org.mockito.Mockito.*;
//
//@ExtendWith(MockitoExtension.class)
//public class LogEsServiceImplTest {
//
//    @InjectMocks
//    private LogEsServiceImpl logEsService;
//
//    @Mock
//    private ElasticsearchRestTemplate elasticsearchRestTemplate;
//
//    @Mock
//    private ElasticsearchConverter elasticsearchConverter;
//
//    @BeforeEach
//    void setUp() {
//        MockitoAnnotations.openMocks(this);
//    }
//
//
//    @Test
//    void testSaveLog_WithOtherRuntimeError_ThrowsException() {
//        // Given
//        LogMessage logMessage = new LogMessage();
//        logMessage.setLogId("test-log-id");
//
//        // Mock EsPrefixUtils to avoid "No ConfigurableListableBeanFactory" error
//        try (MockedStatic<EsPrefixUtils> mockedEsPrefixUtils = mockStatic(EsPrefixUtils.class)) {
//            mockedEsPrefixUtils.when(() -> EsPrefixUtils.buildByPrefix(CommonConstant.ES_LOG_MESSAGE_INDEX_NAME))
//                    .thenReturn("test_log_message");
//
//            RuntimeException exception = new RuntimeException("Other error");
//            doThrow(exception).when(elasticsearchRestTemplate).save(any(LogEsPO.class), any(IndexCoordinates.class));
//
//            // When & Then
//            RuntimeException thrown = assertThrows(RuntimeException.class, () -> logEsService.saveLog(logMessage));
//            assertEquals("Other error", thrown.getMessage());
//        }
//    }
//
//    @Test
//    void testDeleteLogsBySendTimeRange_WithNullStartTime_ThrowsBizException() {
//        // Given
//        LocalDateTime endTime = LocalDateTime.of(2023, 1, 2, 0, 0, 0);
//
//        // When & Then
//        BizException exception = assertThrows(BizException.class, () ->
//                logEsService.deleteLogsBySendTimeRange(null, endTime));
//        assertEquals("AA089", exception.getCode());
//        assertEquals("开始时间和结束时间必须提供", exception.getMessage());
//    }
//
//    @Test
//    void testDeleteLogsBySendTimeRange_WithNullEndTime_ThrowsBizException() {
//        // Given
//        LocalDateTime startTime = LocalDateTime.of(2023, 1, 1, 0, 0, 0);
//
//        // When & Then
//        BizException exception = assertThrows(BizException.class, () ->
//                logEsService.deleteLogsBySendTimeRange(startTime, null));
//        assertEquals("AA089", exception.getCode());
//        assertEquals("开始时间和结束时间必须提供", exception.getMessage());
//    }
//
//
//    @Test
//    void testSearchLogsByTraceId_WithNullTraceId_ThrowsBizException() {
//        // When & Then
//        BizException exception = assertThrows(BizException.class, () ->
//                logEsService.searchLogsByTraceId(null));
//        assertEquals("AA089", exception.getCode());
//        assertEquals("traceId 必须提供", exception.getMessage());
//    }
//
//    @Test
//    void testSearchLogsByTraceId_WithEmptyTraceId_ThrowsBizException() {
//        // When & Then
//        BizException exception = assertThrows(BizException.class, () ->
//                logEsService.searchLogsByTraceId(""));
//        assertEquals("AA089", exception.getCode());
//        assertEquals("traceId 必须提供", exception.getMessage());
//    }
//
//
//    @Test
//    void testSearchLogsByMessageId_WithNullMessageId_ThrowsBizException() {
//        // When & Then
//        BizException exception = assertThrows(BizException.class, () ->
//                logEsService.searchLogsByMessageId(null));
//        assertEquals("AA089", exception.getCode());
//        assertEquals("messageId 必须提供", exception.getMessage());
//    }
//
//    @Test
//    void testSearchLogsByMessageId_WithEmptyMessageId_ThrowsBizException() {
//        // When & Then
//        BizException exception = assertThrows(BizException.class, () ->
//                logEsService.searchLogsByMessageId(""));
//        assertEquals("AA089", exception.getCode());
//        assertEquals("messageId 必须提供", exception.getMessage());
//    }
//
//
//    @Test
//    void testEsPrefixUtils_UsedInIndexCoordinates() {
//        // Given
//        LogMessage logMessage = new LogMessage();
//        logMessage.setLogId("test-log-id");
//
//        try (MockedStatic<EsPrefixUtils> mockedEsPrefixUtils = mockStatic(EsPrefixUtils.class)) {
//            mockedEsPrefixUtils.when(() -> EsPrefixUtils.buildByPrefix(CommonConstant.ES_LOG_MESSAGE_INDEX_NAME))
//                    .thenReturn("test_prefix_log_message");
//
//            // When
//            logEsService.saveLog(logMessage);
//
//            // Then
//            verify(elasticsearchRestTemplate).save(any(LogEsPO.class),
//                    eq(IndexCoordinates.of("test_prefix_log_message")));
//        }
//    }
//}
