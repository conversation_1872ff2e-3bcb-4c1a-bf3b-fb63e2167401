package com.chinatelecom.gs.engine.kms.util.safe;

import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.poifs.filesystem.POIFSFileSystem;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.IOException;
import java.io.InputStream;
import java.util.Iterator;

/**
 * Excel DDE注入检测工具类
 * 发现第一个潜在风险即返回，提高检测效率
 * 目前策略不允许含有公式
 */
@Slf4j
public class ExcelDDECheckUtils {


    /**
     * 检测指定的Excel文件流是否存在DDE注入风险
     *
     * @param inputStream Excel文件输入流
     * @return true表示存在DDE注入风险，false表示未发现风险
     */
    public static boolean hasDDEInjectionRisk(InputStream inputStream, boolean isXlsxFile) {
        try (InputStream input = inputStream) {
            if (isXlsxFile) {
                return detectXLSX(input);
            } else {
                return detectXLS(input);
            }
        } catch (Exception e) {
            log.error("检测过程中发生错误", e);
            return false;
        }
    }

    /**
     * 检测XLS文件中的DDE注入风险
     */
    private static boolean detectXLS(InputStream inputStream) {
        try (POIFSFileSystem fs = new POIFSFileSystem(inputStream);
             HSSFWorkbook workbook = new HSSFWorkbook(fs)) {

            for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
                Sheet sheet = workbook.getSheetAt(i);
                if (processSheet(sheet, workbook.getSheetName(i))) {
                    return true;
                }
            }
        } catch (IOException e) {
            log.error("读取XLS文件失败", e);
        }

        return false;
    }

    /**
     * 处理sheet
     *
     * @param sheet
     * @param workbook
     * @return
     */
    private static boolean processSheet(Sheet sheet, String workbook) {
        Iterator<Row> rowIterator = sheet.iterator();
        while (rowIterator.hasNext()) {
            Row row = rowIterator.next();
            Iterator<Cell> cellIterator = row.cellIterator();

            while (cellIterator.hasNext()) {
                Cell cell = cellIterator.next();
                if (isPotentialDDE(cell)) {
                    String cellValue = getCellValueAsString(cell);
                    log.warn("发现潜在DDE注入风险 - 文件类型: XLS, 工作表: {}, 行: {}, 列: {}, 值: {}",
                            workbook,
                            row.getRowNum() + 1,
                            getColumnLetter(cell.getColumnIndex()),
                            cellValue);
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 检测XLSX文件中的DDE注入风险
     */
    private static boolean detectXLSX(InputStream inputStream) {
        try (XSSFWorkbook workbook = new XSSFWorkbook(inputStream)) {

            for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
                Sheet sheet = workbook.getSheetAt(i);

                if (processSheet(sheet, workbook.getSheetName(i))) {
                    return true;
                }
            }
        } catch (IOException e) {
            log.error("读取XLSX文件失败", e);
        }
        return false;
    }

    /**
     * 获取单元格值的字符串表示
     */
    private static String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return "";
        }

        try {
            switch (cell.getCellType()) {
                case STRING:
                    return cell.getStringCellValue();
                case NUMERIC:
                    if (DateUtil.isCellDateFormatted(cell)) {
                        return cell.getDateCellValue().toString();
                    } else {
                        return String.valueOf(cell.getNumericCellValue());
                    }
                case BOOLEAN:
                    return String.valueOf(cell.getBooleanCellValue());
                case FORMULA:
                    return cell.getCellFormula();
                case ERROR:
                    return "ERROR:" + cell.getErrorCellValue();
                default:
                    return "";
            }
        } catch (Exception e) {
            return "ERROR: " + e.getMessage();
        }
    }

    /**
     * 不允许包含公式
     */
    private static boolean isPotentialDDE(Cell cell) {
        if (cell == null || cell.getCellType() == null) {
            return false;
        }
        return CellType.FORMULA.equals(cell.getCellType());
    }

    /**
     * 将列索引转换为Excel列字母
     */
    private static String getColumnLetter(int columnIndex) {
        StringBuilder column = new StringBuilder();
        int temp = columnIndex;

        while (temp >= 0) {
            column.insert(0, (char) ('A' + (temp % 26)));
            temp = (temp / 26) - 1;
        }
        return column.toString();
    }
}
