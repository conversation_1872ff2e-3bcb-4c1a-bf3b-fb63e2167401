CREATE TABLE IF NOT EXISTS `recent`
(
    `id`                    bigint unsigned   NOT NULL AUTO_INCREMENT COMMENT '主键',
    `code` varchar(64) NOT NULL COMMENT 'Code',
    `app_code`              varchar(64)       NOT NULL COMMENT '应用Code',
    `action_type`                varchar(20)       NOT NULL COMMENT '操作类型',
    `target_code`             bigint unsigned   NOT NULL COMMENT '关联知识或知识库ID',
    `target_type`           varchar(20)       NOT NULL COMMENT '关联类型',
    `yn` bigint unsigned NOT NULL DEFAULT '0' COMMENT '删除标记,0:未删除,其他表示已删除',
    `create_id` varchar(255) NOT NULL DEFAULT '0' COMMENT '创建用户ID',
    `create_name` varchar(255) NOT NULL DEFAULT '未知用户' COMMENT '创建用户名',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_id` varchar(255) NOT NULL DEFAULT '0' COMMENT '最后修改用户ID',
    `update_name` varchar(255) NOT NULL DEFAULT '未知用户' COMMENT '最后修改用户名',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_code` (`code`),
  KEY `idx_app_create` (`app_code`,`create_id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT ='最近操作表';