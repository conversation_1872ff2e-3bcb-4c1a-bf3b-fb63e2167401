CREATE TABLE `knowledge_audit` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `yn` BIGINT UNSIGNED NOT NULL DEFAULT '0' COMMENT '删除标记,0:未删除,其他表示已删除',
  `code` VARCHAR(64) NOT NULL COMMENT 'code',
  `app_code` VARCHAR(64) NOT NULL COMMENT '应用Code',
  `name` VARCHAR(2048) NOT NULL COMMENT '审核流程名称',
  `description` text COMMENT '审核流程描述',
  `knowledge_base_code` varchar(64) NOT NULL COMMENT '所属知识库Code',
  `external_process_code` VARCHAR(64) NOT NULL COMMENT '外部审批系统的流程编码',
  `external_instance_code` VARCHAR(64) NOT NULL COMMENT '外部审批系统的流程实例编码',
  `audit_type` VARCHAR(64) NOT NULL COMMENT '送审类型,DOC:文档变更,FAQ:问答对变更',
  `audit_start_time` DATETIME NOT NULL COMMENT '审核发起时间',
  `audit_end_time` DATETIME COMMENT '审核结束时间',
  `audit_param` text NOT NULL COMMENT '审核参数',
  `audit_status` VARCHAR(64) NOT NULL COMMENT '审核状态',
  `audit_result` VARCHAR(2048) COMMENT '审核结果',
  `create_id` VARCHAR(255) NOT NULL DEFAULT '0' COMMENT '创建用户ID',
  `create_name` VARCHAR(255) NOT NULL DEFAULT '未知用户' COMMENT '创建用户名',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_id` VARCHAR(255) NOT NULL DEFAULT '0' COMMENT '最后修改用户ID',
  `update_name` VARCHAR(255) NOT NULL DEFAULT '未知用户' COMMENT '最后修改用户名',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_code` (`code`),
  KEY `idx_external_instance_code` (`external_instance_code`)
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 COMMENT='知识审核表';

CREATE TABLE `knowledge_audit_data` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `yn` BIGINT UNSIGNED NOT NULL DEFAULT '0' COMMENT '删除标记,0:未删除,其他表示已删除',
  `code` VARCHAR(64) NOT NULL COMMENT 'code',
  `app_code` VARCHAR(64) NOT NULL COMMENT '应用Code',
  `audit_code` VARCHAR(64) NOT NULL COMMENT '审核编码',
  `data_type` VARCHAR(64) NOT NULL COMMENT '审核数据类型',
  `data_code` VARCHAR(64) NOT NULL COMMENT '审核数据编码',
  `data_title` VARCHAR(520) NOT NULL COMMENT '审核数据标题',
  `data_status` VARCHAR(64) NOT NULL COMMENT '审核数据状态',
  `data_content` text  COMMENT '审核数据内容',
  `create_id` VARCHAR(255) NOT NULL DEFAULT '0' COMMENT '创建用户ID',
  `create_name` VARCHAR(255) NOT NULL DEFAULT '未知用户' COMMENT '创建用户名',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_id` VARCHAR(255) NOT NULL DEFAULT '0' COMMENT '最后修改用户ID',
  `update_name` VARCHAR(255) NOT NULL DEFAULT '未知用户' COMMENT '最后修改用户名',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
   PRIMARY KEY (`id`),
   UNIQUE KEY `uk_code` (`code`)
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 COMMENT='知识审核内容表';
