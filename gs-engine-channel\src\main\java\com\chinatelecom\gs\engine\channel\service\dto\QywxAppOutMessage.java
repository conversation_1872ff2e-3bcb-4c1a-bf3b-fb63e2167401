package com.chinatelecom.gs.engine.channel.service.dto;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/12/20 19:45
 * @description 企微消息接口出口消息
 */
@Data
@JacksonXmlRootElement(localName = "xml")
public class QywxAppOutMessage {

    @JacksonXmlProperty(localName = "Nonce")
    private String nonce;

    @JacksonXmlProperty(localName = "timestamp")
    private String timestamp;

    @JacksonXmlProperty(localName = "MsgSignature")
    private String msgSignature;

    @JacksonXmlProperty(localName = "Encrypt")
    private String msgEncrypt;
}
