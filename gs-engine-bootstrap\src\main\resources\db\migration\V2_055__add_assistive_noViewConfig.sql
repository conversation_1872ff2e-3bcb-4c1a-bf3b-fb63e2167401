INSERT INTO `agent_default_config` (`config_key`, `config_value`, `type`, `category`, `yn`, `create_id`, `create_name`,
                                    `update_id`, `update_name`, `create_time`, `update_time`, `version`, `tenant_id`)
VALUES ('agentConfig.assistive.no.view',
        '{\"viewScope\":\"part\",\"noViewConfig\":[{\"code\":\"dialogConfig-baseDialogConfig-suggestionsAfterReply\",\"desc\":\"对话配置-基础对话配置-答复后建议\"},{\"code\":\"dialogConfig-baseDialogConfig-dialogBackGround\",\"desc\":\"对话配置-基础对话配置-对话框背景图\"},{\"code\":\"dialogConfig-baseDialogConfig-noAnswerConfig\",\"desc\":\"对话配置-基础对话配置-兜底话术配置\"},{\"code\":\"audioConfig-tts\",\"desc\":\"语音配置-语音合成\"},{\"code\":\"audioConfig-interruptConfig\",\"desc\":\"语音配置-智能打断\"},{\"code\":\"audioConfig-vadConfig\",\"desc\":\"语音配置-静音检测\"},{\"code\":\"globalPolicy-notMatchedConfig-toManuPolicyConfig\",\"desc\":\"全局策略-未匹配设置-转人工策略\"},{\"code\":\"bindPlugin\",\"desc\":\"插件\"},{\"code\":\"dialogStrategyMode\",\"desc\":\"应答策略\"},{\"code\":\"dialogConfig-triggerSetting\",\"desc\":\"对话配置-触发器设置\"}]}',
        1, 2, 0, '1', '', '1', '', NOW(), NOW(), 1, '');
