CREATE TABLE  IF NOT EXISTS `visits` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
  `yn` BIGINT UNSIGNED NOT NULL DEFAULT '0' COMMENT '删除标记,0:未删除,其他表示已删除',
  `code` VARCHAR(64) NOT NULL COMMENT 'Code',
  `name` VARCHAR(520) DEFAULT '' COMMENT '名称',
  `group` VARCHAR(520) DEFAULT '' COMMENT '分组名称',
  `source_system` VARCHAR(520) DEFAULT 'KS' COMMENT '操作来源',
  `dimension` VARCHAR(255) NOT NULL COMMENT '记录维度：USER、SYSTEM',
  `method` VARCHAR(4096) NOT NULL COMMENT '请求路径',
  `uri` VARCHAR(4096) NOT NULL COMMENT '请求路径',
  `param_key` VARCHAR(4096) DEFAULT NULL COMMENT '请求路径参数区分',
  `visits_num` BIGINT UNSIGNED NOT NULL DEFAULT '0' COMMENT '访问次数',
  `create_id` VARCHAR(255) NOT NULL DEFAULT '0' COMMENT '创建用户ID',
  `create_name` VARCHAR(255) NOT NULL DEFAULT '未知用户' COMMENT '创建用户名',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_id` VARCHAR(255) NOT NULL DEFAULT '0' COMMENT '最后修改用户ID',
  `update_name` VARCHAR(255) NOT NULL DEFAULT '未知用户' COMMENT '最后修改用户名',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_uri` (`uri`(32),`method`(8))
) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='操作访问记录表';


insert into `visits` (`code`, `group`, `name`, source_system,  `dimension`, `method`, `uri`, `param_key`) values
('101318438449840159','系统登录','系统登录','KS','SYSTEM','GET','/base/web/platform/user',NULL),
('101318438449840160','知识管理','新建文档知识库','KS','SYSTEM','POST','/kms/web/knowledge_base','FILE'),
('101318438449840161','知识管理','新建问答知识库','KS','SYSTEM','POST','/kms/web/knowledge_base','FAQ'),
('101318438449840162','知识管理','创建目录','KS','SYSTEM','POST','/kms/web/catalog',NULL),
('101318438449840163','知识管理','上传PDF文档','KS','SYSTEM','POST','/kms/web/knowledge/importByKey','PDF'),
('101318438449840164','知识管理','上传WORD文档','KS','SYSTEM','POST','/kms/web/knowledge/importByKey','WORD'),
('101318438449840165','知识管理','上传PPT文档','KS','SYSTEM','POST','/kms/web/knowledge/importByKey','PPT'),
('101318438449840166','知识管理','上传TEXT文档','KS','SYSTEM','POST','/kms/web/knowledge/importByKey','TEXT'),
('101318438449840167','知识管理','上传问答知识','KS','SYSTEM','POST','/kms/web/knowledge/importByKey','FAQ'),
('101318438449840168','知识管理','文档分片查看','KS','SYSTEM','POST','/kms/web/knowledge/chunks/page/{knowledgeCode}',NULL),
('101318438449840169','知识管理','问答对详情查看','KS','SYSTEM','GET','/kms/web/knowledge/faq/{code}',NULL),
('101318438449840170','知识管理','问答对智能推荐','KS','SYSTEM','POST','/kms/web/prompt/similar',NULL),
('101318438449840171','知识管理','AI效率-问答抽取','KS','SYSTEM','POST','/kms/web/knowledge/faq/faq-extraction/start',NULL),
('101318438449840172','知识管理','AI效率-问答抽取入库','KS','SYSTEM','POST','/kms/web/knowledge/faq/faq-extraction/put',NULL),
('101318438449840176','知识管理','小智问答','KS','SYSTEM','POST','/bot/web/dcc/sseDialog','XZ'),
('101318438449840173','知识检索','基于检索词进行搜索','KS','SYSTEM','POST','/kms/web/search/v1',NULL),
('101318438449840174','DeepSeek对话','DeepSeek对话','KS','SYSTEM','POST','/bot/web/dcc/sseDialog','DP'),
('101318438449840175','DeepSeek对话','DeepSeek对话-上传文件对话','KS','SYSTEM','POST','/bot/web/dcc/sseDialog','FILE'),
('101318438449840177','知识管理-多模态','上传视频文档（多模态）','KS','SYSTEM','POST','/kms/web/knowledge/importByKey','VIDEO'),
('101318438449840178','知识管理-多模态','上传音频文档（多模态）','KS','SYSTEM','POST','/kms/web/knowledge/importByKey','AUDIO'),
('101318438449840179','知识管理-多模态','上传图片文档（多模态）','KS','SYSTEM','POST','/kms/web/knowledge/importByKey','IMAGE'),
('101318438449840180','智能写作','智能写作-大纲生成','KS','SYSTEM','POST','/kms/web/knowledge/ai-report/outline',NULL),
('101318438449840181','智能写作','智能写作-正文生成','KS','SYSTEM','POST','/kms/web/knowledge/ai-report/content',NULL),
('101318438449840182','智能写作','智能写作-润色扩写','KS','SYSTEM','POST','/kms/web/prompt/model',NULL),
('101318438449840183','工作流编排','工作流编排-创建并发布bot','KS','SYSTEM','POST','/bot/web/publish',NULL),
('101318438449840184','工作流编排','工作流编排-创建并发布业务流','KS','SYSTEM','GET','/workflow/web/release',NULL),
('101318438449840185','工作流编排','工作流编排-创建并发布插件','KS','SYSTEM','POST','/plugin/web/plugin/publishPlugin',NULL),
('101318438449840186','知识管理','知识发布-发布文档知识','KS','SYSTEM','POST','/kms/web/knowledge/prod/publish',NULL),
('101318438449840187','知识管理','知识发布-发布问答知识','KS','SYSTEM','POST','/kms/web/knowledge/faq/prod/publish',NULL),
('101318438449840188','知识审核','审核通过','KS','SYSTEM','POST','/kms/rpc/flow/callback',NULL);
