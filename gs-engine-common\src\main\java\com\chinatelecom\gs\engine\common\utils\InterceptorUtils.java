package com.chinatelecom.gs.engine.common.utils;

import com.chinatelecom.gs.engine.common.config.property.GsGlobalConfig;
import com.chinatelecom.gs.engine.common.context.RequestInfo;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import com.chinatelelcom.gs.engine.sdk.common.constants.HeaderKeys;
import com.chinatelelcom.gs.engine.sdk.common.enums.AppSourceType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024年12月22日
 */
@Slf4j
public class InterceptorUtils {

    /**
     * 拦截器输出错误信息说明
     *
     * @param response
     * @param code
     * @param message
     * @throws IOException
     */
    public static boolean writeError(HttpServletResponse response, String code, String message) throws IOException {
        response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
        response.setHeader("X-Content-Type-Options", "nosniff");
        response.setHeader("Content-Security-Policy", "default-src 'self'");
        response.setHeader("X-Frame-Options", "DENY");
        response.setHeader("X-XSS-Protection", "1; mode=block");
        response.setContentType("application/json; charset=UTF-8");
        response.getWriter().write(JsonUtils.toJsonString(Result.failed(code, message, message)));
        return false;
    }

    public static void setAppCode(HttpServletRequest request, RequestInfo requestInfo) {
//        String headerAppCode = request.getHeader(HeaderKeys.APP_CODE);
//        if (StringUtils.isNotBlank(headerAppCode)) {
//            requestInfo.setAppCode(headerAppCode);
//        } else {
//            String reqAppCode = request.getParameter(HeaderKeys.APP_CODE);
//            if (StringUtils.isNotBlank(reqAppCode)) {
//                requestInfo.setAppCode(reqAppCode);
//            }
//        }
        if (StringUtils.isBlank(requestInfo.getTenantId())) {
            log.error("请求无法获取应用信息，requestInfo:{}", JsonUtils.toJsonString(requestInfo));
        }
        requestInfo.setAppCode(requestInfo.getTenantId());
    }

    public static void setSourceType(GsGlobalConfig gsGlobalConfig, HttpServletRequest request, RequestInfo requestInfo) {
        String referer = request.getHeader(HttpHeaders.REFERER);
        if (StringUtils.isNotBlank(referer)) {
            Map<AppSourceType, String> sourceUrlType = gsGlobalConfig.getSystem().getSourceUrlType();
            if (MapUtils.isNotEmpty(sourceUrlType)) {
                for (Map.Entry<AppSourceType, String> entry : sourceUrlType.entrySet()) {
                    AppSourceType sourceType = entry.getKey();
                    String sourceUrl = entry.getValue();
                    if (StringUtils.contains(referer, sourceUrl)) {
                        requestInfo.setAppSourceType(sourceType);
                        log.debug("设置操作来源成功，source:{}", sourceType);
                        return;
                    }
                }
            }
        }
    }

    public static void setSourceTypeByRpc(HttpServletRequest request, RequestInfo requestInfo) {
        String appSource = request.getHeader(HeaderKeys.APP_SOURCE);
        log.debug("设置操作来源，source:{}", appSource);
        if (StringUtils.isNotBlank(appSource)) {
            requestInfo.setAppSourceType(AppSourceType.valueOf(appSource));
        }
    }

    public static void setCheckRole(HttpServletRequest request, RequestInfo requestInfo) {
        String checkRoleStr = request.getHeader(HeaderKeys.CHECK_ROLE);
        Boolean checkRole = true;
        if (StringUtils.isNotBlank(checkRoleStr) && StringUtils.equalsIgnoreCase(Boolean.FALSE.toString(), checkRoleStr)) {
            checkRole = false;
        }
        requestInfo.setCheckRole(checkRole);
    }

    /**
     * 设置关联用户ID，仅在匿名场景下生效
     * @param request
     * @param requestInfo
     * @param anonymous
     */
    public static void setAssociatedUserid(HttpServletRequest request, RequestInfo requestInfo, boolean anonymous) {
        if (anonymous) {
            String associatedUserId = request.getHeader(HeaderKeys.ASSOCIATED_USER_ID);
            if (StringUtils.isNotBlank(associatedUserId)) {
                requestInfo.setAssociatedUserid(associatedUserId);
            }
        }
    }
}
