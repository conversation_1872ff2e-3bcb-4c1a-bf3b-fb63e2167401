package com.chinatelecom.gs.engine.core.corekit.domain.response;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "敏感词库管理")
@Data
public class SensitiveBaseResponse {

    /**
     * 敏感词库名称
     */
    @Schema(description = "敏感词库名称")
    private String name;

    /**
     * 敏感词库编码
     */
    @Schema(description = "敏感词库编码")
    private String code;

    /**
     * 最后修改时间
     */
    @Schema(description = "最后修改时间")
    private LocalDateTime updateTime;
}
