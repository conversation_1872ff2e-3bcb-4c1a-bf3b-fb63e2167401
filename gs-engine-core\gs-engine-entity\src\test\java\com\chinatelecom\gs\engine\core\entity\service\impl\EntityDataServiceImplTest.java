package com.chinatelecom.gs.engine.core.entity.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.context.RequestInfo;
import com.chinatelecom.gs.engine.core.entity.domain.po.EntityDataPO;
import com.chinatelecom.gs.engine.core.entity.mapper.EntityDataMapper;
import com.chinatelecom.gs.engine.robot.sdk.v2.enums.EntityTypeEnum;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.entity.EntityDataDetailVO;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.entity.EntityDetailVO;
import com.chinatelelcom.gs.engine.sdk.common.enums.AppSourceType;
import com.chinatelelcom.gs.engine.sdk.common.enums.RequestSourceType;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.ibatis.executor.BatchResult;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;

class EntityDataServiceImplTest {

    @InjectMocks
    private EntityDataServiceImpl entityDataService;

    @Mock
    private EntityDataMapper entityDataMapper;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        RequestInfo requestInfo = new RequestInfo();
        requestInfo.setUserId("userid");
        requestInfo.setUserName("username");
        requestInfo.setTenantId("ks");
        requestInfo.setAppCode("ks");
        requestInfo.setTeam(Lists.newArrayList());
        requestInfo.setTenant(false);
        requestInfo.setAppSourceType(AppSourceType.GS_ENGINE);
        requestInfo.setRequestSourceType(RequestSourceType.WEB);
        requestInfo.setIsManager(false);
        requestInfo.setIsAdmin(false);
        requestInfo.setExtraParamMap(Maps.newHashMap());
        requestInfo.setIsSuperTenant(false);
        requestInfo.setCheckRole(false);
        RequestContext.set(requestInfo);

        ReflectionTestUtils.setField(entityDataService, "entityDataMapper", entityDataMapper);
    }

    @AfterEach
    void tearDown() {
        RequestContext.remove();
    }


    @Test
    void saveEntityData_WhenEntityTypeIsNotEnumEntity_ShouldReturnTrue() {
        // Given
        EntityDetailVO entityDetailVO = new EntityDetailVO();
        entityDetailVO.setEntityType(EntityTypeEnum.NORMAL_ENTITY); // 非枚举实体类型

        // When
        Boolean result = entityDataService.saveEntityData(entityDetailVO);

        // Then
        assertTrue(result);
        verify(entityDataMapper, never()).delete(any(LambdaQueryWrapper.class));
        verify(entityDataMapper, never()).insert(anyList());
    }

    @Test
    void saveEntityData_WhenEntityDataListIsEmpty_ShouldReturnTrue() {
        // Given
        EntityDetailVO entityDetailVO = new EntityDetailVO();
        entityDetailVO.setEntityType(EntityTypeEnum.ENUM_ENTITY);
        entityDetailVO.setEntityDataList(Collections.emptyList()); // 空数据列表

        // When
        Boolean result = entityDataService.saveEntityData(entityDetailVO);

        // Then
        assertTrue(result);
        verify(entityDataMapper, never()).delete(any(LambdaQueryWrapper.class));
        verify(entityDataMapper, never()).insert(anyList());
    }

    @Test
    void saveEntityData_WhenValidEnumEntityWithNonEmptyDataList_ShouldSaveData() {
        // Given
        EntityDetailVO entityDetailVO = new EntityDetailVO();
        entityDetailVO.setEntityType(EntityTypeEnum.ENUM_ENTITY);
        entityDetailVO.setEntityCode("test_entity_code");

        EntityDataDetailVO dataDetailVO = new EntityDataDetailVO();
        dataDetailVO.setEntityValue("test_value");
        dataDetailVO.setEntitySynonym("test,synonym");

        entityDetailVO.setEntityDataList(Arrays.asList(dataDetailVO));

        // Mock mapper behavior
        when(entityDataMapper.delete(any(LambdaQueryWrapper.class))).thenReturn(1);
        // 模拟insert方法返回一个非空列表
        List<BatchResult> batchResults = new ArrayList<>();
        BatchResult batchResult = mock(BatchResult.class);
        batchResults.add(batchResult);
        when(entityDataMapper.insert(anyList())).thenReturn(batchResults);

        // When
        Boolean result = entityDataService.saveEntityData(entityDetailVO);

        // Then
        assertTrue(result);
        verify(entityDataMapper, times(1)).delete(any(LambdaQueryWrapper.class));
        verify(entityDataMapper, times(1)).insert(anyList());
    }


    @Test
    void delete_WhenGivenEntityCodes_ShouldDeleteData() {
        // Given
        List<String> entityCodes = Arrays.asList("code1", "code2");
        when(entityDataMapper.delete(any(LambdaQueryWrapper.class))).thenReturn(2);

        // When
        Boolean result = entityDataService.delete(entityCodes);

        // Then
        assertTrue(result);
        verify(entityDataMapper, times(1)).delete(any(LambdaQueryWrapper.class));
    }


    @Test
    void delete_WhenNoDataDeleted_ShouldReturnFalse() {
        // Given
        List<String> entityCodes = Arrays.asList("code1", "code2");
        when(entityDataMapper.delete(any(LambdaQueryWrapper.class))).thenReturn(0);

        // When
        Boolean result = entityDataService.delete(entityCodes);

        // Then
        assertFalse(result);
        verify(entityDataMapper, times(1)).delete(any(LambdaQueryWrapper.class));
    }

    @Test
    void getEntityDataList_WhenGivenEntityCodeAndTenantId_ShouldReturnDataList() {
        // Given
        String entityCode = "test_entity_code";
        String tenantId = "test_tenant";

        List<EntityDataPO> expectedList = new ArrayList<>();
        EntityDataPO po = new EntityDataPO();
        po.setEntityCode(entityCode);
        po.setTenantId(tenantId);
        expectedList.add(po);

        when(entityDataMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(expectedList);

        // When
        List<EntityDataPO> result = entityDataService.getEntityDataList(entityCode, tenantId);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(entityCode, result.get(0).getEntityCode());
        assertEquals(tenantId, result.get(0).getTenantId());
        verify(entityDataMapper, times(1)).selectList(any(LambdaQueryWrapper.class));
    }

    @Test
    void getEntityDataList_WhenNoDataFound_ShouldReturnEmptyList() {
        // Given
        String entityCode = "test_entity_code";
        String tenantId = "test_tenant";

        when(entityDataMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // When
        List<EntityDataPO> result = entityDataService.getEntityDataList(entityCode, tenantId);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(entityDataMapper, times(1)).selectList(any(LambdaQueryWrapper.class));
    }
}
