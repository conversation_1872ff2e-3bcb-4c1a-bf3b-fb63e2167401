package com.chinatelecom.gs.engine.core.corekit.domain.po;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinatelecom.gs.engine.common.infra.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 敏感词
 *
 * @author: Wei
 * @date: 2025-02-05 17:35
 */
@Getter
@Setter
@TableName("gs_sensitive")
public class SensitivePO extends BaseEntity implements Serializable {

    /**
     * 应用code
     */
    @TableField(value = "app_code", fill = FieldFill.INSERT)
    private String appCode;

    /**
     * 敏感词库编码
     */
    @TableField(value = "sensitive_base_code", fill = FieldFill.INSERT)
    private String sensitiveBaseCode;

    /**
     * 标准词
     */
    private String standardWords;

    /**
     * 同义词（使用;分割）
     */
    private String synonymousWords;

}
