package com.chinatelecom.gs.engine.core.model.toolkit.adapter.ernie;

import com.chinatelecom.gs.engine.core.sdk.vo.llm.BaseLLMRequest;
import com.chinatelecom.gs.engine.core.sdk.vo.llm.LLMMessage;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description A
 * @date Create by 2023/11/20 19:24
 */

@Data
public class ErnieRequest implements BaseLLMRequest {

    /**
     * 随机性
     */
    private double temperature = 0.95;

    /**
     * 多样性
     */
    private double top_p = 0.8;

    /**
     * 对已生成token的惩罚值
     */
    private int penalty_score = 1;

    /**
     * 模型人设
     */
    private String system;

    /**
     * 是否流式
     */
    private boolean stream = false;

    private List<LLMMessage> messages;

    /**
     * 获取输入内容
     *
     * @return
     */
    @Override
    public String inputContent() {
        StringBuilder inputBuilder = new StringBuilder();
        inputBuilder.append(system);
        if(CollectionUtils.isNotEmpty(messages)){
            for(LLMMessage message : messages){
                inputBuilder.append(message.getContent());
            }
        }
        return inputBuilder.toString();
    }
}
