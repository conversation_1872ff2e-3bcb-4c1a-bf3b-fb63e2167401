package com.chinatelecom.gs.engine.channel.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/22 14:06
 * @description
 */
@Getter
public enum ChannelTypeEnum {
    WEB_LINK("web_link", "网页链接"),
    API("api", "API调用"),
    QYWX_APP("qywx_app", "企业微信"),
    TEST_WINDOW("test_window", "测试窗"),
    DIALOG_WINDOW("dialog_window", "聊天窗-我的智能体"),
    COMMON_DIALOG_WINDOW("common_dialog_window", "公共聊天窗口-为我推荐");

    @JsonValue
    @EnumValue
    private final String code;

    private final String desc;

    public static final List<String> OFF_SITE_CHANNEL = Arrays.asList(
            WEB_LINK.getCode(),
            API.getCode(),
            QYWX_APP.getCode()
    );

    ChannelTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }



}
