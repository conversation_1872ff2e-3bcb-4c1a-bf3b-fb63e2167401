package com.chinatelecom.gs.engine.channel.service.dto;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/12/22 14:41
 * @description 企业微信应用程序推送过来的消息
 */
@Data
@JacksonXmlRootElement(localName = "xml")
public class QywxInMessage {
    @JacksonXmlProperty(localName = "ToUserName")
    private String toUserName;

    @JacksonXmlProperty(localName = "AgentID")
    private String agentId;

    @JacksonXmlProperty(localName = "Encrypt")
    private String msgEncrypt;
}
