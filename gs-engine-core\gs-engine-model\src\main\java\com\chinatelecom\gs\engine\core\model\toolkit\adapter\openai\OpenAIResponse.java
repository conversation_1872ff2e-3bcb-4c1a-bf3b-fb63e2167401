package com.chinatelecom.gs.engine.core.model.toolkit.adapter.openai;

import com.chinatelecom.gs.engine.core.sdk.vo.llm.BaseLLMResponse;
import com.chinatelecom.gs.engine.core.sdk.vo.llm.LLMMessage;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Objects;

@Data
public class OpenAIResponse implements BaseLLMResponse {
    private String id;
    private String object;
    private int created;
    private String model;
    private List<Choices> choices;
    private Usage usage;


    @Data
    public static class Choices {
        private int index;
        private LLMMessage message;
        private LLMMessage delta;
        private String finish_reason;
    }


    @Data
    public static class Usage {
        private int prompt_tokens;
        private int completion_tokens;
        private int total_tokens;
    }

    /**
     * 获取输出内容
     *
     * @return
     */
    @Override
    public String outputContent() {
        StringBuilder outputBuilder = new StringBuilder();
        if(CollectionUtils.isNotEmpty(choices)){
            for(Choices choice:choices){
                if(Objects.nonNull(choice.getMessage())){
                    outputBuilder.append(choice.getMessage().getContent());
                }
                if(Objects.nonNull(choice.getDelta())){
                    outputBuilder.append(choice.getDelta().getContent());
                }
            }
        }
        return outputBuilder.toString();
    }

    /**
     * 获取输入token数
     *
     * @return
     */
    @Override
    public Integer promptTokens() {
        if(this.usage != null){
            return this.usage.prompt_tokens;
        }
        return null;
    }

    /**
     * 获取输出token数
     *
     * @return
     */
    @Override
    public Integer completionTokens() {
        if(this.usage != null){
            return this.usage.completion_tokens;
        }
        return null;
    }
}
