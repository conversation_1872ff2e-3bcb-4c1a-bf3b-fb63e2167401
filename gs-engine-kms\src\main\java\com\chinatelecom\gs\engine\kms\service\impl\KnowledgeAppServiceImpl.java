package com.chinatelecom.gs.engine.kms.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.chinatelecom.gs.engine.common.config.aspect.annotation.Lock;
import com.chinatelecom.gs.engine.common.config.aspect.annotation.VisitsLog;
import com.chinatelecom.gs.engine.common.config.property.GsGlobalConfig;
import com.chinatelecom.gs.engine.common.enums.DimensionEnum;
import com.chinatelecom.gs.engine.common.s3.CloudStorageDao;
import com.chinatelecom.gs.engine.common.s3.FileMetadataRes;
import com.chinatelecom.gs.engine.common.utils.*;
import com.chinatelecom.gs.engine.core.manager.convert.ConfigConvert;
import com.chinatelecom.gs.engine.core.manager.service.BaseConfigAppService;
import com.chinatelecom.gs.engine.core.manager.vo.config.InternalFunctionConfig;
import com.chinatelecom.gs.engine.core.model.enums.ModelTypeEnum;
import com.chinatelecom.gs.engine.core.sdk.vo.ModelPageListParam;
import com.chinatelecom.gs.engine.kms.base.model.CodeParam;
import com.chinatelecom.gs.engine.kms.base.service.impl.BaseAppServiceByCodeImpl;
import com.chinatelecom.gs.engine.kms.convert.vo.KnowledgeVoConverter;
import com.chinatelecom.gs.engine.kms.dto.KnowledgeBaseDTO;
import com.chinatelecom.gs.engine.kms.dto.knowledge.KnowledgeImportByKeyDTO;
import com.chinatelecom.gs.engine.kms.dto.knowledge.KnowledgeParseResult;
import com.chinatelecom.gs.engine.kms.dto.knowledge.KnowledgeSplitDTO;
import com.chinatelecom.gs.engine.kms.enums.CatalogType;
import com.chinatelecom.gs.engine.kms.flow.parse.ParseFlowProcess;
import com.chinatelecom.gs.engine.kms.flow.parse.component.context.KnowledgeParseConfigContext;
import com.chinatelecom.gs.engine.kms.flow.parse.component.context.KnowledgeParseContext;
import com.chinatelecom.gs.engine.kms.infra.po.KnowledgePO;
import com.chinatelecom.gs.engine.kms.model.dto.CatalogDTO;
import com.chinatelecom.gs.engine.kms.model.dto.KnowledgeDTO;
import com.chinatelecom.gs.engine.kms.repository.*;
import com.chinatelecom.gs.engine.kms.sdk.enums.*;
import com.chinatelecom.gs.engine.kms.sdk.vo.catalog.CatalogCreateParam;
import com.chinatelecom.gs.engine.kms.sdk.vo.catalog.CatalogVO;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.*;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.base.KnowledgeBaseParam;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.base.KnowledgeParseConfig;
import com.chinatelecom.gs.engine.kms.search.SearchService;
import com.chinatelecom.gs.engine.kms.service.*;
import com.chinatelecom.gs.engine.kms.util.*;
import com.chinatelecom.gs.engine.kms.util.safe.ExcelDDECheckUtils;
import com.chinatelecom.gs.privilege.common.enums.PrivilegeEnum;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import com.chinatelelcom.gs.engine.sdk.common.utils.SpringContextUtils;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.InputStream;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.chinatelecom.gs.engine.common.constants.Constants.EMPTY_NODE;
import static com.chinatelecom.gs.engine.kms.search.IndexBuild.TAG;
import static com.chinatelecom.gs.engine.kms.search.IndexBuild.TITLE;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024年12月04日
 */
@Service
@Slf4j
public class KnowledgeAppServiceImpl extends BaseAppServiceByCodeImpl<KnowledgeRepository, KnowledgeQueryParam, KnowledgeVoConverter, KnowledgePO, KnowledgeDTO, KnowledgeVO, KnowledgeCreateParam, KnowledgeUpdateParam> implements KnowledgeAppService {

    @Resource
    private GsGlobalConfig gsGlobalConfig;

    @Resource
    private TransactionCommitHandler transactionCommitHandler;

    @Resource
    private ParseFlowProcess parseFlowProcess;

    @Resource
    private KnowledgeRepository knowledgeRepository;

    @Resource
    private CatalogService catalogService;

    @Resource
    private KnowledgeBaseRepository knowledgeBaseRepository;

    @Resource
    private SearchService searchService;

    @Resource
    private KnowledgeFaqAppService knowledgeFaqAppService;

    @Resource
    private KnowledgeFaqRepository knowledgeFaqRepository;

    @Resource
    private FileAppService fileAppService;

    @Resource
    private AttachmentAppService attachmentAppService;

    @Resource
    private TagAppRepository tagAppRepository;
    @Resource
    private TagRelationRepository tagRelationRepository;

    @Resource
    private CollectionAppService collectionAppService;

    @Resource
    private KnowledgeExtraInfoRepository knowledgeExtraInfoRepository;

    @Resource
    private CatalogRepository catalogRepository;

    @Resource
    private KnowledgeExtractRepository knowledgeExtractRepository;

    @Resource
    private KnowledgeEssentialRepository knowledgeEssentialRepository;

    @Resource
    private ModelAppService modelAppService;

    @Resource
    private KnowledgeProdRepository knowledgeProdRepository;

    @Resource
    private KnowledgeExtraInfoAppService knowledgeExtraInfoAppService;
    @Resource
    @Lazy
    private KnowledgeBaseService knowledgeBaseService;

    @Resource
    private BaseConfigAppService baseConfigAppService;

    @Resource
    private CloudStorageDao cloudStorageDao;

    @Resource
    @Lazy
    private SplitService splitService;

    @Resource
    @Qualifier("splitPoolExecutor")
    private ExecutorService splitPoolExecutor;

    protected KnowledgeVoConverter converter() {
        return KnowledgeVoConverter.INSTANCE;
    }

    @Override
    public KnowledgeVO get(String code) {
        KnowledgeDTO knowledgeDTO = repository.selectByCode(code);
        BizAssert.notNull(knowledgeDTO, "AA012", "未查询到知识：{}", code);
        PrivilegeEnum hasPrivilege = knowledgeBaseService.checkKnowledgeBaseAuth(knowledgeDTO.getKnowledgeBaseCode(), PrivilegeEnum.view);

        KnowledgeVO result = toVO(knowledgeDTO);
        result.setPermission(PrivilegeEnumUtils.convertPermission(hasPrivilege));
        return result;
    }

    @Override
    public List<KnowledgeVO> list(List<String> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return Collections.emptyList();
        }
        List<KnowledgeDTO> knowledgeDTOList = repository.findByCodes(codes);
        return toVO(knowledgeDTOList);
    }


    private KnowledgeVO toVO(KnowledgeDTO knowledgeDTO) {
        return toVO(Collections.singletonList(knowledgeDTO)).get(0);
    }

    private List<KnowledgeVO> toVO(List<KnowledgeDTO> knowledgeDTOList) {
        if (CollectionUtils.isEmpty(knowledgeDTOList)) {
            return Collections.emptyList();
        }
        List<String> codes = knowledgeDTOList.stream().map(KnowledgeDTO::getCode).collect(Collectors.toList());

        List<CatalogDTO> catalogDTOS = catalogRepository.findByKnowledgeCodes(codes);
        Map<String, CatalogDTO> collectionDTOMap = catalogDTOS.stream().filter(Objects::nonNull).collect(Collectors.toMap(CatalogDTO::getKnowledgeCode, Function.identity()));

        List<KnowledgeVO> collect = knowledgeDTOList.stream().map(knowledgeDTO -> {
            KnowledgeVO vo = converter().convertVO(knowledgeDTO);
            vo.setUrl(BaseFileUrlUtils.getBaseDownloadUrl(knowledgeDTO.getOriginalFileKey()));
            vo.setViewUrl(BaseFileUrlUtils.getBaseDownloadUrl(knowledgeDTO.getViewFileKey()));
            vo.setCoverFileUrl(BaseFileUrlUtils.getBaseDownloadUrl(knowledgeDTO.getCoverFileKey()));

            CatalogDTO catalogDTO = collectionDTOMap.get(knowledgeDTO.getCode());
            if (catalogDTO != null) {
                vo.setCatalogCode(catalogDTO.getCode());
            }
            return vo;
        }).collect(Collectors.toList());
        collectionAppService.fetchCollectionVO(TargetType.Knowledge, collect);
        tagRelationRepository.fetchTag(TargetType.Knowledge, collect);
        return collect;
    }

    @Transactional(rollbackFor = Exception.class)
    @VisitsLog(paramKey = "#returnValue.type")
    @Override
    public KnowledgeVO importKnowledge(KnowledgeImportByKeyDTO param) {
        Long startTime = System.currentTimeMillis();
        // 检查知识库是否存在
        KnowledgeBaseDTO knowledgeBaseDTO = knowledgeBaseRepository.selectByCode(param.getKnowledgeBaseCode());
        BizAssert.notNull(knowledgeBaseDTO, "AA011", "未查询到知识库：{}", param.getKnowledgeBaseCode());

        // 业务检查，设置必要参数
        KnowledgeDTO knowledgeDTO = checkAndBuild(param, knowledgeBaseDTO);

        // 创建目录层级的数据

        // 创建目录数据
        CatalogCreateParam createParam = buildCatalogData(param, knowledgeDTO);
        catalogService.create(createParam);
        // 存储数据库
        knowledgeRepository.save(knowledgeDTO);

        // 提交解析任务
        KnowledgeParseContext parseInfo = buildKnowledgeParseInfo(knowledgeDTO, knowledgeBaseDTO, param.getConfig());
        parseInfo.getParseTrace().setUploadTime(startTime);
        transactionCommitHandler.handle(() -> parseFlowProcess.executeParse(parseInfo));

        return converter().convertVO(knowledgeDTO);
    }

    @Override
    public void reparse(KnowledgeReparseParam param) {
        String knowledgeCode = param.getKnowledgeCode();
        Long startTime = System.currentTimeMillis();
        KnowledgeDTO knowledgeDTO = knowledgeRepository.selectByCode(knowledgeCode);
        BizAssert.notNull(knowledgeDTO, "AA012", "未查询到知识：{}", param.getKnowledgeCode());
        knowledgeBaseService.checkKnowledgeBaseAuth(knowledgeDTO.getKnowledgeBaseCode(), PrivilegeEnum.edit);
        KnowledgePublishCheckUtils.updatePublishStatusCheck(knowledgeDTO.getPublishStatus());

        KnowledgeBaseDTO knowledgeBaseDTO = knowledgeBaseRepository.selectByCode(knowledgeDTO.getKnowledgeBaseCode());
        BizAssert.notNull(knowledgeBaseDTO, "AA011", "未查询到知识库：{}", knowledgeDTO.getKnowledgeBaseCode());

        // 提交解析任务
        KnowledgeParseContext parseInfo = buildKnowledgeParseInfo(knowledgeDTO, knowledgeBaseDTO, param.getConfig());
        KnowledgeParseConfigContext knowledgeParseConfig = parseInfo.getKnowledgeParseConfig();
        parseInfo.getParseTrace().setUploadTime(startTime);
        if (knowledgeParseConfig != null) {
            knowledgeParseConfig.setReExecute(true);
        }
        KnowledgeParseResult knowledgeParseResult = KnowledgeParseResult.builder()
                .status(KnowledgeStatus.pending).indexingStatus(KnowledgeStatus.pending).imageIndexingStatus(KnowledgeStatus.pending).build();
        knowledgeRepository.updateKnowParseResult(knowledgeCode, knowledgeParseResult);

        this.updatePublishStatus2Update(ImmutableList.of(knowledgeCode));
        parseFlowProcess.executeParse(parseInfo);
    }

    private KnowledgeParseContext buildKnowledgeParseInfo(KnowledgeDTO knowledgeDTO, KnowledgeBaseDTO knowledgeBaseDTO, KnowledgeParseConfig config) {
        KnowledgeParseContext parse = new KnowledgeParseContext();
        KnowledgeParseConfig parseConfig = JsonUtils.parseObject(gsGlobalConfig.getKmsBaseConfig().getDefaultConfig(), KnowledgeParseConfig.class);
        if (parseConfig == null) {
            parseConfig = new KnowledgeParseConfig();
            parseConfig.setChunkConfig(new KnowledgeParseConfig.ChunkConfig());
            parseConfig.setParserConfig(new KnowledgeParseConfig.ParserConfig());
        }
        // 获取知识库的配置
        // 取知识库的配置
        KnowledgeBaseParam knowledgeBaseParam = knowledgeBaseDTO.getConfig();
        if (knowledgeBaseParam != null) {
            // 使用知识库其他配置项
            KnowledgeBaseParam.ParserConfig parserConfig = knowledgeBaseParam.getParserConfig();
            if (parserConfig != null) {
                BeanUtils.copyProperties(parserConfig, parseConfig.getParserConfig(), Utils.getNullPropertyNames(parserConfig));
            }
        }
        if (config != null) {
            BeanUtils.copyProperties(config, parseConfig, Utils.getNullPropertyNames(config));
        }

        KnowledgeParseConfigContext parseContext = new KnowledgeParseConfigContext();
        parseContext.setKnowledgeBaseConfig(parseConfig);
        parse.setKnowledgeParseConfig(parseContext);
        parse.setKnowledgeDTO(knowledgeDTO);
        return parse;
    }

    private KnowledgeParseConfig.ModelUrlList buildModelUrl(Map<String, KnowledgeBaseParam.ModelConfig> modelConfigMap) {
        if (modelConfigMap == null) {
            return null;
        }

        KnowledgeParseConfig.ModelUrlList modelUrlList = new KnowledgeParseConfig.ModelUrlList();
        for (Map.Entry<String, KnowledgeBaseParam.ModelConfig> entry : modelConfigMap.entrySet()) {
            try {
                ModelTypeEnum modelTypeEnum = ModelTypeEnum.valueOf(entry.getKey());
                KnowledgeBaseParam.ModelConfig modelConfigInfo = entry.getValue();
                if (BooleanUtils.isTrue(modelConfigInfo.getModelFlag())) {
                    ModelPageListParam modelPageListParam = modelAppService.queryByModelCode(modelConfigInfo.getModelCode());
                    if (modelPageListParam != null) {
                        setModelUrl(modelTypeEnum, modelUrlList, modelPageListParam);
                    }
                }
            } catch (IllegalArgumentException e) {
                log.warn("非法模型类型，处理忽略，modelType:{}", entry.getKey());
            }
        }
        return modelUrlList;
    }

    private void setModelUrl(ModelTypeEnum modelTypeEnum, KnowledgeParseConfig.ModelUrlList modelUrlList, ModelPageListParam modelPageListParam) {
        switch (modelTypeEnum) {
            case OFFLINE_LLM:
                modelUrlList.setLlmUrl(modelPageListParam.getExternalModelUrl());
                break;
            case ASR:
                modelUrlList.setAsrUrl(modelPageListParam.getExternalModelUrl());
                break;
            case OCR:
                modelUrlList.setOcrUrl(modelPageListParam.getExternalModelUrl());
                break;
            case LAYOUT:
                modelUrlList.setLayoutUrl(modelPageListParam.getExternalModelUrl());
                break;
            case VLLM:
                modelUrlList.setVllmModelUrl(modelPageListParam.getExternalModelUrl());
                break;
            case RERANK:
            case ENTITY:
            case SETR:
            case CLASSIFIER:
            case EMBEDDING:
            default:
        }
    }

    /**
     * 导入基础业务检查
     *
     * @param param
     * @param knowledgeBaseDTO
     */
    private KnowledgeDTO checkAndBuild(KnowledgeImportByKeyDTO param, KnowledgeBaseDTO knowledgeBaseDTO) {
        String fileName = param.getFileName();

        // todo 知识总数限制检查

        String knowledgeCode = IdGenerator.id();

        // 根据知识库类型检查是否支持该文件
        KnowledgeBaseType baseType = knowledgeBaseDTO.getType();
        KnowledgeType type = checkBaseType(baseType, param);
        String uploadFileKey = param.getFileKey();

        // 获取文件大小
        FileMetadataRes fileMetadataRes = null;
        try {
            fileMetadataRes = cloudStorageDao.queryMetadata(uploadFileKey);
        } catch (Exception e) {
            log.warn("文件读取失败，fileKey:{}", uploadFileKey, e);
            BizAssert.throwBizException("AA122", "读取文件信息失败，文件可能已被清理");
        }

        // 检查xlsx文件是否存在安全风险
        boolean isExcel = (type == KnowledgeType.FAQ) || (type == KnowledgeType.EXCEL);
        Integer checkExcelSize = gsGlobalConfig.getKmsBizConfig().getCheckExcelSize();
        if (isExcel && gsGlobalConfig.getKmsBizConfig().isCheckExcelSafe() && fileMetadataRes.fileLength2MB() <= checkExcelSize) {
            Boolean isXlsx = null;
            if (fileName.endsWith(".xlsx")) {
                isXlsx = true;
            } else if (fileName.endsWith(".xls")) {
                isXlsx = false;
            }
            if (isXlsx != null) {
                InputStream inputStream = cloudStorageDao.download(uploadFileKey);
                boolean danger = ExcelDDECheckUtils.hasDDEInjectionRisk(inputStream, isXlsx);
                if (danger) {
                    cloudStorageDao.remove(uploadFileKey);
                    BizAssert.throwBizException("AA120", "导入的excel文件存在安全风险，文件中禁止包含公式或脚本");
                }
            }
        }


        //获取开关配置
        InternalFunctionConfig config = baseConfigAppService.getConfigOrDefault(ConfigConvert.INTERNAL_CONFIG, DimensionEnum.SYSTEM.name());
        List<KnowledgeType> supportKnowledgeType = config.getSupportKnowledgeType();
        if (supportKnowledgeType == null || !supportKnowledgeType.contains(type)) {
            BizAssert.throwBizException("AA080", "系统配置不支持该类型文档");
        }

        // 检查父级目录是否存在
        String parentCode = StringUtils.defaultIfBlank(param.getParentCode(), EMPTY_NODE);
        if (!StringUtils.equals(parentCode, EMPTY_NODE)) {
            CatalogVO catalogVO = catalogService.get(parentCode);
            BizAssert.notNull(catalogVO, "AA014", "父级目录不存在：{}", parentCode);
        }

        if (fileName.length() > gsGlobalConfig.getKmsBizConfig().getFileNameMaxLength()) {
            BizAssert.throwBizException("AA002", "文件名称超过长度：{}", gsGlobalConfig.getKmsBizConfig().getFileNameMaxLength());
        }

        // copy文件
        String fileKey = FileKeyUtils.getKnowledgeFileKey(uploadFileKey, param.getKnowledgeBaseCode(), knowledgeCode);
        fileAppService.moveUpload2Attachment(param.getFileName(), knowledgeCode, uploadFileKey, fileKey);
        param.setFileKey(fileKey);

        return buildKnowledgeDTO(param, knowledgeCode, fileName, type, fileMetadataRes.getFileLength());
    }

    private KnowledgeType checkBaseType(KnowledgeBaseType baseType, KnowledgeImportByKeyDTO param) {
        if (KnowledgeBaseType.FAQ == baseType) {
            if (!StringUtils.equals(FileNameUtils.getFileSuffixWithPoint(param.getFileKey()), ".xlsx")) {
                BizAssert.throwBizException("AA003", "问答知识库仅支持.xlsx文件，文件名：{}", param.getFileKey());
            }
            return KnowledgeType.FAQ;
        } else if (KnowledgeBaseType.FILE == baseType) {
            KnowledgeType knowledgeType = KnowledgeType.fromFileName(param.getFileKey());
            if (knowledgeType == null) {
                BizAssert.throwBizException("AA003", "不支持类型文件，文件名：{}", param.getFileKey());
            }
            return knowledgeType;
        } else {
            throw new BizException("AA003", "不支持的知识库类型");
        }
    }


    private KnowledgeDTO buildKnowledgeDTO(KnowledgeImportByKeyDTO param, String knowledgeCode, String fileName, KnowledgeType type, long fileLength) {
        KnowledgeDTO knowledgeDTO = new KnowledgeDTO();
        knowledgeDTO.setCode(knowledgeCode);
        knowledgeDTO.setKnowledgeBaseCode(param.getKnowledgeBaseCode());
        knowledgeDTO.setType(type);
        knowledgeDTO.setName(fileName);
        knowledgeDTO.setOn(true);
        knowledgeDTO.setStatus(KnowledgeStatus.pending);
        knowledgeDTO.setIndexingStatus(KnowledgeStatus.pending);
        knowledgeDTO.setImageIndexingStatus(KnowledgeStatus.pending);
        knowledgeDTO.setViewCount(0L);
        String fileKey = param.getFileKey();
        knowledgeDTO.setOriginalFileKey(fileKey);
        knowledgeDTO.setFileLength(fileLength);
        return knowledgeDTO;
    }

    private CatalogCreateParam buildCatalogData(KnowledgeImportByKeyDTO param, KnowledgeDTO knowledgeDTO) {
        CatalogCreateParam createParam = new CatalogCreateParam();
        createParam.setName(param.getFileName());
        createParam.setKnowledgeBaseCode(param.getKnowledgeBaseCode());
        createParam.setParentCode(param.getParentCode());
        createParam.setPrevCode(param.getPrevCode());
        createParam.setType(CatalogType.KNOWLEDGE.getValue());
        createParam.setKnowledgeCode(knowledgeDTO.getCode());
        return createParam;
    }


    @Override
    public void switchOn(String code, boolean on) {
        KnowledgeDTO knowledgeDTO = repository.selectByCode(code);
        SpringContextUtils.getBean(this.getClass()).switchOn(knowledgeDTO, on);
    }


    @Lock(key = "'knowledge_status_scan'", leaseTime = 60, waitTime = 0, message = "扫描任务正在执行中...")
    @Override
    public void scanKnowledgeParseTimeout() {
        long parseReadTimeout = gsGlobalConfig.getKmsRpcConfig().getParseReadTimeout();
        long parseTaskMultiple = gsGlobalConfig.getKmsRpcConfig().getParseTaskMultiple();
        // 获取当前超时时间之前的时间点， 避免长时间排队的被修改，这里减去n倍超时时间
        LocalDateTime localDateTime = LocalDateTime.now().minus(Duration.ofMillis(parseReadTimeout * parseTaskMultiple));
        repository.updateKnowledgeStatusTimeout(localDateTime);
    }

    @Lock(key = "'knowledge_status_submit'", leaseTime = 60, waitTime = 0, message = "提交任务正在执行中...")
    @Override
    public void submitPendingParse() {
        long parseReadTimeout = gsGlobalConfig.getKmsRpcConfig().getParseReadTimeout();
        long parseTaskMultiple = gsGlobalConfig.getKmsRpcConfig().getParseTaskMultiple();
        // 获取当前超时时间之前的时间点， 避免长时间排队的被修改，这里减去n倍超时时间
        LocalDateTime localDateTime = LocalDateTime.now().minus(Duration.ofMillis(parseReadTimeout * parseTaskMultiple));
        List<KnowledgeDTO> knowledgeDTOList = repository.queryParsePending(localDateTime);

        //  后期可以提交延迟队列，多次查询解析结果
        if (CollectionUtils.isNotEmpty(knowledgeDTOList)) {
            for (KnowledgeDTO knowledgeDTO : knowledgeDTOList) {
                try {
                    SwitchContextUtils.switchContext(knowledgeDTO, () -> {
                        submitParseTask(knowledgeDTO);
                        return null;
                    });
                } catch (Exception e) {
                    log.warn("重新解析任务提交失败，错误将忽略，knowledgeCode:{}", knowledgeDTO.getCode(), e);
                }
            }
        }
    }

    /**
     * 提交解析任务
     * @param knowledgeDTO
     */
    private void submitParseTask(KnowledgeDTO knowledgeDTO) {
        KnowledgeParseConfig config = getKnowledgeParseConfig(knowledgeDTO.getCode());
        KnowledgeBaseDTO knowledgeBaseDTO = knowledgeBaseRepository.selectByCode(knowledgeDTO.getKnowledgeBaseCode());
        BizAssert.notNull(knowledgeBaseDTO, "AA011", "未查询到知识库：{}", knowledgeDTO.getKnowledgeBaseCode());
        KnowledgeParseContext parseInfo = buildKnowledgeParseInfo(knowledgeDTO, knowledgeBaseDTO, config);
        KnowledgeParseConfigContext knowledgeParseConfig = parseInfo.getKnowledgeParseConfig();
        if (knowledgeParseConfig != null) {
             // 为避免是用户手动触发重新执行导致任务在解析中，这里必须走重新解析逻辑
            knowledgeParseConfig.setReExecute(true);
        }

        // 重新提交解析任务
        parseFlowProcess.executeParse(parseInfo);
        // 解析中的仅查询一次失败率较高，需要循环处理，这里先简单处理直接重新提交解析任务
//        if (StringUtils.isBlank(knowledgeDTO.getViewFileKey()) || StringUtils.isBlank(knowledgeDTO.getTaskId())) {
//            parseFlowProcess.executeParse(parseInfo);
//        } else {
//            // 仅执行切分任务
//            KnowledgeSplitDTO splitParam = buildParseParam(knowledgeDTO, knowledgeParseConfig);
//            if (splitParam != null) {
//                splitPoolExecutor.submit(() -> splitService.submitPendingParse(splitParam));
//            }
//        }
    }

    /**
     * 构造解析入参
     * @param knowledgeDTO
     * @return
     */
    private KnowledgeSplitDTO buildParseParam(KnowledgeDTO knowledgeDTO, KnowledgeParseConfigContext config) {
        KnowledgeSplitDTO param = new KnowledgeSplitDTO();
        SplitIndexType splitIndexType = null;
        if (KnowledgeStatus.pending == knowledgeDTO.getIndexingStatus()) {
            splitIndexType = SplitIndexType.TEXT;
        } else if (KnowledgeStatus.pending == knowledgeDTO.getImageIndexingStatus()) {
            splitIndexType = SplitIndexType.IMAGE;
        } else {
            return null;
        }

        param.setSplitIndexType(splitIndexType);
        param.setKnowledgeCode(knowledgeDTO.getCode());

        // 获取解析配置
        param.setKnowledgeParseConfig(config);
        return param;
    }

    private KnowledgeParseConfig getKnowledgeParseConfig(String knowledgeCode) {
        String content = knowledgeExtraInfoAppService.getContentByType(knowledgeCode, KnowledgeInfoType.PARSE_CONFIG);
        KnowledgeParseConfig config = null;
        if (StringUtils.isNotBlank(content)) {
            config = JsonUtils.parseObject(content, KnowledgeParseConfig.class);
        }
        return config;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void switchOn(KnowledgeDTO knowledgeDTO, boolean on) {
        if (knowledgeDTO == null) {
            return;
        }
        knowledgeBaseService.checkKnowledgeBaseAuth(knowledgeDTO.getKnowledgeBaseCode(), PrivilegeEnum.edit);

        KnowledgePublishCheckUtils.updatePublishStatusCheck(knowledgeDTO.getPublishStatus());

        repository.updateOn(ImmutableList.of(knowledgeDTO.getCode()), on);
        this.updatePublishStatus2Update(ImmutableList.of(knowledgeDTO.getCode()));

        if (knowledgeDTO.getType() == KnowledgeType.FAQ) {
            // 更新所有FAQ的开关
            knowledgeFaqRepository.switchOnByKnowledgeCode(ImmutableList.of(knowledgeDTO.getCode()), on);
            knowledgeFaqAppService.updatePublishStatus2Unpublished(ImmutableList.of(knowledgeDTO.getCode()), null);
        }
        searchService.knowledgeSwitchOn(knowledgeDTO.getCode(), on);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void publishDeleteFlag(String knowledgeCode) {
        KnowledgeDTO knowledgeDTO = repository.selectByCode(knowledgeCode);
        BizAssert.notNull(knowledgeDTO, "AA012", "知识不存在");
        knowledgeBaseService.checkKnowledgeBaseAuth(knowledgeDTO.getKnowledgeBaseCode(), PrivilegeEnum.edit);
        if (knowledgeDTO.getStatus() == KnowledgeStatus.success && knowledgeDTO.getPublishStatus() != PublishStatus.UNPUBLISHED) {
            BizAssert.assertFalse(PublishStatus.NOT_ALLOW_DELETE.contains(knowledgeDTO.getPublishStatus()),
                    "AA029", "当前发布状态不允许删除：{}", knowledgeDTO.getPublishStatus());

            repository.updatePublishStatus(ImmutableList.of(knowledgeCode), PublishStatus.DELETE_UNPUBLISH);
            if (knowledgeDTO.getType() == KnowledgeType.FAQ) {
                // 删除所有问答对
                knowledgeFaqRepository.updatePublishStatus(ImmutableList.of(knowledgeCode), PublishStatus.DELETE_UNPUBLISH, ImmutableList.of());
            }
            searchService.updatePublishStatusDel(knowledgeCode, null);
        } else {
            CodeParam param = new CodeParam();
            param.setCodes(ImmutableList.of(knowledgeCode));
            searchService.deleteKnowledge(knowledgeCode);
            SpringContextUtils.getBean(this.getClass()).delete(param);
        }
    }

    @Transactional
    @Override
    public Boolean batchTagging(BatchTagParam batchTagParam) {
        // 处理标签列表，将逗号分隔的标签拆分为单个标签
        Set<String> processedTagCodes = processTagCodes(batchTagParam.getTagCodes());
        List<String> inputTagCodes = new ArrayList<>(processedTagCodes);
        List<TargetItem> targetItems = batchTagParam.getTargetItems();

        if (CollectionUtils.isEmpty(targetItems) || CollectionUtils.isEmpty(inputTagCodes)) {
            return false;
        }

        // 获取知识类型的目标项
        List<String> knowledgeCodes = targetItems.stream()
                .filter(item -> TargetType.Knowledge.equals(item.getType()))
                .map(TargetItem::getCode)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(knowledgeCodes)) {
            return false;
        }
        this.updatePublishStatus2Update(knowledgeCodes);
        for (String code : knowledgeCodes) {
            // 获取当前知识的标签
            Set<String> existingTags = tagRelationRepository.selectTagCodeByTarget(TargetType.Knowledge, code);
            Set<String> newTags = new HashSet<>(existingTags);
            newTags.addAll(inputTagCodes); // 增量更新

            // 比较新旧标签，只有标签发生变化才更新
            if (!CollUtil.isEqualList(existingTags, newTags)) {
                // 更新知识标签
                tagRelationRepository.saveTagRelation(TargetType.Knowledge, code, newTags);

                // 更新搜索索引 - 使用原始标签code列表
                Map<String, Object> update = ImmutableMap.of(TAG, new ArrayList<>(newTags));
                searchService.updateBaseInfoByKnowledgeCode(code, update);
            }
        }
        return true;
    }

    /**
     * 实际删除操作
     *
     * @param codes
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean delete(CodeParam codes) {
        List<KnowledgeDTO> knowledgeDTOList = repository.findByCodes(codes.getCodes());
        BizAssert.notEmpty(knowledgeDTOList, "AA012", "未查询到知识");
        Set<String> knowledgeBase = knowledgeDTOList.stream().map(KnowledgeDTO::getKnowledgeBaseCode).collect(Collectors.toSet());
        knowledgeBase.stream().forEach(knowledgeBaseCode -> {
            knowledgeBaseService.checkKnowledgeBaseAuth(knowledgeBaseCode, PrivilegeEnum.edit);
        });
        Map<String, KnowledgeDTO> knowDTOMap = knowledgeDTOList.stream().collect(Collectors.toMap(KnowledgeDTO::getCode, k -> k));

        // 相关数据删除
        catalogService.deleteByKnowledgeCode(codes.getCodes());
        knowledgeFaqRepository.deleteByKnowledgeCode(codes.getCodes());

        // 文件附件的删除
        for (String knowledgeCode : codes.getCodes()) {
            attachmentAppService.deleteAttachment(AttachableType.Knowledge, knowledgeCode);
        }
        // 扩展信息删除
        knowledgeExtraInfoRepository.deleteByKnowledgeCodes(codes.getCodes());

        // 知识要素数据删除
        knowledgeEssentialRepository.deleteByKnowledgeCodes(codes.getCodes());

        // 知识抽取抽取
        knowledgeExtractRepository.deleteByKnowledgeCodes(codes.getCodes());

        //删除收藏
        collectionAppService.deleteByTargetCodesAllUser(codes.getCodes(), TargetType.Knowledge);
        //删除标签
        tagRelationRepository.removeByTarget(TargetType.Knowledge, codes.getCodes());

        // 删除ceph相关文件
        deleteCephFile(knowDTOMap, codes.getCodes());

        boolean result = repository.removeByCodes(codes.getCodes());

        knowledgeBaseService.refreshFileLength(knowledgeBase);

        return result;
    }

    private void deleteCephFile(Map<String, KnowledgeDTO> knowDTOMap, Collection<String> codes) {
        for (String code : codes) {
            KnowledgeDTO knowledgeDTO = knowDTOMap.get(code);
            if (knowledgeDTO != null) {
                String filePrefix = FileKeyUtils.getKnowledgeFilePrefix(knowledgeDTO.getKnowledgeBaseCode(), code);
                try {
                    cloudStorageDao.removeByPrefix(filePrefix);
                } catch (Exception e) {
                    log.error("删除知识相关文件异常，异常将忽略", e);
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleTagUpdate(List<String> knowledgeCodes) {
        if (CollectionUtils.isEmpty(knowledgeCodes)) {
            return;
        }
        //获取当前标签
        Map<String, List<String>> byTargetRetMap = tagRelationRepository.findByTargetRetMap(TargetType.Knowledge, knowledgeCodes);
        //更新发布状态
        this.updatePublishStatus2Update(knowledgeCodes);
        //更新索引
        for (String knowledgeCode : knowledgeCodes) {
            List<String> tags = byTargetRetMap.get(knowledgeCode);
            // 如果tags为null则使用空列表
            Map<String, Object> update = ImmutableMap.of(TAG, tags != null ? tags : Collections.emptyList());
            searchService.updateBaseInfoByKnowledgeCode(knowledgeCode, update);
        }
    }

    @Override
    public boolean updatePublishStatus2Update(Collection<String> knowledgeCodes) {
        if (CollectionUtils.isEmpty(knowledgeCodes)) {
            return false;
        }

        boolean result = false;
        Set<String> hasPublishRecord = knowledgeProdRepository.selectRecordByCode(knowledgeCodes);
        Collection<String> noPublishRecord = CollectionUtils.subtract(knowledgeCodes, hasPublishRecord);
        if (CollectionUtils.isNotEmpty(hasPublishRecord)) {
            result = repository.updatePublishStatus2Update(hasPublishRecord);
        }
        if (CollectionUtils.isNotEmpty(noPublishRecord)) {
            repository.updatePublishStatus2Unpublished(noPublishRecord);
        }
        return result;
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean update(String code, KnowledgeUpdateParam updateParam) {
        KnowledgeDTO knowledgeDTO = repository.selectByCode(code);
        BizAssert.notNull(knowledgeDTO, "AA012", "知识不存在");
        knowledgeBaseService.checkKnowledgeBaseAuth(knowledgeDTO.getKnowledgeBaseCode(), PrivilegeEnum.edit);

        // 处理标签列表，将逗号分隔的标签拆分为单个标签
        Set<String> processedTagCodes = processTagCodes(updateParam.getTagCodes());
        updateParam.setTagCodes(new ArrayList<>(processedTagCodes));

        KnowledgePublishCheckUtils.updatePublishStatusCheck(knowledgeDTO.getPublishStatus());
        // 更新索引
        if (!StringUtils.equals(knowledgeDTO.getName(), updateParam.getName()) ||
                (!CollUtil.isEqualList(updateParam.getTagCodes(), knowledgeDTO.getTagCodes()))) {
            List<String> tagCodes = updateParam.getTagCodes();
            if (tagCodes == null) {
                tagCodes = new ArrayList<>();
            }

            Map<String, Object> update = ImmutableMap.of(TAG, tagCodes, TITLE, updateParam.getName());
            searchService.updateBaseInfoByKnowledgeCode(code, update);
        }

        KnowledgeDTO dto = converter().convertUpdate(updateParam);
        dto.setId(null);
        repository.updateOne(dto, Wrappers.<KnowledgePO>lambdaUpdate().eq(KnowledgePO::getCode, code));
        //更新标签
        tagRelationRepository.saveTagRelation(TargetType.Knowledge, code, updateParam.getTagCodes());
        this.updatePublishStatus2Update(ImmutableList.of(code));

        // 更新文档目录名称
        if (!StringUtils.equals(knowledgeDTO.getName(), updateParam.getName())) {
            List<CatalogDTO> catalogDTOS = catalogRepository.findByKnowledgeCodes(Collections.singletonList(code));
            if (CollectionUtils.isNotEmpty(catalogDTOS)) {
                CatalogDTO catalogDTO = catalogDTOS.get(0);
                catalogDTO.setName(updateParam.getName());
                catalogRepository.updateCatalog(catalogDTO);
            }
        }

        return true;
    }

    /**
     * 获取知识库名称
     *
     * @param knowledgeBaseCode 知识库编码
     * @return 知识库名称
     */
    private String getKnowledgeBaseName(String knowledgeBaseCode) {
        KnowledgeBaseDTO base = knowledgeBaseRepository.findByCode(knowledgeBaseCode);
        return base != null ? base.getName() : null;
    }

    @Override
    public KnowledgeWithExtraVO getKnowledgeWithExtra(String code) {
        // 获取基础知识信息
        KnowledgeVO knowledgeVO = this.get(code);
        if (knowledgeVO == null) {
            return null;
        }

        // 创建扩展VO对象
        KnowledgeWithExtraVO extraVO = new KnowledgeWithExtraVO();
        BeanUtils.copyProperties(knowledgeVO, extraVO);

        // 获取知识库名称
        extraVO.setKnowledgeBaseName(getKnowledgeBaseName(knowledgeVO.getKnowledgeBaseCode()));

        // 获取额外信息
        Map<String, String> extraInfos = new HashMap<>();

        // 获取CONTENT类型的额外信息
        String content = knowledgeExtraInfoAppService.getContentByType(code, KnowledgeInfoType.CONTENT);
        if (content != null) {
            extraInfos.put(KnowledgeInfoType.CONTENT.name(), content);
        }

        // 获取META_INFO类型的额外信息
        String metaInfo = knowledgeExtraInfoAppService.getContentByType(code, KnowledgeInfoType.META_INFO);
        if (metaInfo != null) {
            extraInfos.put(KnowledgeInfoType.META_INFO.name(), metaInfo);
        }

        extraVO.setExtraInfos(extraInfos);

        return extraVO;
    }

    //获取上级标签和所有子标签
    private Set<String> getAllChainTags(List<String> tagCodes) {
        if (CollectionUtils.isEmpty(tagCodes)) {
            return new HashSet<>();
        }
        Set<String> allTags = new HashSet<>();
        allTags.addAll(tagAppRepository.fetchAllParentTag(tagCodes));
        allTags.addAll(tagAppRepository.fetchAllSubTag(tagCodes));
        allTags.addAll(tagCodes);

        return allTags;
    }

    //转换标签
    private Set<String> processTagCodes(Collection<String> tagCodes) {
        if (CollectionUtils.isEmpty(tagCodes)) {
            return new HashSet<>();
        }
        Set<String> tagSets = tagCodes.stream()
                .filter(StringUtils::isNotEmpty)
                .flatMap(tag -> Arrays.stream(tag.split(",")))
                .map(String::trim)
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.toSet());
        return tagSets;
    }

}
