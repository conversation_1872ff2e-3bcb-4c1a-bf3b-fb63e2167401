package com.chinatelecom.gs.engine.core.manager.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.chinatelecom.gs.engine.common.log.track.LogMessage;
import com.chinatelecom.gs.engine.common.log.track.LogPO;
import com.chinatelecom.gs.engine.common.mq.KmsSearchMessage;
import com.chinatelecom.gs.engine.common.utils.IpMaskUtils;
import com.chinatelecom.gs.engine.core.manager.service.LogAppService;
import com.chinatelecom.gs.engine.core.manager.service.LogEsService;
import com.chinatelecom.gs.engine.core.manager.service.LogService;
import com.chinatelecom.gs.engine.core.manager.service.SearchKmsLogEsService;
import com.chinatelecom.gs.engine.core.manager.vo.LogMessageVO;
import com.chinatelecom.gs.engine.core.manager.vo.LogVO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 日志处理服务类
 * @USER: pengmc1
 * @DATE: 2025/5/6 19:57
 */

@Service
public class LogAppServiceImpl implements LogAppService {

    @Resource
    private LogService logService;

    @Resource
    private LogEsService logEsService;

    @Value("${es.logMessage.enable:true}")
    private Boolean esLogMessageEnable;

    @Resource
    private SearchKmsLogEsService searchKmsLogEsService;

    @Override
    public void saveLog(LogMessage logMessage) {
        LogPO po = new LogPO();
        BeanUtils.copyProperties(logMessage, po);
        logService.save(po);
        if (Boolean.TRUE.equals(esLogMessageEnable)) {
            logEsService.saveLog(logMessage);
        }
    }

    /**
     * 获取消息日志详情
     *
     * @param sessionId
     * @param messageId
     * @return
     */
    @Override
    public LogMessageVO getLogDetail(String sessionId, String messageId) {
        if(StringUtils.isBlank(messageId)){
            return null;
        }
        List<LogVO> logVOList = getLogVOList(sessionId, messageId);
        if(CollectionUtils.isEmpty(logVOList)){
            return null;
        }
        //按logId进行去重
        Set<String> seenLogIds = new HashSet<>();
        logVOList = logVOList.stream().filter(log -> seenLogIds.add(log.getLogId())).collect(Collectors.toList());
        LogMessageVO logMessageVO = new LogMessageVO();
        logMessageVO.setLogCount(logVOList.size());
        Map<String, LogVO> logVOMap = Maps.newHashMap();
        for(LogVO logVO : logVOList){
            logVO.setConfig(IpMaskUtils.maskIpAddresses(logVO.getConfig()));
            logVO.setUrl(IpMaskUtils.maskIpAddresses(logVO.getUrl()));
            logVO.setInputData(IpMaskUtils.maskIpAddresses(logVO.getInputData()));
            logVO.setOutputData(IpMaskUtils.maskIpAddresses(logVO.getOutputData()));
            logVO.setExtraData(IpMaskUtils.maskIpAddresses(logVO.getExtraData()));
            logVOMap.put(logVO.getLogId(), logVO);
        }
        LogVO root = buildTree(logVOList, logVOMap);
        logMessageVO.setLogs(root.getChildren());
        return logMessageVO;
    }

    /**
     * 获取日志列表
     * @param sessionId
     * @param messageId
     * @return
     */
    private List<LogVO> getLogVOList(String sessionId, String messageId) {
        LambdaQueryWrapper<LogPO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(StringUtils.isNotBlank(sessionId), LogPO::getSessionId, sessionId);
        queryWrapper.eq(LogPO::getMessageId, messageId);
        queryWrapper.orderByAsc(LogPO::getStartTime);
        List<LogPO> logPOList = logService.list(queryWrapper);
        List<LogVO> logVOList = Lists.newArrayList();
        if(CollectionUtils.isNotEmpty(logPOList)){
            for(LogPO logPO : logPOList){
                LogVO logVO = new LogVO();
                BeanUtils.copyProperties(logPO, logVO);
                logVOList.add(logVO);
            }
        }
        if(CollectionUtils.isEmpty(logVOList)){
            logVOList = logEsService.searchLogsByMessageId(messageId);
        }
        return logVOList;
    }

    /**
     * 构造树形tree
     * @param logVOMap
     * @return
     */
    LogVO buildTree(List<LogVO> logVOList, Map<String, LogVO> logVOMap) {
        LogVO root = new LogVO();
        for(LogVO logVO : logVOList){
            if(StringUtils.isBlank(logVO.getPLogId())){
                root.addChild(logVO);
            }else{
                LogVO parent = logVOMap.get(logVO.getPLogId());
                if(Objects.nonNull(parent)){
                    parent.addChild(logVO);
                }
            }
        }
        return root;
    }

    /**
     * 保存知识库搜索日志
     *
     * @param kmsSearchMessage KmsSearchMessage
     */
    @Override
    public void saveSearchKmsLog(KmsSearchMessage kmsSearchMessage) {
        searchKmsLogEsService.saveLog(kmsSearchMessage);
    }
}
