package com.chinatelecom.gs.engine.channel.service.dto;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/12/19 20:25
 * @description
 */
@Data
@JacksonXmlRootElement(localName = "xml")
public class QywxAppMessage {

    @JacksonXmlProperty(localName = "ToUserName")
    private String toUserName;

    @JacksonXmlProperty(localName = "FromUserName")
    private String fromUserName;

    @JacksonXmlProperty(localName = "CreateTime")
    private Long createTime;

    @JacksonXmlProperty(localName = "MsgType")
    private String msgType;

    @JacksonXmlProperty(localName = "Content")
    private String content;

    @JacksonXmlProperty(localName = "MsgId")
    private String msgId;

    @JacksonXmlProperty(localName = "AgentID")
    private String agentId;

    @JacksonXmlProperty(localName = "Encrypt")
    private String msgEncrypt;

    @JacksonXmlProperty(localName = "Event")
    private String event;
}
