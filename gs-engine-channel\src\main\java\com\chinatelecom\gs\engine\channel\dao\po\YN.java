package com.chinatelecom.gs.engine.channel.dao.po;

/**
 * <AUTHOR>
 * @since 2023/8/24 11:09
 */
public enum YN {
    YES(0, "有效"),
    NO(1, "无效");

    private final long value;
    private final String desc;

    YN(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public long getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }
}
