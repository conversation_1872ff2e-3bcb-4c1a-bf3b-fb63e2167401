package com.chinatelecom.gs.engine.core.model.respository.respository;

import com.chinatelecom.gs.engine.common.infra.base.BaseRepository;
import com.chinatelecom.gs.engine.core.model.entity.dto.AgentIntentConfigDTO;
import com.chinatelecom.gs.engine.core.model.entity.po.AgentIntentConfigPO;

/**
 * 意图高级配置表 Repository 接口
 *
 * <AUTHOR>
 * @since 2025-02-12
 */
public interface AgentIntentConfigRepository extends BaseRepository<AgentIntentConfigDTO, AgentIntentConfigPO> {

    AgentIntentConfigDTO selectAllOrDefault(String appCode);

    AgentIntentConfigDTO selectByCode(String code);

    AgentIntentConfigDTO selectByAppCode();

    boolean existsByModelCode(String modelCode);
}
