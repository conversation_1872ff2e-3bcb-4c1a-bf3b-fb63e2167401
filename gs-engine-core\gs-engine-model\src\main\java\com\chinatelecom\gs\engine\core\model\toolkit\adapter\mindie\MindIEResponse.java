package com.chinatelecom.gs.engine.core.model.toolkit.adapter.mindie;

import com.chinatelecom.gs.engine.core.sdk.vo.llm.BaseLLMResponse;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

@Data
public class MindIEResponse implements BaseLLMResponse {
    private List<String> text;

    /**
     * 获取输出内容
     *
     * @return
     */
    @Override
    public String outputContent() {
        StringBuilder outputBuilder = new StringBuilder();
        if(CollectionUtils.isNotEmpty(text)){
            for(String str: text){
                outputBuilder.append(str);
            }
        }
        return outputBuilder.toString();
    }
}
