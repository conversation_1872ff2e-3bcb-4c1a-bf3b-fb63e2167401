package com.chinatelecom.gs.engine.config.filter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024年12月31日
 */
public class BaseInterceptorRegistration {

    private BaseHandlerInterceptor interceptor;

    private List<String> includePatterns = new ArrayList<>();

    private List<String> excludePatterns = new ArrayList<>();

    private int order = 0;

    public BaseInterceptorRegistration(BaseHandlerInterceptor interceptor) {
        this.interceptor = interceptor;
    }

    public BaseInterceptorRegistration addPathPatterns(String... patterns) {
        return addPathPatterns(Arrays.asList(patterns));
    }

    public BaseInterceptorRegistration addPathPatterns(List<String> patterns) {
        this.includePatterns.addAll(patterns);
        return this;
    }


    public BaseInterceptorRegistration excludePathPatterns(String... patterns) {
        return excludePathPatterns(Arrays.asList(patterns));
    }


    public BaseInterceptorRegistration excludePathPatterns(List<String> patterns) {
        this.excludePatterns.addAll(patterns);
        return this;
    }

    public BaseInterceptorRegistration order(int order){
        this.order = order;
        return this;
    }

    public int getOrder() {
        return order;
    }

    public BaseHandlerInterceptor getInterceptor() {
        return interceptor;
    }

    public List<String> getIncludePatterns() {
        return includePatterns;
    }

    public List<String> getExcludePatterns() {
        return excludePatterns;
    }
}
