package com.chinatelecom.gs.engine.channel.dao.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.chinatelecom.gs.engine.channel.dao.convert.ChannelConvert;
import com.chinatelecom.gs.engine.channel.dao.mapper.ChannelApiSecretMapper;
import com.chinatelecom.gs.engine.channel.dao.po.ChannelApiSecretPO;
import com.chinatelecom.gs.engine.channel.dao.po.YN;
import com.chinatelecom.gs.engine.channel.dao.repository.ChannelApiSecretRepository;
import com.chinatelecom.gs.engine.channel.service.dto.ChannelApiSecretDTO;
import com.chinatelecom.gs.engine.robot.sdk.config.enums.ApiSecretType;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * API秘钥管理 服务实现类
 *
 * <AUTHOR>
 * @since 2023-08-16
 */
@Service
public class ChannelApiSecretRepositoryImpl extends ServiceImpl<ChannelApiSecretMapper, ChannelApiSecretPO>
        implements ChannelApiSecretRepository {

    @Override
    public ChannelApiSecretDTO findBySecretId(String secretId, String appId) {
        LambdaQueryWrapper<ChannelApiSecretPO> condition = Wrappers.lambdaQuery();
        condition.eq(ChannelApiSecretPO::getAppId, appId);
        condition.eq(ChannelApiSecretPO::getSecretId, secretId);
        condition.eq(ChannelApiSecretPO::getYn, YN.YES.getValue());
        ChannelApiSecretPO apiSecret = super.getOne(condition);
        return ChannelConvert.INSTANCE.po2dto(apiSecret);
    }

    @Override
    public List<ChannelApiSecretDTO> channelApiSecretListByAppId(String appId, ApiSecretType apiSecretType, int limit) {
        LambdaQueryWrapper<ChannelApiSecretPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ChannelApiSecretPO::getAppId, appId);
        queryWrapper.eq(ChannelApiSecretPO::getSecretType, apiSecretType);
        queryWrapper.eq(ChannelApiSecretPO::getYn, YN.YES.getValue());
        queryWrapper.last("LIMIT " + limit);
        List<ChannelApiSecretPO> list = super.list(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list.stream().map(ChannelConvert.INSTANCE::po2dto).collect(Collectors.toList());
    }
}
