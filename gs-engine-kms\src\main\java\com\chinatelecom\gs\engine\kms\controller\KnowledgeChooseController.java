package com.chinatelecom.gs.engine.kms.controller;

import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.kms.sdk.constants.KmsApis;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.report.KnowledgeChooseInfoParam;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.report.KnowledgeChooseInfoVO;
import com.chinatelecom.gs.engine.kms.service.KnowledgeChooseService;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2025年06月10日
 */
@RestController
@RequestMapping({KmsApis.KMS_API + KmsApis.CHOOSE})
@Slf4j
@Tag(name = "知识库挂载")
public class KnowledgeChooseController {

    @Resource
    private KnowledgeChooseService knowledgeChooseService;

    @Operation(summary = "知识库挂载名称查询", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "知识库挂载名称查询", groupName = "知识库挂载")
    @AuditLog(businessType = "知识库挂载", operType = "知识库挂载名称查询", operDesc = "知识库挂载名称查询", objId="#param.codes")
    @PostMapping(KmsApis.INFO_API)
    public Result<List<KnowledgeChooseInfoVO>> chooseInfo(@Validated @RequestBody List<KnowledgeChooseInfoParam> param) {
        return Result.success(knowledgeChooseService.queryChooseInfo(param));
    }


}
