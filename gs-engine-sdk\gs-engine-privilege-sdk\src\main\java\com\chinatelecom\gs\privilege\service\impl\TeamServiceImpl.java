package com.chinatelecom.gs.privilege.service.impl;

import com.chinatelecom.cloud.common.pojo.BaseResult;
import com.chinatelecom.cloud.common.pojo.PageResult;
import com.chinatelecom.cloud.common.pojo.Pager;
import com.chinatelecom.cloud.platform.client.rpc.CorpRes;
import com.chinatelecom.cloud.platform.client.rpc.Team;
import com.chinatelecom.cloud.platform.client.util.AuthUtils;
import com.chinatelecom.gs.privilege.common.vo.AccessControlTeamInfo;
import com.chinatelecom.gs.privilege.service.TeamService;
import com.chinatelecom.gs.privilege.service.dto.ObjectCheckInfo;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.redisson.Redisson;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.chinatelecom.gs.privilege.service.AuthInfoSyncScheduler.taskPeriod;

@Slf4j
@Service
public class TeamServiceImpl implements TeamService {

    @Resource
    private Redisson redisson;

    private static final String TEAM_CACHE_KEY = "access_privilege_teams_tree";

    private static final String TEAM_CODES_CACHE_KEY = "access_privilege_teams_checkobjects";

    private static final String TEAM_CACHE_CONSTRUCT_LOCK = "access_privilege_teams_construct_lock";

    private static final String TEAM_REFRESH_TIMESTAMP_KEY = "access_privilege_teams_refresh_time";

    @Override
    public List<AccessControlTeamInfo> getAllTeams(String corpCode) {
        Map<String, List<AccessControlTeamInfo>> hash = redisson.getMap(TEAM_CACHE_KEY);
        if (hash == null) {
            log.error("【权限模块】团队树状结构的hash不存在");
            throw new IllegalStateException("团队树状结构的hash不存在");
        }
        return hash.get(corpCode);
    }

    @Override
    public Boolean isTeamCodesExist(String corpCode, List<ObjectCheckInfo> teamObjects) {
        Map<String, Map<String, ObjectCheckInfo>> existTeamCodes = redisson.getMap(TEAM_CODES_CACHE_KEY);
        if (existTeamCodes == null || existTeamCodes.get(corpCode) == null) {
            log.error("【权限模块】团队树状结构的teamCodes不存在");
            return false;
        }

        Map<String, ObjectCheckInfo> corpObjects = existTeamCodes.get(corpCode);
        List<String> teamCodes = Optional.ofNullable(teamObjects)
                .orElse(Collections.emptyList())
                .stream().map(ObjectCheckInfo::getObjectId)
                .collect(Collectors.toList());

        if (!corpObjects.keySet().containsAll(teamCodes)) {
            return false;
        }

        for (ObjectCheckInfo oci : teamObjects) {
            ObjectCheckInfo storedOci = corpObjects.get(oci.getObjectId());
            if (!oci.getObjectName().equalsIgnoreCase(storedOci.getObjectName())) {
                return false;
            }
        }

        return true;
    }

    @Override
    public void construct() {
        log.info("【权限模块】开始重构团队树");

        RLock lock = redisson.getLock(TEAM_CACHE_CONSTRUCT_LOCK);
        if (lock == null) {
            log.error("【权限模块】重构团队树，获取分布式锁为空");
            return;
        }

        List<String> corpCodes;
        try {
            boolean lockStatus = lock.tryLock(5, TimeUnit.SECONDS);
            if (!lockStatus) {
                log.error("【权限模块】重构团队树，分布式锁加锁失败,lockStatus false");
                return;
            }
            long teamStart = System.currentTimeMillis();

            RBucket<Long> lastRefreshTime = redisson.getBucket(TEAM_REFRESH_TIMESTAMP_KEY);
            if (lastRefreshTime == null || lastRefreshTime.get() == null) {
                log.warn("【权限模块】上次团队树刷新时间不存在，执行本次更新");
            } else if (lastRefreshTime.get() + taskPeriod > teamStart){
                log.warn("【权限模块】上次刷新时间小于时间间隔，已经有其他实例更新团队树，本次退出");
                return;
            }

            BaseResult<List<CorpRes>> corpResult = AuthUtils.getCorpByAppCode();
            if (!corpResult.ifSuccess() || CollectionUtils.isEmpty(corpResult.getData())) {
                log.error("【权限模块】获取租户下的公司失败,baseResult:{}", corpResult);
                return;
            }

            corpCodes = corpResult.getData().stream().map(CorpRes::getCorpCode).collect(Collectors.toList());
            for (String corpCode : corpCodes) {
                try {
                    long corpStart = System.currentTimeMillis();
                    List<AccessControlTeamInfo> corpTeamTree = getCorpTeam(corpCode);
                    Map<String, List<AccessControlTeamInfo>> hash = redisson.getMap(TEAM_CACHE_KEY);
                    hash.put(corpCode, corpTeamTree);

                    Map<String, ObjectCheckInfo> teamCodes = getTeamCode(corpTeamTree);
                    if (MapUtils.isNotEmpty(teamCodes)) {
                        Map<String, Map<String, ObjectCheckInfo>> teamCodesMap = redisson.getMap(TEAM_CODES_CACHE_KEY);
                        teamCodesMap.put(corpCode, teamCodes);
                    }

                    log.info("【权限模块】corp：{}的团队树构建耗时:{}ms", corpCode, (System.currentTimeMillis() - corpStart));
                } catch (Exception e) {
                    log.warn("租户团队信息构造失败：{}", corpCode, e);
                }
            }
            long teamEnd = System.currentTimeMillis();
            log.info("【权限模块】重构团队树完成,耗时:{}ms", teamEnd - teamStart);
            if (lastRefreshTime != null) {
                lastRefreshTime.set(teamEnd);
            }
        } catch (Exception e) {
            log.error("获取租户下的所有公司corpCode失败", e);
        } finally {
            lock.unlock();
        }
    }


    private static Map<String, ObjectCheckInfo> getTeamCode(List<AccessControlTeamInfo> corpTeamInfo) {
        if (CollectionUtils.isEmpty(corpTeamInfo)) {
            return null;
        }

        Map<String, ObjectCheckInfo> teamCodes = new HashMap<>();
        for (AccessControlTeamInfo teamInfo : corpTeamInfo) {
            teamCodes.put(teamInfo.getTeamCode(), new ObjectCheckInfo(teamInfo.getTeamCode(), teamInfo.getTeamName()));
            Map<String, ObjectCheckInfo> subTeamCodes = getTeamCode(teamInfo.getChildTeams());
            if (MapUtils.isNotEmpty(subTeamCodes)) {
                teamCodes.putAll(subTeamCodes);
            }
        }

        return teamCodes;
    }


    private List<AccessControlTeamInfo> getCorpTeam(String corpCode) {
        long pageIdx = 0L;
        long pageSize = 200L;
        Map<String, AccessControlTeamInfo> teamMap = new HashMap<>();
        while (true) {
            Pager pager = new Pager();
            pager.setPage(pageIdx);
            pager.setPageSize(pageSize);
            try {
                BaseResult<PageResult<Team>> teamPageResult = AuthUtils.getTeamList(null, pager, corpCode);
                if (!teamPageResult.ifSuccess()) {
                    log.error("【权限模块】corp:{}获取团队信息失败, result:{}", corpCode, teamPageResult);
                    break;
                }

                PageResult<Team> page = teamPageResult.getData();
                if (page == null || CollectionUtils.isEmpty(page.getRecords())) {
                    log.warn("【权限模块】corpCode:{} 重构团队树状结构，遇到中间结果为空: {}", corpCode, teamPageResult);
                    break;
                }

                for (Team team : page.getRecords()) {
                    AccessControlTeamInfo accessControlTeamInfo = new AccessControlTeamInfo();
                    accessControlTeamInfo.setTeamCode(team.getTeamCode());
                    accessControlTeamInfo.setTeamName(team.getName());
                    accessControlTeamInfo.setParentTeamCode(team.getParentTeamCode());
                    teamMap.put(accessControlTeamInfo.getTeamCode(), accessControlTeamInfo);
                }

                if (teamPageResult.getData().isHasNext()) {
                    pageIdx++;
                } else {
                    break;
                }

            } catch (Exception e) {
                log.error("【权限模块】获取corp: {} 下的团队信息异常", corpCode, e);
                break;
            }
        }

        List<AccessControlTeamInfo> result = new ArrayList<>();
        for (Map.Entry<String, AccessControlTeamInfo> entry : teamMap.entrySet()) {
            AccessControlTeamInfo e = entry.getValue();
            if (e.getParentTeamCode() != null) {
                AccessControlTeamInfo teamInfo = teamMap.get(e.getParentTeamCode());
                if (teamInfo != null) {
                    teamInfo.addChild(e);
                }
            }
        }

        for (Map.Entry<String, AccessControlTeamInfo> entry : teamMap.entrySet()) {
            AccessControlTeamInfo e = entry.getValue();
            if (e.getParentTeamCode() == null) {
                result.add(e);
            }
        }

        return result;
    }
}
