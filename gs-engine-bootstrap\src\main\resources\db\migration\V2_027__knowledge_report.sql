ALTER TABLE `knowledge`
ADD COLUMN  `task_id` varchar(64) NULL COMMENT '任务ID' AFTER `description`,
ADD COLUMN  `image_task_id` varchar(64) NULL COMMENT '图像任务ID' AFTER `task_id`,
ADD COLUMN  `progress` tinyint NOT NULL DEFAULT '0' COMMENT '文本解析进度' AFTER `image_task_id`,
ADD COLUMN `dev_message` text NULL COMMENT '解析失败开发信息' AFTER `progress`;

ALTER TABLE knowledge DROP INDEX idx_knowledge_name;
ALTER TABLE knowledge ADD INDEX idx_name (`name`);
ALTER TABLE knowledge ADD INDEX idx_code (`code`);

update `knowledge` set `progress` = 100 where `indexing_status` = 'success' and yn=0;

CREATE TABLE `knowledge_report_system_template` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `name` varchar(64) NOT NULL COMMENT '名称',
  `code` varchar(64) NOT NULL COMMENT 'Code',
  `theme_text` varchar(512) NOT NULL COMMENT '用户原始主题',
  `content` mediumtext COMMENT '文章内容',
  `type` varchar(64) NOT NULL COMMENT '文章内容格式',
  `cover_file_key` varchar(1024) DEFAULT NULL COMMENT '报告封面',
  `tpl_type` varchar(64) NOT NULL COMMENT '模板类型',
  `yn` bigint unsigned NOT NULL DEFAULT '0' COMMENT '删除标记,0:未删除,其他表示已删除',
  `create_id` varchar(255) NOT NULL DEFAULT '0' COMMENT '创建用户ID',
  `create_name` varchar(255) NOT NULL DEFAULT '未知用户' COMMENT '创建用户名',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_id` varchar(255) NOT NULL DEFAULT '0' COMMENT '最后修改用户ID',
  `update_name` varchar(255) NOT NULL DEFAULT '未知用户' COMMENT '最后修改用户名',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_code` (`code`),
  KEY `idx_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='系统内置模板表';

CREATE TABLE `knowledge_report_my_template` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `name` varchar(64) NOT NULL COMMENT '名称',
  `code` varchar(64) NOT NULL COMMENT 'Code',
  `app_code` varchar(64) NOT NULL COMMENT '应用Code',
  `theme_text` varchar(512) NOT NULL COMMENT '用户原始主题',
  `content` mediumtext COMMENT '文章内容',
  `type` varchar(64) NOT NULL COMMENT '文章内容格式',
  `cover_file_key` varchar(1024) DEFAULT NULL COMMENT '报告封面',
  `tpl_type` varchar(64) NOT NULL COMMENT '模板类型',
  `yn` bigint unsigned NOT NULL DEFAULT '0' COMMENT '删除标记,0:未删除,其他表示已删除',
  `create_id` varchar(255) NOT NULL DEFAULT '0' COMMENT '创建用户ID',
  `create_name` varchar(255) NOT NULL DEFAULT '未知用户' COMMENT '创建用户名',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_id` varchar(255) NOT NULL DEFAULT '0' COMMENT '最后修改用户ID',
  `update_name` varchar(255) NOT NULL DEFAULT '未知用户' COMMENT '最后修改用户名',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_code` (`code`),
  KEY `idx_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='我的模板表';

update prompt_template set content='"""\n### 任务\n根据用户提供的主题、关键词和相关要求，生成一个结构化的**大纲**，并为大纲中的**一级标题**和**二级标题**生成**内容摘要**。请确保：\n1.  **根据主题生成**： 大纲的核心内容必须围绕提供的主题展开。\n1.  **覆盖所有关键词（如提供）**：大纲内容必须涵盖用户提供的所有关键词。\n2.  **围绕内容概要（如提供）**：如果用户提供了参考的内容概要，生成的大纲应紧密围绕该概要展开。\n\n### 生成要求\n1.  **大纲结构（必须严格遵守）**：\n    *   层级：生成**一级标题（#）**、**二级标题（##）** 和 **三级标题（###）** 三个层级。\n    *   数量限制：\n        *   最多生成 **3个** 一级标题。\n        *   每个一级标题下，最多生成 **2个** 二级标题。\n        *   每个二级标题下，最多生成 **2个** 三级标题。\n    *   **仅输出大纲**：最终输出应只包含大纲本身（标题+摘要），不要添加任何其他解释性文字。\n2.  **内容摘要（必须生成）**：\n    *   为**每个一级标题**生成一句简要的内容摘要（直接写在 `# 标题` 下面）。\n    *   为**每个二级标题**生成一句简要的内容摘要（直接写在 `## 标题` 下面）。\n    *   为**每个三级标题**生成一句简要的内容摘要（直接写在 `### 标题` 下面）。\n3.  **格式要求（必须严格遵守）**：\n    *   使用 **Markdown** 格式。\n    *   一级标题：以 `#` 开头（例如 `# 标题`）。\n    *   二级标题：以 `##` 开头（例如 `## 标题`）。\n    *   三级标题：以 `###` 开头（例如 `### 标题`）。\n    *   **禁止序号**：标题中**不得**包含任何形式的序号（如 1.、2.、A、B、i、ii 等）。\n\n### 示例参考\n**输入 (Input)：**\n主题：人工智能的应用\n<#if keywords??>\n关键词：医疗，教育，金融\n</#if>\n**输出 (Output - 仅大纲与摘要)：**\n```markdown\n# 医疗健康\n人工智能在医疗领域的应用正深刻改变诊疗方式，提升医疗服务效率与精准度。\n## 疾病诊断\nAI技术辅助医生进行更快速准确的病理识别与判断。\n### 影像识别\n利用深度学习技术分析X光、CT等医学影像，辅助医生发现早期病变。\n### 数据驱动的个性化治疗\n结合患者的基因组数据和电子病历，提供精准的疾病预测和治疗方案。\n## 药物研发\n人工智能显著缩短新药开发周期，降低研发成本与研发周期。\n### 分子筛选与设计\nAI加速新药物分子的筛选过程，通过模拟实验预测其有效性及副作用。\n### 临床试验优化\n利用机器学习算法提高临床试验的设计效率和成功率。\n\n# 教育领域\nAI技术推动教育个性化变革，实现因材施教的教学新模式。\n## 个性化学习\n基于学生特征定制专属学习方案，提升学习效果。\n### 学习路径规划\n根据学生的学习进度和兴趣点定制个性化的学习路径。\n### 实时反馈系统\n通过自然语言处理技术对学生作业和考试结果进行即时评估和反馈。\n## 智能辅导\n人工智能提供全天候学习支持，弥补传统教育不足。\n### 自适应学习材料\n提供适合不同学习风格的学生的自适应教材和练习题。\n### 虚拟导师\n使用AI构建虚拟教师，能够24小时解答学生的问题，提供一对一的教学支持。\n\n# 金融服务\n人工智能重塑金融行业风控与交易体系，提升服务智能化水平。\n## 风险控制\nAI技术构建更精准的风险评估模型，保障金融安全。\n### 信用评分模型\n采用机器学习算法建立更精确的信用评分体系，降低信贷风险。\n### 反欺诈检测\n利用AI实时监控交易行为，快速识别潜在的欺诈活动。\n## 自动化交易\n### 投资策略优化\n开发智能算法自动执行复杂的金融市场交易策略。\n### 市场情绪分析\n通过分析社交媒体和新闻报道来预测市场趋势，辅助投资决策。\n```\n\n### 请生成以下主题的大纲\n主题：${content}\n<#if keywords??>\n关键词：${keywords}\n</#if>\n<#if additionalKeywords??>\n其他附加要求：${additionalKeywords}\n</#if>\n<#if summary??>\n可供参考的一些文章内容概要：\n${summary}\n</#if>\n\n请严格遵循上述“生成要求”和“格式要求”，生成大纲及内容摘要。\n"""\n'
where `code`='OUTLINE' AND app_code="0" limit 1;

update prompt_template set content='"""\n### 任务\n根据用户提供的主题、关键词和相关要求，生成一个结构化的**大纲**，并为大纲中的**一级标题**和**二级标题**生成**内容摘要**。请确保：\n1.  **根据主题生成**： 大纲的核心内容必须围绕提供的主题展开。\n1.  **覆盖所有关键词（如提供）**：大纲内容必须涵盖用户提供的所有关键词。\n2.  **围绕内容概要（如提供）**：如果用户提供了参考的内容概要，生成的大纲应紧密围绕该概要展开。\n\n### 生成要求\n1.  **大纲结构（必须严格遵守）**：\n    *   层级：生成**一级标题（#）**、**二级标题（##）** 和 **三级标题（###）** 三个层级。\n    *   数量限制：\n        *   最多生成 **3个** 一级标题。\n        *   每个一级标题下，最多生成 **2个** 二级标题。\n        *   每个二级标题下，最多生成 **2个** 三级标题。\n    *   **仅输出大纲**：最终输出应只包含大纲本身（标题+摘要），不要添加任何其他解释性文字。\n2.  **内容摘要（必须生成）**：\n    *   为**每个一级标题**生成一句简要的内容摘要（直接写在 `# 标题` 下面）。\n    *   为**每个二级标题**生成一句简要的内容摘要（直接写在 `## 标题` 下面）。\n    *   为**每个三级标题**生成一句简要的内容摘要（直接写在 `### 标题` 下面）。\n3.  **格式要求（必须严格遵守）**：\n    *   使用 **Markdown** 格式。\n    *   一级标题：以 `#` 开头（例如 `# 标题`）。\n    *   二级标题：以 `##` 开头（例如 `## 标题`）。\n    *   三级标题：以 `###` 开头（例如 `### 标题`）。\n    *   **禁止序号**：标题中**不得**包含任何形式的序号（如 1.、2.、A、B、i、ii 等）。\n\n### 示例参考\n**输入 (Input)：**\n主题：人工智能的应用\n<#if keywords??>\n关键词：医疗，教育，金融\n</#if>\n**输出 (Output - 仅大纲与摘要)：**\n```markdown\n# 医疗健康\n人工智能在医疗领域的应用正深刻改变诊疗方式，提升医疗服务效率与精准度。\n## 疾病诊断\nAI技术辅助医生进行更快速准确的病理识别与判断。\n### 影像识别\n利用深度学习技术分析X光、CT等医学影像，辅助医生发现早期病变。\n### 数据驱动的个性化治疗\n结合患者的基因组数据和电子病历，提供精准的疾病预测和治疗方案。\n## 药物研发\n人工智能显著缩短新药开发周期，降低研发成本与研发周期。\n### 分子筛选与设计\nAI加速新药物分子的筛选过程，通过模拟实验预测其有效性及副作用。\n### 临床试验优化\n利用机器学习算法提高临床试验的设计效率和成功率。\n\n# 教育领域\nAI技术推动教育个性化变革，实现因材施教的教学新模式。\n## 个性化学习\n基于学生特征定制专属学习方案，提升学习效果。\n### 学习路径规划\n根据学生的学习进度和兴趣点定制个性化的学习路径。\n### 实时反馈系统\n通过自然语言处理技术对学生作业和考试结果进行即时评估和反馈。\n## 智能辅导\n人工智能提供全天候学习支持，弥补传统教育不足。\n### 自适应学习材料\n提供适合不同学习风格的学生的自适应教材和练习题。\n### 虚拟导师\n使用AI构建虚拟教师，能够24小时解答学生的问题，提供一对一的教学支持。\n\n# 金融服务\n人工智能重塑金融行业风控与交易体系，提升服务智能化水平。\n## 风险控制\nAI技术构建更精准的风险评估模型，保障金融安全。\n### 信用评分模型\n采用机器学习算法建立更精确的信用评分体系，降低信贷风险。\n### 反欺诈检测\n利用AI实时监控交易行为，快速识别潜在的欺诈活动。\n## 自动化交易\n### 投资策略优化\n开发智能算法自动执行复杂的金融市场交易策略。\n### 市场情绪分析\n通过分析社交媒体和新闻报道来预测市场趋势，辅助投资决策。\n```\n\n### 你的任务：请对以及生成的大纲进行优化，重新生成以下主题的大纲\n主题：${content}\n<#if keywords??>\n关键词：${keywords}\n</#if>\n<#if additionalKeywords??>\n其他附加要求：${additionalKeywords}\n</#if>\n<#if summary??>\n可供参考的一些文章内容概要：\n${summary}\n</#if>\n\n\n用户不满意的大纲：\n```\n${outline!}\n```\n\n请严格遵循上述“生成要求”和“格式要求”，对用户不满意的大纲进行优化，重新生成大纲及内容摘要。\n"""\n'
where `code`='OUTLINE_REWRITE' AND app_code="0" limit 1;

update prompt_template set content='"""\n### Task\n根据主题和关键内容摘要，对大纲内容进行扩展，生成指定大纲内容下的详细文章内容，如果有多个方面的信息，请分多个自然段落进行输出。\n\n### Instructions\n- **内容要求**：\n1. 严格按照给定的大纲主题和摘要输出内容，如果有参考信息，输出内容应该严格根据参考信息进行生成，生成的文章应该语言应专业、清晰，适合目标读者。请确保逻辑严谨，结构清晰，内容要全面完整。\n<#if outline??>\n2. 当前主题的整体大纲及每级大纲摘要信息如下，请根据整体大纲的信息生成当前指定章节的详细内容。\n```\n${outline!}\n```\n</#if>\n\n- **格式要求**：\n1. 提供给你的是一个大纲的内容，请根据文章主题及当前大纲主题要求生成相应的文章内容。\n2. 输出内容是当前大纲的具体文章内容，不需要输出对应的大纲标题及序号。\n3. 输出内容应为纯文本，绝对不能包含任何markdown格式符号，特别是不能包含#号。\n\n### Question\n用户要求的文章主题是：${content}\n<#if keywords??>\n用户要求生成主题相关的关键字：${keywords}\n</#if>\n<#if additionalKeywords??>\n用户提出的其他附加要求：${additionalKeywords}\n</#if>\n\n本次要求输出内容的大纲主题是：\n${title}\n<#if introduction??>\n本次要求输出内容的大纲摘要是：\n${introduction}\n</#if>\n\n<#if example??>\n本次要求输出内容的可参考信息如下：\n```\n${example}\n```\n</#if>\n\n根据指令要求和大纲，生成当前大纲下的内容信息\n"""\n'
where `code`='CONTENT' AND app_code="0" limit 1;

ALTER TABLE `gs_session_file`
    ADD COLUMN  `task_id` varchar(64) NOT NULL DEFAULT '' COMMENT '任务ID' AFTER `file_content_length`,
    ADD COLUMN  `status` varchar(64) NOT NULL DEFAULT 'success' COMMENT '解析状态' AFTER `task_id`,
    ADD COLUMN  `progress` tinyint NOT NULL DEFAULT '100' COMMENT '文本解析进度' AFTER `status`;

ALTER TABLE `knowledge_report`
CHANGE `name` `name` varchar(512) DEFAULT NULL COMMENT '名称',
CHANGE `theme_text` `theme_text` varchar(512) DEFAULT NULL COMMENT '用户原始主题',
ADD COLUMN  `cover_file_info` varchar(4096) DEFAULT NULL COMMENT '封面视图信息' AFTER `cover_file_key`;

