package com.chinatelecom.gs.privilege.common.enums;

import lombok.Getter;

public enum ResourceTypeEnum {

    ROBOT("AGENT", "机器人"),
    KNOWLEDGE("KNOWLEDGE", "知识库"),
    REPORT("REPORT", "智能写作"),
    WORKFLOW("WOR<PERSON><PERSON><PERSON>", "业务流"),
    PLUGIN("P<PERSON><PERSON>GI<PERSON>", "插件"),
    ;

    @Getter
    private final String code;

    private final String desc;

    ResourceTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
