package com.chinatelecom.gs.engine.core.corekit.domain.po;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.chinatelecom.gs.engine.common.infra.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@TableName("gs_sensitive_base")
public class SensitiveBasePO extends BaseEntity implements Serializable {

    /**
     * 应用code
     */
    @TableField(value = "app_code", fill = FieldFill.INSERT)
    private String appCode;


    /**
     * 应用code
     */
    @TableField(value = "name", fill = FieldFill.INSERT)
    private String name;

    /**
     * 应用code
     */
    @TableField(value = "code", fill = FieldFill.INSERT)
    private String code;


}
