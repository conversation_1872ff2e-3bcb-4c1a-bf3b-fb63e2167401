package com.chinatelecom.gs.engine.channel.openai.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * OpenAI Chat Completion API 流式响应格式
 */
@Data
public class ChatCompletionStreamResponse {
    /**
     * 响应ID
     */
    @Schema(description = "响应的唯一标识符")
    private String id;

    /**
     * 对象类型，固定为"chat.completion.chunk"
     */
    @Schema(description = "对象类型，流式响应为chat.completion.chunk")
    private String object = "chat.completion.chunk";

    /**
     * 创建时间戳（秒）
     */
    @Schema(description = "创建响应的Unix时间戳（以秒为单位）")
    private long created;

    /**
     * 使用的模型
     */
    @Schema(description = "用于补全的模型")
    private String model;

    /**
     * 系统指纹，用于确定性生成
     */
    @JsonProperty("system_fingerprint")
    @Schema(description = "用于确定性生成的系统指纹")
    private String systemFingerprint;

    /**
     * 补全选项列表
     */
    @Schema(description = "补全选项列表")
    private List<ChatCompletionStreamChoice> choices;

    @Data
    public static class ChatCompletionStreamChoice {
        /**
         * 索引
         */
        @Schema(description = "选项的索引")
        private int index;

        /**
         * 增量消息
         */
        @Schema(description = "增量消息对象（在流式响应中）")
        private ChatCompletionStreamDelta delta;

        /**
         * 结束原因：stop, length, content_filter, function_call, null
         */
        @JsonProperty("finish_reason")
        @Schema(description = "结束原因：stop, length, content_filter, function_call, null")
        private String finishReason;
    }

    @Data
    public static class ChatCompletionStreamDelta {
        /**
         * 角色
         */
        @Schema(description = "消息的角色，通常为assistant")
        private String role = "assistant";

        /**
         * 内容
         */
        @Schema(description = "消息的内容")
        private String content;

        /**
         * 推理内容，对应bot的reasoningContent字段
         */
        @Schema(description = "可选的推理内容")
        private String reasoning_content;
    }
}
