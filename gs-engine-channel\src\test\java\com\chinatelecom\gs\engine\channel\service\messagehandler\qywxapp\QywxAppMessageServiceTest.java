package com.chinatelecom.gs.engine.channel.service.messagehandler.qywxapp;

import com.chinatelecom.gs.engine.channel.common.SessionIdHolder;
import com.chinatelecom.gs.engine.channel.common.cache.localcache.ChannelCaches;
import com.chinatelecom.gs.engine.channel.service.dto.QywxRequestMessage;
import com.chinatelecom.gs.engine.channel.service.messagehandler.MessageRecordService;
import com.chinatelecom.gs.engine.channel.service.messagehandler.qywxapp.encrypt.WXBizMsgCrypt;
import com.chinatelecom.gs.engine.common.cache.RedisService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class QywxAppMessageServiceTest {

    @InjectMocks
    private QywxAppMessageService qywxAppMessageService;

    @Mock
    private ChannelCaches channelCaches;

    @Mock
    private SessionIdHolder sessionIdHolder;

    @Mock
    private MessageRecordService messageRecordService;

    @Mock
    private RedisService redisService;

    @Mock
    private QywxMessageSendService qywxMessageSendService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    // ------------------ handshake ------------------

    @Test
    void testHandshake_Success() throws Exception {
        String channelId = "test";
        String msgSignature = "sig";
        String timestamp = "ts";
        String nonce = "nonce";
        String echoStr = "echo";

        WXBizMsgCrypt mockCrypt = mock(WXBizMsgCrypt.class);
        when(channelCaches.getWxBizMsgCrypt(channelId)).thenReturn(mockCrypt);
        when(mockCrypt.verifyURL(msgSignature, timestamp, nonce, echoStr)).thenReturn("success");

        String result = qywxAppMessageService.handshake(channelId, msgSignature, timestamp, nonce, echoStr);

        assertEquals("success", result);
    }

    // ------------------ asyncChat ------------------

    @Test
    void testAsyncChat_WithEventNode_ShouldReturnEmpty() {
        QywxRequestMessage message = new QywxRequestMessage();
        message.setChannelId("channel");
        message.setQwMessage("<xml><Event>test</Event></xml>");

        String result = qywxAppMessageService.asyncChat(message);

        assertEquals("", result);
    }
}
