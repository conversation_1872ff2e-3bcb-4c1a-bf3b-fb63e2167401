package com.chinatelecom.gs.engine.core.corekit.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.chinatelecom.gs.engine.common.infra.base.impl.BaseExtendServiceImpl;
import com.chinatelecom.gs.engine.core.corekit.domain.po.SensitiveBasePO;
import com.chinatelecom.gs.engine.core.corekit.domain.request.SensitiveBasePageRequest;
import com.chinatelecom.gs.engine.core.corekit.domain.request.SensitiveBaseRequest;
import com.chinatelecom.gs.engine.core.corekit.domain.response.SensitiveBaseResponse;
import com.chinatelecom.gs.engine.core.corekit.mapper.SensitiveBaseMapper;
import com.chinatelelcom.gs.engine.sdk.common.PageImpl;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.BeanUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

class SensitiveBaseServiceImplTest {

    @Mock
    private SensitiveBaseMapper sensitiveBaseMapper;

    @InjectMocks
    private SensitiveBaseServiceImpl sensitiveBaseService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void saveSensitiveBase_WhenCodeExists_ShouldUpdateSuccessfully() {
        // 准备测试数据 - 存在code的情况
        SensitiveBaseRequest request = new SensitiveBaseRequest();
        request.setCode("existing_code");
        request.setName("new_name");

        SensitiveBasePO existingPo = new SensitiveBasePO();
        existingPo.setCode("existing_code");
        existingPo.setName("old_name");

        when(sensitiveBaseMapper.selectOne(any())).thenReturn(existingPo);
        BeanUtils.copyProperties(request, existingPo);
        when(sensitiveBaseMapper.insertOrUpdate((SensitiveBasePO) any())).thenReturn(true);

        // 执行测试
        Boolean result = sensitiveBaseService.saveSensitiveBase(request);

        // 验证结果
        assertTrue(result);
        assertEquals("new_name", existingPo.getName());
    }

    @Test
    void saveSensitiveBase_WhenCodeNotExists_ShouldCreateNewRecord() {
        // 准备测试数据 - 不存在code的情况
        SensitiveBaseRequest request = new SensitiveBaseRequest();
        request.setName("test_name");

        when(sensitiveBaseMapper.selectOne(any())).thenReturn(null);
        when(sensitiveBaseMapper.insertOrUpdate((SensitiveBasePO) any())).thenReturn(true);

        // 执行测试
        Boolean result = sensitiveBaseService.saveSensitiveBase(request);

        // 验证结果
        assertTrue(result);
    }

    @Test
    void delete_WithValidCodes_ShouldReturnTrue() {
        // 准备测试数据
        List<String> codes = Arrays.asList("code1", "code2");
        when(sensitiveBaseMapper.deleteByIds(codes)).thenReturn(1);

        // 执行测试
        Boolean result = sensitiveBaseService.delete(codes);

        // 验证结果
        assertTrue(result);
    }

    @Test
    void delete_WithEmptyCodes_ShouldReturnFalse() {
        // 准备测试数据
        List<String> emptyCodes = Collections.emptyList();

        // 执行测试
        Boolean result = sensitiveBaseService.delete(emptyCodes);

        // 验证结果
        assertFalse(result);
    }

    @Test
    void pageQuery_WithKeyword_ShouldReturnFilteredResults() {
        // 准备测试数据
        SensitiveBasePageRequest request = new SensitiveBasePageRequest();
        request.setPageNum(1);
        request.setPageSize(10);
        request.setKeyword("test");

        PageDTO<SensitiveBasePO> page = new PageDTO<>(1, 10);
        SensitiveBasePO po = new SensitiveBasePO();
        po.setName("test_name");
        po.setCode("test_code");
        page.setRecords(Collections.singletonList(po));
        page.setTotal(1);

        when(sensitiveBaseMapper.selectPage(any(), any())).thenReturn(page);

        // 执行测试
        PageImpl result = (PageImpl) sensitiveBaseService.pageQuery(request);

        // 验证结果
        assertEquals(1, result.getTotal());
        assertEquals(1, result.getRecords().size());
        SensitiveBaseResponse response = (SensitiveBaseResponse) result.getRecords().get(0);
        assertEquals("test_name", response.getName());
    }

    @Test
    void pageQuery_WithoutKeyword_ShouldReturnAllResults() {
        // 准备测试数据
        SensitiveBasePageRequest request = new SensitiveBasePageRequest();
        request.setPageNum(1);
        request.setPageSize(10);

        PageDTO<SensitiveBasePO> page = new PageDTO<>(1, 10);
        page.setRecords(Arrays.asList(new SensitiveBasePO(), new SensitiveBasePO()));
        page.setTotal(2);

        when(sensitiveBaseMapper.selectPage(any(), any())).thenReturn(page);

        // 执行测试
        PageImpl result = (PageImpl) sensitiveBaseService.pageQuery(request);

        // 验证结果
        assertEquals(2, result.getTotal());
        assertEquals(2, result.getRecords().size());
    }
}
