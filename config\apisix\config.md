## 配置上游(upstream)

将以下配置中的nodes.host和node.port替换为对应服务地址：

(1) 机器人引擎(telecom-ai-gs-engine)后端地址

```json
{
  "id": "547719355027162046",
  "nodes": [
    {
      "host": "xxx",
      "port": 30556,
      "weight": 1
    }
  ],
  "timeout": {
    "connect": 6,
    "send": 6,
    "read": 6
  },
  "type": "roundrobin",
  "scheme": "http",
  "pass_host": "pass",
  "name": "ais_gs_engine_server",
  "keepalive_pool": {
    "idle_timeout": 60,
    "requests": 1000,
    "size": 320
  }
}
```

(2) 机器人引擎(gs-engine-front)前端地址

```json
{
  "id": "547719502146569150",
  "nodes": [
    {
      "host": "xxx",
      "port": 30326,
      "weight": 1
    }
  ],
  "timeout": {
    "connect": 6,
    "send": 6,
    "read": 6
  },
  "type": "roundrobin",
  "scheme": "http",
  "pass_host": "pass",
  "name": "ais_gs_engine_front",
  "keepalive_pool": {
    "idle_timeout": 60,
    "requests": 1000,
    "size": 320
  }
}
```

(3) im前端地址

```json
{
  "id": "547724367438545854",
  "nodes": [
    {
      "host": "xxx",
      "port": 9010,
      "weight": 1
    }
  ],
  "timeout": {
    "connect": 6,
    "send": 6,
    "read": 6
  },
  "type": "roundrobin",
  "scheme": "http",
  "pass_host": "pass",
  "name": "ais_gs_im_front",
  "keepalive_pool": {
    "idle_timeout": 60,
    "requests": 1000,
    "size": 320
  }
}
```

（4）审批流前端

```json
{
  "id": "548113554994103261",
  "nodes": [
    {
      "host": "xxx",
      "port": 30198,
      "weight": 1
    }
  ],
  "timeout": {
    "connect": 6,
    "send": 6,
    "read": 6
  },
  "type": "roundrobin",
  "scheme": "http",
  "pass_host": "pass",
  "name": "ais_flowable_front",
  "keepalive_pool": {
    "idle_timeout": 60,
    "requests": 1000,
    "size": 320
  }
}
```

（5）审批流后端

```json
{
  "id": "548113633914127325",
  "nodes": [
    {
      "host": "xxx.xxx.xxx.xxx",
      "port": 30849,
      "weight": 1
    }
  ],
  "timeout": {
    "connect": 6,
    "send": 6,
    "read": 6
  },
  "type": "roundrobin",
  "scheme": "http",
  "pass_host": "pass",
  "name": "ais_flowable_server",
  "keepalive_pool": {
    "idle_timeout": 60,
    "requests": 1000,
    "size": 320
  }
}
```

## 配置消费者(consumer)

消费者用于提供openapi

（1）默认消费者配置

```json
{
  "username": "ais_openapi_default_consumer",
  "plugins": {
    "hmac-auth": {
      "_meta": {
        "disable": false
      },
      "access_key": "生成随机字符串，10位以上",
      "encode_uri_params": false,
      "key_id": "与access_key一致",
      "secret_key": "生成随机字符串，10位以上"
    }
  }
}
```

## 配置路由(route)

将route.json下的路由配置到网关中

## 修改认证系统配置

将app-url改为：http://{网关地址}/ais/ks/f/knowledgeBase，统一认证系统同样修改

## 访问网关地址验证

http://{网关地址}/ais/ks/f/knowledgeBase
