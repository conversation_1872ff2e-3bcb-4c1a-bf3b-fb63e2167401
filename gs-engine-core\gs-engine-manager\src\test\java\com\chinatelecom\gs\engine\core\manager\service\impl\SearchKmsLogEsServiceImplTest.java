//package com.chinatelecom.gs.engine.core.manager.service.impl;
//
//import com.chinatelecom.cloud.platform.client.rpc.PlatformUser;
//import com.chinatelecom.cloud.platform.client.util.SsoUtil;
//import com.chinatelecom.gs.engine.common.context.RequestContext;
//import com.chinatelecom.gs.engine.common.mq.KmsSearchMessage;
//import com.chinatelecom.gs.engine.common.mq.TermQueryCount;
//import com.chinatelecom.gs.engine.common.utils.EsPrefixUtils;
//import com.chinatelecom.gs.engine.core.manager.repository.impl.KmsSearchMessageRepository;
//import com.chinatelecom.gs.engine.core.manager.vo.config.base.QueryKmsSearchMessageVO;
//import com.chinatelecom.gs.engine.robot.sdk.constant.CommonConstant;
//import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.MockedStatic;
//import org.mockito.MockitoAnnotations;
//import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
//import org.springframework.data.elasticsearch.core.SearchHits;
//import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
//import org.springframework.data.elasticsearch.core.query.ByQueryResponse;
//import org.springframework.data.elasticsearch.core.query.CriteriaQuery;
//import org.springframework.data.elasticsearch.core.query.Query;
//
//import java.time.LocalDateTime;
//import java.util.ArrayList;
//import java.util.Collections;
//import java.util.List;
//
//import static org.junit.Assert.*;
//import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
//import static org.mockito.Mockito.*;
//
//class SearchKmsLogEsServiceImplTest {
//
//    @InjectMocks
//    private SearchKmsLogEsServiceImpl searchKmsLogEsService;
//
//    @Mock
//    private ElasticsearchRestTemplate elasticsearchRestTemplate;
//
//    @Mock
//    private KmsSearchMessageRepository repository;
//
//    @BeforeEach
//    void setUp() {
//        MockitoAnnotations.openMocks(this);
//    }
//
//    // 对公共方法的测试
//    @Test
//    void testSaveLog_WithValidKmsSearchMessage_SavesToRepository() {
//        // Given
//        KmsSearchMessage logMessage = new KmsSearchMessage();
//        logMessage.setLogId("test-log-id");
//        logMessage.setQuery("test query");
//
//        // When
//        searchKmsLogEsService.saveLog(logMessage);
//
//        // Then
//        verify(repository).save(logMessage);
//    }
//
//    // 对异常情况的测试
//    @Test
//    void testSaveLog_WithRuntimeErrorContainingResponseBody_DoesNotThrowException() {
//        // Given
//        KmsSearchMessage logMessage = new KmsSearchMessage();
//        logMessage.setLogId("test-log-id");
//
//        doThrow(new RuntimeException("Unable to parse response body"))
//                .when(repository).save(logMessage);
//
//        // When & Then
//        assertDoesNotThrow(() -> searchKmsLogEsService.saveLog(logMessage));
//    }
//
//    // 对异常情况的测试
//    @Test
//    void testSaveLog_WithOtherRuntimeError_ThrowsException() {
//        // Given
//        KmsSearchMessage logMessage = new KmsSearchMessage();
//        logMessage.setLogId("test-log-id");
//
//        RuntimeException exception = new RuntimeException("Other error");
//        doThrow(exception).when(repository).save(logMessage);
//
//        // When & Then
//        RuntimeException thrown = assertThrows(RuntimeException.class, () -> searchKmsLogEsService.saveLog(logMessage));
//        assertEquals("Other error", thrown.getMessage());
//    }
//
//    // 对公共方法的测试
//    @Test
//    void testDeleteLogsBySearchTimeRange_WithValidRangeAndAdminUser_DeletesLogs() {
//        // Given
//        LocalDateTime startTime = LocalDateTime.of(2023, 1, 1, 0, 0, 0);
//        LocalDateTime endTime = LocalDateTime.of(2023, 1, 2, 0, 0, 0);
//
//        // Mock PlatformUser
//        PlatformUser user = mock(PlatformUser.class);
//        when(user.getIsSuperAdmin()).thenReturn(true);
//        try (MockedStatic<SsoUtil> mockedSsoUtil = mockStatic(SsoUtil.class)) {
//            mockedSsoUtil.when(SsoUtil::get).thenReturn(user);
//
//            ByQueryResponse response = mock(ByQueryResponse.class);
//            when(response.getDeleted()).thenReturn(5L);
//            when(elasticsearchRestTemplate.delete(any(Query.class), eq(KmsSearchMessage.class), any(IndexCoordinates.class)))
//                    .thenReturn(response);
//            when(elasticsearchRestTemplate.count(any(Query.class), eq(KmsSearchMessage.class), any(IndexCoordinates.class)))
//                    .thenReturn(5L);
//
//            // Mock EsPrefixUtils
//            try (MockedStatic<EsPrefixUtils> mockedEsPrefixUtils = mockStatic(EsPrefixUtils.class)) {
//                mockedEsPrefixUtils.when(() -> EsPrefixUtils.buildByPrefix(CommonConstant.KMS_SEARCH_ES_LOG_MESSAGE_INDEX_NAME))
//                        .thenReturn("test_kms_search_log_message");
//
//                // When
//                Boolean result = searchKmsLogEsService.deleteLogsBySearchTimeRange(startTime, endTime);
//
//                // Then
//                assertTrue(result);
//                verify(elasticsearchRestTemplate).delete(any(Query.class), eq(KmsSearchMessage.class), any(IndexCoordinates.class));
//                verify(elasticsearchRestTemplate).count(any(Query.class), eq(KmsSearchMessage.class), any(IndexCoordinates.class));
//            }
//        }
//    }
//
//    // 对边界条件的测试
//    @Test
//    void testDeleteLogsBySearchTimeRange_WithNullStartTime_ThrowsBizException() {
//        // Given
//        LocalDateTime endTime = LocalDateTime.of(2023, 1, 2, 0, 0, 0);
//
//        // Mock PlatformUser
//        PlatformUser user = mock(PlatformUser.class);
//        when(user.getIsSuperAdmin()).thenReturn(true);
//        try (MockedStatic<SsoUtil> mockedSsoUtil = mockStatic(SsoUtil.class)) {
//            mockedSsoUtil.when(SsoUtil::get).thenReturn(user);
//
//            // When & Then
//            BizException exception = assertThrows(BizException.class, () ->
//                    searchKmsLogEsService.deleteLogsBySearchTimeRange(null, endTime));
//            assertEquals("AA089", exception.getCode());
//            assertEquals("开始时间和结束时间必须提供", exception.getMessage());
//        }
//    }
//
//    // 对边界条件的测试
//    @Test
//    void testDeleteLogsBySearchTimeRange_WithNullEndTime_ThrowsBizException() {
//        // Given
//        LocalDateTime startTime = LocalDateTime.of(2023, 1, 1, 0, 0, 0);
//
//        // Mock PlatformUser
//        PlatformUser user = mock(PlatformUser.class);
//        when(user.getIsSuperAdmin()).thenReturn(true);
//        try (MockedStatic<SsoUtil> mockedSsoUtil = mockStatic(SsoUtil.class)) {
//            mockedSsoUtil.when(SsoUtil::get).thenReturn(user);
//
//            // When & Then
//            BizException exception = assertThrows(BizException.class, () ->
//                    searchKmsLogEsService.deleteLogsBySearchTimeRange(startTime, null));
//            assertEquals("AA089", exception.getCode());
//            assertEquals("开始时间和结束时间必须提供", exception.getMessage());
//        }
//    }
//
//    // 对异常情况的测试
//    @Test
//    void testDeleteLogsBySearchTimeRange_WithNonAdminUser_ThrowsBizException() {
//        // Given
//        LocalDateTime startTime = LocalDateTime.of(2023, 1, 1, 0, 0, 0);
//        LocalDateTime endTime = LocalDateTime.of(2023, 1, 2, 0, 0, 0);
//
//        // Mock PlatformUser
//        PlatformUser user = mock(PlatformUser.class);
//        when(user.getIsSuperAdmin()).thenReturn(false);
//        when(user.getIsManager()).thenReturn(0);
//        try (MockedStatic<SsoUtil> mockedSsoUtil = mockStatic(SsoUtil.class)) {
//            mockedSsoUtil.when(SsoUtil::get).thenReturn(user);
//
//            // When & Then
//            BizException exception = assertThrows(BizException.class, () ->
//                    searchKmsLogEsService.deleteLogsBySearchTimeRange(startTime, endTime));
//            assertEquals("AA090", exception.getCode());
//            assertEquals("非管理员用户不能删除日志", exception.getMessage());
//        }
//    }
//
//    // 对异常情况的测试
//    @Test
//    void testDeleteLogsBySearchTimeRange_WithNoDeletedLogs_ReturnsFalse() {
//        // Given
//        LocalDateTime startTime = LocalDateTime.of(2023, 1, 1, 0, 0, 0);
//        LocalDateTime endTime = LocalDateTime.of(2023, 1, 2, 0, 0, 0);
//
//        // Mock PlatformUser
//        PlatformUser user = mock(PlatformUser.class);
//        when(user.getIsSuperAdmin()).thenReturn(true);
//        try (MockedStatic<SsoUtil> mockedSsoUtil = mockStatic(SsoUtil.class)) {
//            mockedSsoUtil.when(SsoUtil::get).thenReturn(user);
//
//            ByQueryResponse response = mock(ByQueryResponse.class);
//            when(response.getDeleted()).thenReturn(0L);
//            when(elasticsearchRestTemplate.delete(any(Query.class), eq(KmsSearchMessage.class), any(IndexCoordinates.class)))
//                    .thenReturn(response);
//            when(elasticsearchRestTemplate.count(any(Query.class), eq(KmsSearchMessage.class), any(IndexCoordinates.class)))
//                    .thenReturn(0L);
//
//            // Mock EsPrefixUtils
//            try (MockedStatic<EsPrefixUtils> mockedEsPrefixUtils = mockStatic(EsPrefixUtils.class)) {
//                mockedEsPrefixUtils.when(() -> EsPrefixUtils.buildByPrefix(CommonConstant.KMS_SEARCH_ES_LOG_MESSAGE_INDEX_NAME))
//                        .thenReturn("test_kms_search_log_message");
//
//                // When
//                Boolean result = searchKmsLogEsService.deleteLogsBySearchTimeRange(startTime, endTime);
//
//                // Then
//                assertFalse(result);
//            }
//        }
//    }
//
//    // 对公共方法的测试
//    @Test
//    void testSearchLogsByLogId_WithValidLogId_ReturnsQueryKmsSearchMessageVOList() {
//        // Given
//        String logId = "test-log-id";
//
//        // Mock SearchHits
//        SearchHits<KmsSearchMessage> searchHits = mock(SearchHits.class);
//        List<org.springframework.data.elasticsearch.core.SearchHit<KmsSearchMessage>> searchHitList = new ArrayList<>();
//
//        KmsSearchMessage kmsSearchMessage = new KmsSearchMessage();
//        kmsSearchMessage.setLogId(logId);
//        kmsSearchMessage.setQuery("test query");
//
//        org.springframework.data.elasticsearch.core.SearchHit<KmsSearchMessage> searchHit =
//                new org.springframework.data.elasticsearch.core.SearchHit<>("index", "1", null, 1.0f, null, null, null, null, null, null, kmsSearchMessage);
//        searchHitList.add(searchHit);
//
//        when(searchHits.getSearchHits()).thenReturn(searchHitList);
//        when(elasticsearchRestTemplate.search(any(CriteriaQuery.class), eq(KmsSearchMessage.class), any(IndexCoordinates.class)))
//                .thenReturn(searchHits);
//
//        // Mock EsPrefixUtils
//        try (MockedStatic<EsPrefixUtils> mockedEsPrefixUtils = mockStatic(EsPrefixUtils.class)) {
//            mockedEsPrefixUtils.when(() -> EsPrefixUtils.buildByPrefix(CommonConstant.KMS_SEARCH_ES_LOG_MESSAGE_INDEX_NAME))
//                    .thenReturn("test_kms_search_log_message");
//
//            // When
//            List<QueryKmsSearchMessageVO> result = searchKmsLogEsService.searchLogsByLogId(logId);
//
//            // Then
//            assertNotNull(result);
//            assertEquals(1, result.size());
//            assertEquals(logId, result.get(0).getLogId());
//            verify(elasticsearchRestTemplate).search(any(CriteriaQuery.class), eq(KmsSearchMessage.class), any(IndexCoordinates.class));
//        }
//    }
//
//    // 对边界条件的测试
//    @Test
//    void testSearchLogsByLogId_WithNullLogId_ThrowsBizException() {
//        // When & Then
//        BizException exception = assertThrows(BizException.class, () ->
//                searchKmsLogEsService.searchLogsByLogId(null));
//        assertEquals("AA089", exception.getCode());
//        assertEquals("logId 必须提供", exception.getMessage());
//    }
//
//    // 对边界条件的测试
//    @Test
//    void testSearchLogsByLogId_WithEmptyLogId_ThrowsBizException() {
//        // When & Then
//        BizException exception = assertThrows(BizException.class, () ->
//                searchKmsLogEsService.searchLogsByLogId(""));
//        assertEquals("AA089", exception.getCode());
//        assertEquals("logId 必须提供", exception.getMessage());
//    }
//
//    // 对边界条件的测试
//    @Test
//    void testSearchLogsByLogId_WithNoResults_ReturnsEmptyList() {
//        // Given
//        String logId = "test-log-id";
//
//        // Mock SearchHits
//        SearchHits<KmsSearchMessage> searchHits = mock(SearchHits.class);
//        when(searchHits.getSearchHits()).thenReturn(Collections.emptyList());
//        when(elasticsearchRestTemplate.search(any(CriteriaQuery.class), eq(KmsSearchMessage.class), any(IndexCoordinates.class)))
//                .thenReturn(searchHits);
//
//        // Mock EsPrefixUtils
//        try (MockedStatic<EsPrefixUtils> mockedEsPrefixUtils = mockStatic(EsPrefixUtils.class)) {
//            mockedEsPrefixUtils.when(() -> EsPrefixUtils.buildByPrefix(CommonConstant.KMS_SEARCH_ES_LOG_MESSAGE_INDEX_NAME))
//                    .thenReturn("test_kms_search_log_message");
//
//            // When
//            List<QueryKmsSearchMessageVO> result = searchKmsLogEsService.searchLogsByLogId(logId);
//
//            // Then
//            assertNotNull(result);
//            assertTrue(result.isEmpty());
//        }
//    }
//
//    // 对公共方法的测试
//    @Test
//    void testGetTopQueries_CallsGetTopQueriesByTenantAndTimeRange() {
//        // Given
//        String dimension = "test-dimension";
//        LocalDateTime startTime = LocalDateTime.of(2023, 1, 1, 0, 0, 0);
//        LocalDateTime endTime = LocalDateTime.of(2023, 1, 2, 0, 0, 0);
//
//        List<TermQueryCount> expectedResult = new ArrayList<>();
//        TermQueryCount termQueryCount = new TermQueryCount();
//        termQueryCount.setQuery("test query");
//        termQueryCount.setCount(5L);
//        expectedResult.add(termQueryCount);
//
//        when(repository.findTopQueriesByTenantAndTimeRange("test-tenant", startTime, endTime, 50))
//                .thenReturn(expectedResult);
//
//        try (MockedStatic<RequestContext> mockedRequestContext = mockStatic(RequestContext.class)) {
//            mockedRequestContext.when(RequestContext::getTenantId).thenReturn("test-tenant");
//
//            // When
//            List<TermQueryCount> result = searchKmsLogEsService.getTopQueries(dimension, startTime, endTime);
//
//            // Then
//            assertNotNull(result);
//            assertEquals(1, result.size());
//            assertEquals("test query", result.get(0).getQuery());
//            assertEquals(5L, result.get(0).getCount());
//            verify(repository).findTopQueriesByTenantAndTimeRange("test-tenant", startTime, endTime, 50);
//        }
//    }
//
//    // 对公共方法的测试
//    @Test
//    void testGetTopQueriesByTenantAndTimeRange_WithValidParameters_ReturnsTermQueryCountList() {
//        // Given
//        String tenantId = "test-tenant";
//        LocalDateTime startTime = LocalDateTime.of(2023, 1, 1, 0, 0, 0);
//        LocalDateTime endTime = LocalDateTime.of(2023, 1, 2, 0, 0, 0);
//        int limit = 10;
//
//        List<TermQueryCount> expectedResult = new ArrayList<>();
//        TermQueryCount termQueryCount = new TermQueryCount();
//        termQueryCount.setQuery("test query");
//        termQueryCount.setCount(5L);
//        expectedResult.add(termQueryCount);
//
//        when(repository.findTopQueriesByTenantAndTimeRange(tenantId, startTime, endTime, limit))
//                .thenReturn(expectedResult);
//
//        // When
//        List<TermQueryCount> result = searchKmsLogEsService.getTopQueriesByTenantAndTimeRange(tenantId, startTime, endTime, limit);
//
//        // Then
//        assertNotNull(result);
//        assertEquals(1, result.size());
//        assertEquals("test query", result.get(0).getQuery());
//        assertEquals(5L, result.get(0).getCount());
//        verify(repository).findTopQueriesByTenantAndTimeRange(tenantId, startTime, endTime, limit);
//    }
//
//    // 使用Mock对象测试依赖项
//    @Test
//    void testEsPrefixUtils_UsedInIndexCoordinates() {
//        // Given
//        KmsSearchMessage logMessage = new KmsSearchMessage();
//        logMessage.setLogId("test-log-id");
//
//        try (MockedStatic<EsPrefixUtils> mockedEsPrefixUtils = mockStatic(EsPrefixUtils.class)) {
//            mockedEsPrefixUtils.when(() -> EsPrefixUtils.buildByPrefix(CommonConstant.KMS_SEARCH_ES_LOG_MESSAGE_INDEX_NAME))
//                    .thenReturn("test_prefix_kms_search_log_message");
//
//            // When
//            searchKmsLogEsService.saveLog(logMessage);
//
//            // Then
//            verify(repository).save(logMessage);
//        }
//    }
//
//    // 验证方法返回值和副作用
//    @Test
//    void testDeleteLogsBySearchTimeRange_ManagerUserWithIsManagerOne_CanDeleteLogs() {
//        // Given
//        LocalDateTime startTime = LocalDateTime.of(2023, 1, 1, 0, 0, 0);
//        LocalDateTime endTime = LocalDateTime.of(2023, 1, 2, 0, 0, 0);
//
//        // Mock PlatformUser
//        PlatformUser user = mock(PlatformUser.class);
//        when(user.getIsSuperAdmin()).thenReturn(false);
//        when(user.getIsManager()).thenReturn(1); // Manager user
//        try (MockedStatic<SsoUtil> mockedSsoUtil = mockStatic(SsoUtil.class)) {
//            mockedSsoUtil.when(SsoUtil::get).thenReturn(user);
//
//            ByQueryResponse response = mock(ByQueryResponse.class);
//            when(response.getDeleted()).thenReturn(3L);
//            when(elasticsearchRestTemplate.delete(any(Query.class), eq(KmsSearchMessage.class), any(IndexCoordinates.class)))
//                    .thenReturn(response);
//            when(elasticsearchRestTemplate.count(any(Query.class), eq(KmsSearchMessage.class), any(IndexCoordinates.class)))
//                    .thenReturn(3L);
//
//            // Mock EsPrefixUtils
//            try (MockedStatic<EsPrefixUtils> mockedEsPrefixUtils = mockStatic(EsPrefixUtils.class)) {
//                mockedEsPrefixUtils.when(() -> EsPrefixUtils.buildByPrefix(CommonConstant.KMS_SEARCH_ES_LOG_MESSAGE_INDEX_NAME))
//                        .thenReturn("test_kms_search_log_message");
//
//                // When
//                Boolean result = searchKmsLogEsService.deleteLogsBySearchTimeRange(startTime, endTime);
//
//                // Then
//                assertTrue(result);
//                verify(elasticsearchRestTemplate).delete(any(Query.class), eq(KmsSearchMessage.class), any(IndexCoordinates.class));
//            }
//        }
//    }
//}
