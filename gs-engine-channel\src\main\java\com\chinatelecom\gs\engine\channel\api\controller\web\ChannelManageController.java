package com.chinatelecom.gs.engine.channel.api.controller.web;


import cn.hutool.core.bean.BeanUtil;
import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.channel.api.converter.VOBeanConvertor;
import com.chinatelecom.gs.engine.channel.api.vo.ChannelInfoVO;
import com.chinatelecom.gs.engine.channel.api.vo.ChannelRobotConfigVO;
import com.chinatelecom.gs.engine.channel.api.vo.QywxConfigVO;
import com.chinatelecom.gs.engine.channel.api.vo.WebLinkConfigVO;
import com.chinatelecom.gs.engine.channel.common.enums.ChannelTypeEnum;
import com.chinatelecom.gs.engine.channel.dao.repository.ChannelInfoRepository;
import com.chinatelecom.gs.engine.channel.manage.*;
import com.chinatelecom.gs.engine.channel.service.dto.ChannelInfoDTO;
import com.chinatelecom.gs.engine.channel.service.dto.QywxConfigDTO;
import com.chinatelecom.gs.engine.channel.service.dto.RobotConfigDTO;
import com.chinatelecom.gs.engine.channel.service.dto.WebLinkConfigDTO;
import com.chinatelecom.gs.engine.common.auth.KsMenuConfig;
import com.chinatelecom.gs.engine.common.auth.MenuConfig;
import com.chinatelecom.gs.engine.common.auth.PermissionTag;
import com.chinatelecom.gs.engine.common.constants.Apis;
import com.chinatelecom.gs.engine.common.context.Constants;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.kms.dto.SessionFileUploadParam;
import com.chinatelecom.gs.engine.kms.sdk.vo.file.SessionFileUploadVO;
import com.chinatelecom.gs.engine.kms.service.SessionFileService;
import com.chinatelecom.gs.engine.robot.manage.info.service.AgentBasicConfigService;
import com.chinatelecom.gs.engine.robot.sdk.dto.Result;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2023/12/25 14:28
 * @description
 */
@Slf4j
@Tag(name = "渠道管理")
@PermissionTag(code = {MenuConfig.ROBOT, KsMenuConfig.BOT})
@RestController
@RequestMapping(value = Constants.ROBOT_PREFIX + Constants.WEB_PREFIX + "/channel", produces = MediaType.APPLICATION_JSON_VALUE)
public class ChannelManageController {

    @Resource
    private ChannelInfoRepository channelInfoRepository;

    @Resource
    private QywxConfigService qywxConfigService;

    @Resource
    private WebLinkConfigService webLinkConfigService;

    @Resource
    private RobotConfigService robotConfigService;

    @Autowired
    private AgentBasicConfigService agentBasicConfigService;

    @Autowired
    private ChannelConfigService channelConfigService;

    @Autowired
    private OffSiteChannelSwitchCacheService offSiteChannelSwitchCacheService;

    @Resource
    private SessionFileService sessionFileService;

    @Operation(summary = "指定渠道类型的开关", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "指定渠道类型的开关", groupName = "渠道管理")
    @PostMapping
    @AuditLog(businessType = "渠道管理", operType = "指定渠道类型的开关", operDesc = "指定渠道类型的开关", objId="#agentCode")
    public Result<Boolean> channelOperateAction(@RequestParam("agentCode") String agentCode, @RequestParam("channelType") String channelType, @RequestParam("action") int action) {
        Long editVersion = agentBasicConfigService.getAgentEditVersion(agentCode);
        if (editVersion <= 1) {
            throw new BizException("AD011", "当前无发布的bot");
        }
        boolean open = offSiteChannelSwitchCacheService.offsiteOffSwitch(agentCode, editVersion);
        if (!open) {
            throw new BizException("AD012", "该bot站外部署开关未打开");
        }
        boolean batchEnableOperateResult = channelInfoRepository.channelOperateEnable(agentCode, channelType, action == 1);
        return Result.success(batchEnableOperateResult);
    }


    @Operation(summary = "查询渠道信息列表", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "查询渠道信息列表", groupName = "渠道管理")
    @GetMapping(value = "/all")
    @AuditLog(businessType = "渠道管理", operType = "查询渠道信息列表", operDesc = "查询渠道信息列表", objId="#agentCode")
    public Result<List<ChannelInfoVO>> getChannel(@RequestParam("agentCode") @NotBlank(message = "agent code不能为空") String agentCode, @RequestParam(value = "channelType", required = false) String channelType) {
        List<String> channelTypeList;
        if (StringUtils.hasLength(channelType)) {
            channelTypeList = new ArrayList<>();
            channelTypeList.add(channelType);
        } else {
            channelTypeList = ChannelTypeEnum.OFF_SITE_CHANNEL;
        }
        List<ChannelInfoDTO> channelInfoList = channelInfoRepository.getChannelInfoList(agentCode, channelTypeList);
        //更新每个渠道的渠道信息
        for (ChannelInfoDTO channelInfo : channelInfoList) {
            boolean isConfigExist = channelConfigService.isConfigExist(channelInfo);
            channelInfo.setConfigExist(isConfigExist);
        }
        return Result.success(channelInfoList.stream().map(o -> {
            ChannelInfoVO channelInfoVO = new ChannelInfoVO();
            BeanUtil.copyProperties(o, channelInfoVO);
            return channelInfoVO;
        }).collect(Collectors.toList()));
    }

    @Operation(summary = "生成一组企业微信token", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "生成一组企业微信token", groupName = "渠道管理")
    @GetMapping(value = "qywx/generateToken")
    @AuditLog(businessType = "渠道管理", operType = "生成一组企业微信token", operDesc = "生成一组企业微信token", objId="null")
    public Result<Map<String, String>> generateQywxToken() {
        HashMap<String, String> tokenMap = new HashMap<>(2);
        tokenMap.put("token", RandomStringUtils.randomAlphanumeric(32));
        tokenMap.put("encodingAESKey", RandomStringUtils.randomAlphanumeric(43));
        return Result.success(tokenMap);
    }

    @Operation(summary = "企业微信应用消息接口配置", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "企业微信应用消息接口配置", groupName = "渠道管理")
    @PostMapping(value = "/qywx/{channelId}/{agentCode}/createOrUpdate", produces = MediaType.APPLICATION_JSON_VALUE)
    @AuditLog(businessType = "渠道管理", operType = "企业微信应用消息接口配置", operDesc = "企业微信应用消息接口配置", objId="#agentCode")
    public Result<Boolean> createQywxConfig(@PathVariable("channelId") String channelId, @PathVariable("agentCode") String agentCode, @Valid @RequestBody QywxConfigVO configVO) {
        QywxConfigDTO configDTO = VOBeanConvertor.INSTANCE.convert(configVO);
        return Result.success(qywxConfigService.addConfig(channelId, agentCode, configDTO));
    }

    @Operation(summary = "获取企业微信应用消息接口配置", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "获取企业微信应用消息接口配置", groupName = "渠道管理")
    @GetMapping(value = "/qywx/{channelId}/{agentCode}", produces = MediaType.APPLICATION_JSON_VALUE)
    @AuditLog(businessType = "渠道管理", operType = "获取企业微信应用消息接口配置", operDesc = "获取企业微信应用消息接口配置", objId="#agentCode")
    public Result<QywxConfigVO> getQywxConfig(@PathVariable("channelId") String channelId, @PathVariable("agentCode") String agentCode) {
        QywxConfigDTO configDTO = qywxConfigService.getQywxConfig(channelId, agentCode);
        return Result.success(configDTO != null ? VOBeanConvertor.INSTANCE.convert(configDTO) : new QywxConfigVO());
    }

    @Operation(summary = "渠道机器人配置", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "渠道机器人配置", groupName = "渠道管理")
    @PostMapping(value = "/{channelId}/robotconfig", produces = MediaType.APPLICATION_JSON_VALUE)
    @AuditLog(businessType = "渠道管理", operType = "渠道机器人配置", operDesc = "渠道机器人配置", objId="#channelId")
    public Result<Boolean> addChannelRobotConfig(@PathVariable("channelId") String channelId, @Valid @RequestBody ChannelRobotConfigVO configVO) {
        RobotConfigDTO configDTO = VOBeanConvertor.INSTANCE.convert(configVO);
        return Result.success(robotConfigService.addConfig(channelId, configDTO));
    }

    @Operation(summary = "查询机器人配置信息", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "查询机器人配置信息", groupName = "渠道管理")
    @GetMapping(value = "/{channelId}/{robotCode}/robotconfig", produces = MediaType.APPLICATION_JSON_VALUE)
    @AuditLog(businessType = "渠道管理", operType = "查询机器人配置信息", operDesc = "查询机器人配置信息", objId="#robotCode")
    public Result<ChannelRobotConfigVO> getChannelRobotConfig(@PathVariable("channelId") String channelId, @PathVariable("robotCode") String robotCode) {
        RobotConfigDTO robotConfigDTO = robotConfigService.getRobotConfig(channelId, robotCode);
        return Result.success(VOBeanConvertor.INSTANCE.convert(robotConfigDTO));
    }

    @Operation(summary = "网页链接渠道接口配置", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "网页链接渠道接口配置", groupName = "渠道管理")
    @PostMapping(value = "/link/{channelId}/{agentCode}/create", produces = MediaType.APPLICATION_JSON_VALUE)
    @AuditLog(businessType = "渠道管理", operType = "网页链接渠道接口配置", operDesc = "网页链接渠道接口配置", objId="#agentCode")
    public Result<Boolean> createLinkConfig(@PathVariable("channelId") String channelId, @PathVariable("agentCode") String agentCode, @Valid @RequestBody WebLinkConfigVO configVO) {
        WebLinkConfigDTO configDTO = VOBeanConvertor.INSTANCE.convert(configVO);
        return Result.success(webLinkConfigService.addConfig(channelId, agentCode, configDTO));
    }

    @Operation(summary = "获取网页链接配置信息", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "获取网页链接配置信息", groupName = "渠道管理")
    @GetMapping(value = "/link/{channelId}/{agentCode}", produces = MediaType.APPLICATION_JSON_VALUE)
    @AuditLog(businessType = "渠道管理", operType = "获取网页链接配置信息", operDesc = "获取网页链接配置信息", objId="#agentCode")
    public Result<WebLinkConfigVO> getLinkConfig(@PathVariable("channelId") String channelId, @PathVariable("agentCode") String agentCode) {
        WebLinkConfigDTO configDTO = webLinkConfigService.getWebLinkConfig(channelId, agentCode);
        return Result.success(configDTO != null ? VOBeanConvertor.INSTANCE.convert(configDTO) : new WebLinkConfigVO());
    }

    @Operation(summary = "会话文件上传", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "会话文件上传", groupName = "对话框挂载文件")
    @PostMapping(value = Apis.UPLOAD, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @AuditLog(businessType = "会话文件上传", operType = "会话文件上传", operDesc = "会话文件上传", objId="#param.fileCode")
    public Result<SessionFileUploadVO> upload(@Valid SessionFileUploadParam param) {
        return Result.success(sessionFileService.upload(param));
    }
}
