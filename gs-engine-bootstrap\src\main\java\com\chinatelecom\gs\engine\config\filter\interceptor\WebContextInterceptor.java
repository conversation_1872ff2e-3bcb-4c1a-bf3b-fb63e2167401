package com.chinatelecom.gs.engine.config.filter.interceptor;

import com.chinatelecom.cloud.platform.client.rpc.PlatformUser;
import com.chinatelecom.cloud.platform.client.util.SsoUtil;
import com.chinatelecom.gs.engine.common.config.property.GsGlobalConfig;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.context.RequestInfo;
import com.chinatelecom.gs.engine.common.utils.InterceptorUtils;
import com.chinatelecom.gs.engine.common.utils.UserInfoUtils;
import com.chinatelecom.gs.engine.config.filter.BaseHandlerInterceptor;
import com.chinatelelcom.gs.engine.sdk.common.enums.RequestSourceType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024年12月31日
 */
@Component
@Slf4j
public class WebContextInterceptor implements BaseHandlerInterceptor {

    @Resource
    private GsGlobalConfig gsGlobalConfig;

    @Value("${platform.super.tenant:SuperAis}")
    private String superTenant;


    public boolean preHandle(ServletRequest request, ServletResponse response) throws Exception {
        RequestInfo requestInfo = new RequestInfo();
        RequestContext.set(requestInfo);
        requestInfo.setTenant(true);
        requestInfo.setEntry("web");
        requestInfo.setRequestSourceType(RequestSourceType.WEB);
        if (gsGlobalConfig.getMockRequest().getEnabled()) {
            requestInfo.setUserId(gsGlobalConfig.getMockRequest().getId());
            requestInfo.setUserName(gsGlobalConfig.getMockRequest().getName());
            requestInfo.setTenantId(gsGlobalConfig.getMockRequest().getTenantId());
            requestInfo.setAppCode(gsGlobalConfig.getMockRequest().getAppCode());
            requestInfo.setIsSuperTenant(false);
            InterceptorUtils.setSourceType(gsGlobalConfig, (HttpServletRequest) request, requestInfo);
        } else {
            PlatformUser platformUser = SsoUtil.get();
            if (platformUser == null) {
                log.error("获取用户信息失败");
                InterceptorUtils.writeError((HttpServletResponse) response, "A0006", "用户未登录，获取登录信息失败");
                return false;
            }
            requestInfo.setUserId(platformUser.getUserId());
            requestInfo.setUserName(platformUser.getName());
            requestInfo.setTenantId(platformUser.getCorpCode());
            requestInfo.setIsSuperTenant(superTenant.equals(platformUser.getCorpCode()));
            requestInfo.setTeam(UserInfoUtils.getUserTeam());
            InterceptorUtils.setSourceType(gsGlobalConfig, (HttpServletRequest) request, requestInfo);
            boolean isAdmin = UserInfoUtils.isAdmin(requestInfo.getTenantId(), requestInfo.getUserId(), requestInfo.getAppSourceType());
            requestInfo.setIsAdmin(isAdmin);
            platformUser.setIsAdmin(isAdmin);
            requestInfo.setIsManager(Objects.equals(platformUser.getIsManager(), RequestInfo.IS_MANAGER));
            InterceptorUtils.setAppCode((HttpServletRequest) request, requestInfo);
        }
        return true;
    }


    /**
     * 完成后调用
     *
     * @param request
     * @param response
     * @param ex
     * @throws Exception
     */
    public void afterCompletion(ServletRequest request, ServletResponse response, Exception ex) throws Exception {
        RequestContext.remove();
    }
}
