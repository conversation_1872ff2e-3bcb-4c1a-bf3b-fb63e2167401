package com.chinatelecom.gs.engine.core.model.toolkit.adapter.mindie;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.chinatelecom.gs.engine.common.enums.LogStatusEnum;
import com.chinatelecom.gs.engine.core.model.toolkit.AbstractLLMClient;
import com.chinatelecom.gs.engine.core.model.toolkit.ContentLLMResponse;
import com.chinatelecom.gs.engine.core.model.toolkit.StreamingResponseHandler;
import com.chinatelecom.gs.engine.core.model.toolkit.Transformer;
import com.chinatelecom.gs.engine.core.model.toolkit.core.dto.LLMRequest;
import com.chinatelecom.gs.engine.core.model.toolkit.core.dto.MessageRoleEnum;
import com.chinatelecom.gs.engine.core.model.toolkit.core.dto.Model;
import com.chinatelecom.gs.engine.core.model.toolkit.core.dto.Response;
import com.chinatelecom.gs.engine.core.model.toolkit.core.internal.URLUtils;
import com.chinatelecom.gs.engine.core.model.toolkit.transformers.ParseContext;
import com.chinatelecom.gs.engine.core.model.toolkit.transformers.TransformerHolder;
import com.chinatelecom.gs.engine.core.sdk.enums.LLMMessageBizCode;
import com.chinatelecom.gs.engine.core.sdk.vo.llm.Function;
import com.chinatelecom.gs.engine.core.sdk.vo.llm.LLMMessage;
import com.chinatelecom.gs.engine.core.sdk.vo.llm.ToolCall;
import com.chinatelecom.gs.engine.core.sdk.vo.llm.WrapLLMMessage;
import com.chinatelelcom.gs.engine.sdk.common.utils.SpringContextUtils;
import com.google.gson.FieldNamingPolicy;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import lombok.Builder;
import lombok.SneakyThrows;
import okhttp3.OkHttpClient;
import okhttp3.ResponseBody;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.URL;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;

public class MindIELLMClient extends AbstractLLMClient {

    private final MindIEApi mindIEApi;

    private String appKey;

    private String appSign;

    private String url;

    private static final Gson GSON = new GsonBuilder().excludeFieldsWithModifiers().setFieldNamingPolicy(FieldNamingPolicy.LOWER_CASE_WITH_UNDERSCORES).create();

    private static final Logger log = LoggerFactory.getLogger(MindIELLMClient.class);

    @SneakyThrows
    @Builder
    public MindIELLMClient(Model.ModelConfig modelConfig) {
        OkHttpClient okHttpClient = buildOkHttpClient(modelConfig);

        String[] splitUrl = URLUtils.splitURL(modelConfig.getBaseUrl());

        Retrofit retrofit = new Retrofit.Builder()
                .baseUrl(splitUrl[0])
                .client(okHttpClient)
                .addConverterFactory(GsonConverterFactory.create(GSON))
                .build();

        this.mindIEApi = retrofit.create(MindIEApi.class);
        this.appKey = modelConfig.getApi();
        this.appSign = modelConfig.getSecret();
        this.url = new URL(modelConfig.getBaseUrl()).toString();
        this.modelConfig = modelConfig;
    }

    /**
     * 同步chat (string)
     *
     * @param request
     * @param <R>
     * @return string
     */
    @SneakyThrows
    @Override
    public <R> Response<String> completion(LLMRequest<R> request) {
        if (Boolean.TRUE.equals(request.getLlmModelInfo().getNativeCall())) {
            //原生调用
            String modelUrl = new URL(request.getLlmModelInfo().getNativeCallUrl()).toString();
            MindIERequest mindIERequest = MindIEConvert.toCompletionRequest(request, false,false);
            LocalDateTime startTime = LocalDateTime.now();
            try {
                retrofit2.Response<MindIEResponse> response = mindIEApi.completion(modelUrl, this.appKey, this.appSign, mindIERequest).execute();
                MindIEResponse mindIEResponse = response.body();
                log.info("【llm】 推理服务返回completion接口 {}", JSON.toJSONString(mindIEResponse));
                sendTraceMessage(mindIERequest, mindIEResponse, LogStatusEnum.SUCCESS, startTime, LocalDateTime.now(), null);
                return Response.from(getContent(mindIEResponse));
            } catch (Exception e) {
                sendTraceMessage(mindIERequest, null, LogStatusEnum.FAILED, startTime, LocalDateTime.now(), null);
                throw new RuntimeException(e);
            }
        } else {
            //通用调用
            String modelUrl = new URL(request.getLlmModelInfo().getModelUrl()).toString();
            MindIEChatRequest mindIEChatRequest = MindIEConvert.toChatCompletionRequest(request, false);
            LocalDateTime startTime = LocalDateTime.now();
            try {
                retrofit2.Response<MindIEChatResponse> response = mindIEApi.chatCompletion(modelUrl, this.appKey, this.appSign, mindIEChatRequest).execute();
                log.info("【llm】 推理服务返回chat/completion接口 {}", JSON.toJSONString(response.body()));
                sendTraceMessage(mindIEChatRequest, response.body(), LogStatusEnum.SUCCESS, startTime, LocalDateTime.now(), null);
                return Response.from(getContent(response.body()));
            } catch (Exception e) {
                sendTraceMessage(mindIEChatRequest, null, LogStatusEnum.FAILED, startTime, LocalDateTime.now(), null);
                throw new RuntimeException(e);
            }
        }
    }

    /**
     * 同步chat (LLMMessage)
     *
     * @param request
     * @param <R>
     * @return LLMMessage
     */
    @Override
    @SneakyThrows
    public <R> Response<WrapLLMMessage> completionMessage(LLMRequest<R> request) {
        if (Boolean.TRUE.equals(request.getLlmModelInfo().getNativeCall())) {
            //原生调用
            String modelUrl = new URL(request.getLlmModelInfo().getNativeCallUrl()).toString();
            MindIERequest mindIERequest = MindIEConvert.toCompletionRequest(request, false, false);
            LocalDateTime startTime = LocalDateTime.now();
            try {
                retrofit2.Response<MindIEResponse> response = mindIEApi.completion(modelUrl, this.appKey, this.appSign, mindIERequest).execute();
                MindIEResponse mindIEResponse = response.body();
                log.info("【llm】 推理服务返回completion接口 {}", JSON.toJSONString(mindIEResponse));
                sendTraceMessage(mindIERequest, mindIEResponse, LogStatusEnum.SUCCESS, startTime, LocalDateTime.now(), null);
                WrapLLMMessage wrapLLMMessage = BeanUtil.toBean(getMessage(request, mindIEResponse), WrapLLMMessage.class);
                wrapLLMMessage.setCode(LLMMessageBizCode.SUCCESS.getCode());
                return Response.from(wrapLLMMessage);
            } catch (Exception e) {
                sendTraceMessage(mindIERequest, null, LogStatusEnum.FAILED, startTime, LocalDateTime.now(), null);
                throw new RuntimeException(e);
            }
        } else {
            //通用调用
            String modelUrl = new URL(request.getLlmModelInfo().getModelUrl()).toString();
            MindIEChatRequest mindIEChatRequest = MindIEConvert.toChatCompletionRequest(request, false);
            LocalDateTime startTime = LocalDateTime.now();
            try {
                retrofit2.Response<MindIEChatResponse> response = mindIEApi.chatCompletion(modelUrl, this.appKey, this.appSign, mindIEChatRequest).execute();
                log.info("【llm】 推理服务返回chat/completion接口 {}", JSON.toJSONString(response.body()));
                sendTraceMessage(mindIEChatRequest, response.body(), LogStatusEnum.SUCCESS, startTime, LocalDateTime.now(), null);
                if (Objects.nonNull(response.body())) {
                    WrapLLMMessage wrapLLMMessage = BeanUtil.toBean(getMessage(response.body()), WrapLLMMessage.class);
                    wrapLLMMessage.setCode(LLMMessageBizCode.SUCCESS.getCode());
                    return Response.from(wrapLLMMessage);
                }
                return null;
            } catch (Exception e) {
                sendTraceMessage(mindIEChatRequest, null, LogStatusEnum.FAILED, startTime, LocalDateTime.now(), null);
                throw new RuntimeException(e);
            }
        }
    }

    /**
     * 异步chat (string)
     *
     * @param request
     * @param handler
     * @param <R>
     */
    @Override
    public <R> void streamingCompletion(LLMRequest<R> request, StreamingResponseHandler<String> handler) {
        if (Boolean.TRUE.equals(request.getLlmModelInfo().getNativeCall())) {
            //原生调用
            this.innerStreamingCompletion(request, handler);
        } else {
            //通用调用
            this.innerChatStreamingCompletion(request, handler);
        }
    }

    /**
     * 异步chat (LLMMessage)
     *
     * @param request
     * @param handler
     * @param <R>
     */
    @Override
    public <R> void streamingCompletionMessage(LLMRequest<R> request, StreamingResponseHandler<WrapLLMMessage> handler) {
        if (Boolean.TRUE.equals(request.getLlmModelInfo().getNativeCall())) {
            //原生调用
            this.innerStreamingMessageCompletion(request, handler, false);
        } else {
            //通用调用
            this.innerStreamingMessageChatCompletion(request, handler);
        }
    }

    /**
     * 同步原生
     *
     * @param request
     * @param <R>
     * @return
     */
    @Override
    public <R> Response<WrapLLMMessage> directCompletionMessage(LLMRequest<R> request) {
        try {
            String modelUrl = new URL(request.getLlmModelInfo().getNativeCallUrl()).toString();
            MindIERequest mindIERequest = MindIEConvert.toCompletionRequest(request, false,true);
            retrofit2.Response<MindIEResponse> response = mindIEApi.completion(modelUrl, this.appKey, this.appSign, mindIERequest).execute();
            MindIEResponse mindIEResponse = response.body();
            log.info("【llm】 推理服务返回completion接口 {}", JSON.toJSONString(mindIEResponse));
            if (Objects.isNull(mindIEResponse) || CollectionUtils.isEmpty(mindIEResponse.getText())) {
                return null;
            }
            if (StringUtils.isEmpty(mindIEResponse.getText().get(0))) {
                return null;
            }
            LLMMessage llmMessage = new LLMMessage();
            llmMessage.setContent(mindIEResponse.getText().get(0));
            llmMessage.setRole(MessageRoleEnum.assistant.name());
            WrapLLMMessage wrapLLMMessage = BeanUtil.toBean(llmMessage, WrapLLMMessage.class);
            wrapLLMMessage.setCode(LLMMessageBizCode.SUCCESS.getCode());
            return Response.from(wrapLLMMessage);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    /**
     * 流式原生
     *
     * @param request
     * @param handler
     * @param <R>
     */
    @Override
    public <R> void streamingDirectCompletionMessage(LLMRequest<R> request, StreamingResponseHandler<WrapLLMMessage> handler) {
        this.innerStreamingMessageCompletion(request, handler, true);
    }

    /**
     * 原生调用
     *
     * @param request
     * @param handler
     */
    @SneakyThrows
    private void innerStreamingCompletion(LLMRequest request, StreamingResponseHandler<String> handler) {
        String modelUrl = new URL(request.getLlmModelInfo().getNativeCallUrl()).toString();
        MindIERequest mindIERequest = MindIEConvert.toCompletionRequest(request, true, false);
        LocalDateTime startTime = LocalDateTime.now();
        AtomicReference<LocalDateTime> firstTokenTime = new AtomicReference<>();
        mindIEApi.streamingCompletion(modelUrl, this.appKey, this.appSign, mindIERequest).enqueue(new Callback<ResponseBody>() {
            @Override
            public void onResponse(Call<ResponseBody> call, retrofit2.Response<ResponseBody> response) {
                StringBuilder content = new StringBuilder();

                try (InputStream is = response.body().byteStream()) {
                    while (true) {
                        byte[] bytes = new byte[1024];
                        int len = is.read(bytes);
                        if (len <= 0) {
                            log.info("===========流式输出结束===========");
                            handler.onComplete(Response.from(content.toString()));
                            break;
                        }
                        if (Objects.isNull(firstTokenTime.get())) {
                            firstTokenTime.set(LocalDateTime.now());
                        }
                        String partialResponse = new String(bytes, 0, len);
                        log.debug("【llm】流式结果 {}", partialResponse);
                        String partialContent = parsePartialContent(partialResponse);
                        if (StringUtils.isEmpty(partialContent)) {
                            continue;
                        }
                        content.append(partialContent);
                        handler.onNext(call, partialContent);
                    }
                    sendTraceMessage(mindIERequest, new ContentLLMResponse(content.toString()), LogStatusEnum.SUCCESS, startTime, LocalDateTime.now(), null);
                } catch (Exception e) {
                    sendTraceMessage(mindIERequest, new ContentLLMResponse(content.toString()), LogStatusEnum.FAILED, startTime, LocalDateTime.now(), null);
                    handler.onError(e, Response.from(content.toString()));
                }
            }

            @Override
            public void onFailure(Call<ResponseBody> call, Throwable throwable) {
                sendTraceMessage(mindIERequest, null, LogStatusEnum.FAILED, startTime, LocalDateTime.now(), null);
                handler.onError(throwable, null);
            }
        });
    }

    private String parsePartialContent(String partialResponse) {
        try {
            MindIEResponse directResponse = JSON.parseObject(partialResponse, MindIEResponse.class);
            return directResponse.getText().get(0);
        } catch (Exception e) {
            log.error("gson fail ", e);
            return null;
        }
    }

    /**
     * Chat调用
     *
     * @param request
     * @param handler
     */
    @SneakyThrows
    private void innerChatStreamingCompletion(LLMRequest request, StreamingResponseHandler<String> handler) {
        String modelUrl = new URL(request.getLlmModelInfo().getModelUrl()).toString();
        MindIEChatRequest mindIEChatRequest = MindIEConvert.toChatCompletionRequest(request, true);
        LocalDateTime startTime = LocalDateTime.now();
        AtomicReference<LocalDateTime> firstTokenTime = new AtomicReference<>();
        mindIEApi.streamingChatCompletion(modelUrl, this.appKey, this.appSign, mindIEChatRequest).enqueue(new Callback<ResponseBody>() {
            @Override
            public void onResponse(Call<ResponseBody> call, retrofit2.Response<ResponseBody> response) {
                StringBuilder content = new StringBuilder();

                try (BufferedReader reader = new BufferedReader(new InputStreamReader(response.body().byteStream()))) {
                    String line;
                    int index = 0;
                    while ((line = reader.readLine()) != null) {
                        if (StringUtils.isNotBlank(line)) {
                            if (Objects.isNull(firstTokenTime.get())) {
                                firstTokenTime.set(LocalDateTime.now());
                            }
                            line = line.replaceFirst("data: ", "");
                            if (!line.equalsIgnoreCase("[DONE]")) {
                                MindIEChatResponse mindIEChatResponse = GSON.fromJson(line, MindIEChatResponse.class);
                                String partialContent = getStreamContent(mindIEChatResponse);
                                if (index != 0 || !StringUtils.isEmpty(partialContent)) {
                                    if (!StringUtils.isEmpty(partialContent)) {
                                        content.append(partialContent);
                                        handler.onNext(call, partialContent);
                                    }
                                }
                            }
                        }
                        index++;
                    }
                    log.info("===========流式输出结束===========");
                    sendTraceMessage(mindIEChatRequest, new ContentLLMResponse(content.toString()), LogStatusEnum.SUCCESS, startTime, LocalDateTime.now(), firstTokenTime.get());
                    handler.onComplete(Response.from(content.toString()));
                } catch (IOException e) {
                    sendTraceMessage(mindIEChatRequest, new ContentLLMResponse(content.toString()), LogStatusEnum.FAILED, startTime, LocalDateTime.now(), firstTokenTime.get());
                    handler.onError(e, Response.from(content.toString()));
                }
            }

            @Override
            public void onFailure(Call<ResponseBody> call, Throwable throwable) {
                sendTraceMessage(mindIEChatRequest, null, LogStatusEnum.FAILED, startTime, LocalDateTime.now(), null);
                handler.onError(throwable, null);
            }
        });
    }


    /**
     * 原生流式调用 （LLMessage)
     *
     * @param request
     * @param handler
     */
    @SneakyThrows
    private void innerStreamingMessageCompletion(LLMRequest request, StreamingResponseHandler<WrapLLMMessage> handler,boolean direct) {
        String modelUrl = new URL(request.getLlmModelInfo().getNativeCallUrl()).toString();
        MindIERequest mindIERequest = MindIEConvert.toCompletionRequest(request, true, direct);
        mindIEApi.streamingCompletion(modelUrl, this.appKey, this.appSign, mindIERequest).enqueue(new Callback<ResponseBody>() {
            @Override
            public void onResponse(Call<ResponseBody> call, retrofit2.Response<ResponseBody> response) {
                StringBuilder content = new StringBuilder();
                LLMMessage lastMessage = null;
                ParseContext parseContext = new ParseContext();
                try (InputStream is = response.body().byteStream()) {
                    while (true) {
                        byte[] bytes = new byte[1024];
                        int len = is.read(bytes);
                        if (len <= 0) {
                            log.info("===========流式输出结束===========");
                            if (Objects.nonNull(lastMessage) && CollectionUtils.isNotEmpty(lastMessage.getToolCalls())) {
                                WrapLLMMessage wrapLLMMessage = BeanUtil.toBean(lastMessage, WrapLLMMessage.class);
                                wrapLLMMessage.setCode(LLMMessageBizCode.SUCCESS.getCode());
                                handler.onComplete(Response.from(wrapLLMMessage));

                            } else {
                                LLMMessage llmMessage = LLMMessage.builder().content(content.toString()).role(MessageRoleEnum.assistant.getCode()).build();
                                WrapLLMMessage wrapLLMMessage = BeanUtil.toBean(llmMessage, WrapLLMMessage.class);
                                wrapLLMMessage.setCode(LLMMessageBizCode.SUCCESS.getCode());
                                handler.onComplete(Response.from(wrapLLMMessage));
                            }
                            break;
                        }

                        String partialResponse = new String(bytes, 0, len);
                        log.debug("【llm】流式结果 {}", partialResponse);
                        String partialContent = parsePartialContent(partialResponse);

                        if (StringUtils.isEmpty(partialContent)) {
                            continue;
                        }
                        content.append(partialContent);
                        parseContext.setDeltaText(partialContent);
                        parseContext.setAppendText(content.toString());
                        TransformerHolder transformerHolder = SpringContextUtils.getBean(TransformerHolder.class);
                        Transformer transformer = transformerHolder.getThransformer(request.getLlmModelInfo().getTransformerType());
                        LLMMessage curMessage = transformer.streamParse(parseContext);
                        log.debug("当前消息：{},累计消息内容：{}", JSON.toJSONString(lastMessage), content);
                        if (Objects.isNull(curMessage)) {
                            continue;
                        }
                        lastMessage = curMessage;
                        WrapLLMMessage wrapLLMMessage = BeanUtil.toBean(lastMessage, WrapLLMMessage.class);
                        wrapLLMMessage.setCode(LLMMessageBizCode.SUCCESS.getCode());
                        handler.onNext(call, wrapLLMMessage);
                    }
                } catch (Exception e) {
                    LLMMessage llmMessage = LLMMessage.builder().content(content.toString()).role(MessageRoleEnum.assistant.getCode()).build();
                    WrapLLMMessage wrapLLMMessage = BeanUtil.toBean(llmMessage, WrapLLMMessage.class);
                    wrapLLMMessage.setCode(LLMMessageBizCode.FAILURE.getCode());
                    handler.onError(e, Response.from(wrapLLMMessage));
                }
            }

            @Override
            public void onFailure(Call<ResponseBody> call, Throwable throwable) {
                handler.onError(throwable, null);
            }
        });
    }

    /**
     * 通用流式调用 （LLMMessage)
     *
     * @param request
     * @param handler
     */
    @SneakyThrows
    private void innerStreamingMessageChatCompletion(LLMRequest request, StreamingResponseHandler<WrapLLMMessage> handler) {
        String modelUrl = new URL(request.getLlmModelInfo().getModelUrl()).toString();
        MindIEChatRequest mindIEChatRequest = MindIEConvert.toChatCompletionRequest(request, true);
        mindIEApi.streamingChatCompletion(modelUrl, this.appKey, this.appSign, mindIEChatRequest).enqueue(new Callback<ResponseBody>() {
            @Override
            public void onResponse(Call<ResponseBody> call, retrofit2.Response<ResponseBody> response) {
                StringBuilder content = new StringBuilder();
                LLMMessage lastMessage = null;

                try (BufferedReader reader = new BufferedReader(new InputStreamReader(response.body().byteStream()))) {
                    String line;
                    int index = 0;
                    while ((line = reader.readLine()) != null) {
                        if (StringUtils.isNotBlank(line)) {
                            line = line.replaceFirst("data: ", "");
                            log.debug("【原始输出】 {}", line);
                            if (!line.equalsIgnoreCase("[DONE]")) {
                                MindIEChatResponse mindIEChatResponse = GSON.fromJson(line, MindIEChatResponse.class);
                                LLMMessage llmMessage = getStreamMessageFromChatCompletion(mindIEChatResponse);
                                if (Objects.isNull(llmMessage)) {
                                    index++;
                                    continue;
                                }
                                lastMessage = appendLastMessage(lastMessage, llmMessage);

                                if (index != 0 || !StringUtils.isEmpty(llmMessage.getContent())) {
                                    if (!StringUtils.isEmpty(llmMessage.getContent())) {
                                        content.append(llmMessage.getContent());
                                        WrapLLMMessage wrapLLMMessage = BeanUtil.toBean(llmMessage, WrapLLMMessage.class);
                                        wrapLLMMessage.setCode(LLMMessageBizCode.SUCCESS.getCode());
                                        handler.onNext(call, wrapLLMMessage);
                                    }

                                    if (!StringUtils.isEmpty(llmMessage.getReasoning_content())) {
                                        WrapLLMMessage wrapLLMMessage = BeanUtil.toBean(llmMessage, WrapLLMMessage.class);
                                        wrapLLMMessage.setCode(LLMMessageBizCode.SUCCESS.getCode());
                                        handler.onNext(call, wrapLLMMessage);
                                    }

                                }
                            }
                        }
                        index++;
                    }
                    log.info("===========流式输出结束===========");
                    if (Objects.nonNull(lastMessage)) {
                        lastMessage.setContent(content.toString());
                        if (StringUtils.isEmpty(lastMessage.getRole())) {
                            lastMessage.setRole(MessageRoleEnum.assistant.getCode());
                        }
                    }
                    WrapLLMMessage wrapLLMMessage = BeanUtil.toBean(lastMessage, WrapLLMMessage.class);
                    wrapLLMMessage.setCode(LLMMessageBizCode.SUCCESS.getCode());
                    handler.onComplete(Response.from(wrapLLMMessage));
                } catch (IOException e) {
                    WrapLLMMessage wrapLLMMessage = BeanUtil.toBean(lastMessage, WrapLLMMessage.class);
                    wrapLLMMessage.setCode(LLMMessageBizCode.FAILURE.getCode());
                    handler.onError(e, Response.from(wrapLLMMessage));
                }
            }

            @Override
            public void onFailure(Call<ResponseBody> call, Throwable throwable) {
                handler.onError(throwable, null);
            }
        });
    }

    private String getContent(MindIEResponse mindIEResponse) {
        if (CollectionUtils.isEmpty(mindIEResponse.getText())) {
            return "";
        }
        return mindIEResponse.getText().get(0);
    }

    private String getContent(MindIEChatResponse mindIEChatResponse) {
        if (CollectionUtils.isEmpty(mindIEChatResponse.getChoices())) {
            return "";
        }
        MindIEChatResponse.Choice choice = mindIEChatResponse.getChoices().get(0);
        if (Objects.isNull(choice.getMessage())) {
            return "";
        }
        return choice.getMessage().getContent();
    }

    private LLMMessage getMessage(LLMRequest request, MindIEResponse mindIEResponse) {
        if (Objects.isNull(mindIEResponse) || CollectionUtils.isEmpty(mindIEResponse.getText())) {
            return null;
        }
        if (StringUtils.isEmpty(mindIEResponse.getText().get(0))) {
            return null;
        }
        TransformerHolder transformerHolder = SpringContextUtils.getBean(TransformerHolder.class);
        Transformer transformer = transformerHolder.getThransformer(request.getLlmModelInfo().getTransformerType());
        return transformer.parse(mindIEResponse.getText().get(0));
    }

    private LLMMessage getMessage(MindIEChatResponse mindIEChatResponse) {
        if (CollectionUtils.isEmpty(mindIEChatResponse.getChoices())) {
            return null;
        }
        MindIEChatResponse.Choice choice = mindIEChatResponse.getChoices().get(0);
        if (Objects.isNull(choice.getMessage())) {
            return null;
        }
        return toMessage(choice.getMessage());
    }

    private LLMMessage toMessage(MindIEMessage mindIEMessage) {
        LLMMessage llmMessage = new LLMMessage();
        llmMessage.setContent(mindIEMessage.getContent());
        llmMessage.setRole(mindIEMessage.getRole());
        llmMessage.setToolCalls(mindIEMessage.getTool_calls());
        llmMessage.setReasoning_content(mindIEMessage.getReasoning_content());
        return llmMessage;
    }

    private String getStreamContent(MindIEChatResponse mindIEChatResponse) {
        if (CollectionUtils.isEmpty(mindIEChatResponse.getChoices())) {
            return "";
        }
        MindIEChatResponse.Choice choice = mindIEChatResponse.getChoices().get(0);
        if (Objects.isNull(choice.getDelta())) {
            return "";
        }
        return choice.getDelta().getContent();
    }

    private LLMMessage getStreamMessageFromChatCompletion(MindIEChatResponse mindIEChatResponse) {
        if (CollectionUtils.isEmpty(mindIEChatResponse.getChoices())) {
            return null;
        }

        MindIEChatResponse.Choice choice = mindIEChatResponse.getChoices().get(0);

        if (Objects.isNull(choice.getDelta())) {
            return null;
        }

        return toMessage(choice.getDelta());
    }

    private LLMMessage appendLastMessage(LLMMessage lastMessage, LLMMessage currentMessage) {
        if (Objects.isNull(lastMessage)) {
            lastMessage = currentMessage;
        }

        if (!StringUtils.isEmpty(currentMessage.getRole())) {
            lastMessage.setRole(currentMessage.getRole());
        }

        if (!CollectionUtils.isEmpty(currentMessage.getToolCalls())) {
            if (CollectionUtils.isEmpty(lastMessage.getToolCalls())) {
                lastMessage.setToolCalls(currentMessage.getToolCalls());
            } else {
                ToolCall current = currentMessage.getToolCalls().get(0);
                Function currentFunction = current.getFunction();

                ToolCall last = lastMessage.getToolCalls().get(0);
                Function lastFunction = last.getFunction();
                if (StringUtils.isEmpty(lastFunction.getArguments())) {
                    lastFunction.setArguments(currentFunction.getArguments());
                } else {
                    lastFunction.setArguments(lastFunction.getArguments() + currentFunction.getArguments());
                }
                last.setFunction(lastFunction);

                lastMessage.setToolCalls(Collections.singletonList(last));
            }
        }


        if (!StringUtils.isEmpty(currentMessage.getContent())) {
            if (StringUtils.isEmpty(lastMessage.getContent())) {
                lastMessage.setContent(currentMessage.getContent());
            } else {
                lastMessage.setContent(lastMessage.getContent() + currentMessage.getContent());
            }
        }

        if (!StringUtils.isEmpty(currentMessage.getReasoning_content())) {
            if (StringUtils.isEmpty(lastMessage.getReasoning_content())) {
                lastMessage.setReasoning_content(currentMessage.getReasoning_content());
            } else {
                lastMessage.setReasoning_content(lastMessage.getReasoning_content() + currentMessage.getReasoning_content());
            }
        }

        return lastMessage;
    }
}
