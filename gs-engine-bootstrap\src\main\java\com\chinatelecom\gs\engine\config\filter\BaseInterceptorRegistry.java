package com.chinatelecom.gs.engine.config.filter;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024年12月31日
 */
public class BaseInterceptorRegistry {

    private List<BaseInterceptorRegistration> registrations = new ArrayList<>();

    private volatile boolean isInit = false;

    public BaseInterceptorRegistration addInterceptor(BaseHandlerInterceptor interceptor) {
        BaseInterceptorRegistration registration = new BaseInterceptorRegistration(interceptor);
        this.registrations.add(registration);
        return registration;
    }

    public List<BaseInterceptorRegistration> getRegistrations() {
        if (!isInit) {
            synchronized (this) {
                this.registrations = this.registrations.stream().sorted((Comparator.comparingInt(BaseInterceptorRegistration::getOrder)))
                        .collect(Collectors.toList());
                isInit = true;
            }
        }
        return registrations;
    }
}
