package com.chinatelecom.gs.engine.task.web;

import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.common.auth.KsMenuConfig;
import com.chinatelecom.gs.engine.common.auth.MenuConfig;
import com.chinatelecom.gs.engine.common.auth.PermissionTag;
import com.chinatelecom.gs.engine.common.auth.PermissionTypeEnum;
import com.chinatelecom.gs.engine.common.constants.Constants;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.common.platform.StatOpenApi;
import com.chinatelecom.gs.engine.common.utils.DataResourceAccess;
import com.chinatelecom.gs.engine.sdk.common.annotation.ApiType;
import com.chinatelecom.gs.engine.sdk.common.annotation.HideFromApiTypes;
import com.chinatelecom.gs.engine.task.sdk.TaskApis;
import com.chinatelecom.gs.workflow.core.domain.dto.NodeRunRequest;
import com.chinatelecom.gs.workflow.core.domain.dto.Result;
import com.chinatelecom.gs.workflow.core.domain.param.WorkFlowNodeRunRsp;
import com.chinatelecom.gs.workflow.core.service.NodeRunService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 节点调试接口
 * @date 2024年12月09日
 */

@Slf4j
@Tag(name = "节点调试")
@RestController
@HideFromApiTypes(ApiType.OPENAPI)
@RequestMapping({TaskApis.TASK_API + Constants.WEB_PREFIX, TaskApis.TASK_API + Constants.API_PREFIX})
@Validated
public class WorkFlowNodeRunController {

    @Autowired
    private DataResourceAccess dataResourceAccess;

    @Autowired
    private NodeRunService nodeRunService;

    @PermissionTag(code = {MenuConfig.DIALOG_FLOW, MenuConfig.WORKFLOW, KsMenuConfig.WORKFLOW_FLOW})
    @PermissionTag(code = MenuConfig.STRATEGY, type = PermissionTypeEnum.MENU)
    @Operation(summary = "节点调试", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "业务流管理")})})
    @PlatformRestApi(name = "节点调试", groupName = "节点调试")
    @StatOpenApi(name = "节点调试", groupName = "节点调试")
    @PostMapping("/nodeRun")
    @AuditLog(businessType = "节点调试", operType = "节点调试", operDesc = "节点调试", objId = "#request.workflowId")
    public Result<WorkFlowNodeRunRsp> nodeRun(@RequestBody NodeRunRequest request) {
        String appCode = RequestContext.getAppCode();
        return Result.success(nodeRunService.nodeRun(request, appCode));
    }
}
