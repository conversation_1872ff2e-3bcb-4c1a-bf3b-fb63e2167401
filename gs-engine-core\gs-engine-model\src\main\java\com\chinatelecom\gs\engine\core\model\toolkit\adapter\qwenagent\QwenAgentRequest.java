package com.chinatelecom.gs.engine.core.model.toolkit.adapter.qwenagent;

import com.chinatelecom.gs.engine.core.sdk.vo.llm.BaseLLMRequest;
import com.chinatelecom.gs.engine.core.sdk.vo.llm.ToolFunction;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

@Data
public class QwenAgentRequest implements BaseLLMRequest {

    private String model;

    private List<QwenAgentMessage> messages;

    private boolean stream = true;

    private Integer max_tokens = 3000;

    private Integer topk = 5;

    private double temperature = 1d;

    private List<ToolFunction> functions;

    private String llm_url;

    /**
     * 获取输入内容
     *
     * @return
     */
    @Override
    public String inputContent() {
        StringBuilder inputBuilder = new StringBuilder();
        if(CollectionUtils.isNotEmpty(messages)){
            for(QwenAgentMessage message: messages){
                inputBuilder.append(message.getContent());
            }
        }
        return inputBuilder.toString();
    }
}
