package com.chinatelecom.gs.engine.channel.service.messagehandler;

import cn.hutool.core.exceptions.ValidateException;
import com.chinatelecom.gs.engine.channel.api.vo.CommonAuthParam;
import com.chinatelecom.gs.engine.channel.manage.ChannelSecretManagerService;
import com.chinatelecom.gs.engine.channel.manage.OffSiteChannelSwitchCacheService;
import com.chinatelecom.gs.engine.channel.service.dto.ChannelApiSecretDTO;
import org.apache.commons.codec.binary.Hex;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;
import org.springframework.mock.web.MockHttpServletRequest;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

public class ApiMessageAuthCheckServiceTest {

    @InjectMocks
    private ApiMessageAuthCheckService apiMessageAuthCheckService;

    @Mock
    private ChannelSecretManagerService channelSecretManagerService;

    @Mock
    private OffSiteChannelSwitchCacheService offSiteChannelSwitchCacheService;

    @Mock
    private CommonAuthParam commonAuthParam;

    @Captor
    private ArgumentCaptor<String> stringArgumentCaptor;

    private MockHttpServletRequest request;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        request = new MockHttpServletRequest();
    }

    // ------------------ checkAuth 测试 ------------------

    @Test
    public void testCheckAuth_SignAndAuthInfoEmpty_ThrowsException() {
        when(commonAuthParam.getSign()).thenReturn(null);
        when(commonAuthParam.getAuthInfo()).thenReturn(null);

        ValidateException exception = assertThrows(ValidateException.class, () ->
                apiMessageAuthCheckService.checkAuth(commonAuthParam, request, "content"));

        assertEquals("签名为空", exception.getMessage());
    }

    // ------------------ validReChannelId 测试 ------------------

    @Test
    public void testValidReChannelId_ChannelNotOpen_ThrowsException() {
        ChannelApiSecretDTO dto = new ChannelApiSecretDTO();
        dto.setSecret("test-secret");
        dto.setAppId("app123");
        dto.setChannelId("channel456");

        when(channelSecretManagerService.getSecretWithSecretId("secretId")).thenReturn(dto);
        when(offSiteChannelSwitchCacheService.channelOpen("app123", "channel456")).thenReturn(false);

        ValidateException exception = assertThrows(ValidateException.class, () ->
                apiMessageAuthCheckService.validReChannelId("secretId", "content", "sign"));

        assertNotNull(exception.getMessage());
    }

    @Test
    public void testValidReChannelId_SecretEmpty_ThrowsException() {
        ChannelApiSecretDTO dto = new ChannelApiSecretDTO();
        dto.setSecret(null); // secret is empty

        when(channelSecretManagerService.getSecretWithSecretId("secretId")).thenReturn(dto);

        ValidateException exception = assertThrows(ValidateException.class, () ->
                apiMessageAuthCheckService.validReChannelId("secretId", "content", "sign"));

        assertNotNull(exception.getMessage());
    }

    @Test
    public void testValidReChannelId_SignMismatch_ThrowsException() throws Exception {
        ChannelApiSecretDTO dto = new ChannelApiSecretDTO();
        dto.setSecret("test-secret");
        dto.setAppId("app123");
        dto.setChannelId("channel456");

        when(channelSecretManagerService.getSecretWithSecretId("secretId")).thenReturn(dto);
        when(offSiteChannelSwitchCacheService.channelOpen("app123", "channel456")).thenReturn(true);

        // 手动构造一个不同的签名
        String wrongSign = "wrong-sign";

        ValidateException exception = assertThrows(ValidateException.class, () ->
                apiMessageAuthCheckService.validReChannelId("secretId", "content", wrongSign));

        assertNotNull(exception.getMessage());
    }

    @Test
    public void testValidReChannelId_SignMatch_ReturnsChannelId() throws Exception {
        ChannelApiSecretDTO dto = new ChannelApiSecretDTO();
        dto.setSecret("test-secret");
        dto.setAppId("app123");
        dto.setChannelId("channel456");

        when(channelSecretManagerService.getSecretWithSecretId("secretId")).thenReturn(dto);
        when(offSiteChannelSwitchCacheService.channelOpen("app123", "channel456")).thenReturn(true);

        // 计算正确的签名
        String signContent = "secretKey=test-secret&content=content";
        Mac hmacSha256 = Mac.getInstance("HmacSHA256");
        SecretKeySpec keySpec = new SecretKeySpec("test-secret".getBytes(), "HmacSHA256");
        hmacSha256.init(keySpec);
        byte[] signatureBytes = hmacSha256.doFinal(signContent.getBytes());
        String correctSign = Hex.encodeHexString(signatureBytes);

        String result = apiMessageAuthCheckService.validReChannelId("secretId", "content", correctSign);

        assertEquals("channel456", result);
    }

    @Test
    public void testValidReChannelId_ExceptionDuringSigning_ThrowsSystemError() throws Exception {
        ChannelApiSecretDTO dto = new ChannelApiSecretDTO();
        dto.setSecret("test-secret");

        when(channelSecretManagerService.getSecretWithSecretId("secretId")).thenReturn(dto);
        doThrow(new RuntimeException("加密失败")).when(offSiteChannelSwitchCacheService).channelOpen(anyString(), anyString());

        ValidateException exception = assertThrows(ValidateException.class, () ->
                apiMessageAuthCheckService.validReChannelId("secretId", "content", "sign"));

        assertNotNull(exception.getMessage());
    }
}
