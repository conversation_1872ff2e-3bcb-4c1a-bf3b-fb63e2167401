package com.chinatelecom.gs.engine.kms.controller;

import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.common.config.aspect.annotation.DebugLog;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.kms.fileSearch.FileSplitSearchService;
import com.chinatelecom.gs.engine.kms.sdk.api.FileSplitSearchApi;
import com.chinatelecom.gs.engine.kms.sdk.constants.KmsApis;
import com.chinatelecom.gs.engine.kms.sdk.vo.fileSearch.FileSplitSearchParam;
import com.chinatelecom.gs.engine.kms.sdk.vo.search.FileItem;
import com.chinatelecom.gs.engine.kms.sdk.vo.search.SearchResp;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;


/**
 * 文件切片检索
 * @USER: pengmc1
 * @DATE: 2025/2/25 17:39
 */

@RestController
@Slf4j
@Tag(name = "文件切片检索接口")
@RequestMapping({KmsApis.KMS_API + KmsApis.FILE_SPLIT_SEARCH, KmsApis.RPC + KmsApis.FILE_SPLIT_SEARCH})
public class FileSplitSearchController implements FileSplitSearchApi {

    @Resource
    private FileSplitSearchService fileSplitSearchService;

    @Override
    @Operation(summary = "搜索接口", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "切片搜索", groupName = "文件切片")
    @AuditLog(businessType = "文件切片", operType = "切片搜索", operDesc = "切片搜索", objId="#param.sessionId")
    @DebugLog(operation = "【文件检索】搜索接口")
    @PostMapping("/v1")
    public SearchResp<FileItem> search(@RequestBody FileSplitSearchParam param) {
        return fileSplitSearchService.search(param);
    }
}
