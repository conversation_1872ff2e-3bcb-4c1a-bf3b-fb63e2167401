package com.chinatelecom.gs.engine.common.s3.impl;

import com.chinatelecom.gs.engine.common.config.property.GsGlobalConfig;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * CephCloudDao测试类 - 验证AWS SDK v2迁移后的功能
 * 这是一个简单的编译测试，确保迁移后的代码可以正常编译
 */
class CephCloudDaoTest {

    @Test
    void testCephCloudDaoCanBeInstantiated() {
        // 简单的编译测试 - 确保类可以被实例化
        CephCloudDao cephCloudDao = new CephCloudDao();
        assertNotNull(cephCloudDao);
    }

    @Test
    void testS3ConfigCanBeCreated() {
        // 测试S3配置类可以正常创建
        GsGlobalConfig.S3Config s3Config = new GsGlobalConfig.S3Config();
        s3Config.setBucketName("test-bucket");
        s3Config.setAccessKey("test-access-key");
        s3Config.setSecretKey("test-secret-key");
        s3Config.setEndPoint("http://localhost:9000");

        assertEquals("test-bucket", s3Config.getBucketName());
        assertEquals("test-access-key", s3Config.getAccessKey());
        assertEquals("test-secret-key", s3Config.getSecretKey());
        assertEquals("http://localhost:9000", s3Config.getEndPoint());
    }

}
