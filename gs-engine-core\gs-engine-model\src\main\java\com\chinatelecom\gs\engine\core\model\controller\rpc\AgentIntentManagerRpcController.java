package com.chinatelecom.gs.engine.core.model.controller.rpc;

import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.common.constants.Apis;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.core.model.service.AgentIntentManagerAppService;
import com.chinatelecom.gs.engine.core.sdk.request.CodeRequest;
import com.chinatelecom.gs.engine.core.sdk.request.IntentQueryRequest;
import com.chinatelecom.gs.engine.core.sdk.rpc.IntentManagerRpcApi;
import com.chinatelecom.gs.engine.core.sdk.vo.AgentIntentConfigResponse;
import com.chinatelecom.gs.engine.core.sdk.vo.AgentIntentResponse;
import com.chinatelecom.gs.engine.core.sdk.vo.IntentIndexData;
import com.chinatelecom.gs.engine.core.sdk.vo.IntentSearchVO;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * @author: Wei
 * @date: 2025-02-05 09:24
 */
@Slf4j
@RestController
@RequestMapping(Apis.BASE_PREFIX + Apis.RPC_PREFIX + "/intent")
public class AgentIntentManagerRpcController implements IntentManagerRpcApi {

    @Resource
    private AgentIntentManagerAppService agentIntentManagerAppService;


    @Operation(summary = "例句查询", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "例句查询", groupName = "意图管理")
    @PostMapping("/query")
    @AuditLog(businessType = "意图管理", operType = "例句查询", operDesc = "例句查询", objId="#request.query")
    public IntentSearchVO<IntentIndexData> queryIntent(@RequestBody IntentQueryRequest request, @RequestHeader("appCode") String appCode) {
        return agentIntentManagerAppService.queryIntent(request, appCode);
    }

    @Operation(summary = "意图查询", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "意图查询", groupName = "意图管理")
    @PostMapping("/queryList")
    @AuditLog(businessType = "意图管理", operType = "意图查询", operDesc = "意图查询", objId="null")
    public Result<List<AgentIntentResponse>> queryList(@RequestBody CodeRequest request) {
        return Result.success(agentIntentManagerAppService.queryList(request));
    }

    @Operation(summary = "意图高级配置查询", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "意图高级配置查询", groupName = "意图管理")
    @GetMapping("/intent-manager")
    @AuditLog(businessType = "意图管理", operType = "意图高级配置查询", operDesc = "意图高级配置查询", objId="null")
    public Result<AgentIntentConfigResponse> getIntentConfig(String appCode) {
        return Result.success(agentIntentManagerAppService.getIntentConfigOrDefault(appCode));
    }


}
