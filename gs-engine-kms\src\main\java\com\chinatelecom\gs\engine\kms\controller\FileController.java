package com.chinatelecom.gs.engine.kms.controller;


import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.core.manager.param.UploadCompleteParam;
import com.chinatelecom.gs.engine.core.manager.param.UploadPartParam;
import com.chinatelecom.gs.engine.core.manager.param.UploadStartParam;
import com.chinatelecom.gs.engine.core.manager.service.FileUploadService;
import com.chinatelecom.gs.engine.core.manager.vo.UploadStartResult;
import com.chinatelecom.gs.engine.kms.sdk.constants.KmsApis;
import com.chinatelecom.gs.engine.kms.sdk.enums.AttachableType;
import com.chinatelecom.gs.engine.kms.sdk.enums.RelevanceType;
import com.chinatelecom.gs.engine.kms.sdk.enums.SystemFileType;
import com.chinatelecom.gs.engine.kms.sdk.vo.file.*;
import com.chinatelecom.gs.engine.kms.service.FileAppService;
import com.chinatelecom.gs.engine.sdk.common.annotation.ApiType;
import com.chinatelecom.gs.engine.sdk.common.annotation.HideFromApiTypes;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;


/**
 * <p>
 * 文件缓存表 Controller
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-15
 */
@RestController
@Slf4j
@Tag(name = "文件管理")
@RequestMapping({KmsApis.KMS_API + KmsApis.FILE_API, KmsApis.OPENAPI + KmsApis.FILE_API})
public class FileController {

    @Resource
    private FileAppService fileAppService;
    @Resource
    private FileUploadService fileUploadService;


    @HideFromApiTypes(ApiType.OPENAPI)
    @Operation(summary = "查询是否存在文件", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "查询是否存在文件", groupName = "文件管理")
    @AuditLog(businessType = "文件管理", operType = "查询是否存在文件", operDesc = "查询是否存在文件", objId="#param.md5")
    @PostMapping(KmsApis.CHECK_MD5_API)
    public Result<UploadCheckResult> checkMd5(@Validated @RequestBody UploadCheckParam param) {
        return Result.success(fileAppService.checkMD5ByUser(param.getMd5()));
    }

    @HideFromApiTypes(ApiType.OPENAPI)
    @Parameters({
            @Parameter(name = "md5", description = "文件MD5", required = true, schema = @Schema(type = "string")),
            @Parameter(name = "file", description = "文件", required = true, schema = @Schema(type = "string", format = "binary")),
            @Parameter(name = "param", hidden = true)
    })
    @Operation(summary = "直接上传", description = "直接上传文件接口。适用于没有关联关系的独立文件。如果需要将文件导入知识库，还需调用从路径导入知识接口。", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "直接上传", groupName = "文件管理")
//    @AuditLog(businessType = "文件管理", operType = "直接上传", operDesc = "直接上传", objId="null")
    @PostMapping(value = KmsApis.UPLOAD, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Result<UploadCompleteResult> upload(@Validated UploadParam param) throws IOException {
        return Result.success(fileAppService.upload(param));
    }


    @Operation(summary = "上传附件", description = "上传有资源关联关系的文件。对于没有关联关系的文件，请使用直接上传接口。", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "上传附件", groupName = "文件管理")
//    @AuditLog(businessType = "文件管理", operType = "上传附件", operDesc = "上传附件", objId="#param.attachableCode")
    @Parameters({
            @Parameter(name = "attachableCode", description = "关联知识库或知识编码", required = true, schema = @Schema(type = "string")),
            @Parameter(name = "attachableType", description = "关联数据类型", required = true, schema = @Schema(type = "string", implementation = AttachableType.class)),
            @Parameter(name = "type", description = "附件类型", required = true, schema = @Schema(type = "string", implementation = RelevanceType.class)),
            @Parameter(name = "file", description = "文件", required = true, schema = @Schema(type = "string", format = "binary")),
            @Parameter(name = "param", hidden = true)
    })
    @PostMapping(value = KmsApis.ATTACH_API, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Result<AttachmentVO> uploadAttachment(@Validated AttachmentUploadParam param) {
        return Result.success(fileAppService.uploadAttachment(param));
    }


    @HideFromApiTypes(ApiType.OPENAPI)
    @Operation(summary = "获取系统文件", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "获取系统文件", groupName = "文件管理")
    @AuditLog(businessType = "文件管理", operType = "获取系统文件", operDesc = "获取系统文件", objId="#systemFileType")
    @GetMapping(KmsApis.SYSTEM_FILE_API)
    public void downloadSystemFileApi(@RequestParam SystemFileType systemFileType, HttpServletResponse response) throws IOException {
        fileAppService.getSystemFile(systemFileType, response);
    }

    @HideFromApiTypes(ApiType.OPENAPI)
    @Operation(summary = "HTML转PDF", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "HTML转PDF", groupName = "文件管理")
//    @AuditLog(businessType = "文件管理", operType = "HTML转PDF", operDesc = "HTML转PDF", objId="null")
    @Parameters({
            @Parameter(name = "file", description = "文件", required = true, schema = @Schema(type = "string", format = "binary")),
    })
    @PostMapping(value = KmsApis.FILE_CONVERT_API, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public void fileConvert(@RequestPart MultipartFile file, HttpServletResponse response) throws IOException {
        fileAppService.convertToPdf(file, response);
    }

    /**
     * 开始上传文件
     * @param param
     * @return
     */
    @PostMapping("/upload-init")
    @Operation(summary = "开始上传分片文件", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "开始上传分片文件", groupName = "文件管理")
//    @AuditLog(businessType = "文件管理", operType = "开始上传分片文件", operDesc = "开始上传分片文件", objId="#_RESULT_.data.uploadId")
    public Result<UploadStartResult> startUpload(@Valid @RequestBody UploadStartParam param) {
        return Result.success(fileUploadService.startUpload(param));
    }

    /**
     * 分片文件上传
     * @param param
     * @return
     * @throws IOException
     */
    @PostMapping(value = "/upload-part", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "分片文件上传", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "分片文件上传", groupName = "文件管理")
//    @AuditLog(businessType = "文件管理", operType = "分片文件上传", operDesc = "分片文件上传", objId="#param.uploadId")
    public Result<String> partUpload(@Validated UploadPartParam param) throws IOException {
        return Result.success(fileUploadService.uploadPart(param));
    }

    /**
     * 完成文件分片上传
     * @param param
     * @return
     */
    @PostMapping("/complete-upload")
    @Operation(summary = "完成文件分片上传", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "完成文件分片上传", groupName = "文件管理")
//    @AuditLog(businessType = "文件管理", operType = "完成文件分片上传", operDesc = "完成文件分片上传", objId="#param.uploadId")
    public Result<com.chinatelecom.gs.engine.core.manager.vo.UploadCompleteResult> completeUpload(@Validated @RequestBody UploadCompleteParam param) {
        return Result.success(fileUploadService.completeUpload(param.getUploadId()));
    }

    /**
     * 取消分片上传
     * @param param
     * @return
     */
    @PostMapping("/abort-upload")
    @Operation(summary = "取消分片上传", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "取消分片上传", groupName = "文件管理")
//    @AuditLog(businessType = "文件管理", operType = "取消分片上传", operDesc = "取消分片上传", objId="#param.uploadId")
    public Result<String> abortUpload(@Validated @RequestBody UploadCompleteParam param){
        fileUploadService.abortUpload(param.getUploadId());
        return Result.success();
    }


}


