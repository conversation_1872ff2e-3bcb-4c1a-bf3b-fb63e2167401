package com.chinatelecom.gs.engine.channel.openai.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import java.util.List;
import java.util.Map;

/**
 * OpenAI Chat Completion API 请求格式
 */
@Data
@Schema(description = "OpenAI兼容请求")
public class ChatCompletionRequest {

    @Schema(description = "要使用的模型ID，在系统中映射到agentCode", requiredMode = Schema.RequiredMode.REQUIRED)
    private String model;

    @Schema(description = "会话标识符。用于多轮对话的上下文管理，同一个会话需要传递相同的sessionId。注意：多轮对话必须传递相同的sessionId，由后台bot管理，无法通过messages传输历史会话控制，每次只读取最后一条消息。如果未提供，将生成一个UUID")
    private String sessionId;

    @NotEmpty(message = "消息列表不能为空")
    @Schema(description = "表示对话历史的消息对象列表")
    private List<ChatMessage> messages;

    @Schema(description = "采样温度，取值范围0-2。值越高结果越随机，值越低结果越确定。注意：当前版本仅兼容参数，暂无实际效果。")
    private Float temperature;

    @Schema(description = "核采样概率质量，取值范围0-1。用于控制输出的多样性。注意：当前版本仅兼容参数，暂无实际效果。")
    private Float top_p;

    @Schema(description = "是流式返回响应(true)还是返回完整响应(false)", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean stream;

    @Schema(description = "停止生成的标记，最多可提供4个。注意：当前版本仅兼容参数，暂无实际效果。")
    private List<String> stop;

    @Schema(description = "生成的最大token数量。注意：当前版本仅兼容参数，暂无实际效果。")
    private Integer max_tokens;

    @Schema(description = "存在惩罚，取值范围-2.0到2.0。用于控制生成内容的新颖性。注意：当前版本仅兼容参数，暂无实际效果。")
    private Float presence_penalty;

    @Schema(description = "频率惩罚，取值范围-2.0到2.0。用于控制生成内容的重复性。注意：当前版本仅兼容参数，暂无实际效果。")
    private Float frequency_penalty;

    @Schema(description = "用户唯一标识符，用于跟踪对话上下文")
    private String user;

    @Schema(description = "指定生成的补全数量。注意：当前版本仅兼容参数，暂无实际效果。")
    private Integer n;

    @Schema(description = "附加数据")
    private Map<String, Object> extraData;

    @Schema(description = "是否Test模式 默认是",example = "1")
    private Integer test = 1;
}
