package com.chinatelecom.gs.engine.kms.controller;


import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.common.config.aspect.annotation.DebugLog;
import com.chinatelecom.gs.engine.common.constants.Constants;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.common.utils.HtmlXssUtils;
import com.chinatelecom.gs.engine.kms.sdk.constants.KmsApis;
import com.chinatelecom.gs.engine.kms.sdk.enums.KnowledgeInfoType;
import com.chinatelecom.gs.engine.kms.sdk.vo.extra.KnowledgeExtraInfoCreateParam;
import com.chinatelecom.gs.engine.kms.sdk.vo.note.KnowledgeNoteCreateParam;
import com.chinatelecom.gs.engine.kms.sdk.vo.note.KnowledgeNoteVO;
import com.chinatelecom.gs.engine.kms.service.KnowledgeExtraInfoAppService;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;


@RestController
@Tag(name = "笔记管理")
@RequestMapping({KmsApis.KMS_API + KmsApis.KNOWLEDGE + KmsApis.NOTE})
public class KnowledgeNoteController {

    @Resource
    private KnowledgeExtraInfoAppService knowledgeExtraInfoAppService;


    @Operation(summary = "获取笔记", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "获取笔记", groupName = "笔记管理")
    @AuditLog(businessType = "笔记管理", operType = "获取笔记", operDesc = "获取笔记", objId="#knowledgeCode")
    @DebugLog(operation = "获取笔记")
    @GetMapping(KmsApis.KNOWLEDGE_CODE_PATH)
    public Result<KnowledgeNoteVO> get(@PathVariable("knowledgeCode") String knowledgeCode) {
        String content = knowledgeExtraInfoAppService.getContentByType(knowledgeCode, KnowledgeInfoType.NOTE);
        return Result.success(KnowledgeNoteVO.builder().content(content).build());
    }


    @Operation(summary = "创建或更新笔记", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "创建或更新笔记", groupName = "笔记管理")
    @AuditLog(businessType = "笔记管理", operType = "创建或更新笔记", operDesc = "创建或更新笔记", objId="#knowledgeCode")
    @DebugLog(operation = "创建或更新笔记")
    @PutMapping(KmsApis.KNOWLEDGE_CODE_PATH)
    public Result<Boolean> update(@PathVariable("knowledgeCode") String knowledgeCode, @Validated @RequestBody KnowledgeNoteCreateParam dto) {
        KnowledgeExtraInfoCreateParam createParam = new KnowledgeExtraInfoCreateParam();
        createParam.setKnowledgeCode(knowledgeCode);
        if (StringUtils.isNotBlank(dto.getContent())) {
            createParam.setContent(HtmlXssUtils.safe(dto.getContent()));
        }
        createParam.setType(KnowledgeInfoType.NOTE);
        createParam.setExtraKey(Constants.DEFAULT);
        knowledgeExtraInfoAppService.createOrUpdate(createParam);
        return Result.success(true);
    }
}
