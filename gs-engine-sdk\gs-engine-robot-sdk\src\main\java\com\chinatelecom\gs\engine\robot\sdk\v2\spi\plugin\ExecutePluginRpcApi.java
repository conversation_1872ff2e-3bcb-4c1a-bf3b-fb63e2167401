package com.chinatelecom.gs.engine.robot.sdk.v2.spi.plugin;

import com.chinatelecom.gs.engine.robot.sdk.dto.Result;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.plugin.request.ExecutePluginApiRequest;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.plugin.response.ExecutePluginApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "gs-engine-robot", url = "${gs.kmsRpcConfig.gsUrl:}", contextId = "executePluginRpcApi", path = "/ais/plugin/rpc/plugin", configuration = PluginFeignConfig.class)
public interface ExecutePluginRpcApi {

    @Operation(summary = "执行插件", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "插件管理")})})
    @PostMapping("/executeApi")
    Result<ExecutePluginApiResponse> executeApi(@RequestBody ExecutePluginApiRequest debugPluginApiRequest);

}
