package com.chinatelecom.gs.engine.common.config.aspect.annotation;

import java.lang.annotation.*;

/**
 * DebugLog
 * 打印入参和出参
 *
 * <AUTHOR>
 * @date 2022-11-13 15:45
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface VisitsLog {

    /**
     * http请求方法
     *
     * @return
     */
    String method() default "";

    /**
     * 请求路径
     * @return
     */
    String uri() default "";

    /**
     * 额外参数
     */
    String paramKey() default "";

}
