package com.chinatelecom.gs.engine.kms.search.impl;

import com.chinatelecom.ai.search.client.AiSearchClient;
import com.chinatelecom.ai.search.query_dsl.FieldValue;
import com.chinatelecom.ai.search.query_dsl.Query;
import com.chinatelecom.ai.search.query_dsl.Script;
import com.chinatelecom.ai.search.query_dsl.SortOptions;
import com.chinatelecom.ai.search.query_dsl.SortOrder;
import com.chinatelecom.ai.search.req.*;
import com.chinatelecom.ai.search.resp.DocResponse;
import com.chinatelecom.ai.search.resp.SearchResponse;
import com.chinatelecom.ai.search.sdk.enums.DocAction;
import com.chinatelecom.ai.search.sdk.enums.MigrationMode;
import com.chinatelecom.ai.search.sdk.enums.SearchType;
import com.chinatelecom.gs.engine.common.config.property.GsGlobalConfig;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.enums.DimensionEnum;
import com.chinatelecom.gs.engine.common.utils.BaseFileUrlUtils;
import com.chinatelecom.gs.engine.common.utils.BizAssert;
import com.chinatelecom.gs.engine.common.utils.IPageUtils;
import com.chinatelecom.gs.engine.common.utils.JsonUtils;
import com.chinatelecom.gs.engine.core.manager.search.AnalyzerTypeEnum;
import com.chinatelecom.gs.engine.core.manager.search.SearchAnalyzerConfigService;
import com.chinatelecom.gs.engine.core.manager.search.dto.SearchAnalyzerConfigDTO;
import com.chinatelecom.gs.engine.core.model.toolkit.core.internal.RetryUtils;
import com.chinatelecom.gs.engine.kms.convert.common.CommonConverter;
import com.chinatelecom.gs.engine.kms.dto.KnowledgeBaseDTO;
import com.chinatelecom.gs.engine.kms.dto.KnowledgeFaqDTO;
import com.chinatelecom.gs.engine.kms.flow.search.SearchFlowProcess;
import com.chinatelecom.gs.engine.kms.model.dto.KnowledgeDTO;
import com.chinatelecom.gs.engine.kms.model.dto.KnowledgeDetailDTO;
import com.chinatelecom.gs.engine.kms.repository.KnowledgeBaseRepository;
import com.chinatelecom.gs.engine.kms.repository.KnowledgeRepository;
import com.chinatelecom.gs.engine.kms.repository.TagAppRepository;
import com.chinatelecom.gs.engine.kms.repository.TagRelationRepository;
import com.chinatelecom.gs.engine.kms.rpc.split.dto.SplitResponse;
import com.chinatelecom.gs.engine.kms.rpc.split.dto.SplitResult;
import com.chinatelecom.gs.engine.kms.sdk.enums.DataChangeStatus;
import com.chinatelecom.gs.engine.kms.sdk.enums.EnvType;
import com.chinatelecom.gs.engine.kms.sdk.enums.KnowledgeType;
import com.chinatelecom.gs.engine.kms.sdk.enums.PublishStatus;
import com.chinatelecom.gs.engine.kms.sdk.enums.RecallType;
import com.chinatelecom.gs.engine.kms.sdk.enums.SplitIndexType;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.chunk.ChunkCompareDataVO;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.chunk.ChunkCompareInfo;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.chunk.ChunkCompareQueryParam;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.chunk.ChunkDataVO;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.chunk.ChunkQueryParam;
import com.chinatelecom.gs.engine.kms.sdk.vo.search.BaseItem;
import com.chinatelecom.gs.engine.kms.sdk.vo.search.SearchParam;
import com.chinatelecom.gs.engine.kms.sdk.vo.search.SearchResp;
import com.chinatelecom.gs.engine.kms.sdk.vo.search.SuggestVO;
import com.chinatelecom.gs.engine.kms.search.IndexBuild;
import com.chinatelecom.gs.engine.kms.search.SearchService;
import com.chinatelecom.gs.engine.kms.search.model.BaseIndexData;
import com.chinatelecom.gs.engine.kms.search.model.KmsIndexCreateConfig;
import com.chinatelelcom.gs.engine.sdk.common.Page;
import com.chinatelelcom.gs.engine.sdk.common.PageImpl;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Strings;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;
import java.io.IOException;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.chinatelecom.gs.engine.kms.search.IndexBuild.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024年12月05日
 */
@Service
@Validated
@Slf4j
public class AiSearchService implements SearchService {

    @Resource
    @Lazy
    private AiSearchClient aiSearchClient;

    @Resource
    private GsGlobalConfig gsGlobalConfig;

    @Resource
    @Lazy
    private KnowledgeRepository knowledgeRepository;

    @Resource
    @Lazy
    private KnowledgeBaseRepository knowledgeBaseRepository;

    @Resource
    private SearchFlowProcess searchFlowProcess;

    @Resource
    private TagRelationRepository tagRelationRepository;

    @Resource
    private TagAppRepository tagAppRepository;

    @Resource
    private SearchAnalyzerConfigService searchAnalyzerConfig;

    @Override
    public void createOrUpdateIndex(String knowledgeBaseCode, KmsIndexCreateConfig config) {
        try {
            log.info("开始创建知识库索引，knowledgeBaseCode:{}, config:{}", knowledgeBaseCode, JsonUtils.toJsonString(config));

            String tenantId = RequestContext.getTenantId();

            // 获取租户白名单
            List<String> tenantSynonymWhitelist = gsGlobalConfig.getSearch().getTenantSynonymWhitelist();
            boolean createWithSynonym = false;

            // 如果白名单不为空，则检查租户是否在白名单中
            if (CollectionUtils.isNotEmpty(tenantSynonymWhitelist)) {
                log.info("存在租户同义词白名单配置，当前租户:{}, 白名单:{}", tenantId, tenantSynonymWhitelist);
                createWithSynonym = tenantSynonymWhitelist.contains(tenantId);
                if (!createWithSynonym) {
                    log.info("当前租户{}不在同义词白名单中，将使用普通方式创建索引", tenantId);
                }
            }

            List<DimensionEnum> dimensions = Arrays.asList(DimensionEnum.SYSTEM, DimensionEnum.TENANT);
            List<SearchAnalyzerConfigDTO> allConfigs = searchAnalyzerConfig.getByDimensionsAndTypeWithBusinessNo(dimensions, AnalyzerTypeEnum.MUTUAL_SYNONYM, tenantId);

            // 处理同义词内容
            List<String> synonymContent = filterSynonymContent(allConfigs);

            InstanceCreateRequest.Index indexConfig = IndexBuild.buildIndex(config);
            String indexNameTest = IndexBuild.indexName(knowledgeBaseCode, EnvType.TEST);
            String indexNameProd = IndexBuild.indexName(knowledgeBaseCode, EnvType.PROD);

            if (createWithSynonym && CollectionUtils.isNotEmpty(synonymContent)) {
                log.info("知识库索引存在同义词配置，添加同义词分析器，knowledgeBaseCode:{}, tenantId:{}", knowledgeBaseCode, tenantId);

                // 使用查询到的租户ID创建同义词分析器
                List<InstanceCreateRequest.QueryAnalysis> queryAnalyses = getQueryAnalyses(synonymContent, tenantId);

                aiSearchClient.instance().createOrUpdate(InstanceCreateOrUpdateNonExistFieldsRequest.builder()
                        .appCode(aiSearchClient.getAppCode())
                        .name(indexNameTest)
                        .index(indexConfig)
                        .queryAnalysis(queryAnalyses)
                        .build());

                aiSearchClient.instance().createOrUpdate(InstanceCreateOrUpdateNonExistFieldsRequest.builder()
                        .appCode(aiSearchClient.getAppCode())
                        .name(indexNameProd)
                        .index(indexConfig)
                        .queryAnalysis(queryAnalyses)
                        .build());

                log.info("创建知识库索引并添加同义词分析器成功, knowledgeBaseCode: {}, tenantId: {}, synonyms: {}", knowledgeBaseCode, tenantId, synonymContent);
            } else {
                // 正常创建索引
                aiSearchClient.instance().createOrUpdate(InstanceCreateOrUpdateNonExistFieldsRequest.builder()
                        .name(indexNameTest).index(indexConfig).build());
                aiSearchClient.instance().createOrUpdate(InstanceCreateOrUpdateNonExistFieldsRequest.builder()
                        .name(indexNameProd).index(indexConfig).build());
            }
        } catch (IOException e) {
            BizAssert.throwBizException(e, "C0002", ArrayUtils.EMPTY_OBJECT_ARRAY, "创建索引异常");
        }
    }

    @Override
    public void deleteIndex(String knowledgeBaseCode) {
        try {
            log.info("开始删除知识库索引，knowledgeBaseCode:{}", knowledgeBaseCode);
            aiSearchClient.instance().delete(InstanceDeleteRequest.builder().instanceName(IndexBuild.indexName(knowledgeBaseCode, EnvType.TEST)).build());
            aiSearchClient.instance().delete(InstanceDeleteRequest.builder().instanceName(IndexBuild.indexName(knowledgeBaseCode, EnvType.PROD)).build());
        } catch (IOException e) {
            BizAssert.throwBizException(e, "C0002", ArrayUtils.EMPTY_OBJECT_ARRAY, "删除索引异常");
        }
    }

    @Override
    public void reindexAndMigrate(String knowledgeBaseCode, KmsIndexCreateConfig config) {
        // 构造分析器
        try {
            log.info("开始迁移知识库索引，knowledgeBaseCode:{}, config:{}", knowledgeBaseCode, JsonUtils.toJsonString(config));
            aiSearchClient.instance().updateAndMigrate(buildMigrateRequest(knowledgeBaseCode, EnvType.TEST, config));
            aiSearchClient.instance().updateAndMigrate(buildMigrateRequest(knowledgeBaseCode, EnvType.PROD, config));
        } catch (IOException e) {
            BizAssert.throwBizException(e, "C0002", ArrayUtils.EMPTY_OBJECT_ARRAY, "重新构造索引异常");
        }
    }

    @Override
    public SearchResp<? extends BaseItem> searchSync(SearchParam searchParam) {
        return searchFlowProcess.search(searchParam);
    }

    @Override
    public SearchResp<? extends BaseItem> searchChunkSync(SearchParam searchParam) {
        BizAssert.notEmpty(searchParam.getQuery(),"AA115", "查询内容不能为空");
        BizAssert.notEquals(searchParam.getRecallType(),  RecallType.filter,"AA116", "分片召回不支持filter");
        return searchFlowProcess.searchChunk(searchParam);
    }

    @Override
    public SuggestVO searchSuggestSync(SearchParam searchParam) {
        return searchFlowProcess.searchSuggest(searchParam);
    }

    @Override
    public void updatePublishStatusDel(String knowledgeCode, List<String> chunkIds) {
        KnowledgeDTO knowledgeDTO = knowledgeRepository.selectByCode(knowledgeCode);
        BizAssert.notNull(knowledgeDTO, "AA012", "知识点不存在");

        Map<String, Object> update = ImmutableMap.of(
                PUBLISH_STATUS, PublishStatus.DELETE_UNPUBLISH,
                UPDATE_TIME, getNow()
        );
        Query knowledgeQuery = Query.of(q -> q.term(t -> t.field(KNOWLEDGE_CODE).value(knowledgeDTO.getCode())));
        Query finalQuery = knowledgeQuery;
        if (CollectionUtils.isNotEmpty(chunkIds)) {
            List<FieldValue> valueList = chunkIds.stream().map(FieldValue::of).collect(Collectors.toList());
            Query ids = Query.of(q2 -> q2.terms(t -> t.field(IndexBuild.ES_ID).terms(f -> f.value(valueList))));
            finalQuery = Query.of(q -> q.bool(b -> b.must(knowledgeQuery, ids)));
        }
        updateByQuery(knowledgeDTO.getKnowledgeBaseCode(), EnvType.TEST, finalQuery, update);
    }

    @Override
    public void updatePublishStatusByFaqCodeDel(String knowledgeCode, Collection<String> faqCodes) {
        KnowledgeDTO knowledgeDTO = knowledgeRepository.selectByCode(knowledgeCode);
        BizAssert.notNull(knowledgeDTO, "B0001", "知识点不存在");

        Map<String, Object> update = ImmutableMap.of(
                PUBLISH_STATUS, PublishStatus.DELETE_UNPUBLISH,
                UPDATE_TIME, getNow()
        );
        List<FieldValue> valueList = faqCodes.stream().map(FieldValue::of).collect(Collectors.toList());
        Query query = Query.of(q -> q.bool(b -> b.must(
                Query.of(q1 -> q1.term(t -> t.field(KNOWLEDGE_CODE).value(knowledgeCode))),
                Query.of(q2 -> q2.terms(t -> t.field(IndexBuild.SPLIT_ID).terms(f -> f.value(valueList))))
        )));
        updateByQuery(knowledgeDTO.getKnowledgeBaseCode(), EnvType.TEST, query, update);

    }

    @Override
    public void updateBaseInfoByKnowledgeCode(String knowledgeCode, Map<String, Object> updateInfo) {
        if (updateInfo == null) {
            return;
        }

        KnowledgeDTO knowledgeDTO = knowledgeRepository.selectByCode(knowledgeCode);
        BizAssert.notNull(knowledgeDTO, "AA012", "知识点不存在");

        List<String> allowUpdate = ImmutableList.of(TITLE, PARENT_CATALOG_CODE, KNOWLEDGE_ON, TAG);

        Map<String, Object> update = new HashMap<>();
        for (Map.Entry<String, Object> entry : updateInfo.entrySet()) {
            if (allowUpdate.contains(entry.getKey())) {
                update.put(entry.getKey(), entry.getValue());
            }
        }
        if (PublishStatus.UNPUBLISHED != knowledgeDTO.getPublishStatus()) {
            update.put(PUBLISH_STATUS, PublishStatus.UPDATE_UNPUBLISH);
        }
        update.put(UPDATE_TIME, getNow());
        Query queryKnowledge = Query.of(q -> q.term(t -> t.field(KNOWLEDGE_CODE).value(knowledgeDTO.getCode())));
        updateByQuery(knowledgeDTO.getKnowledgeBaseCode(), EnvType.TEST, queryKnowledge, update);
    }

    @Override
    public void resetKnowledge(String knowledgeBaseCode, String knowledgeCode) {
        // 1. 删除下数据
        Query query = Query.of(q -> q.term(t -> t.field(KNOWLEDGE_CODE).value(knowledgeCode)));
        deleteByQuery(knowledgeBaseCode, EnvType.TEST, query);
        // 2. 将线上数据copy到线下
        List<Query> globalFilters = ImmutableList.of(query);

        processAllDoc(knowledgeBaseCode, EnvType.TEST, globalFilters, prodAllDoc -> {
            if (CollectionUtils.isNotEmpty(prodAllDoc)) {
                List<DocBulkRequest.Doc> pushDoc = prodAllDoc.stream().map(b -> resetDataConvert(b)).collect(Collectors.toList());
                publishData(knowledgeCode, knowledgeBaseCode, pushDoc, true, EnvType.TEST);
            }
        });
    }

    private DocBulkRequest.Doc resetDataConvert(BaseIndexData baseIndexData) {
//        baseIndexData.setPublishStatus(PublishStatus.UPDATE_UNPUBLISH);
//        baseIndexData.setUpdateTime(getNow());
        return buildDoc(baseIndexData.getDocId(), baseIndexData);
    }

    @Override
    public void pushDocData(String knowledgeCode, SplitResponse splitResponse) {
        KnowledgeDetailDTO knwlDetail = knowledgeRepository.selectKnowledgeDetail(knowledgeCode);
        BizAssert.notNull(knwlDetail, "AA012", "知识点不存在");
        String knowledgeBaseCode = knwlDetail.getKnowledgeBaseCode();
        //获取所有关联标签
        fetchTag(knwlDetail);
        List<DocBulkRequest.Doc> docList = buildPublishData(knwlDetail, splitResponse);
        publishData(knowledgeCode, knowledgeBaseCode, docList, false, EnvType.TEST);
    }


    @Override
    public void pushFaqData(KnowledgeFaqDTO knowledgeFaqDTO) {
        BizAssert.notNull(knowledgeFaqDTO, "B0001", "问答知识不能为空");
        String knowledgeCode = knowledgeFaqDTO.getKnowledgeCode();
        KnowledgeDetailDTO knwlDetail = knowledgeRepository.selectKnowledgeDetail(knowledgeCode);
        BizAssert.notNull(knwlDetail, "AA012", "知识点不存在");
        String knowledgeBaseCode = knwlDetail.getKnowledgeBaseCode();
        //获取所有关联标签
        fetchTag(knwlDetail);
        List<DocBulkRequest.Doc> docList = buildPublishData(knwlDetail, knowledgeFaqDTO);
        publishData(knowledgeCode, knowledgeBaseCode, docList, false, EnvType.TEST);
    }

    @Override
    public void pushImageData(String knowledgeCode, SplitResponse splitResponse) {
        KnowledgeDetailDTO knwlDetail = knowledgeRepository.selectKnowledgeDetail(knowledgeCode);
        BizAssert.notNull(knwlDetail, "AA012", "知识点不存在");
        String knowledgeBaseCode = knwlDetail.getKnowledgeBaseCode();

        List<SplitResult> splitResults = splitResponse.getSplitResults();
        if (CollectionUtils.isEmpty(splitResults)) {
            // 删除图片内容
            log.info("图片切分结果为空，删除图片片段，knowledgeCode:{}", knowledgeCode);
            deleteImageChunk(knowledgeCode);
            return;
        }

        Map<String, SplitResult> splitMap = splitResults.stream()
                .collect(Collectors.toMap(SplitResult::getPlaceholderId,
                        Function.identity(),
                        (oldValue, newValue) -> newValue)); // 保留新值
        Set<String> placeholderIds = splitMap.keySet();

        // 查询所有分片的数据
        List<FieldValue> valueList = placeholderIds.stream().map(FieldValue::of).collect(Collectors.toList());
        Query query = Query.of(q -> q.bool(b -> b.must(
                Query.of(q1 -> q1.term(t -> t.field(KNOWLEDGE_CODE).value(knowledgeCode))),
                Query.of(q2 -> q2.terms(t -> t.field(IndexBuild.PLACEHOLDER_ID).terms(f -> f.value(valueList))))
        )));

        List<Query> globalFilters = ImmutableList.of(query);

        processAllDoc(knowledgeBaseCode, EnvType.TEST, globalFilters, imageAllDoc -> {
            if (CollectionUtils.isNotEmpty(imageAllDoc)) {
                List<DocBulkRequest.Doc> docs = Lists.newArrayList();
                for (BaseIndexData indexData : imageAllDoc) {
                    String placeholderId = indexData.getPlaceholderId();
                    SplitResult splitResult = splitMap.get(placeholderId);
                    if (splitResult != null) {
                        indexData.setFileContent(splitResult.getContent());
                        indexData.setFileContentVector(splitResult.getContent());
                        indexData.setFileContentSpell(splitResult.getContent());
                        DocBulkRequest.Doc doc = DocBulkRequest.Doc.<BaseIndexData>builder().id(indexData.getDocId()).action(DocAction.update).timestamp(System.currentTimeMillis()).source(indexData).build();
                        docs.add(doc);
                    }
                }
                if (CollectionUtils.isNotEmpty(docs)) {
                    publishData(knowledgeCode, knowledgeBaseCode, docs, false, EnvType.TEST);
                }
            }
        });
    }

    @Override
    public void deleteImageChunk(String knowledgeCode) {
        KnowledgeDetailDTO knwlDetail = knowledgeRepository.selectKnowledgeDetail(knowledgeCode);
        BizAssert.notNull(knwlDetail, "AA012", "知识点不存在");
        String knowledgeBaseCode = knwlDetail.getKnowledgeBaseCode();
        // 删除标记的数据
        Query delKnowledge = Query.of(q -> q.bool(b -> b.must(
                Query.of(q1 -> q1.term(t -> t.field(KNOWLEDGE_CODE).value(knowledgeCode))),
                Query.of(q1 -> q1.term(t -> t.field(SPLIT_INDEX_TYPE).value(SplitIndexType.IMAGE.name())))
        )));
        deleteByQuery(knowledgeBaseCode, EnvType.TEST, delKnowledge);
    }

    @Override
    public void deleteKnowledge(String knowledgeCode) {
        KnowledgeDTO knowledgeDTO = knowledgeRepository.selectByCode(knowledgeCode);
        BizAssert.notNull(knowledgeDTO, "AA012", "知识点不存在");
        Query query = Query.of(q -> q.term(t -> t.field(KNOWLEDGE_CODE).value(knowledgeCode)));
        deleteByQuery(knowledgeDTO.getKnowledgeBaseCode(), EnvType.TEST, query);
    }

    @Override
    public void deleteFaqData(String knowledgeCode, String faqCode) {
        KnowledgeDTO knowledgeDTO = knowledgeRepository.selectByCode(knowledgeCode);
        BizAssert.notNull(knowledgeDTO, "AA012", "知识点不存在");
        Query query = Query.of(q -> q.bool(b -> b.must(
                Query.of(q1 -> q1.term(t -> t.field(KNOWLEDGE_CODE).value(knowledgeCode))),
                Query.of(q2 -> q2.term(t -> t.field(IndexBuild.SPLIT_ID).value(faqCode)))
        )));
        deleteByQuery(knowledgeDTO.getKnowledgeBaseCode(), EnvType.TEST, query);
    }

    @Override
    public void deleteChunk(String knowledgeCode, String chunkId) {
        KnowledgeDTO knowledgeDTO = knowledgeRepository.selectByCode(knowledgeCode);
        BizAssert.notNull(knowledgeDTO, "AA012", "知识点不存在");
        Query query = Query.of(q -> q.term(t -> t.field(IndexBuild.ES_ID).value(chunkId)));
        deleteByQuery(knowledgeDTO.getKnowledgeBaseCode(), EnvType.TEST, query);
    }

    @Override
    public void deleteChunk(String knowledgeCode, List<String> chunkId) {
        KnowledgeDTO knowledgeDTO = knowledgeRepository.selectByCode(knowledgeCode);
        BizAssert.notNull(knowledgeDTO, "AA012", "知识点不存在");
        List<FieldValue> valueList = chunkId.stream().map(FieldValue::of).collect(Collectors.toList());
        Query query = Query.of(q -> q.terms(t -> t.field(IndexBuild.ES_ID).terms(f -> f.value(valueList))));
        deleteByQuery(knowledgeDTO.getKnowledgeBaseCode(), EnvType.TEST, query);
    }

    @Override
    public void knowledgeSwitchOn(String knowledgeCode, boolean on) {
        KnowledgeDTO knowledgeDTO = knowledgeRepository.selectByCode(knowledgeCode);
        BizAssert.notNull(knowledgeDTO, "AA012", "知识点不存在");
        Query query = Query.of(q -> q.term(t -> t.field(KNOWLEDGE_CODE).value(knowledgeCode)));
        Map<String, Object> update = new HashMap<>();
        update.put(KNOWLEDGE_ON, on);
        update.put(UPDATE_TIME, getNow());
        if (PublishStatus.UNPUBLISHED != knowledgeDTO.getPublishStatus()) {
            update.put(PUBLISH_STATUS, PublishStatus.UPDATE_UNPUBLISH.name());
        }

        updateByQuery(knowledgeDTO.getKnowledgeBaseCode(), EnvType.TEST, query, update);
    }

    @Override
    public void faqSwitchOn(String knowledgeCode, String faqCode, boolean on, boolean updateUpdateStatus) {
        KnowledgeDTO knowledgeDTO = knowledgeRepository.selectByCode(knowledgeCode);
        BizAssert.notNull(knowledgeDTO, "AA012", "知识点不存在");
        Query query = Query.of(q -> q.bool(b -> b.must(
                Query.of(q1 -> q1.term(t -> t.field(KNOWLEDGE_CODE).value(knowledgeCode))),
                Query.of(q2 -> q2.term(t -> t.field(IndexBuild.SPLIT_ID).value(faqCode)))
        )));
        Map<String, Object> update = new HashMap<>();
        update.put(KNOWLEDGE_ON, on);
        update.put(UPDATE_TIME, getNow());

        if (updateUpdateStatus) {
            update.put(PUBLISH_STATUS, PublishStatus.UPDATE_UNPUBLISH.name());
        }
        updateByQuery(knowledgeDTO.getKnowledgeBaseCode(), EnvType.TEST, query, update);
    }

    @Override
    public void chunkSwitchOn(String knowledgeCode, String chunkId, boolean on) {
        KnowledgeDTO knowledgeDTO = knowledgeRepository.selectByCode(knowledgeCode);
        BizAssert.notNull(knowledgeDTO, "AA012", "知识点不存在");
        Query query = Query.of(q -> q.term(t -> t.field(IndexBuild.ES_ID).value(chunkId)));
        Map<String, Object> update = new HashMap<>();
        update.put(KNOWLEDGE_ON, on);
        update.put(UPDATE_TIME, getNow());
        if (PublishStatus.UNPUBLISHED != knowledgeDTO.getPublishStatus()) {
            update.put(PUBLISH_STATUS, PublishStatus.UPDATE_UNPUBLISH.name());
        }
        updateByQuery(knowledgeDTO.getKnowledgeBaseCode(), EnvType.TEST, query, update);
        refresh(knowledgeDTO.getKnowledgeBaseCode(), EnvType.TEST);
    }

    @Override
    public void updateChunk(String knowledgeCode, String chunkId, String content) {
        KnowledgeDTO knowledgeDTO = knowledgeRepository.selectByCode(knowledgeCode);
        BizAssert.notNull(knowledgeDTO, "AA012", "知识点不存在");
        Map<String, Object> update = new HashMap<>();
        update.put(CONTENT, content);
        update.put(CONTENT_VECTOR, content);
        update.put(CONTENT_SPELL, content);
        update.put(UPDATE_TIME, getNow());
        if (PublishStatus.UNPUBLISHED != knowledgeDTO.getPublishStatus()) {
            update.put(PUBLISH_STATUS, PublishStatus.UPDATE_UNPUBLISH.name());
        }

        ObjectNode source = getSource(knowledgeDTO.getKnowledgeBaseCode(), EnvType.TEST, chunkId);
        updateVector(knowledgeDTO.getKnowledgeBaseCode(), knowledgeCode, source, update);
        refresh(knowledgeDTO.getKnowledgeBaseCode(), EnvType.TEST);
    }

    @Override
    public Page<ChunkDataVO> pageChunk(ChunkQueryParam chunkQueryParam) {
        KnowledgeDTO knowledgeDTO = knowledgeRepository.selectByCode(chunkQueryParam.getKnowledgeCode());
        BizAssert.notNull(knowledgeDTO, "AA012", "知识点不存在");
        Integer pageNum = chunkQueryParam.getPageNum();
        Integer pageSize = chunkQueryParam.getPageSize();
        Integer maxFrom = gsGlobalConfig.getIndex().getMaxFrom();
        if (pageNum * pageSize > maxFrom) {
            throw new BizException("AA118", new String[]{maxFrom.toString()}, "不支持查看超过分片数超过{}之后的数据", maxFrom);
        }
        List<Query> globalFilters = buildPageChunkGlobalFilters(chunkQueryParam);

        SearchRequest request = SearchRequest.builder().instanceName(IndexBuild.indexName(knowledgeDTO.getKnowledgeBaseCode(), EnvType.TEST))
                .globalFilters(globalFilters).type(SearchType.TRADITION)
                .sort(ImmutableList.of(SortOptions.of(s -> s.field(f -> f.field(INDEX).order(SortOrder.Asc)))))
                .trackTotalHits(true)
                .page(chunkQueryParam.getPageNum()).size(chunkQueryParam.getPageSize()).build();

        try {
            SearchResponse<BaseIndexData> resp = aiSearchClient.online().search(request, BaseIndexData.class);
            if (resp != null && resp.isSuccess() && resp.getData() != null && CollectionUtils.isNotEmpty(resp.getData().getItems())) {
                long total = resp.getData().getTotal();
                List<ChunkDataVO> result = new ArrayList<>(resp.getData().getItems().size());
                for (int i = 0; i < resp.getData().getItems().size(); i++) {
                    SearchResponse.RetrieveItem<BaseIndexData> item = resp.getData().getItems().get(i);
                    long currentIndex = (long) (pageNum - 1) * pageSize + i + 1;
                    result.add(convertChunkDataVO(knowledgeDTO, item, total, currentIndex));
                }

                long pages = (long) Math.ceil((double) total / pageSize);
                return PageImpl.of(pageNum, pageSize, total, pages, result);
            }
        } catch (IOException e) {
            log.error("C0002", "搜索异常", e);
            BizAssert.throwBizException(e, "C0002", ArrayUtils.EMPTY_OBJECT_ARRAY, "搜索异常");
        }
        return PageImpl.of(0, 0);
    }

    @Override
    public BaseIndexData queryByChunkId(String knowledgeCode, String chunkId) {
        KnowledgeDTO knowledgeDTO = knowledgeRepository.selectByCode(knowledgeCode);
        BizAssert.notNull(knowledgeDTO, "AA012", "知识点不存在");
        BaseIndexData indexData = queryByChunkId(knowledgeDTO.getKnowledgeBaseCode(), EnvType.TEST, chunkId);
        return indexData;
    }


    @Override
    public void publishDoc(KnowledgeBaseDTO knowledgeBaseDTO, KnowledgeDTO knowledgeDTO) {
        PublishStatus publishStatus = knowledgeDTO.getPublishStatus();
        String knowledgeCode = knowledgeDTO.getCode();
        String knowledgeBaseCode = knowledgeBaseDTO.getCode();

        // 1. 删除线上数据
        Query queryKnowledge = Query.of(q -> q.term(t -> t.field(KNOWLEDGE_CODE).value(knowledgeCode)));
        deleteByQuery(knowledgeBaseCode, EnvType.PROD, queryKnowledge);

        if (PublishStatus.DELETE_UNPUBLISH == publishStatus) {
            // 2. 删除线下数据
            deleteByQuery(knowledgeBaseCode, EnvType.TEST, queryKnowledge);
        } else {
            // 2. 删除标记的数据
            Query delKnowledge = Query.of(q -> q.bool(b -> b.must(
                    Query.of(q1 -> q1.term(t -> t.field(KNOWLEDGE_CODE).value(knowledgeCode))),
                    Query.of(q1 -> q1.term(t -> t.field(PUBLISH_STATUS).value(PublishStatus.DELETE_UNPUBLISH.name())))
            )));
            deleteByQuery(knowledgeBaseCode, EnvType.TEST, delKnowledge);
            // 删除有刷盘延迟，可能导致线上数据未删除和状态更新有冲突，这里加一个手动刷盘操作
            refresh(knowledgeBaseCode, EnvType.TEST);

            // 3. 更新线下数据状态
            Map<String, Object> update = ImmutableMap.of(
                    PUBLISH_STATUS, PublishStatus.PUBLISHED,
                    PUBLISH_TIME, getNow(),
                    UPDATE_TIME, getNow()
            );
            Query updateKnowledge = Query.of(q -> q.bool(b ->
                    b.must(Query.of(q1 -> q1.term(t -> t.field(KNOWLEDGE_CODE).value(knowledgeCode))))
                            .mustNot(Query.of(q1 -> q1.term(t -> t.field(PUBLISH_STATUS).value(PublishStatus.DELETE_UNPUBLISH.name()))))));
            updateByQuery(knowledgeBaseCode, EnvType.TEST, updateKnowledge, update);

            // 4. 将线下数据copy到线上
            copyData2Prod(queryKnowledge, knowledgeBaseCode, knowledgeCode);
        }
    }


    @Override
    public void publishFaq(KnowledgeBaseDTO knowledgeBaseDTO, KnowledgeFaqDTO knowledgeFaqDTO) {
        PublishStatus publishStatus = knowledgeFaqDTO.getPublishStatus();
        String knowledgeCode = knowledgeBaseDTO.getCode();
        String faqCode = knowledgeFaqDTO.getCode();
        String knowledgeBaseCode = knowledgeBaseDTO.getCode();

        // 1. 删除线上数据
        Query queryFaq = Query.of(q -> q.term(t -> t.field(SPLIT_ID).value(faqCode)));
        deleteByQuery(knowledgeBaseCode, EnvType.PROD, queryFaq);

        if (PublishStatus.DELETE_UNPUBLISH == publishStatus) {
            // 2. 删除线下数据
            deleteByQuery(knowledgeBaseCode, EnvType.TEST, queryFaq);
        } else {
            // 2. 更新线下数据状态
            Map<String, Object> update = ImmutableMap.of(
                    PUBLISH_STATUS, PublishStatus.PUBLISHED,
                    PUBLISH_TIME, getNow(),
                    UPDATE_TIME, getNow()
            );
            updateByQuery(knowledgeBaseCode, EnvType.TEST, queryFaq, update);

            // 3. 将线下数据copy到线上
            copyData2Prod(queryFaq, knowledgeBaseCode, knowledgeCode);
        }
    }

    @Override
    public void publishKnowledgeDoc(KnowledgeBaseDTO knowledgeBaseDTO, KnowledgeDTO knowledgeDTO) {
        PublishStatus publishStatus = knowledgeDTO.getPublishStatus();
        String knowledgeCode = knowledgeDTO.getCode();
        String knowledgeBaseCode = knowledgeBaseDTO.getCode();


        Query queryKnowledge = Query.of(q -> q.bool(b -> b.must(
                Query.of(q1 -> q1.term(t -> t.field(KNOWLEDGE_CODE).value(knowledgeCode))),
                Query.of(q1 -> q1.term(t -> t.field(SPLIT_INDEX_TYPE).value(SplitIndexType.DOC.name()))),
                Query.of(q1 -> q1.term(t -> t.field(INDEX).value("0")))
        )));
        // 暂时只处理发布成功的情况
        if (PublishStatus.PUBLISHED == publishStatus) {
            // 1. 删除线上数据
            deleteByQuery(knowledgeBaseCode, EnvType.PROD, queryKnowledge);

            // 2. 更新线下数据状态
            Map<String, Object> update = ImmutableMap.of(
                    PUBLISH_STATUS, PublishStatus.PUBLISHED,
                    PUBLISH_TIME, getNow(),
                    UPDATE_TIME, getNow()
            );
            updateByQuery(knowledgeBaseCode, EnvType.TEST, queryKnowledge, update);

            // 3. 将线下数据copy到线上
            copyData2Prod(queryKnowledge, knowledgeBaseCode, knowledgeCode);
        }

    }

    public void refresh(String knowledgeBaseCode, EnvType env) {
        try {
            InstanceRefreshRequest req = InstanceRefreshRequest.builder().instanceName(IndexBuild.indexName(knowledgeBaseCode, env))
                    .build();
            aiSearchClient.instance().refresh(req);
        } catch (IOException e) {
            log.error("刷新索引异常:knowledgeBaseCode:{}", knowledgeBaseCode, e);
            BizAssert.throwBizException(e, "C0002", ArrayUtils.EMPTY_OBJECT_ARRAY, "刷新索引异常");
        }
    }

    @Override
    public Page<ChunkCompareDataVO> pageChunkCompare(ChunkCompareQueryParam param) {
        KnowledgeDTO knowledgeDTO = knowledgeRepository.selectByCode(param.getKnowledgeCode());
        BizAssert.notNull(knowledgeDTO, "AA012", "知识点不存在");

        ChunkQueryParam queryParam = new ChunkQueryParam();
        queryParam.setKnowledgeCode(param.getKnowledgeCode());
        queryParam.setParseType(param.getParseType());
        queryParam.setFilterDelFlag(false);
        queryParam.setPageNum(param.getPageNum());
        queryParam.setPageSize(param.getPageSize());
        if (CollectionUtils.isNotEmpty(param.getChangeStatus())) {
            queryParam.setPublishStatuses(DataChangeStatus.convert(param.getChangeStatus()));
        }

        Page<ChunkDataVO> page = pageChunk(queryParam);
        List<ChunkDataVO> chunkDataVOS = page.getRecords();
        if (CollectionUtils.isNotEmpty(chunkDataVOS)) {
            List<String> ids = chunkDataVOS.stream().map(ChunkDataVO::getId).collect(Collectors.toList());
            List<FieldValue> valueList = ids.stream().map(FieldValue::of).collect(Collectors.toList());
            Query query = Query.of(q -> q.terms(t -> t.field(IndexBuild.ES_ID).terms(f -> f.value(valueList))));
            List<Query> globalFilters = ImmutableList.of(query);
            List<BaseIndexData> prod = queryAllDoc(knowledgeDTO.getKnowledgeBaseCode(), EnvType.PROD, globalFilters);
            Map<String, BaseIndexData> prodMap = prod.stream().collect(Collectors.toMap(BaseIndexData::getDocId, Function.identity()));
            Page<ChunkCompareDataVO> result = IPageUtils.convert(page, chunkDataVO -> convert2CompareData(chunkDataVO, prodMap));
            return result;
        }
        return PageImpl.of(0, 0);
    }

    private ChunkCompareDataVO convert2CompareData(ChunkDataVO chunkDataVO, Map<String, BaseIndexData> prodMap) {
        ChunkCompareDataVO result = new ChunkCompareDataVO();
        ChunkCompareInfo test = CommonConverter.INSTANCE.convert(chunkDataVO);
        test.setChangeStatus(DataChangeStatus.convert(chunkDataVO.getPublishStatus()));
        result.setTest(test);
        BaseIndexData prodBase = prodMap.get(chunkDataVO.getId());
        if (prodBase != null) {
            ChunkCompareInfo prod = CommonConverter.INSTANCE.convert2ChunkCompare(prodBase);
            prod.setId(prodBase.getDocId());
            result.setProd(prod);
        }
        return result;
    }

    private void copyData2Prod(Query query, String knowledgeBaseCode, String knowledgeCode) {
        List<Query> globalFilters = ImmutableList.of(query);
        processAllDoc(knowledgeBaseCode, EnvType.TEST, globalFilters, testAllDoc -> {
            if (CollectionUtils.isNotEmpty(testAllDoc)) {
                List<DocBulkRequest.Doc> pushDoc = testAllDoc.stream().map(b -> publishDataConvert(b)).collect(Collectors.toList());
                publishData(knowledgeCode, knowledgeBaseCode, pushDoc, false, EnvType.PROD);
            }
        });
    }

    private DocBulkRequest.Doc publishDataConvert(BaseIndexData baseIndexData) {
        baseIndexData.setPublishStatus(PublishStatus.PUBLISHED);
        baseIndexData.setUpdateTime(getNow());
        baseIndexData.setPublishTime(getNow());
        return buildDoc(baseIndexData.getDocId(), baseIndexData);
    }

    /**
     * 查询所有文档
     *
     * @param knowledgeBaseCode
     * @param env
     * @param globalFilters
     * @return
     */
    private List<BaseIndexData> queryAllDoc(String knowledgeBaseCode, EnvType env, List<Query> globalFilters) {
        final List<BaseIndexData> result = new ArrayList<>();
        processAllDoc(knowledgeBaseCode, env, globalFilters, allDoc -> result.addAll(allDoc));
        return result;
    }

    /**
     * 遍历处理所有符合条件的数据
     * @param knowledgeBaseCode
     * @param env
     * @param globalFilters
     * @param consumer
     */
    private void processAllDoc(String knowledgeBaseCode, EnvType env, List<Query> globalFilters, Consumer<List<BaseIndexData>> consumer) {
        try {
            //避免爆内存，暂时限制100页
            Integer maxPageNum = ObjectUtils.defaultIfNull(gsGlobalConfig.getKmsBizConfig().getSearchMaxBatchPageNum(), 10000);
            Integer pageSize = ObjectUtils.defaultIfNull(gsGlobalConfig.getKmsBizConfig().getSearchMaxBatchPageSize(), 5000);
            Integer pageNum = 1;
            String searchAfterId = null;
            while (pageNum <= maxPageNum) {
                SearchRequest request = SearchRequest.builder().instanceName(IndexBuild.indexName(knowledgeBaseCode, env))
                        .globalFilters(globalFilters).type(SearchType.TRADITION)
                        .searchAfterId(searchAfterId)
                        .sort(ImmutableList.of(SortOptions.of(s -> s.field(f -> f.field(ID).order(SortOrder.Asc)))))
                        .size(pageSize)
                        .build();
                SearchResponse<BaseIndexData> resp = aiSearchClient.online().search(request, BaseIndexData.class);
                if (resp != null && resp.isSuccess() && resp.getData() != null && CollectionUtils.isNotEmpty(resp.getData().getItems())) {
                    List<SearchResponse.RetrieveItem<BaseIndexData>> items = resp.getData().getItems();
                    List<BaseIndexData> currentIndex = items.stream().map(SearchResponse.RetrieveItem::getSource).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(currentIndex)) {
                        consumer.accept(currentIndex);
                    }

                    String currentSearchAfterId = items.get(items.size() - 1).getSearchAfterId();
                    if (StringUtils.isNotBlank(currentSearchAfterId) && Strings.CS.equals(searchAfterId, currentSearchAfterId)) {
                        log.error("当前轮次searchAfterId和上轮一致，搜索异常，currentSearchAfterId：{}", currentSearchAfterId);
                        break;
                    }
                    searchAfterId = currentSearchAfterId;
                    if (currentIndex.size() < pageSize) {
                        break;
                    } else {
                        pageNum++;
                    }
                } else {
                    break;
                }
            }
        } catch (IOException e) {
            log.error("C0002", "搜索异常", e);
            BizAssert.throwBizException(e, "C0002", ArrayUtils.EMPTY_OBJECT_ARRAY, "搜索异常");
        }
    }

    private ChunkDataVO convertChunkDataVO(KnowledgeDTO knowledgeDTO, SearchResponse.RetrieveItem<BaseIndexData> item, long total, long currentIndex) {
        ChunkDataVO result = new ChunkDataVO();
        BaseIndexData baseIndexData = item.getSource();

        result.setId(item.getId());
        result.setType(baseIndexData.getType());
        result.setKnowledgeCode(baseIndexData.getKnowledgeCode());
        result.setKnowledgeOn(baseIndexData.getKnowledgeOn());
        result.setUpdateTime(baseIndexData.getUpdateTime());
        result.setFileContent(baseIndexData.getFileContent());
        if (StringUtils.isNotBlank(baseIndexData.getFileOriginalContent()) &&
                !StringUtils.equals(baseIndexData.getFileContent(), baseIndexData.getFileOriginalContent())) {
            result.setFileOriginalContent(baseIndexData.getFileOriginalContent());
            result.setFileOriginalContentUrl(BaseFileUrlUtils.getBaseDownloadUrl(baseIndexData.getFileOriginalContent()));
        }
        result.setSplitIndexType(baseIndexData.getSplitIndexType());
        result.setParseType(baseIndexData.getParseType());
        result.setFileIndex(baseIndexData.getFileIndex());
        result.setTotal(total);
        result.setCurrentIndex(currentIndex);

        BaseItem.Source source = convertSource(baseIndexData.getFileSource());
        result.setSource(source);
        result.setPublishStatus(baseIndexData.getPublishStatus());

        SplitIndexType splitIndexType = baseIndexData.getSplitIndexType();
        if (Objects.equals(splitIndexType, SplitIndexType.IMAGE)) {
            result.setParseStatus(knowledgeDTO.getImageIndexingStatus());
        } else {
            result.setParseStatus(knowledgeDTO.getIndexingStatus());
        }

        return result;
    }

    private BaseItem.Source convertSource(String fileSource) {
        if (StringUtils.isBlank(fileSource)) {
            return null;
        }

        SplitResult.SegSource esSource = JsonUtils.parseObject(fileSource, SplitResult.SegSource.class);
        BaseItem.Source source = new BaseItem.Source();
        if (esSource.getStart() != null) {
            source.setStart(esSource.getStart().intValue());
        }
        if (esSource.getEnd() != null) {
            source.setEnd(esSource.getEnd().intValue());

        }
        source.setStartPoint(esSource.getStartPoint());
        source.setEndPoint(esSource.getEndPoint());
        source.setCharPos(esSource.getCharPos());
        source.setOriginModal(esSource.getOriginModal());
        return source;
    }

    private List<Query> buildPageChunkGlobalFilters(ChunkQueryParam chunkQueryParam) {
        List<Query> queryList = new ArrayList<>();

        Query query = Query.of(q -> q.term(t -> t.field(KNOWLEDGE_CODE).value(chunkQueryParam.getKnowledgeCode())));
        queryList.add(query);

        if (chunkQueryParam.getParseType() != null) {
            Query query1 = Query.of(q -> q.term(t -> t.field(PARSE_TYPE).value(chunkQueryParam.getParseType().name())));
            queryList.add(query1);
        }

        if (StringUtils.isNotBlank(chunkQueryParam.getContent())) {
            Query query2 = Query.of(q -> q.matchPhrase(b1 -> b1.field(CONTENT).query(chunkQueryParam.getContent())));
            queryList.add(query2);
        }

        if (chunkQueryParam.isFilterDelFlag()) {
            //过滤掉删除未发布状态
            Query publishStatusQuery = Query.of(q -> q.term(t -> t.field(IndexBuild.PUBLISH_STATUS).value(PublishStatus.DELETE_UNPUBLISH.name())));
            Query notFilter = Query.of(q -> q.bool(b -> b.mustNot(publishStatusQuery)));
            queryList.add(notFilter);
        }

        if (CollectionUtils.isNotEmpty(chunkQueryParam.getPublishStatuses())) {
            List<FieldValue> valueList = chunkQueryParam.getPublishStatuses().stream().map(t -> FieldValue.of(t.name())).collect(Collectors.toList());
            Query publishStatusFilter = Query.of(q2 -> q2.terms(t -> t.field(IndexBuild.PUBLISH_STATUS).terms(f -> f.value(valueList))));
            queryList.add(publishStatusFilter);
        }


        return queryList;
    }

    private void publishData(String knowledgeCode, String knowledgeBaseCode, List<DocBulkRequest.Doc> docList, boolean syncIndex, EnvType envType) {
        if (CollectionUtils.isEmpty(docList)) {
            return;
        }

        try {
            Integer maxBatchSize = ObjectUtils.defaultIfNull(gsGlobalConfig.getKmsBizConfig().getPushSearchMaxBatchSize(), 100);
            List<List<DocBulkRequest.Doc>> shards = Lists.partition(docList, maxBatchSize);

            log.info("开始推送切分数据至ES，knowledgeCode:{}", knowledgeCode);
            for (List<DocBulkRequest.Doc> shard : shards) {
                RetryUtils.withRetry(() -> {
                    if (log.isDebugEnabled()) {
                        log.debug("请求推送数据：{}", JsonUtils.toJsonString(shard));
                    }

                    DocBulkWithParamRequest request = DocBulkWithParamRequest.builder().appCode(aiSearchClient.getAppCode())
                            .instanceName(IndexBuild.indexName(knowledgeBaseCode, envType))
                            .docs(shard).writeHistory(false).syncIndexing(syncIndex).build();
                    DocResponse docResponse = aiSearchClient.doc().bulkWithParam(request);
                    if (docResponse.isSuccess()) {
                        log.info("推送切分数据至ES成功，knowledgeCode:{}", knowledgeCode);
                    } else {
                        log.error("推送切分数据至ES失败，knowledgeCode:{}, docResponse:{}", knowledgeCode, JsonUtils.toJsonString(docResponse));
                    }
                    return null;
                }, 3);
            }
            log.info("推送文档至ES所有切片完成，knowledgeCode:{}", knowledgeCode);
        } catch (Exception e) {
            throw new BizException(e, "AA006", "添加索引数据失败");
        }
    }

    private List<DocBulkRequest.Doc> buildPublishData(KnowledgeDetailDTO knwlDetail, SplitResponse splitResponse) {
        List<DocBulkRequest.Doc> split = new ArrayList<>();

        // 1. 原始知识维度的数据
        BaseIndexData indexData = buildBaseIndexData(knwlDetail);

        if (Objects.equals(knwlDetail.getType(), KnowledgeType.IMAGE)) {
            indexData.setFileOriginalContent(knwlDetail.getOriginalFileKey());
        }

        // 知识维度数据
        split.add(buildDoc(indexData.getDocId(), indexData));

        // 2. 切分数据
        String knowledgeCode = knwlDetail.getCode();
        List<SplitResult> splitResults = splitResponse.getSplitResults();
        Integer index = 1;
        if (CollectionUtils.isNotEmpty(splitResults)) {
            if (Objects.equals(knwlDetail.getType(), KnowledgeType.VIDEO)) {
                SplitResult oneSplit = splitResults.get(0);
                if (oneSplit != null && !StringUtils.equals(oneSplit.getContent(), oneSplit.getOriginalContent())
                        && StringUtils.contains(oneSplit.getOriginalContent(), "/")) {
                    indexData.setFileOriginalContent(oneSplit.getOriginalContent());
                }
            }

            // 算法这里直接返回压缩图片
            if (Objects.equals(knwlDetail.getType(), KnowledgeType.IMAGE)) {
                SplitResult oneSplit = splitResults.get(0);
                if (oneSplit != null && StringUtils.contains(oneSplit.getOriginalContent(), "/")) {
                    indexData.setFileOriginalContent(oneSplit.getOriginalContent());
                }
            }

            for (SplitResult splitResultItem : splitResults) {
                BaseIndexData splitIndex = CommonConverter.INSTANCE.convert(indexData);
                splitIndex.setDocId(knowledgeCode + "_" + index);
                splitIndex.setPlaceholderId(splitResultItem.getPlaceholderId());
                splitIndex.setFileSplitId(knwlDetail.getCode());
                if (!StringUtils.equals(splitResultItem.getOriginalContent(), splitResultItem.getContent())
                        && StringUtils.contains(splitResultItem.getOriginalContent(), "/")) {
                    splitIndex.setFileOriginalContent(splitResultItem.getOriginalContent());
                }
                String content = StringUtils.defaultIfBlank(splitResultItem.getContent(), null);
                splitIndex.setFileContentVector(content);
                splitIndex.setFileContentSpell(content);
                splitIndex.setFileContent(content);
                splitIndex.setSplitIndexType(splitResultItem.getSplitIndexType());
                splitIndex.setParseType(splitResultItem.getSplitParseType());
                splitIndex.setFileSource(JsonUtils.toJsonString(splitResultItem.getSource()));
                splitIndex.setFileIndex(index);
                index++;
                splitIndex.setTags(knwlDetail.getTagCodes());
                split.add(buildDoc(splitIndex.getDocId(), splitIndex));
            }
        } else {
            log.warn("分片结果数据为空， knowledgeCode:{}", knwlDetail.getCode());
        }
        return split;
    }

    private List<DocBulkRequest.Doc> buildPublishData(KnowledgeDetailDTO knwlDetail, KnowledgeFaqDTO knowledgeFaqDTO) {
        List<DocBulkRequest.Doc> split = new ArrayList<>();

        // 1. 原始知识维度的数据
        BaseIndexData indexData = buildBaseIndexData(knwlDetail);
        split.add(buildDoc(indexData.getDocId(), indexData));

        // 2. 切分数据
        List<String> allCorpus = new ArrayList<>();
        allCorpus.add(knowledgeFaqDTO.getQuestion());
        if (CollectionUtils.isNotEmpty(knowledgeFaqDTO.getCorpus())) {
            allCorpus.addAll(knowledgeFaqDTO.getCorpus());
        }

        String faqCode = knowledgeFaqDTO.getCode();
        for (int i = 0; i < allCorpus.size(); i++) {
            String corpus = allCorpus.get(i);
            Integer index = i + 1;
            BaseIndexData splitIndex = CommonConverter.INSTANCE.convert(indexData);
            splitIndex.setDocId(StringUtils.join(faqCode, "_", index));
            splitIndex.setPlaceholderId(StringUtils.join(faqCode, "_", index));
            splitIndex.setFileSplitId(faqCode);
            splitIndex.setFileOriginalContent(corpus);
            splitIndex.setFileContentVector(corpus);
            splitIndex.setFileContentSpell(corpus);
            splitIndex.setFileContent(corpus);
            splitIndex.setSplitIndexType(SplitIndexType.FAQ);
//            splitIndex.setParseType();
//            splitIndex.setFileSource(JsonUtils.toJsonString(splitResultItem.getSource()));
            splitIndex.setFileIndex(index);
            splitIndex.setTags(knwlDetail.getTagCodes());
            split.add(buildDoc(splitIndex.getDocId(), splitIndex));
        }
        return split;
    }


    private <T> DocBulkRequest.Doc<T> buildDoc(String id, T data) {
        return buildDoc(id, data, DocAction.index);
    }

    private <T> DocBulkRequest.Doc<T> buildDoc(String id, T data, DocAction action) {
        return DocBulkRequest.Doc.<T>builder().id(id)
                .action(action).timestamp(System.currentTimeMillis()).source(data).build();
    }

    private BaseIndexData buildBaseIndexData(KnowledgeDetailDTO knwlDetail) {
        BaseIndexData baseIndexData = new BaseIndexData();
        baseIndexData.setDocId(knwlDetail.getCode() + "_0");
        baseIndexData.setTitle(knwlDetail.getName());
        baseIndexData.setType(knwlDetail.getType());
        baseIndexData.setKnowledgeCode(knwlDetail.getCode());
        if (!Objects.equals(knwlDetail.getType(), KnowledgeType.FAQ)) {
            baseIndexData.setFileSplitId(knwlDetail.getCode());
        }
        baseIndexData.setParentCatalogCode(knwlDetail.getParentCatalogCode());
        baseIndexData.setKnowledgeBaseCode(knwlDetail.getKnowledgeBaseCode());
        baseIndexData.setKnowledgeOn(knwlDetail.getOn());
        baseIndexData.setSplitIndexType(SplitIndexType.DOC);
        baseIndexData.setUpdateTime(knwlDetail.getUpdateTime().toEpochSecond(ZoneOffset.UTC) * 1000);
        baseIndexData.setPublishTime(null);
        baseIndexData.setPublishStatus(PublishStatus.UNPUBLISHED);
        baseIndexData.setFile(knwlDetail.getOriginalFileKey());
        baseIndexData.setFileIndex(0);
        baseIndexData.setTags(knwlDetail.getTagCodes());
        return baseIndexData;
    }

    private void updateByQuery(String knowledgeBaseCode, EnvType env, Query query, Map<String, Object> updateInfo) {
        if (MapUtils.isEmpty(updateInfo)) {
            return;
        }

        StringBuilder sb = new StringBuilder();

        String updateScript = null;
        for (Map.Entry<String, Object> entry : updateInfo.entrySet()) {
            String key = entry.getKey();
            Object valueObj = entry.getValue();
            if (valueObj instanceof Collection || valueObj instanceof Map) {
                String value = JsonUtils.toJsonString(valueObj);
                updateScript = "ctx._source.%s = %s;".formatted(key, value);
            } else {
                String value = String.valueOf(entry.getValue());
                updateScript = "ctx._source.%s = '%s';".formatted(key, value);
            }
            sb.append(updateScript);
        }

        InstanceUpdateByQueryRequest request = InstanceUpdateByQueryRequest.builder()
                .instanceName(IndexBuild.indexName(knowledgeBaseCode, env))
                .query(query)
                .script(Script.of(s -> s.inline(i -> i.source(sb.toString())))).build();
        log.info("updateByQuery: " + request.toString());
        try {
            aiSearchClient.instance().updateByQuery(request);
        } catch (IOException e) {
            log.error("AA010", "更新数据异常", e);
            BizAssert.throwBizException(e, "AA010", ArrayUtils.EMPTY_OBJECT_ARRAY, "更新数据异常");
        }
    }

    /**
     * 向量字段单独更新
     *
     * @param knowledgeBaseCode
     * @param source
     * @param updateInfo
     * @param updateInfo
     */
    private void updateVector(String knowledgeBaseCode, String knowledgeCode, ObjectNode source, Map<String, Object> updateInfo) {
        String chunkId = source.get(ID).asText();
        for (Map.Entry<String, Object> entry : updateInfo.entrySet()) {
            source.putPOJO(entry.getKey(), entry.getValue());
        }
        DocBulkRequest.Doc doc = DocBulkRequest.Doc.<ObjectNode>builder().id(chunkId).action(DocAction.update).timestamp(System.currentTimeMillis()).source(source).build();
        publishData(knowledgeCode, knowledgeBaseCode, Lists.newArrayList(doc), true, EnvType.TEST);
    }

    /**
     * 根据片段ID获取片段
     */
    public ObjectNode getSource(String knowledgeBaseCode, EnvType env, String chunkId) {
        try {
            ObjectNode objectNode = aiSearchClient.doc().get(DocGetRequest.builder().instanceName(indexName(knowledgeBaseCode, env))
                    .id(chunkId).build(), ObjectNode.class).getData();
            if (objectNode == null) {
                throw new BizException("AA013", "数据不存在");
            }
            return objectNode;
        } catch (IOException e) {
            throw new BizException(e, "AA013", "数据不存在");
        }
    }

    /**
     * 根据片段ID获取片段
     */
    public BaseIndexData queryByChunkId(String knowledgeBaseCode, EnvType env, String chunkId) {
        try {
            BaseIndexData indexData = aiSearchClient.doc().get(DocGetRequest.builder().instanceName(indexName(knowledgeBaseCode, env))
                    .id(chunkId).build(), BaseIndexData.class).getData();
            return indexData;
        } catch (IOException e) {
            log.error("索引数据查询失败, chunkId:{}", chunkId, e);
            throw new BizException(e, "AA013", "数据不存在");
        }
    }

    private void deleteByQuery(String knowledgeBaseCode, EnvType env, Query query) {
        InstanceDeleteByQueryRequest request = InstanceDeleteByQueryRequest.builder()
                .instanceName(IndexBuild.indexName(knowledgeBaseCode, env))
                .query(query)
                .build();
        log.info("deleteByQuery: " + request.toString());
        try {
            aiSearchClient.instance().deleteByQuery(request);
        } catch (IOException e) {
            log.error("AA009", "删除索引失败", e);
            BizAssert.throwBizException(e, "AA009", ArrayUtils.EMPTY_OBJECT_ARRAY, "删除索引失败");
        }
    }

    private long getNow() {
        return System.currentTimeMillis();
    }

    /**
     * 构造迁移请求
     *
     * @param knowledgeBaseCode
     * @param env
     * @return
     * @throws IOException
     */
    private InstanceUpdateAndMigrateRequest buildMigrateRequest(String knowledgeBaseCode, EnvType env, KmsIndexCreateConfig config) throws IOException {
        InstanceCreateRequest.Index index = IndexBuild.buildIndex(config);
        return InstanceUpdateAndMigrateRequest.builder()
                .instanceName(IndexBuild.indexName(knowledgeBaseCode, env))
                .index(index)
                // publishWhenComplete为true，代表迁移完成后自动将线下实例发布到线上实例
                .publishWhenComplete(true)
                // doubleWrite为true，代表实时推送到线上的数据，线下实例会同步更新
                .doubleWrite(true)
                // 迁移模式，支持DOC_ONLY，STRUCTURE_OVERWRITE和STRUCTURE_KEEP_NEW
                .mode(MigrationMode.STRUCTURE_OVERWRITE)
                // 回调参数，不传就不回调
                .build();
    }

    private void fetchTag(KnowledgeDetailDTO knwlDetail) {
        tagRelationRepository.fetchTag(Stream.of(knwlDetail).collect(Collectors.toList()));
        if (CollectionUtils.isNotEmpty(knwlDetail.getTagCodes())) {
            List<String> tagCodes = knwlDetail.getTagCodes();
            knwlDetail.setTagCodes(new ArrayList<>(tagAppRepository.fetchAllParentTag(tagCodes)));
        }
    }


    @Override
    public void updateSynonymAnalyzer(String baseCode) {
        KnowledgeBaseDTO knowledgeBaseDTO = knowledgeBaseRepository.findByCode(baseCode);
        if (knowledgeBaseDTO == null) {
            throw new BizException("知识库编码[%s]不存在或已删除".formatted(baseCode));
        }
        String tenantId = knowledgeBaseDTO.getAppCode();

        // 同时查询系统维度和租户维度的配置
        List<DimensionEnum> dimensions = Arrays.asList(DimensionEnum.SYSTEM, DimensionEnum.TENANT);
        List<SearchAnalyzerConfigDTO> configs = searchAnalyzerConfig.getByDimensionsAndTypeWithBusinessNo(
                dimensions, AnalyzerTypeEnum.MUTUAL_SYNONYM, tenantId);


        try {
            // 构建同义词字典
            List<String> synonymContent = filterSynonymContent(configs);
            if (CollectionUtils.isEmpty(synonymContent)) {
                log.warn("未找到同义词配置，知识库:{}, 租户ID:{}", baseCode, tenantId);
                return;
            }

            // 构建查询分析配置
            List<InstanceCreateRequest.QueryAnalysis> queryAnalyses = getQueryAnalyses(synonymContent, tenantId);
            InstanceCreateRequest.Index index = InstanceCreateRequest.Index.builder().build();

            // 更新测试和生产环境索引
            String indexNameTest = indexName(baseCode, EnvType.TEST);
            String indexNameProd = indexName(baseCode, EnvType.PROD);

            InstanceCreateOrUpdateNonExistFieldsRequest build = InstanceCreateOrUpdateNonExistFieldsRequest.builder()
                    .appCode(aiSearchClient.getAppCode())
                    .name(indexNameTest)
                    .index(index)
                    .queryAnalysis(queryAnalyses)
                    .build();

            log.info("updateSynonymAnalyzer更新搜索同义词请求入参: {}", JsonUtils.toJsonString(build));
            aiSearchClient.instance().createOrUpdate(build);

            build.setName(indexNameProd);
            aiSearchClient.instance().createOrUpdate(build);

            log.info("更新同义词分析器成功, knowledgeBaseCode: {}, tenantId: {}, synonyms: {}", baseCode, tenantId, synonymContent);
        } catch (IOException e) {
            log.error("更新同义词分析器失败", e);
            throw new BizException("更新同义词分析器失败: " + e.getMessage());
        }
    }


    private List<String> filterSynonymContent(List<SearchAnalyzerConfigDTO> configs) {
        if (CollectionUtils.isEmpty(configs)) {
            return Collections.emptyList();
        }
        List<SearchAnalyzerConfigDTO> synonymConfigs = configs.stream()
                .filter(config -> AnalyzerTypeEnum.MUTUAL_SYNONYM == (config.getAnalyzerType()))
                .collect(Collectors.toList());
        List<String> synonymContent = new ArrayList<>(synonymConfigs.size());
        for (SearchAnalyzerConfigDTO config : synonymConfigs) {
            synonymContent.add(config.getConfigValue());
        }
        return synonymContent;
    }


    @NotNull
    private List<InstanceCreateRequest.QueryAnalysis> getQueryAnalyses(List<String> synonymContent, String tenantId) {
        // 构建查询分析配置
        GsGlobalConfig.KmsIndexConfig indexConfig = gsGlobalConfig.getIndex();

        // 生成分析器名称，如果有租户ID，则添加租户ID后缀
        String analyzerName = "智文自定义查询分析器";
        String synonymDictName = "智文自定义同义词";
        if (StringUtils.isNotBlank(tenantId)) {
            analyzerName = analyzerName + "_" + tenantId;
            synonymDictName = synonymDictName + "_" + tenantId;
        }

        InstanceCreateRequest.QueryAnalysis queryAnalysis = InstanceCreateRequest.QueryAnalysis.builder()
                .name(analyzerName).analyzer(indexConfig.getTextAnalyzer())
                .includeFields(ImmutableList.of(CONTENT))
                .synonymDict(InstanceCreateRequest.Dictionary.builder()
                        .name(synonymDictName)
                        .content(synonymContent)
                        .build())
                .build();

        List<InstanceCreateRequest.QueryAnalysis> queryAnalyses = Lists.newArrayList(queryAnalysis);
        return queryAnalyses;
    }


}
