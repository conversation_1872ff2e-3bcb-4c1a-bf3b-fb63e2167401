package com.chinatelecom.gs.engine.core.manager.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.PartETag;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.context.RequestInfo;
import com.chinatelecom.gs.engine.common.enums.UploadStatus;
import com.chinatelecom.gs.engine.common.s3.CloudStorageDao;
import com.chinatelecom.gs.engine.core.manager.infra.mapper.FileUploadMapper;
import com.chinatelecom.gs.engine.core.manager.infra.po.UploadPO;
import com.chinatelecom.gs.engine.core.manager.param.UploadPartParam;
import com.chinatelecom.gs.engine.core.manager.param.UploadStartParam;
import com.chinatelecom.gs.engine.core.manager.vo.UploadCompleteResult;
import com.chinatelecom.gs.engine.core.manager.vo.UploadStartResult;
import com.chinatelecom.gs.engine.kms.sdk.enums.KnowledgeType;
import com.chinatelelcom.gs.engine.sdk.common.enums.AppSourceType;
import com.chinatelelcom.gs.engine.sdk.common.enums.RequestSourceType;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import com.google.common.collect.Maps;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class FileUploadServiceImplTest {

    @InjectMocks
    private FileUploadServiceImpl fileUploadService;

    @Mock
    private CloudStorageDao cloudStorageDao;

    @Mock
    private FileUploadMapper fileUploadMapper;

    // 设置基础Mapper，因为BaseExtendServiceImpl需要
    @BeforeEach
    public void setUp() {
        RequestInfo requestInfo = new RequestInfo();
        requestInfo.setUserId("userid");
        requestInfo.setUserName("userName");
        requestInfo.setTenantId("ks");
        requestInfo.setAppCode("ks");
        requestInfo.setTenant(false);
        requestInfo.setAppSourceType(AppSourceType.GS_ENGINE);
        requestInfo.setRequestSourceType(RequestSourceType.WEB);
        requestInfo.setIsManager(false);
        requestInfo.setIsAdmin(false);
        requestInfo.setExtraParamMap(Maps.newHashMap());
        requestInfo.setIsSuperTenant(false);
        requestInfo.setCheckRole(false);
        RequestContext.set(requestInfo);

        // 通过反射设置baseMapper字段
        try {
            java.lang.reflect.Field baseMapperField = fileUploadService.getClass().getSuperclass().getDeclaredField("baseMapper");
            baseMapperField.setAccessible(true);
            baseMapperField.set(fileUploadService, fileUploadMapper);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @AfterEach
    public void tearDown() {
        RequestContext.remove();
    }

    /**
     * Test case: 文件类型不支持
     * Expected: 抛出BizException("AA003")
     */
    @Test
    public void testStartUpload_FileTypeNotSupported() {
        // Given
        UploadStartParam param = new UploadStartParam();
        param.setName("test.unknown");

        try (MockedStatic<KnowledgeType> mockedKnowledgeType = mockStatic(KnowledgeType.class)) {
            mockedKnowledgeType.when(() -> KnowledgeType.fromFileName(anyString())).thenReturn(null);

            // When & Then
            BizException exception = assertThrows(BizException.class, () -> {
                fileUploadService.startUpload(param);
            });
            assertEquals("AA003", exception.getCode());
        }
    }

    /**
     * Test case: 存在上传中的相同文件且分片大小一致
     * Expected: 返回已上传分片信息
     */
    @Test
    public void testStartUpload_ExistingUploadWithSamePartSize() {
        // Given
        UploadStartParam param = new UploadStartParam();
        param.setName("test.txt");
        param.setMd5("md5123");
        param.setPartSize(1024);
        param.setTotalPartNum(5);
        param.setTotalSize(5120L);

        UploadPO existingPO = new UploadPO();
        existingPO.setUploadId("upload123");
        existingPO.setFileKey("fileKey123");
        existingPO.setPartSize(1024);
        existingPO.setName("test.txt");
        existingPO.setStatus(UploadStatus.uploading.name());

        List<PartETag> partETags = new ArrayList<>();
        partETags.add(new PartETag(1, "etag1"));
        partETags.add(new PartETag(2, "etag2"));

        try (MockedStatic<KnowledgeType> mockedKnowledgeType = mockStatic(KnowledgeType.class);
             MockedStatic<RequestContext> mockedRequestContext = mockStatic(RequestContext.class)) {

            mockedKnowledgeType.when(() -> KnowledgeType.fromFileName(anyString())).thenReturn(KnowledgeType.TEXT);
            mockedRequestContext.when(RequestContext::getUserId).thenReturn("user123");

            when(fileUploadMapper.selectOne(any())).thenReturn(existingPO);
            when(cloudStorageDao.listParts(anyString(), anyString())).thenReturn(partETags);

            // When
            UploadStartResult result = fileUploadService.startUpload(param);

            // Then
            assertEquals(UploadStatus.uploading.name(), result.getStatus());
            assertEquals("upload123", result.getUploadId());
            assertEquals(2, result.getUploadedParts().size());
            assertTrue(result.getUploadedParts().contains(1));
            assertTrue(result.getUploadedParts().contains(2));
        }
    }

    /**
     * Test case: 存在上传中的相同文件但分片大小不一致
     * Expected: 抛出BizException("AA064")
     */
    @Test
    public void testStartUpload_ExistingUploadWithDifferentPartSize() {
        // Given
        UploadStartParam param = new UploadStartParam();
        param.setName("test.txt");
        param.setMd5("md5123");
        param.setPartSize(1024);

        UploadPO existingPO = new UploadPO();
        existingPO.setPartSize(2048); // Different part size
        existingPO.setStatus(UploadStatus.uploading.name());

        try (MockedStatic<KnowledgeType> mockedKnowledgeType = mockStatic(KnowledgeType.class);
             MockedStatic<RequestContext> mockedRequestContext = mockStatic(RequestContext.class)) {

            mockedKnowledgeType.when(() -> KnowledgeType.fromFileName(anyString())).thenReturn(KnowledgeType.TEXT);
            mockedRequestContext.when(RequestContext::getUserId).thenReturn("user123");

            when(fileUploadMapper.selectOne(any())).thenReturn(existingPO);

            // When & Then
            BizException exception = assertThrows(BizException.class, () -> {
                fileUploadService.startUpload(param);
            });
            assertEquals("AA064", exception.getCode());
        }
    }

    /**
     * Test case: 不存在相同上传记录
     * Expected: 创建新上传任务
     */
    @Test
    public void testStartUpload_NewUpload() {
        // Given
        UploadStartParam param = new UploadStartParam();
        param.setName("test.txt");
        param.setMd5("md5123");
        param.setPartSize(1024);
        param.setTotalPartNum(5);
        param.setTotalSize(5120L);

        try (MockedStatic<KnowledgeType> mockedKnowledgeType = mockStatic(KnowledgeType.class);
             MockedStatic<RequestContext> mockedRequestContext = mockStatic(RequestContext.class);
             MockedStatic<com.chinatelecom.gs.engine.common.utils.IdGenerator> mockedIdGenerator = mockStatic(com.chinatelecom.gs.engine.common.utils.IdGenerator.class)) {

            mockedKnowledgeType.when(() -> KnowledgeType.fromFileName(anyString())).thenReturn(KnowledgeType.TEXT);
            mockedRequestContext.when(RequestContext::getUserId).thenReturn("user123");
            mockedRequestContext.when(RequestContext::getTenantId).thenReturn("tenant123");
            mockedIdGenerator.when(com.chinatelecom.gs.engine.common.utils.IdGenerator::id).thenReturn("id123");

            when(fileUploadMapper.selectOne(any())).thenReturn(null);
            when(cloudStorageDao.startPartUpload(anyString())).thenReturn("upload123");

            // When
            UploadStartResult result = fileUploadService.startUpload(param);

            // Then
            assertEquals(UploadStatus.unUpload.name(), result.getStatus());
            assertEquals("upload123", result.getUploadId());
            assertTrue(CollUtil.isEmpty(result.getUploadedParts()));

            // Verify that save method was called
            verify(fileUploadMapper).insert(any(UploadPO.class));
        }
    }

    /**
     * Test case: 正常上传分片
     * Expected: 返回etag
     */
    @Test
    public void testUploadPart_Success() throws IOException {
        // Given
        String uploadId = "upload123";
        int partNumber = 1;
        byte[] content = "test content".getBytes();
        InputStream inputStream = new ByteArrayInputStream(content);
        MultipartFile multipartFile = new MockMultipartFile("file", "test.txt", "text/plain", content);

        UploadPartParam param = new UploadPartParam();
        param.setUploadId(uploadId);
        param.setPartNumber(partNumber);
        param.setPart(multipartFile);

        UploadPO uploadPO = new UploadPO();
        uploadPO.setFileKey("fileKey123");

        when(fileUploadMapper.selectOne(any())).thenReturn(uploadPO);
        when(cloudStorageDao.uploadPart(anyString(), anyString(), any(InputStream.class), anyLong(), anyInt()))
                .thenReturn("etag123");

        // When
        String etag = fileUploadService.uploadPart(param);

        // Then
        assertEquals("etag123", etag);
    }

    /**
     * Test case: 上传状态不是"uploading"
     * Expected: 抛出BizException("AA071")
     */
    @Test
    public void testCompleteUpload_NotUploadingStatus() {
        // Given
        String uploadId = "upload123";
        UploadPO uploadPO = new UploadPO();
        uploadPO.setStatus(UploadStatus.finished.name()); // Not uploading

        when(fileUploadMapper.selectOne(any())).thenReturn(uploadPO);

        // When & Then
        BizException exception = assertThrows(BizException.class, () -> {
            fileUploadService.completeUpload(uploadId);
        });
        assertEquals("AA071", exception.getCode());
    }


    /**
     * Test case: 合并分片失败
     * Expected: 抛出BizException("AA068")并更新状态为abort
     */
    @Test
    public void testCompleteUpload_MergeFailed() {
        // Given
        String uploadId = "upload123";
        UploadPO uploadPO = new UploadPO();
        uploadPO.setStatus(UploadStatus.uploading.name());
        uploadPO.setFileKey("fileKey123");
        uploadPO.setUploadId(uploadId);

        List<PartETag> partETags = new ArrayList<>();
        partETags.add(new PartETag(1, "etag1"));

        when(fileUploadMapper.selectOne(any())).thenReturn(uploadPO);
        when(cloudStorageDao.listParts(anyString(), anyString())).thenReturn(partETags);
        when(cloudStorageDao.completePartUpload(anyString(), anyString(), anyList())).thenReturn(""); // Empty string means failure

        // When & Then
        BizException exception = assertThrows(BizException.class, () -> {
            fileUploadService.completeUpload(uploadId);
        });
        assertEquals("AA068", exception.getCode());

        // Verify status was updated
        verify(fileUploadMapper).updateById(argThat((UploadPO po) -> UploadStatus.abort.name().equals(po.getStatus())));
    }

    /**
     * Test case: 文件大小不匹配
     * Expected: 抛出BizException("AA065")并更新状态为abort
     */
    @Test
    public void testCompleteUpload_SizeMismatch() {
        // Given
        String uploadId = "upload123";
        UploadPO uploadPO = new UploadPO();
        uploadPO.setStatus(UploadStatus.uploading.name());
        uploadPO.setFileKey("fileKey123");
        uploadPO.setUploadId(uploadId);
        uploadPO.setTotalSize(1024L); // Expected size

        List<PartETag> partETags = new ArrayList<>();
        partETags.add(new PartETag(1, "etag1"));

        ObjectMetadata metadata = new ObjectMetadata();
        metadata.setContentLength(2048L); // Actual size (mismatch)

        when(fileUploadMapper.selectOne(any())).thenReturn(uploadPO);
        when(cloudStorageDao.listParts(anyString(), anyString())).thenReturn(partETags);
        when(cloudStorageDao.completePartUpload(anyString(), anyString(), anyList())).thenReturn("fileKey123");
        when(cloudStorageDao.getObjectMetadata(anyString())).thenReturn(metadata);

        // When & Then
        BizException exception = assertThrows(BizException.class, () -> {
            fileUploadService.completeUpload(uploadId);
        });
        assertEquals("AA065", exception.getCode());

        // Verify status was updated and file was removed
        verify(fileUploadMapper).updateById(argThat((UploadPO po) -> UploadStatus.abort.name().equals(po.getStatus())));
        verify(cloudStorageDao).remove(anyString());
    }

    /**
     * Test case: 正常完成上传
     * Expected: 返回UploadCompleteResult
     */
    @Test
    public void testCompleteUpload_Success() {
        // Given
        String uploadId = "upload123";
        UploadPO uploadPO = new UploadPO();
        uploadPO.setStatus(UploadStatus.uploading.name());
        uploadPO.setFileKey("fileKey123");
        uploadPO.setUploadId(uploadId);
        uploadPO.setTotalSize(1024L);

        List<PartETag> partETags = new ArrayList<>();
        partETags.add(new PartETag(1, "etag1"));

        ObjectMetadata metadata = new ObjectMetadata();
        metadata.setContentLength(1024L); // Size match

        when(fileUploadMapper.selectOne(any(Wrapper.class))).thenReturn(uploadPO);
        when(cloudStorageDao.listParts(anyString(), anyString())).thenReturn(partETags);
        when(cloudStorageDao.completePartUpload(anyString(), anyString(), anyList())).thenReturn("fileKey123");
        when(cloudStorageDao.getObjectMetadata(anyString())).thenReturn(metadata);

        // When
        UploadCompleteResult result = fileUploadService.completeUpload(uploadId);

        // Then
        assertNotNull(result);
        assertEquals("fileKey123", result.getFileKey());

        // Verify status was updated - 修复：应该验证finished状态而不是abort状态
        verify(fileUploadMapper).updateById(argThat((UploadPO po) -> UploadStatus.finished.name().equals(po.getStatus())));
    }

    /**
     * Test case: 上传状态不是"uploading"
     * Expected: 抛出BizException("AA069")
     */
    @Test
    public void testAbortUpload_NotUploadingStatus() {
        // Given
        String uploadId = "upload123";
        UploadPO uploadPO = new UploadPO();
        uploadPO.setStatus(UploadStatus.finished.name()); // Not uploading

        when(fileUploadMapper.selectOne(any())).thenReturn(uploadPO);

        // When & Then
        BizException exception = assertThrows(BizException.class, () -> {
            fileUploadService.abortUpload(uploadId);
        });
        assertEquals("AA069", exception.getCode());
    }

    /**
     * Test case: 正常取消上传
     * Expected: 更新状态为abort
     */
    @Test
    public void testAbortUpload_Success() {
        // Given
        String uploadId = "upload123";
        UploadPO uploadPO = new UploadPO();
        uploadPO.setStatus(UploadStatus.uploading.name());
        uploadPO.setFileKey("fileKey123");
        uploadPO.setUploadId(uploadId);

        when(fileUploadMapper.selectOne(any())).thenReturn(uploadPO);

        // When
        fileUploadService.abortUpload(uploadId);

        // Then
        verify(cloudStorageDao).abortMultipartUpload(anyString(), anyString());
        verify(fileUploadMapper).updateById(argThat((UploadPO po) -> UploadStatus.abort.name().equals(po.getStatus())));
    }

    /**
     * Test case: 上传记录不存在
     * Expected: 抛出BizException("AA066")
     */
    @Test
    public void testGetUploadPO_NotFound() {
        // Given
        String uploadId = "upload123";

        when(fileUploadMapper.selectOne(any())).thenReturn(null);

        // When & Then
        BizException exception = assertThrows(BizException.class, () -> {
            fileUploadService.abortUpload(uploadId); // Using abortUpload to test getUploadPO
        });
        assertEquals("AA066", exception.getCode());
    }
}
