package com.chinatelecom.gs.engine.channel.api.converter;


import com.chinatelecom.gs.engine.channel.api.param.ChannelInfoParam;
import com.chinatelecom.gs.engine.channel.api.vo.ChannelInfoVO;
import com.chinatelecom.gs.engine.channel.api.vo.ChannelRobotConfigVO;
import com.chinatelecom.gs.engine.channel.api.vo.QywxConfigVO;
import com.chinatelecom.gs.engine.channel.api.vo.WebLinkConfigVO;
import com.chinatelecom.gs.engine.channel.dao.po.ChannelApiSecretPO;
import com.chinatelecom.gs.engine.channel.service.dto.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2023/12/25 14:46
 * @description
 */
@Mapper
public interface VOBeanConvertor {

    VOBeanConvertor INSTANCE = Mappers.getMapper(VOBeanConvertor.class);

    /**
     * channelInfo转换到DTO
     *
     * @param channelInfoVO
     * @return
     */
    ChannelInfoDTO convert(ChannelInfoVO channelInfoVO);

    /**
     * channelInfoDTO转VO
     *
     * @param channelInfoDTO
     * @return
     */
    ChannelInfoVO convert(ChannelInfoDTO channelInfoDTO);

    QywxConfigDTO convert(QywxConfigVO qywxConfigVO);

    QywxConfigVO convert(QywxConfigDTO qywxConfigDTO);

    RobotConfigDTO convert(ChannelRobotConfigVO configVO);

    ChannelRobotConfigVO convert(RobotConfigDTO configDTO);

    WebLinkConfigDTO convert(WebLinkConfigVO webLinkConfigVO);

    WebLinkConfigVO convert(WebLinkConfigDTO webLinkConfigDTO);

    @Mapping(target = "yn", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    @Mapping(target = "updateName", ignore = true)
    @Mapping(target = "updateId", ignore = true)
    @Mapping(target = "tenantId", ignore = true)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "enable", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "createName", ignore = true)
    @Mapping(target = "createId", ignore = true)
    ChannelInfoDTO convert(ChannelInfoParam param);

    ChannelApiSecretDTO convert(ChannelApiSecretPO po);
}
