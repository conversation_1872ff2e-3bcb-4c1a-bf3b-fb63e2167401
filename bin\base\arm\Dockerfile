FROM artifact.srdcloud.cn/ct_aics-release-docker-local/nlp/python_arm:3.9.13 AS build

RUN mkdir -p /usr/src/app/jdk/ && \
    mkdir -p /usr/src/app/telecom/ && \
    cd /usr/src/app/jdk/

# 创建一个目录来存放JDK
RUN mkdir -p /usr/local/java

# 将本地的JDK压缩包复制到镜像中
COPY jdk-8u421-linux-aarch64.tar.gz /usr/local/java/

# 解压JDK到指定目录
RUN cd /usr/local/java && \
    tar -xzf jdk-8u421-linux-aarch64.tar.gz && \
    rm jdk-8u421-linux-aarch64.tar.gz && \
    ln -s /usr/local/java/jdk1.8.0_421 /usr/local/java/latest  # 注意这里的版本号

# 设置JAVA_HOME环境变量
ENV JAVA_HOME=/usr/local/java/latest

# 将Java命令添加到PATH
ENV PATH=$JAVA_HOME/bin:$PATH

# 确保安装的工具能正常工作
RUN java -version
RUN python3 --version
RUN pip3 --version
RUN pip3 install RestrictedPython
RUN pip3 install jep

ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

WORKDIR /usr/src/app/telecom
