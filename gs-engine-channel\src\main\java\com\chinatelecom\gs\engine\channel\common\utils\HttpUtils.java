package com.chinatelecom.gs.engine.channel.common.utils;

import com.blade.kit.http.HttpRequest;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import okhttp3.MultipartBody.Builder;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;

import java.io.File;
import java.net.HttpURLConnection;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/1/16 15:20
 */
@Slf4j
public class HttpUtils {

    private final static long CONNECT_TIMEOUT_MS = 10L;
    private final static long READ_TIMEOUT_MS = 10L;

    private static OkHttpClient client = new OkHttpClient.Builder()
            .connectTimeout(CONNECT_TIMEOUT_MS, TimeUnit.SECONDS)
            .readTimeout(READ_TIMEOUT_MS, TimeUnit.SECONDS)
            .build();

    public static HttpRequest post(String url, int timeOut, String contentType) {
        HttpRequest request = HttpRequest.post(url);
        request.contentType(contentType);
        request.connectTimeout(200);
        request.readTimeout(timeOut);
        return request;
    }

    public static Response doGet(String url, Map<String, String> headerMap) {
        return doGet(url, CONNECT_TIMEOUT_MS, READ_TIMEOUT_MS, headerMap);
    }

    public static Response doGet(String url, long connectTimeout, long readTimeout, Map<String, String> headerMap) {
        StopWatch timer = StopWatch.createStarted();
        Response response = null;
        try {
            Request.Builder requestBuilder = new Request.Builder()
                    .url(url)
                    .get();
            Request request = addHeader(requestBuilder, headerMap).build();
            if (connectTimeout != -1 && readTimeout != -1) {
                response = new OkHttpClient.Builder()
                        .connectTimeout(connectTimeout, TimeUnit.SECONDS)
                        .readTimeout(readTimeout, TimeUnit.SECONDS)
                        .build()
                        .newCall(request).execute();
            } else {
                response = client.newCall(request).execute();
            }
        } catch (Exception e) {
            log.error("", e);
        } finally {
            timer.stop();
        }
        return response;
    }

    public static String doPostFile(String url, Map<String, File> mapFile, Map<String, String> headerMap) {
        StopWatch timer = StopWatch.createStarted();
        String responseStr = null;
        Builder builder = new MultipartBody.Builder().setType(MultipartBody.FORM);
        for (Map.Entry<String, File> stringFileEntry : mapFile.entrySet()) {
            builder.addFormDataPart(stringFileEntry.getKey(), stringFileEntry.getValue().getName(),
                    RequestBody.create(MediaType.parse("application/octet-stream"),
                            stringFileEntry.getValue()));
        }
        Request request = addHeader(new Request.Builder(), headerMap)
                .url(url)
                .method("POST", builder.build())
                .build();
        try {
            Response response = client.newCall(request).execute();
            if (response.code() != HttpURLConnection.HTTP_OK
                    && response.code() != HttpURLConnection.HTTP_NO_CONTENT) {
                log.error("do post response code:[{}]", response.code());
            } else {
                try (ResponseBody responseBody = response.body()) {
                    assert responseBody != null;
                    responseStr = responseBody.string();
                }
            }
        } catch (Exception e) {
            log.error("doPostFile error", e);
        } finally {
            timer.stop();
            log.info("do post url:[{}], cost time:[{}]", url, timer.toString());
        }
        return responseStr;
    }

    private static Request.Builder addHeader(Request.Builder requestBuilder, Map<String, String> headerMap) {
        if (headerMap != null && !headerMap.isEmpty()) {
            for (Map.Entry<String, String> entry : headerMap.entrySet()) {
                if (StringUtils.isNotBlank(entry.getKey()) && StringUtils.isNotBlank(entry.getValue())) {
                    requestBuilder.addHeader(entry.getKey(), entry.getValue());
                }
            }
        }
        return requestBuilder;
    }
}
