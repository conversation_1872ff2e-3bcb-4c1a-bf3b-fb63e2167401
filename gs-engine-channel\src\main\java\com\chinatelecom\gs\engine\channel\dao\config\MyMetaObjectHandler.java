//package com.chinatelecom.csbotplatform.channel.dao.config;
//
//import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.ibatis.reflection.MetaObject;
//import org.springframework.stereotype.Component;
//
//import java.time.LocalDateTime;
//
///**
// * <AUTHOR>
// * @date 2023/8/8 9:55
// */
//@Slf4j
//@Component
//public class MyMetaObjectHandler implements MetaObjectHandler {
//
//    @Override
//    public void insertFill(MetaObject metaObject) {
//        LocalDateTime time = LocalDateTime.now();
//        this.strictInsertFill(metaObject, "createTime", LocalDateTime.class, time);
//        this.strictInsertFill(metaObject, "updateTime", LocalDateTime.class, time);
//
//        Long tenantId = 0L;
//        Long userId = 0L;
//        String userName = "DEFAULT";
//        this.strictInsertFill(metaObject, "tenantId", Long.class, tenantId);
//        this.strictInsertFill(metaObject, "createId", Long.class, userId);
//        this.strictInsertFill(metaObject, "updateId", Long.class, userId);
//
//        this.strictInsertFill(metaObject, "createName", String.class, userName);
//        this.strictInsertFill(metaObject, "updateName", String.class, userName);
//    }
//
//    @Override
//    public void updateFill(MetaObject metaObject) {
//        LocalDateTime time = LocalDateTime.now();
//        this.strictUpdateFill(metaObject, "updateTime", LocalDateTime.class, time);
//
//        Long tenantId = 0L;
//        Long userId = 0L;
//        String userName = "DEFAULT";
//        this.strictInsertFill(metaObject, "tenantId", Long.class, tenantId);
//        this.strictInsertFill(metaObject, "updateId", Long.class, userId);
//
//        this.strictInsertFill(metaObject, "updateName", String.class, userName);
//    }
//}
