
CREATE TABLE IF NOT EXISTS `plugin_api_meta_info` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增 id',
  `tenant_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '租户id',
  `api_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '工具id',
  `plugin_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '插件id',
  `api_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '工具名称',
  `api_desc` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '工具描述',
  `path` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '接口path',
  `method` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '方法类型',
  `service_status` int NOT NULL COMMENT '服务状态:0-下线, 1-上线',
  `debug_status` int NOT NULL COMMENT '调试状态:0-调试失败, 1-调试成功',
  `quote_number` int DEFAULT '0' COMMENT '引用数, 包含agent引用数和工作流引用数',
  `enabled` int DEFAULT '1' COMMENT '是否启用',
  `version` int NOT NULL COMMENT '版本号',
  `create_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '1' COMMENT '创建用户ID',
  `create_name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '记录创建人名称',
  `update_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '1' COMMENT '最后修改用户ID',
  `update_name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '最后一次记录操作人',
  `create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '记录创建时间',
  `update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '记录最后一次更新时间',
  `yn` bigint unsigned NOT NULL DEFAULT '0' COMMENT '记录删除标识',
  `app_code` varchar(128) CHARACTER SET armscii8 COLLATE armscii8_general_ci NOT NULL DEFAULT '',
  PRIMARY KEY (`id`),
  KEY `api_id_key` (`api_id`,`plugin_id`,`version`,`yn`)
) ENGINE=InnoDB AUTO_INCREMENT=81 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='插件工具元数据表';