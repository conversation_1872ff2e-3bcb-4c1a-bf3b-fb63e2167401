INSERT INTO plugin_meta_info
( tenant_id, plugin_id, plugin_name, plugin_desc, create_api_type, url, plugin_icon, auth_type, location, service_key, service_token, oauth_info, common_headers, status, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code, plugin_type)
VALUES( '', '1022408486262673408', '文档解析', '内客解析功能支持多种文档格式，自动提取文本、图像等信息，转换为结构化数据。', 2, 'http://aics', '/ais/plugin/web/pluginMall/defaultIcon/doc.png', 0, NULL, NULL, NULL, NULL, '[{"name":"","value":""},{"name":"","value":""}]', 2, 2, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-19 15:26:18.560', '2025-03-19 15:30:26.648', 0, 'bot', 2);
INSERT INTO plugin_meta_info
(tenant_id, plugin_id, plugin_name, plugin_desc, create_api_type, url, plugin_icon, auth_type, location, service_key, service_token, oauth_info, common_headers, status, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code, plugin_type)
VALUES( '', '1022408486262673408', '文档解析', '内客解析功能支持多种文档格式，自动提取文本、图像等信息，转换为结构化数据。', 2, 'http://aics', '/ais/plugin/web/pluginMall/defaultIcon/doc.png', 0, NULL, NULL, NULL, NULL, '[{"name":"","value":""},{"name":"","value":""}]', 2, 1, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-19 15:26:18.560', '2025-03-19 15:30:26', 0, 'bot', 2);
INSERT INTO plugin_meta_info
( tenant_id, plugin_id, plugin_name, plugin_desc, create_api_type, url, plugin_icon, auth_type, location, service_key, service_token, oauth_info, common_headers, status, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code, plugin_type)
VALUES( '', '1022407489129811968', '视频解析', '视频文本识别功能能够自动识别视频中的文字，并提取文字内容及其出现的时间和位置。', 2, 'http://aics', '/ais/plugin/web/pluginMall/defaultIcon/video.png', 0, NULL, NULL, NULL, NULL, '[{"name":"","value":""},{"name":"","value":""}]', 2, 2, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-19 15:22:20.939', '2025-03-19 15:25:43.073', 0, 'bot', 2);
INSERT INTO plugin_meta_info
( tenant_id, plugin_id, plugin_name, plugin_desc, create_api_type, url, plugin_icon, auth_type, location, service_key, service_token, oauth_info, common_headers, status, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code, plugin_type)
VALUES( '', '1022407489129811968', '视频解析', '视频文本识别功能能够自动识别视频中的文字，并提取文字内容及其出现的时间和位置。', 2, 'http://aics', '/ais/plugin/web/pluginMall/defaultIcon/video.png', 0, NULL, NULL, NULL, NULL, '[{"name":"","value":""},{"name":"","value":""}]', 2, 1, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-19 15:22:20.939', '2025-03-19 15:25:43', 0, 'bot', 2);
INSERT INTO plugin_meta_info
( tenant_id, plugin_id, plugin_name, plugin_desc, create_api_type, url, plugin_icon, auth_type, location, service_key, service_token, oauth_info, common_headers, status, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code, plugin_type)
VALUES( '', '1022404434082402304', '音频解析', '将语音转换为文本，应用于语音助手、车裁控制，会议记录等场景。', 2, 'http://aics/', '/ais/plugin/web/pluginMall/defaultIcon/audio.png', 0, NULL, NULL, NULL, NULL, '[{"name":"","value":""},{"name":"","value":""}]', 2, 2, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-19 15:10:12.443', '2025-03-19 15:14:00.648', 0, 'bot', 2);
INSERT INTO plugin_meta_info
( tenant_id, plugin_id, plugin_name, plugin_desc, create_api_type, url, plugin_icon, auth_type, location, service_key, service_token, oauth_info, common_headers, status, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code, plugin_type)
VALUES( '', '1022404434082402304', '音频解析', '将语音转换为文本，应用于语音助手、车裁控制，会议记录等场景。', 2, 'http://aics/', '/ais/plugin/web/pluginMall/defaultIcon/audio.png', 0, NULL, NULL, NULL, NULL, '[{"name":"","value":""},{"name":"","value":""}]', 2, 1, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-19 15:10:12.443', '2025-03-19 15:14:00', 0, 'bot', 2);
INSERT INTO plugin_meta_info
( tenant_id, plugin_id, plugin_name, plugin_desc, create_api_type, url, plugin_icon, auth_type, location, service_key, service_token, oauth_info, common_headers, status, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code, plugin_type)
VALUES( '', '1022400335559528448', 'QA抽取', '自动提取文章关键信息，生成结构化问答，提升信息检索效率。', 2, 'http://aics/', '/ais/plugin/web/pluginMall/defaultIcon/qa.png', 0, NULL, NULL, NULL, NULL, '[{"name":"","value":""},{"name":"","value":""}]', 2, 2, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-19 14:53:55.286', '2025-03-19 07:07:29.046', 0, 'bot', 2);
INSERT INTO plugin_meta_info
( tenant_id, plugin_id, plugin_name, plugin_desc, create_api_type, url, plugin_icon, auth_type, location, service_key, service_token, oauth_info, common_headers, status, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code, plugin_type)
VALUES( '', '1022400335559528448', 'QA抽取', '自动提取文章关键信息，生成结构化问答，提升信息检索效率。', 2, 'http://aics/', '/ais/plugin/web/pluginMall/defaultIcon/qa.png', 0, NULL, NULL, NULL, NULL, '[{"name":"","value":""},{"name":"","value":""}]', 2, 1, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-19 14:53:55.286', '2025-03-19 07:07:29.037', 0, 'bot', 2);

INSERT INTO plugin_version_info
(tenant_id, plugin_id, version, status, runtime_status, version_desc, yn, create_id, create_name, update_id, update_name, create_time, update_time)
VALUES( '', '1022408486262673408', 2, 'EDITING', 'OFF_LINE', '', 0, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-19 15:30:26.613', '2025-03-19 15:30:26.613');
INSERT INTO plugin_version_info
( tenant_id, plugin_id, version, status, runtime_status, version_desc, yn, create_id, create_name, update_id, update_name, create_time, update_time)
VALUES( '', '1022408486262673408', 1, 'PUBLISHED', 'RUNNING_PROD', '', 0, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-19 15:26:18.557', '2025-03-19 15:30:26');
INSERT INTO plugin_version_info
( tenant_id, plugin_id, version, status, runtime_status, version_desc, yn, create_id, create_name, update_id, update_name, create_time, update_time)
VALUES( '', '1022407489129811968', 2, 'EDITING', 'OFF_LINE', '', 0, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-19 15:25:43.042', '2025-03-19 15:25:43.042');
INSERT INTO plugin_version_info
( tenant_id, plugin_id, version, status, runtime_status, version_desc, yn, create_id, create_name, update_id, update_name, create_time, update_time)
VALUES( '', '1022407489129811968', 1, 'PUBLISHED', 'RUNNING_PROD', '', 0, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-19 15:22:20.899', '2025-03-19 15:25:43');
INSERT INTO plugin_version_info
( tenant_id, plugin_id, version, status, runtime_status, version_desc, yn, create_id, create_name, update_id, update_name, create_time, update_time)
VALUES( '', '1022404434082402304', 2, 'EDITING', 'OFF_LINE', '', 0, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-19 15:14:00.621', '2025-03-19 15:14:00.621');
INSERT INTO plugin_version_info
( tenant_id, plugin_id, version, status, runtime_status, version_desc, yn, create_id, create_name, update_id, update_name, create_time, update_time)
VALUES( '', '1022404434082402304', 1, 'PUBLISHED', 'RUNNING_PROD', '', 0, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-19 15:10:12.441', '2025-03-19 15:14:00');
INSERT INTO plugin_version_info
( tenant_id, plugin_id, version, status, runtime_status, version_desc, yn, create_id, create_name, update_id, update_name, create_time, update_time)
VALUES( '', '1022400335559528448', 2, 'EDITING', 'OFF_LINE', '', 0, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-19 15:04:54.573', '2025-03-19 15:04:54.573');
INSERT INTO plugin_version_info
(tenant_id, plugin_id, version, status, runtime_status, version_desc, yn, create_id, create_name, update_id, update_name, create_time, update_time)
VALUES( '', '1022400335559528448', 1, 'PUBLISHED', 'RUNNING_PROD', '', 0, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-19 14:53:55.277', '2025-03-19 15:04:54');

INSERT INTO plugin_api_meta_info
( tenant_id, api_id, plugin_id, api_name, api_desc, `path`, `method`, service_status, debug_status, quote_number, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1022408782879657984', '1022408486262673408', 'docAnalysis', '文档解析功能模块旨在将各种格式的文档（如TXT、PDF、Word文档、PPT幻灯片等）转换成可被计算机程序处理和分析的数据。该模块通过读取文档内容，识别和提取关键信息，如文本、图像、表格和元数据等，并将这些信息转换成结构化数据，便于进一步的处理和分析。', '/doc/analysis', '1', 1, 1, 0, 1, 2, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-19 15:27:29.275', '2025-03-19 15:30:26.663', 0, 'bot');
INSERT INTO plugin_api_meta_info
( tenant_id, api_id, plugin_id, api_name, api_desc, `path`, `method`, service_status, debug_status, quote_number, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1022408782879657984', '1022408486262673408', 'docAnalysis', '文档解析功能模块旨在将各种格式的文档（如TXT、PDF、Word文档、PPT幻灯片等）转换成可被计算机程序处理和分析的数据。该模块通过读取文档内容，识别和提取关键信息，如文本、图像、表格和元数据等，并将这些信息转换成结构化数据，便于进一步的处理和分析。', '/doc/analysis', '1', 1, 1, 0, 1, 1, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-19 15:27:29.275', '2025-03-19 15:30:26', 0, 'bot');
INSERT INTO plugin_api_meta_info
( tenant_id, api_id, plugin_id, api_name, api_desc, `path`, `method`, service_status, debug_status, quote_number, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1022407974431756288', '1022407489129811968', 'videoAnalysis', '视频解析是一种利用人工智能技术，特别是计算机视觉和机器学习，从视频中自动识别和提取文字信息的功能。这项技术能够对输入的视频进行结构化处理，返回对应的文字内容。视频文本识别功能可以支持多种视频格式，如AVI、MP4、H264编码格式，并能够处理不同大小和时长的视频内容。', '/video/analysis', '1', 1, 1, 0, 1, 2, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-19 15:24:16.526', '2025-03-19 15:25:43.088', 0, 'bot');
INSERT INTO plugin_api_meta_info
( tenant_id, api_id, plugin_id, api_name, api_desc, `path`, `method`, service_status, debug_status, quote_number, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES('', '1022407974431756288', '1022407489129811968', 'videoAnalysis', '视频解析是一种利用人工智能技术，特别是计算机视觉和机器学习，从视频中自动识别和提取文字信息的功能。这项技术能够对输入的视频进行结构化处理，返回对应的文字内容。视频文本识别功能可以支持多种视频格式，如AVI、MP4、H264编码格式，并能够处理不同大小和时长的视频内容。', '/video/analysis', '1', 1, 1, 0, 1, 1, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-19 15:24:16.526', '2025-03-19 15:25:43', 0, 'bot');
INSERT INTO plugin_api_meta_info
( tenant_id, api_id, plugin_id, api_name, api_desc, `path`, `method`, service_status, debug_status, quote_number, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1022404839977783296', '1022404434082402304', 'audioAnalysis', '音频识别功能模块能够将人的语音转换成文字信息，主要功能是识别和理解语音内容。它广泛应用于智能家居控制、语音助手、会议记录、客服系统等场景。', '/audio/analysis', '1', 1, 1, 0, 1, 2, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-19 15:11:49.214', '2025-03-19 15:14:00.658', 0, 'bot');
INSERT INTO plugin_api_meta_info
( tenant_id, api_id, plugin_id, api_name, api_desc, `path`, `method`, service_status, debug_status, quote_number, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES('', '1022404839977783296', '1022404434082402304', 'audioAnalysis', '音频识别功能模块能够将人的语音转换成文字信息，主要功能是识别和理解语音内容。它广泛应用于智能家居控制、语音助手、会议记录、客服系统等场景。', '/audio/analysis', '1', 1, 1, 0, 1, 1, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-19 15:11:49.214', '2025-03-19 15:14:00', 0, 'bot');
INSERT INTO plugin_api_meta_info
( tenant_id, api_id, plugin_id, api_name, api_desc, `path`, `method`, service_status, debug_status, quote_number, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1022400948464783360', '1022400335559528448', 'QAExtract', '能够自动从您提供的文章中抽取关键信息，生成问答对。这项技术通过深度学习和自然语言处理，精准识别文章中的重要概念和问题点，为您提供一个结构化的问答集合。无论是教育材料、新闻报道还是技术文档，我们的提取器都能帮助您快速提取核心知识点，提高信息检索效率，增强内容的互动性和教育价值。', '/qa/extract', '1', 1, 1, 0, 1, 2, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-19 14:56:21.405', '2025-03-19 15:04:54.607', 0, 'bot');
INSERT INTO plugin_api_meta_info
( tenant_id, api_id, plugin_id, api_name, api_desc, `path`, `method`, service_status, debug_status, quote_number, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1022400948464783360', '1022400335559528448', 'QAExtract', '能够自动从您提供的文章中抽取关键信息，生成问答对。这项技术通过深度学习和自然语言处理，精准识别文章中的重要概念和问题点，为您提供一个结构化的问答集合。无论是教育材料、新闻报道还是技术文档，我们的提取器都能帮助您快速提取核心知识点，提高信息检索效率，增强内容的互动性和教育价值。', '/qa/extract', '1', 1, 1, 0, 1, 1, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-19 14:56:21.405', '2025-03-19 15:04:54', 0, 'bot');



INSERT INTO plugin_api_param
( tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1022694124994170882', '1022694124989976576', '1022407489129811968', '1022407974431756288', 'fileName', '文件名称', 2, 4, 0, '', 1, 1, 1, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-20 10:21:20.137', '2025-03-20 10:21:20.137', 0, '');
INSERT INTO plugin_api_param
( tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1022694124994170881', '1022694124989976576', '1022407489129811968', '1022407974431756288', 'fileCode', '文件编码', 2, 4, 0, '', 1, 1, 1, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-20 10:21:20.137', '2025-03-20 10:21:20.137', 0, '');
INSERT INTO plugin_api_param
( tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1022694124994170880', '1022694124989976576', '1022407489129811968', '1022407974431756288', 'content', '解析内容', 2, 4, 0, '', 1, 1, 1, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-20 10:21:20.137', '2025-03-20 10:21:20.137', 0, '');
INSERT INTO plugin_api_param
( tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1022694124989976576', NULL, '1022407489129811968', '1022407974431756288', 'data', '解析结果', 2, 4, 0, '', 10, 1, 1, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-20 10:21:20.136', '2025-03-20 10:21:20.136', 0, '');
INSERT INTO plugin_api_param
( tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1022693872157331456', NULL, '1022407489129811968', '1022407974431756288', 'fileCodes', '文件编码列表', 1, 1, 1, '[]', 6, 1, 1, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-20 10:20:19.856', '2025-03-20 10:20:19.856', 0, '');
INSERT INTO plugin_api_param
( tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1022693708063576067', '1022693708063576064', '1022408486262673408', '1022408782879657984', 'fileName', '文件名称', 2, 4, 0, '', 1, 1, 1, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-20 10:19:40.734', '2025-03-20 10:19:40.734', 0, '');
INSERT INTO plugin_api_param
( tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1022693708063576066', '1022693708063576064', '1022408486262673408', '1022408782879657984', 'fileCode', '文件编码', 2, 4, 0, '', 1, 1, 1, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-20 10:19:40.734', '2025-03-20 10:19:40.734', 0, '');
INSERT INTO plugin_api_param
( tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1022693708063576065', '1022693708063576064', '1022408486262673408', '1022408782879657984', 'content', '解析内容', 2, 4, 0, '', 1, 1, 1, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-20 10:19:40.733', '2025-03-20 10:19:40.733', 0, '');
INSERT INTO plugin_api_param
( tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1022693708063576064', NULL, '1022408486262673408', '1022408782879657984', 'data', '解析结果', 2, 4, 0, '', 10, 1, 1, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-20 10:19:40.733', '2025-03-20 10:19:40.733', 0, '');
INSERT INTO plugin_api_param
( tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1022693452500439040', NULL, '1022408486262673408', '1022408782879657984', 'fileCodes', '文件编码列表', 1, 1, 1, NULL, 6, 1, 1, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-20 10:18:39.801', '2025-03-20 10:18:39.801', 0, '');
INSERT INTO plugin_api_param
( tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1022692616353353730', '1022692616332382208', '1022404434082402304', '1022404839977783296', 'fileName', '文件名称', 2, 4, 0, '', 1, 1, 1, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-20 10:15:20.453', '2025-03-20 10:15:20.453', 0, '');
INSERT INTO plugin_api_param
( tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1022692616353353729', '1022692616332382208', '1022404434082402304', '1022404839977783296', 'fileCode', '文件编码', 2, 4, 0, '', 1, 1, 1, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-20 10:15:20.452', '2025-03-20 10:15:20.452', 0, '');
INSERT INTO plugin_api_param
( tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1022692616353353728', '1022692616332382208', '1022404434082402304', '1022404839977783296', 'content', '解析内容', 2, 4, 0, '', 1, 1, 1, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-20 10:15:20.451', '2025-03-20 10:15:20.451', 0, '');
INSERT INTO plugin_api_param
( tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1022692616332382208', NULL, '1022404434082402304', '1022404839977783296', 'data', '解析结果', 2, 4, 0, '', 10, 1, 1, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-20 10:15:20.449', '2025-03-20 10:15:20.449', 0, '');
INSERT INTO plugin_api_param
( tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1022691727492255744', NULL, '1022404434082402304', '1022404839977783296', 'fileCodes', '文件编码列表', 1, 1, 1, '[]', 6, 1, 1, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-20 10:11:48.529', '2025-03-20 10:11:48.529', 0, '');
INSERT INTO plugin_api_param
( tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1022405091891875840', NULL, '1022404434082402304', '1022404839977783296', 'fileCodes', '文件编码列表', 1, 1, 1, '[]', 6, 1, 1, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-19 15:12:49.276', '2025-03-20 10:11:48', 66229, '');
INSERT INTO plugin_api_param
( tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1022405176172220416', NULL, '1022404434082402304', '1022404839977783296', 'content', '解析结果', 2, 4, 0, NULL, 1, 1, 1, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-19 15:13:09.370', '2025-03-20 10:15:20', 66228, '');
INSERT INTO plugin_api_param
( tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1022408194448166912', NULL, '1022407489129811968', '1022407974431756288', 'fileCodes', '文件编码列表', 1, 1, 1, '[]', 6, 1, 1, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-19 15:25:08.982', '2025-03-20 10:20:19', 66223, '');
INSERT INTO plugin_api_param
( tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1022408205420466176', NULL, '1022407489129811968', '1022407974431756288', 'content', '解析结果', 2, 4, 0, NULL, 1, 1, 1, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-19 15:25:11.599', '2025-03-20 10:21:20', 66222, '');
INSERT INTO plugin_api_param
( tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1022408850261151744', NULL, '1022408486262673408', '1022408782879657984', 'fileCodes', '文件编码列表', 1, 1, 1, NULL, 6, 1, 1, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-19 15:27:45.341', '2025-03-20 10:18:39', 66219, '');
INSERT INTO plugin_api_param
( tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1022409400082763776', NULL, '1022408486262673408', '1022408782879657984', 'content', '解析结果', 2, 4, 0, NULL, 1, 1, 1, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-19 15:29:56.441', '2025-03-20 10:19:40', 66218, '');
INSERT INTO plugin_api_param
( tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1022694124994170882', '1022694124989976576', '1022407489129811968', '1022407974431756288', 'fileName', '文件名称', 2, 4, 0, '', 1, 1, 2, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-20 10:21:20.137', '2025-03-20 10:21:20.137', 0, '');
INSERT INTO plugin_api_param
( tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1022694124994170881', '1022694124989976576', '1022407489129811968', '1022407974431756288', 'fileCode', '文件编码', 2, 4, 0, '', 1, 1, 2, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-20 10:21:20.137', '2025-03-20 10:21:20.137', 0, '');
INSERT INTO plugin_api_param
( tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1022694124994170880', '1022694124989976576', '1022407489129811968', '1022407974431756288', 'content', '解析内容', 2, 4, 0, '', 1, 1, 2, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-20 10:21:20.137', '2025-03-20 10:21:20.137', 0, '');
INSERT INTO plugin_api_param
( tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1022694124989976576', NULL, '1022407489129811968', '1022407974431756288', 'data', '解析结果', 2, 4, 0, '', 10, 1, 2, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-20 10:21:20.136', '2025-03-20 10:21:20.136', 0, '');
INSERT INTO plugin_api_param
( tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1022693872157331456', NULL, '1022407489129811968', '1022407974431756288', 'fileCodes', '文件编码列表', 1, 1, 1, '[]', 6, 1, 2, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-20 10:20:19.856', '2025-03-20 10:20:19.856', 0, '');
INSERT INTO plugin_api_param
( tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1022693708063576067', '1022693708063576064', '1022408486262673408', '1022408782879657984', 'fileName', '文件名称', 2, 4, 0, '', 1, 1, 2, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-20 10:19:40.734', '2025-03-20 10:19:40.734', 0, '');
INSERT INTO plugin_api_param
( tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1022693708063576066', '1022693708063576064', '1022408486262673408', '1022408782879657984', 'fileCode', '文件编码', 2, 4, 0, '', 1, 1, 2, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-20 10:19:40.734', '2025-03-20 10:19:40.734', 0, '');
INSERT INTO plugin_api_param
( tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1022693708063576065', '1022693708063576064', '1022408486262673408', '1022408782879657984', 'content', '解析内容', 2, 4, 0, '', 1, 1, 2, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-20 10:19:40.733', '2025-03-20 10:19:40.733', 0, '');
INSERT INTO plugin_api_param
( tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1022693708063576064', NULL, '1022408486262673408', '1022408782879657984', 'data', '解析结果', 2, 4, 0, '', 10, 1, 2, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-20 10:19:40.733', '2025-03-20 10:19:40.733', 0, '');
INSERT INTO plugin_api_param
( tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1022693452500439040', NULL, '1022408486262673408', '1022408782879657984', 'fileCodes', '文件编码列表', 1, 1, 1, NULL, 6, 1, 2, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-20 10:18:39.801', '2025-03-20 10:18:39.801', 0, '');
INSERT INTO plugin_api_param
( tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1022692616353353730', '1022692616332382208', '1022404434082402304', '1022404839977783296', 'fileName', '文件名称', 2, 4, 0, '', 1, 1, 2, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-20 10:15:20.453', '2025-03-20 10:15:20.453', 0, '');
INSERT INTO plugin_api_param
( tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1022692616353353729', '1022692616332382208', '1022404434082402304', '1022404839977783296', 'fileCode', '文件编码', 2, 4, 0, '', 1, 1, 2, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-20 10:15:20.452', '2025-03-20 10:15:20.452', 0, '');
INSERT INTO plugin_api_param
( tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1022692616353353728', '1022692616332382208', '1022404434082402304', '1022404839977783296', 'content', '解析内容', 2, 4, 0, '', 1, 1, 2, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-20 10:15:20.451', '2025-03-20 10:15:20.451', 0, '');
INSERT INTO plugin_api_param
( tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1022692616332382208', NULL, '1022404434082402304', '1022404839977783296', 'data', '解析结果', 2, 4, 0, '', 10, 1, 2, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-20 10:15:20.449', '2025-03-20 10:15:20.449', 0, '');
INSERT INTO plugin_api_param
( tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1022691727492255744', NULL, '1022404434082402304', '1022404839977783296', 'fileCodes', '文件编码列表', 1, 1, 1, '[]', 6, 1, 2, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-20 10:11:48.529', '2025-03-20 10:11:48.529', 0, '');
INSERT INTO plugin_api_param
( tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1022405091891875840', NULL, '1022404434082402304', '1022404839977783296', 'fileCodes', '文件编码列表', 1, 1, 1, '[]', 6, 1, 2, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-19 15:12:49.276', '2025-03-20 10:11:48', 66229, '');
INSERT INTO plugin_api_param
( tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1022405176172220416', NULL, '1022404434082402304', '1022404839977783296', 'content', '解析结果', 2, 4, 0, NULL, 1, 1, 2, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-19 15:13:09.370', '2025-03-20 10:15:20', 66228, '');
INSERT INTO plugin_api_param
( tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1022408194448166912', NULL, '1022407489129811968', '1022407974431756288', 'fileCodes', '文件编码列表', 1, 1, 1, '[]', 6, 1, 2, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-19 15:25:08.982', '2025-03-20 10:20:19', 66223, '');
INSERT INTO plugin_api_param
( tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1022408205420466176', NULL, '1022407489129811968', '1022407974431756288', 'content', '解析结果', 2, 4, 0, NULL, 1, 1, 2, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-19 15:25:11.599', '2025-03-20 10:21:20', 66222, '');
INSERT INTO plugin_api_param
( tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1022408850261151744', NULL, '1022408486262673408', '1022408782879657984', 'fileCodes', '文件编码列表', 1, 1, 1, NULL, 6, 1, 2, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-19 15:27:45.341', '2025-03-20 10:18:39', 66219, '');
INSERT INTO plugin_api_param
( tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1022409400082763776', NULL, '1022408486262673408', '1022408782879657984', 'content', '解析结果', 2, 4, 0, NULL, 1, 1, 2, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-19 15:29:56.441', '2025-03-20 10:19:40', 66218, '');
INSERT INTO plugin_api_param
( tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1022401512040828927', NULL, '1022400335559528448', '1022400948464783360', 'data', '抽取结果', 2, 4, 0, NULL, 10, 1, 2, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-19 14:58:35.779', '2025-03-27 03:17:18.538', 0, '');
INSERT INTO plugin_api_param
( tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1022401512040828927', NULL, '1022400335559528448', '1022400948464783360', 'data', '抽取结果', 2, 4, 0, NULL, 10, 1, 1, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-19 14:58:35.779', '2025-03-27 03:17:18.538', 0, '');
INSERT INTO plugin_api_param
( tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1022401291651125248', NULL, '1022400335559528448', '1022400948464783360', 'content', '待抽取QA文本片段', 1, 1, 1, NULL, 1, 1, 1, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-19 14:57:43.228', '2025-03-19 14:57:43.228', 0, '');
INSERT INTO plugin_api_param
( tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1022401512040828928', '1022401512040828927', '1022400335559528448', '1022400948464783360', 'question', '问题', 2, 4, 0, NULL, 1, 1, 1, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-19 14:58:35.779', '2025-03-27 03:17:18.548', 0, '');
INSERT INTO plugin_api_param
( tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1022401512040828929', '1022401512040828927', '1022400335559528448', '1022400948464783360', 'answer', '答案', 2, 4, 0, NULL, 1, 1, 1, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-19 14:58:35.780', '2025-03-27 03:17:18.557', 0, '');
INSERT INTO plugin_api_param
( tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1022401512049217536', '1022401512040828927', '1022400335559528448', '1022400948464783360', 'source', '来源片段', 2, 4, 0, NULL, 1, 1, 1, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-19 14:58:35.780', '2025-03-27 03:17:18.564', 0, '');
INSERT INTO plugin_api_param
( tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1022401291651125248', NULL, '1022400335559528448', '1022400948464783360', 'content', '待抽取QA文本片段', 1, 1, 1, NULL, 1, 1, 2, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-19 14:57:43.228', '2025-03-19 15:04:54.664', 0, '');
INSERT INTO plugin_api_param
( tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1022401512040828928', '1022401512040828927', '1022400335559528448', '1022400948464783360', 'question', '问题', 2, 4, 0, NULL, 1, 1, 2, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-19 14:58:35.779', '2025-03-27 03:17:18.570', 0, '');
INSERT INTO plugin_api_param
( tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1022401512040828929', '1022401512040828927', '1022400335559528448', '1022400948464783360', 'answer', '答案', 2, 4, 0, NULL, 1, 1, 2, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-19 14:58:35.780', '2025-03-27 03:17:18.577', 0, '');
INSERT INTO plugin_api_param
( tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '1022401512049217536', '1022401512040828927', '1022400335559528448', '1022400948464783360', 'source', '来源片段', 2, 4, 0, NULL, 1, 1, 2, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-19 14:58:35.780', '2025-03-27 03:17:18.582', 0, '');


INSERT INTO plugin_mall_categories
( category_code, category_name, category_icon, create_id, create_name, update_id, update_name, create_time, update_time, yn)
VALUES( '1', '公共插件', '/ais/common/f/images/Avatar/Avatar_default.png', '1', 'admin', '1', 'admin', '2025-03-19 07:56:19.924', '2025-03-19 07:56:19.924', 0);

INSERT INTO plugin_mall_info
( plugin_id, plugin_name, category_code, category_name, plugin_source, create_id, create_name, update_id, update_name, create_time, update_time, yn)
VALUES( '1022400335559528448', 'QA抽取', '1', '公共插件', 1, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-19 15:58:51.258', '2025-03-20 07:16:41.432', 0);
INSERT INTO plugin_mall_info
( plugin_id, plugin_name, category_code, category_name, plugin_source, create_id, create_name, update_id, update_name, create_time, update_time, yn)
VALUES( '1022404434082402304', '音频解析', '1', '公共插件', 1, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-19 15:59:10.232', '2025-03-20 07:16:41.440', 0);
INSERT INTO plugin_mall_info
( plugin_id, plugin_name, category_code, category_name, plugin_source, create_id, create_name, update_id, update_name, create_time, update_time, yn)
VALUES( '1022407489129811968', '视频解析', '1', '公共插件', 1, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-19 15:59:24.220', '2025-03-20 07:16:41.446', 0);
INSERT INTO plugin_mall_info
( plugin_id, plugin_name, category_code, category_name, plugin_source, create_id, create_name, update_id, update_name, create_time, update_time, yn)
VALUES( '1022408486262673408', '文档解析', '1', '公共插件', 1, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-19 15:59:35.161', '2025-03-20 07:16:41.452', 0);