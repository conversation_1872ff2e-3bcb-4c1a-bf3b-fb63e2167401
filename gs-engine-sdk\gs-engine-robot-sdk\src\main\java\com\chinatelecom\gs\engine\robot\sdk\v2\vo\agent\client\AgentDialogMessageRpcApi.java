package com.chinatelecom.gs.engine.robot.sdk.v2.vo.agent.client;

import com.chinatelecom.gs.engine.robot.sdk.dto.Result;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.data.AgentDialogHistoryRecord;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.data.AgentDialogRecordInfoDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 */
@FeignClient(name = "gs-engine-robot", url = "${gs.kmsRpcConfig.gsUrl:}", contextId = "agentDialogMessageRpc", path = "/ais/bot/rpc/dialogMessage")
public interface AgentDialogMessageRpcApi {

    /**
     * 查询聊天日志
     * @param agentCode  机器人编码
     * @param tenantId  租户id
     * @param sessionId 会话id
     * @param limit     每页大小
     * @return 聊天日志
     */
    @GetMapping("/getSessionLogTopN")
    Result<List<AgentDialogRecordInfoDTO>> getSessionLogTopN(@RequestParam("agentCode") String agentCode, @RequestParam("tenantId") String tenantId,
                                                             @RequestParam("sessionId") String sessionId, @RequestParam("limit") Integer limit);


    @Operation(summary = "通过会话id查询topN的聊天记录", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "BOT管理")})})
    @GetMapping("/getHistoryMessage")
    Result<List<AgentDialogHistoryRecord>> getHistoryMessage(@RequestParam(name = "agentCode") String agentCode,
                                                             @RequestParam(name = "sessionId") String sessionId,
                                                             @RequestParam(name = "limit") Integer limit);

    @Operation(summary = "通过会话id查询前N轮对话消息", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "BOT管理")})})
    @GetMapping("/getSessionLogTopNRound")
    Result<List<AgentDialogHistoryRecord>> getSessionLogTopNRound(@RequestParam(name = "agentCode") String agentCode,
                                                             @RequestParam(name = "sessionId") String sessionId,
                                                             @RequestParam(name = "limit") Integer limit);
}
