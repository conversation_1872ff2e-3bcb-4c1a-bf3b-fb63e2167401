package com.chinatelecom.gs.engine.common.s3;

import software.amazon.awssdk.services.s3.model.HeadObjectResponse;
import software.amazon.awssdk.services.s3.model.CompletedPart;

import java.io.InputStream;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/20 9:48
 * @desc 云存储dao接口
 */
public interface CloudStorageDao {


    /**
     * 上传文件
     * @param bucketName 桶名称
     * @param uploadKey 文件名
     * @param fileInputStream 文件流
     * @param len 文件长度
     * @return
     */

    String upload(String uploadKey, InputStream fileInputStream, long len, String contentTye);


    /**
     * 删除文件
     * @param bucketName 桶名称
     * @param fileKey 文件key
     * @return
     */
    void remove(String fileKey);

    /**
     * 根据文件前缀删除文件
     * @param bucketName 桶名称
     * @param prefix 文件key前缀
     * @return
     */
    void removeByPrefix(String prefix);

    /**
     * 下载文件
     * 注意关闭流
     * @param bucketName
     * @param fileKey
     * @return
     */
    InputStream download(String fileKey);

    /**
     * copy文件
     * @param sourceKey
     * @param targetKey
     */
    void copy(String sourceKey, String targetKey);


    /**
     * 查询桶是否存在
     *
     * @param bucketName
     *         String
     *
     * @return Boolean
     */
    Boolean bucketExists(String bucketName);

    /**
     * 创建一个桶
     *
     * @param bucketName
     *         String
     */
    void makeBucket(String bucketName);

    /**
     * 删除一个桶
     *
     * @param bucketName
     *
     * @return
     */
    void removeBucket(String bucketName);

    /**
     * 上传文件
     *
     * @param bucketName
     *         桶名称
     * @param uploadKey
     *         文件名
     * @param fileInputStream
     *         文件流
     * @param orgFileName
     *         原始文件名
     * @param len
     *         文件长度
     *
     * @return
     */

    String upload(String bucketName, String uploadKey, InputStream fileInputStream, String orgFileName, long len,
                  String contentTye);

    /**
     * 从本地文件系统上传到Ceph
     *
     * @param localFilePath 本地文件路径
     * @param cephKey       Ceph存储键
     */
    String uploadFromLocalFile(String localFilePath, String cephKey);


    String uploadByBytes(String bucketName, String uploadKey, byte[] fileBytes);


    /**
     * 删除文件
     *
     * @param bucketName
     *         桶名称
     * @param fileKey
     *         文件key
     *
     * @return
     */
    void remove(String bucketName, String fileKey);

    /**
     * 下载文件
     * 注意关闭流
     *
     * @param bucketName
     * @param fileKey
     *
     * @return
     */
    InputStream download(String bucketName, String fileKey);


    /**
     * 移动文件
     * @param sourceKey
     * @param targetKey
     */
    void move(String sourceKey, String targetKey);

    /**
     * 分块下载
     * @return
     */
    ObjectMetadataRes download(ObjectMetadataReq req);

    /**
     * 文件基础信息查询
     */
    FileMetadataRes queryMetadata(String fileKey);

    /***
     * 开始分片上传
     * @param uploadKey  文件key
     * @return 分片上传ID
     */
    String startPartUpload(String uploadKey);

    /**
     * 分片上传
     * @param uploadKey 文件key
     * @param fileInputStream 文件流
     * @param partSize 分片大小
     * @param partNum 分片号
     * @return
     */
    String uploadPart(String uploadKey, String uploadId, InputStream fileInputStream, long partSize, int partNum);

    /**
     * 完成分片上传
     * @param uploadId 分片上传ID
     * @param uploadKey 文件key
     * @param partETags 分片信息集合
     * @return
     */
    String completePartUpload(String uploadId, String uploadKey, List<CompletedPart> partETags);

    /**
     * 分片文件信息
     * @param uploadId
     * @param uploadKey
     * @return
     */
    List<CompletedPart> listParts(String uploadId, String uploadKey);

    /**
     * 获取文件源信息
     * @param uploadKey
     * @return
     */
    HeadObjectResponse getObjectMetadata(String uploadKey);

    /**
     * 取消分片上传
     * @param uploadId
     * @param fileKey
     */
    void abortMultipartUpload(String uploadId, String fileKey);

    /**
     * 批量删除文件
     *
     * @param fileKeys
     */
    void remove(List<String> fileKeys);
}
