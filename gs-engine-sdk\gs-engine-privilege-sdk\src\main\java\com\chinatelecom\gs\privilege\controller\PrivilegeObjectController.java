package com.chinatelecom.gs.privilege.controller;

import com.chinatelecom.cloud.platform.client.util.AuthUtils;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.privilege.common.dto.GrantObjectDTO;
import com.chinatelecom.gs.privilege.common.dto.ResourceDTO;
import com.chinatelecom.gs.privilege.common.enums.ObjectTypeEnum;
import com.chinatelecom.gs.privilege.common.enums.PrivilegeEnum;
import com.chinatelecom.gs.privilege.common.enums.PrivilegeExceptionEnum;
import com.chinatelecom.gs.privilege.common.enums.ResourceTypeEnum;
import com.chinatelecom.gs.privilege.common.vo.*;
import com.chinatelecom.gs.privilege.service.PrivilegeObjectService;
import com.chinatelecom.gs.privilege.service.TeamService;
import com.chinatelecom.gs.privilege.service.UserService;
import com.chinatelecom.gs.privilege.service.dto.ObjectCheckInfo;
import com.chinatelecom.gs.privilege.util.PriResultUtils;
import com.chinatelecom.gs.privilege.util.PrivilegeUtil;
import com.chinatelelcom.gs.engine.sdk.common.Page;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import com.google.common.collect.Lists;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.chinatelecom.gs.privilege.common.enums.PrivilegeExceptionEnum.NOT_ALLOWED;

@Slf4j
@RestController
@RequestMapping("/privilege/web/accesscontrol")
public class PrivilegeObjectController {

    @Resource
    private PrivilegeObjectService privilegeObjectService;

    @Resource
    private TeamService teamService;

    @Resource
    private UserService userService;

    @Resource
    private PrivilegeUtil privilegeUtil;

    @GetMapping("/user/list")
    public Result<Page<AccessControlUserInfo>> getUserPage(@RequestParam(name = "page", required = false)Integer page,
                                                           @RequestParam(name = "pageSize", required = false)Integer pageSize,
                                                           @RequestParam(name = "userName", required = false)String userName) {
        Page<AccessControlUserInfo> userPage = privilegeObjectService.getUserInfo(userName, page, pageSize);
        if (userPage == null) {
            return PriResultUtils.failed("E000001","查询用户列表失败");
        } else {
            // 特殊逻辑：如果是查人员，则需判断人员是否是管理员
            if (CollectionUtils.isNotEmpty(userPage.getRecords())) {
                String corpCode = RequestContext.getAppCode();
                for (AccessControlUserInfo user : userPage.getRecords()) {
                    user.setIsAdmin(AuthUtils.isAppOwner(user.getUserId(), corpCode).getData());
                }
            }
            return Result.success(userPage);
        }
    }

    @GetMapping("/teams/list")
    public Result<Page<AccessControlTeamInfo>> getTeamPage(@RequestParam(name = "page", required = false)Integer page,
                                                           @RequestParam(name = "pageSize", required = false)Integer pageSize,
                                                           @RequestParam(name = "teamName", required = false)String teamCode) {
        Page<AccessControlTeamInfo> teamPage = privilegeObjectService.getTeamInfo(teamCode, page, pageSize);
        if (teamPage == null) {
            return PriResultUtils.failed("E000002", "查询团队列表失败");
        } else {
            return Result.success(teamPage);
        }
    }

    @GetMapping("/teams/getAll")
    public Result<List<AccessControlTeamInfo>> getTeamTree() {
        List<AccessControlTeamInfo> teamPage = teamService.getAllTeams(RequestContext.getAppCode());
        if (teamPage == null) {
            log.warn("查询团队列表失败，结果为空");
            return Result.success(Lists.newArrayList());
        } else {
            return Result.success(teamPage);
        }
    }

    @PostMapping("/resource/privilege")
    public Result createPrivilege(@RequestBody @Valid ObjectPrivilegeInfo objectPrivilegeInfo) {
        try {
            if (!checkManagePrivilege(objectPrivilegeInfo.getResourceId(), objectPrivilegeInfo.getResourceType())) {
                return PriResultUtils.failed(NOT_ALLOWED);
            }

            // 校验传入的对象是否存在
            if (!checkGrantObject(objectPrivilegeInfo)) {
                return PriResultUtils.failed(NOT_ALLOWED);
            }
            privilegeObjectService.upsertPrivileges(objectPrivilegeInfo);
            return Result.success();
        } catch (Exception e) {
            return PriResultUtils.failed("E000401", e.getMessage());
        }

    }

    @GetMapping("/resourceobject/list")
    public Result<Page<GrantObjectPrivilegeResult>> getResourceObject(@RequestParam(name = "resourceId", required = true) String resourceId,
                                                                      @RequestParam(name = "resourceType", required = true) ResourceTypeEnum resourceType,
                                                                      @RequestParam(name = "objectType", required = true) ObjectTypeEnum objectType,
                                                                      @RequestParam(name = "page", required = false) Integer page,
                                                                      @RequestParam(name = "pageSize", required = false) Integer pageSize) {
        if (!checkManagePrivilege(resourceId, resourceType)) {
            return PriResultUtils.failed(NOT_ALLOWED);
        }
        Page<GrantObjectPrivilegeResult> result = privilegeObjectService.getPrivilegeObjects(resourceId, resourceType, objectType, page, pageSize);
        return Result.success(result);
    }

    @PostMapping("/resource/privilege/delete")
    public Result<Boolean> deleteResource(@Valid @RequestBody DeletePrivilegeRequest request) {
        try {
            if (!checkManagePrivilege(request.getResourceId(), request.getResourceType())) {
                return PriResultUtils.failed(NOT_ALLOWED);
            }
            Boolean result = privilegeObjectService.deletePrivileges(request.getResourceId(), request.getResourceType(), request.getPrivilegeObjectType(), request.getPrivilegeObject());
            return Result.success(result);
        } catch (Exception e) {
            log.error("删除权限数据异常", e);
            return PriResultUtils.failed(PrivilegeExceptionEnum.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/resource/getresourceinfo")
    public Result<ResourceMetaInfo> getResourceMeta(@RequestParam("resourceId")String resourceId,
                                                    @RequestParam("resourceType")ResourceTypeEnum resourceType) {
        if (!checkManagePrivilege(resourceId, resourceType)) {
            return PriResultUtils.failed(NOT_ALLOWED);
        }
        ResourceMetaInfo resourceMetaInfo = privilegeObjectService.getResourceMeta(resourceId, resourceType);
        if (resourceMetaInfo != null) {
            return Result.success(resourceMetaInfo);
        } else {
            return PriResultUtils.failed(PrivilegeExceptionEnum.NOT_ALLOWED);
        }
    }

    @PostMapping("/resource/ispublic")
    public Result<Boolean> editResourceIsPublic(@RequestBody @Valid ResourceIsPublicRequest request) {
        if (!checkManagePrivilege(request.getResourceId(), request.getResourceType())) {
            return PriResultUtils.failed(NOT_ALLOWED);
        }

        ResourceDTO resourceDTO = new ResourceDTO();
        resourceDTO.setIsPublic(request.getIsPublic());
        resourceDTO.setResourceType(request.getResourceType());
        resourceDTO.setResourceId(request.getResourceId());

        Boolean editResult = privilegeUtil.editResource(resourceDTO);
        return Result.success(editResult);

    }

    /**
     * 检查当前用户是否有资源的管理权限
     * @param resourceId
     * @param resourceType
     * @return
     */
    private boolean checkManagePrivilege(String resourceId, ResourceTypeEnum resourceType) {
        GrantObjectDTO grantObjectDTO = privilegeUtil.hasPrivilege(resourceId, resourceType);
        if (grantObjectDTO == null || grantObjectDTO.getPrivilege() == null || grantObjectDTO.getPrivilege() != PrivilegeEnum.manage) {
            return false;
        }

        return true;
    }

    /**
     * 测试授权对象数据是否为合法用户或团队
     */
    private boolean checkGrantObject(ObjectPrivilegeInfo grantObjectInfo) {
        if (grantObjectInfo == null) {
            return true;
        }

        if (CollectionUtils.isEmpty(grantObjectInfo.getGrantObjects())) {
            return true;
        }

        List<ObjectCheckInfo> objectsToCheck = Optional.ofNullable(grantObjectInfo.getGrantObjects())
                .orElse(Collections.emptyList())
                .stream()
                .filter(o -> Objects.nonNull(o.getPrivilegeObject()))
                .map(o -> {
                    return new ObjectCheckInfo(o.getPrivilegeObject(), o.getPrivilegeObjectName());
                })
                .collect(Collectors.toList());

        if (grantObjectInfo.getPrivilegeObjectType() == ObjectTypeEnum.USER) {
            return userService.isUserIdExist(RequestContext.getAppCode(), objectsToCheck);
        } else if (grantObjectInfo.getPrivilegeObjectType() == ObjectTypeEnum.TEAM){
            return teamService.isTeamCodesExist(RequestContext.getAppCode(), objectsToCheck);
        } else {
            throw new UnsupportedOperationException("暂不支持对该类型的对象数据操作");
        }
    }
}
