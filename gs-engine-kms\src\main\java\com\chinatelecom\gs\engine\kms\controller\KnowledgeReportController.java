package com.chinatelecom.gs.engine.kms.controller;

import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.common.auth.KsMenuConfig;
import com.chinatelecom.gs.engine.common.auth.MenuConfig;
import com.chinatelecom.gs.engine.common.auth.PermissionTag;
import com.chinatelecom.gs.engine.common.config.aspect.annotation.DebugLog;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.common.utils.HtmlXssUtils;
import com.chinatelecom.gs.engine.kms.base.model.CodeParam;
import com.chinatelecom.gs.engine.kms.sdk.constants.KmsApis;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.report.*;
import com.chinatelecom.gs.engine.kms.sdk.vo.prompt.PromptRequest;
import com.chinatelecom.gs.engine.kms.service.KnowledgeReportService;
import com.chinatelecom.gs.engine.kms.service.PromptApplicationService;
import com.chinatelecom.gs.engine.sdk.common.annotation.ApiType;
import com.chinatelecom.gs.engine.sdk.common.annotation.HideFromApiTypes;
import com.chinatelelcom.gs.engine.sdk.common.Page;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import jakarta.annotation.Resource;
import java.io.IOException;

/**
 * @Author: gaoxianjun
 * @CreateTime: 2024-12-17
 * @Description:
 * @Version: 1.0
 */
@RestController
@RequestMapping({KmsApis.KMS_API + KmsApis.KNOWLEDGE + KmsApis.AI_REPORT, KmsApis.OPENAPI + KmsApis.KNOWLEDGE + KmsApis.AI_REPORT})
@Slf4j
@Tag(name = "AI文章报告（智能写作）管理")
public class KnowledgeReportController {

    @Resource
    private KnowledgeReportService knowledgeReportService;

    @Resource
    private PromptApplicationService promptApplicationService;

    @Operation(summary = "分页查询AI文章报告", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "智能写作")})})
    @PlatformRestApi(name = "分页查询AI文章报告", groupName = "智能写作")
    @AuditLog(businessType = "智能写作", operType = "分页查询AI文章报告", operDesc = "分页查询AI文章报告", objId="null")
    @PostMapping(KmsApis.PAGE_API)
    @PermissionTag(code = {KsMenuConfig.CUSTOM_REPORT, KsMenuConfig.CUSTOM_REPORT_1, MenuConfig.CUSTOM_REPORT})
    public Result<Page<KnowledgeReportVO>> page(@Validated @RequestBody KnowledgeReportQueryParam appQueryParam) {
        return Result.success(knowledgeReportService.pageQuery(appQueryParam));
    }

    @Operation(summary = "AI文章报告详情", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "智能写作")})})
    @PlatformRestApi(name = "AI文章报告详情", groupName = "智能写作")
    @AuditLog(businessType = "智能写作", operType = "AI文章报告详情", operDesc = "AI文章报告详情", objId="#code")
    @GetMapping(KmsApis.CODE_PATH)
    @PermissionTag(code = {KsMenuConfig.CUSTOM_REPORT, KsMenuConfig.CUSTOM_REPORT_1, MenuConfig.CUSTOM_REPORT})
    public Result<KnowledgeReportVO> get(@PathVariable("code") String code) {
        return Result.success(knowledgeReportService.get(code));
    }

    @Operation(summary = "删除AI文章报告", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "智能写作")})})
    @PlatformRestApi(name = "删除AI文章报告", groupName = "智能写作")
    @AuditLog(businessType = "智能写作", operType = "删除AI文章报告", operDesc = "删除AI文章报告", objId="#codeParam.codes")
    @PostMapping(KmsApis.DELETE_API)
    @PermissionTag(code = {KsMenuConfig.CUSTOM_REPORT, KsMenuConfig.CUSTOM_REPORT_1, MenuConfig.CUSTOM_REPORT})
    public Result<Boolean> delete(@Validated @RequestBody CodeParam codeParam) {
        return Result.success(knowledgeReportService.delete(codeParam));
    }

    @Operation(summary = "新增AI文章报告", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "智能写作")})})
    @PlatformRestApi(name = "新增AI文章报告", groupName = "智能写作")
    @AuditLog(businessType = "智能写作", operType = "新增AI文章报告", operDesc = "新增AI文章报告", objId="_RESULT_.data.code")
    @PostMapping
    public Result<KnowledgeReportVO> add(@Validated @RequestBody KnowledgeReportCreateParam dto) {
        if (StringUtils.isNotBlank(dto.getContent())) {
            dto.setContent(HtmlXssUtils.safe(dto.getContent()));
        }
        return Result.success(knowledgeReportService.create(dto));
    }

    @Operation(summary = "更新AI文章报告", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "智能写作")})})
    @PlatformRestApi(name = "更新AI文章报告", groupName = "智能写作")
    @AuditLog(businessType = "智能写作", operType = "更新AI文章报告", operDesc = "更新AI文章报告", objId="#code")
    @PutMapping(KmsApis.CODE_PATH)
    @PermissionTag(code = {KsMenuConfig.CUSTOM_REPORT, KsMenuConfig.CUSTOM_REPORT_1, MenuConfig.CUSTOM_REPORT})
    public Result<Boolean> update(@PathVariable("code") String code, @Validated @RequestBody KnowledgeReportUpdateParam dto) {
        if (StringUtils.isNotBlank(dto.getContent())) {
            dto.setContent(HtmlXssUtils.safe(dto.getContent()));
        }
        return Result.success(knowledgeReportService.update(code, dto));
    }

    @Operation(summary = "更新AI文章报告名称", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "智能写作")})})
    @PlatformRestApi(name = "更新AI文章报告名称", groupName = "智能写作")
    @AuditLog(businessType = "智能写作", operType = "更新AI文章报告名称", operDesc = "更新AI文章报告名称", objId="#code")
    @PutMapping(KmsApis.CODE_PATH + KmsApis.RENAME)
    @PermissionTag(code = {KsMenuConfig.CUSTOM_REPORT, KsMenuConfig.CUSTOM_REPORT_1, MenuConfig.CUSTOM_REPORT})
    public Result<Boolean> rename(@PathVariable("code") String code, @Validated @RequestBody KnowledgeRenameParam dto) {
        knowledgeReportService.rename(code, dto);
        return Result.success(true);
    }

    @Operation(summary = "更新AI文章报告生成信息", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "智能写作")})})
    @PlatformRestApi(name = "更新AI文章报告生成信息", groupName = "智能写作")
    @AuditLog(businessType = "智能写作", operType = "更新AI文章报告生成信息", operDesc = "更新AI文章报告生成信息", objId="#code")
    @PutMapping(KmsApis.CODE_PATH + KmsApis.GEN_INFO)
    @PermissionTag(code = {KsMenuConfig.CUSTOM_REPORT, KsMenuConfig.CUSTOM_REPORT_1, MenuConfig.CUSTOM_REPORT})
    public Result<Boolean> updateGenInfo(@PathVariable("code") String code, @Validated @RequestBody KnowledgeReportUpdateGenInfoParam dto) {
        knowledgeReportService.updateGenInfo(code, dto);
        return Result.success(true);
    }

    @Deprecated
    @Operation(summary = "AI报告大纲编写", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "智能写作")})})
    @PlatformRestApi(name = "AI报告大纲编写", groupName = "智能写作")
    @AuditLog(businessType = "智能写作", operType = "AI报告大纲编写", operDesc = "AI报告大纲编写", objId="null")
    @DebugLog(operation = "AI报告大纲编写")
    @PostMapping(KmsApis.AI_REPORT_OUTLINE)
    @PermissionTag(code = {KsMenuConfig.CUSTOM_REPORT, KsMenuConfig.CUSTOM_REPORT_1, MenuConfig.CUSTOM_REPORT})
    public SseEmitter outline(@Validated @RequestBody ReportOutlineQueryParam dto) throws IOException {
        try {
            return knowledgeReportService.outlineSse(dto);
        } catch (Exception e) {
            log.error("生成大纲异常", e);
            return promptApplicationService.exceptionResponse(e);
        }
    }

    @Operation(summary = "AI报告正文编写", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "智能写作")})})
    @PlatformRestApi(name = "AI报告正文编写", groupName = "智能写作")
    @AuditLog(businessType = "智能写作", operType = "AI报告正文编写", operDesc = "AI报告正文编写", objId="null")
    @PostMapping(path = KmsApis.AI_REPORT_CONTENT, produces = {MediaType.TEXT_EVENT_STREAM_VALUE})
    @PermissionTag(code = {KsMenuConfig.CUSTOM_REPORT, KsMenuConfig.CUSTOM_REPORT_1, MenuConfig.CUSTOM_REPORT})
    public SseEmitter content(@Validated @RequestBody ReportContentQueryParam param) throws IOException {
        try {
            return knowledgeReportService.content(param);
        } catch (Exception e) {
            log.error("生成正文异常", e);
            return promptApplicationService.exceptionResponse(e);
        }
    }

    @HideFromApiTypes(ApiType.OPENAPI)
    @Operation(summary = "Prompt使用AI报告形式调用", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "智能写作")})})
    @PlatformRestApi(name = "Prompt使用AI报告形式调用", groupName = "智能写作")
    @AuditLog(businessType = "智能写作", operType = "Prompt使用AI报告形式调用", operDesc = "Prompt使用AI报告形式调用", objId="#param.code")
    @PostMapping(path = KmsApis.AI_REPORT_REPLY, produces = {MediaType.TEXT_EVENT_STREAM_VALUE})
    public SseEmitter promptModel(@Validated @RequestBody PromptRequest param) throws IOException {
        try {
            return knowledgeReportService.promptModel(param);
        } catch (Exception e) {
            log.error("生成正文异常", e);
            return promptApplicationService.exceptionResponse(e);
        }
    }

}
