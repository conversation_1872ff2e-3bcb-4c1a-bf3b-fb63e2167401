package com.chinatelecom.gs.engine.kms.dto;

import com.baomidou.mybatisplus.annotation.TableName;
import com.chinatelecom.gs.engine.kms.dto.base.BaseAppDTO;
import com.chinatelecom.gs.engine.kms.infra.po.base.BaseAppPO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;

/**
 * <p>
 * 知识抽取主表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-09
 */
@Data
@Schema(description ="知识抽取主表查询参数")
public class KnowledgeExtractDTO extends BaseAppDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "知识id")
    private String knowledgeCode;

    @Schema(description = "失败原因")
    private String failureReason;

    @Schema(description = "0：抽取中,1:抽取失败，2：抽取成功")
    private String extractStatus;

    @Schema(description = "抽取进度，百分比0-100")
    private int progress;

}
