# 本地环境启动使用配置,需要配合nacos使用
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://${app.db.kms.host}:${app.db.kms.port}/${app.db.kms.dbname}?useUnicode=true&characterEncoding=utf8&useSSL=false&allowMultiQueries=true
    username: ${app.db.kms.username}
    password: ${app.db.kms.password}

  flyway:
    enabled: true # 是否启用flyway
    baseline-on-migrate: true  # 空库时候自动执行建库脚本
    placeholderReplacement: false

  session:
    store-type: redis
  data:
    redis:
      database: 0
      connect-timeout: 10s
      username: ${app.redis.username}
      password: ${app.redis.password}
      host: ${app.redis.host}
      port: ${app.redis.port}
  kafka:
    bootstrap-servers: ${app.kafka.servers:127.0.0.1:9092} # kafka集群信息，多个节点通过“,”分隔


springdoc:
  api-docs:
    enabled: true # 是否启用接口文档 生产需禁用

knife4j:
  # 开启增强配置
  enable: true




# 项目从2.0升级到2.2 才需要打开该配置
#  agent:
#    strategy:
#      migrate:
#        enable: true
#    kms:
#      migrate:
#        enable: true


#  bot:
#    summary:
#      check:
#        url: http://************:31418/plugin/query_ana


logging:
  level:
#    root: debug
#    com.chinatelcom.gs: debug
    com.chinatelecom.ai.kms.infra.mapper: debug
    org.apache.kafka.clients: error
    com.chinatelecom.cloud.platform: warn

platform:
  client:
    apiPptEnable: false
    close-platform: false
    dev: true
    mock: true
    #网关端口：31170 （外网地址）
    app-url: http://************:31170/ais/ks/f/
    #网关端口：31170 （外网地址）
    api-url: http://************:31170/ais/im/f/impc/share
    # 外网地址
    server-url: http://************:32088
    # 内网地址
    dmz-url: http://************:32088
    include-urls: /login
    exclude-urls: /login,/bot/rpc/*,/bot/openapi/*,/base/rpc/*,/kms/rpc/*,/workflow/rpc/*,/plugin/rpc/*,/base/openapi/*,/channel/rpc/*,/rpc/*,/ping,/v3/api-docs,/doc.html,/webjars/*,/swagger-resources,/swagger-resources/*,/kms/web/user/logout,/swagger-ui/*,/kms/openapi/*,/channel/openapi/*,/openapi/channel/qywx/*
    #auth-exclude-urls: /*
    app-code: ais-ks
    app-secret: 50231a5a-2fe6-4921-a07c-b2b8ceb172b8
    manager: redis
    open-swap-session: false
    filters:
      - com.chinatelecom.cloud.platform.client.filter.LogoutFilter
      - com.chinatelecom.cloud.platform.client.filter.LoginFilter
  enableCsrfCheck: false
  trustedDomain: ************

jython:
  isLocal: false

# 流程系统相关配置
feign:
  service:
    url:
      tele-flowable: http://************:31170/ais/dtflow/web/

starlink:
  feign:
    appCode: ais-ks

csbotplatform:
  agent:
    rank:
      model: model_992668744299715591
      doc:
        accept: 0

agentinit:
  agentName: "小智"
  agentPicture: "/ais/bot/web/defaultIcon"
  agentDesc: "全能机器人，为您提供全方位的服务支持。"
  agentReply: "你是一个基于大语言模型的AI助手。现在给你一个用户的提问，请给出简洁、清晰且准确的回答。你将会收到一系列与问题相关的上下文，每个上下文都以\"```[x]```\"这样的特殊的参考标记开头，其中x为上下文编号（从0开始计数），每个上下文由文章标题以及关联文本片段构成。在回答问题时，请使用上下文。同时你的回答必须正确、准确，并由专家以中立、专业的语气书写。请限制在{max_token}个token以内。不要提供与问题无关的任何信息，也不要重复。除了代码、具体名称和引用外，你的回答必须用中文书写。"
  modelCode: "model_992668744299715570"
  modelName: "DeepSeek-R1-Qwen32b"
  multiModelCode: "model_992668744299712569"
  multiModelName: "多模态模型"
  modelMode: "PRECISION_MODE"
  modelRandomness: 0.1
  memoryCount: 1
  outputType: "txt"
  enableAdvConf: true
  enableSortModel: true
  agentPrologue: "您好！我是您的智能伙伴Hi 小智。致力于为您提供精准、高效的知识解答。快来向我提问吧，让我用智能为您开启知识探索之旅！"
  agentGuide: ["如何用AI提高工作效率？","你能帮我做什么呢？"]
  noAnswerScript: "对不起，我无法回答你的问题。你是否有其他问题需要帮助？"
  agentSuggest: true
  suggestCount: 3
  globalVarSwitch: false
  securityFenceSwitch: true
  isDefault: "1"

deepseekinit:
  agentName: "DeepSeek"
  agentPicture: "/ais/common/f/images/Avatar/avatar_ds.png"
  agentDesc: "deepseek机器人，专注提供精准、深度的知识服务。擅长深度思考。技术解析、生活建议、学习指导，支持多轮对话与复杂问题拆解，以逻辑清晰、响应高效为核心优势。"
  agentReply: "你是一个充满智慧的AI伙伴，兼具专业性和亲和力。以高效、准确的回答著称，同时具备幽默感和创造力，能够与用户进行轻松愉快的对话。擅长逻辑推理和深度思考，致力于帮助用户解决问题，激发灵感。回答问题优先匹配预设知识库与实时数据，复杂问题自动拆解为子问题分步解答，引导用户深入思考，提供多角度的解决方案。多轮对话中主动追问关键细节。"
  modelCode: "model_992668744299715570"
  modelName: "DeepSeek-R1-Qwen32b"
  multiModelCode: "model_992668744299712569"
  multiModelName: "多模态模型"
  modelMode: "PRECISION_MODE"
  modelRandomness: 0.1
  memoryCount: 1
  outputType: "txt"
  enableAdvConf: true
  enableSortModel: true
  agentPrologue: "我是 DeepSeek，很高兴见到你！我可以帮你写代码、读文件、写作各种创意内容，请把你的任务交给我吧~"
  agentGuide: ["如何用AI提高工作效率？","你能帮我做什么呢？"]
  noAnswerScript: "我还在学习中，这个问题我暂时回答不了呢。不过你可以试试问一些别的问题哦。"
  agentSuggest: true
  suggestCount: 3
  globalVarSwitch: false
  securityFenceSwitch: true
  isDefault: "1"


weixin:
  token:
    get:
      url: https://127.0.0.1/cgi-bin/gettoken

ais:
  telephone:
    rpcUrl: http://ais-telephone-manager-srv.ais.svc.cluster.local:8910

plugin:
  whitelist:
    switch: false
    ips: 127.0.0.1
  exec:
    # 单位ms
    readTimeout: 1800000

workflow:
  exec:
    # 执行超时时间，单位毫秒,默认10分钟
    timeout: 180000000
  sse:
    # sse超时时间，单位毫秒，默认10分钟
    timeout: 180000000



send:
  dialogResultMsg: true
  invokeProcessMsg: true
  pageMsg: true


#nl2sql:
#  host: http://bi:8091 #注意：使用telebi后端内网调用地址（最好是k8s集群内域名）
#  signKey: xxxx #找telebi给
#  openapi:
#    chatDataUri: /telebi/api/chat/data/nologin?chatId={}&page={}&pageSize={}&userId={}&corpCode={}
#    streamUri: /telebi/api/chat/stream/nologin
#
#app：
#  agent:
#    plugin:
#      highPriority: BI





app:
  db:
    kms:
      host: ************
      port: 3306
      username: root
      password: mysql_password_local
      dbname: ai_kms_gs_engine
  redis:
    username: default
    password: Admin@coc1
    host: ************
    port: 6379
  kafka:
    servers: ************:9092
  es:
    address: ************:9200
    username: elastic
    password: ELASTIC_PASSWORD
    connection-timeout: 30s
  trace:
    enable: false
  safeCheck:
    url: http://************:8080/api/v1/safeCheck
    enableFence: false
    accessKeyId: 123
    accessKeySecret: 123
    tenantId: 123
    modelType: xinghaizhiwen
    whitelist:
      tenants: aisSafe



telecom:
  ai:
    search:
      discovery-type: simple
      app: # 自动创建应用及关联es集群的配置
        auto-create: true
        name: 知识中台
        cluster-name: ES测试集群
        cluster-address: ************:9200
        cluster-password: "qmc%WxVEH79C"
        cluster-username: elastic
        s3-access-key: ${gs.s3.accessKey}
        s3-secret-key: ${gs.s3.secretKey}
        s3-endpoint: ${gs.s3.endPoint}
        s3-bucket-name: ${gs.s3.bucketName}
      user-id: gs-ks
      simple: # simple类型，需手动指定搜索服务地址
        address: ai-search-manage-srv.ais.svc.cluster.local:8020
      enabled: true
  environment: prod

gs:
  scriptExecutor:
    apiKey: ais-bot-workflow-code-ai-sandbox
    host: http://ais-sandbox-srv.ais.svc.cluster.local:8194
  stat:
    parserUrl: http://ais-gcba-srv:16129
  system:
    env: prod  # 环境隔离前缀，生产使用prod
    logEnabled: true # 是否打印日志
  s3:
    type: MINIO
    endpoint: http://************:9000
    accessKey: 5phg5qGwxBqQ7Kg7TTdI
    secretKey: cNDlzX8qiYJ4nA5eU9iAE5J1mvVH455lOYAZ7QrR
    bucketName: ais-micro-app
  cache:
    type: REDIS
  mockRequest:
    enabled: false
  kmsRpcConfig:
    dataParserUrl: http://************:31418
    parseReadTimeout: 7200000
    summaryReadTimeout: 1800000
    gsUrl: http://localhost:8092
  rag:
    response-text:
      empty: "无法在知识库中找到相关答案，请换个问法试试。"
      error: "服务器繁忙，请重试"
      sensitive: "我无法回答您的问题，请换个问法试试。"
  search:
    searchTemplate:
      doc: "{\"query\":{\"bool\":{\"must\":[{\"bool\":{\"should\":[{\"match\":{\"fileContent\":{\"query\":\"${query?json_string}\"}}},{\"match\":{\"fileContentSpell\":{\"query\":\"${query?json_string}\"}}},{\"match\":{\"title\":{\"query\":\"${query?json_string}\"}}}]}}]}},\"knn\":{\"field\":\"fileContentVector\",\"query\":\"${query?json_string}\",\"k\":100,\"num_candidates\":1000},\"highlightFields\":[],\"page\":1,\"size\":50,\"knnMinScore\":0.65,\"firstRankExpression\":\"atan_normalized_bm25(0.01)*0.5+knn_score()*0.5\",\"type\":\"VECTOR\"}"
      faq: "{\"query\":{\"bool\":{\"must\":[{\"bool\":{\"should\":[{\"match\":{\"fileContent\":{\"query\":\"${query?json_string}\"}}},{\"match\":{\"title\":{\"query\":\"${query?json_string}\"}}}]}}]}},\"knn\":{\"field\":\"fileContentVector\",\"query\":\"${query?json_string}\",\"k\":100,\"num_candidates\":1000},\"highlightFields\":[],\"page\":1,\"size\":50,\"knnMinScore\":0.65,\"firstRankExpression\":\"atan_normalized_bm25(0.01)*0.5+knn_score()*0.5\",\"type\":\"VECTOR\"}"
      filter: "{\"query\":{\"bool\":{\"must\":[{\"bool\":{\"should\":[{\"match\":{\"fileContent\":{\"query\":\"${query?json_string}\"}}},{\"match\":{\"fileContentSpell\":{\"query\":\"${query?json_string}\"}}},{\"match\":{\"title\":{\"query\":\"${query?json_string}\"}}}]}}]}},\"knn\":{\"field\":\"fileContentVector\",\"query\":\"${query?json_string}\",\"k\":100,\"num_candidates\":1000},\"distinct\":{\"distField\":\"knowledgeCode\",\"distTimes\":1,\"distCount\":1,\"collectFields\":[\"type\",\"knowledgeCode\",\"file\",\"fileOriginalContent\",\"fileContent\",\"fileSource\",\"type\"],\"collectCount\":3},\"highlightFields\":[],\"page\":1,\"size\":50,\"knnMinScore\":0.65,\"firstRankExpression\":\"atan_normalized_bm25(0.01)*0.5+knn_score()*0.5\",\"type\":\"VECTOR\"}"    # analysis,report保持和doc一致
      #analysis: "{\"query\":{\"bool\":{\"must\":[{\"bool\":{\"should\":[{\"match\":{\"file_content__\":{\"query\":\"${query?json_string}\"}}},{\"match\":{\"title\":{\"query\":\"${query?json_string}\"}}}]}},{\"exists\":{\"field\":\"file_index__\"}}]}},\"knn\":{\"field\":\"file_content_vector__\",\"query\":\"${query?json_string}\",\"k\":100,\"num_candidates\":1000},\"distinct\":{\"distField\":\"file_id__\",\"distTimes\":1,\"distCount\":1,\"collectFields\":[\"type\",\"file\",\"file_original_content__\",\"file_content__\",\"file_source__\",\"file_type__\"],\"collectCount\":3},\"highlightFields\":[],\"page\":1,\"size\":15,\"knnMinScore\":0.65,\"firstRankExpression\":\"atan_normalized_bm25(0.01)*0.5+knn_score()*0.5\",\"type\":\"VECTOR\"}"
      #report: "{\"query\":{\"bool\":{\"must\":[{\"bool\":{\"should\":[{\"match\":{\"file_content__\":{\"query\":\"${query?json_string}\"}}},{\"match\":{\"title\":{\"query\":\"${query?json_string}\"}}}]}},{\"exists\":{\"field\":\"file_index__\"}}]}},\"knn\":{\"field\":\"file_content_vector__\",\"query\":\"${query?json_string}\",\"k\":100,\"num_candidates\":1000},\"distinct\":{\"distField\":\"file_id__\",\"distTimes\":1,\"distCount\":1,\"collectFields\":[\"type\",\"file\",\"file_original_content__\",\"file_content__\",\"file_source__\",\"file_type__\"],\"collectCount\":3},\"highlightFields\":[],\"page\":1,\"size\":15,\"knnMinScore\":0.65,\"firstRankExpression\":\"atan_normalized_bm25(0.01)*0.5+knn_score()*0.5\",\"type\":\"VECTOR\"}"
    rankExpression:
      knn: 2*knn_score()-1
      keyword: atan_normalized_bm25(0.1)
      both: atan_normalized_bm25(0.1)*0.5+(2*knn_score()-1)*0.5
      filter: atan_normalized_bm25(0.1)
  kmsBaseConfig:
    defaultConfig: "{\"modelUrlList\":{},\"chunkConfig\":{\"baseChunkFlag\":true,\"semanticChunkFlag\":true,\"customChunkFlag\":true,\"customChunkConfig\":{\"splitChr\":[\"\\n\"],\"maxLen\":512,\"overlapLen\":128,\"splitTime\":300}},\"parserConfig\":{\"ocrFlag\":false,\"cpuFlag\":false,\"layoutFlag\":true}}"
  filterModes:
    publishFilter:
      name: "发布权限过滤"
      description: "发布权限过滤"
      filtered-codes:
        - "manage_doclib_reset"
        - "manage_doclib_batchrelease"
        - "manage_doclib_batchreset"
        - "manage_qalib_release_show"
        - "manage_qalib_batchreset"
        - "manage_qalib_batchrelease"
        - "manage_doclib_release_show"
        - "manage_doclib_release"
        - "manage_qalib_reset"
        - "manage_qalib_release"
        - "release_approval"
    auditFilter:
      name: "审核权限过滤"
      description: "审核权限过滤"
      filtered-codes:
        - "/flow-sub/approval"
        - "/flow-sub/approval/bpmn"
        - "/flow-sub/approval/listener"
        - "/flow-sub/approval/process"
        - "release_approval"
        - "/approval"
        - "/approval/bpmn"
        - "/approval/listener"
        - "/approval/process"
  pool:
    splitPool:
      coreSize: 16
      maxSize: 16
    splitImgPool:
      coreSize: 8
      maxSize: 8
