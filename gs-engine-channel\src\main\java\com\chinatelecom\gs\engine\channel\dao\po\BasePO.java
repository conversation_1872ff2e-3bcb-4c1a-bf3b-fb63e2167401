package com.chinatelecom.gs.engine.channel.dao.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/12/22 10:48
 * @description
 */
@Setter
@Getter
public class BasePO {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    protected Long id;

    /**
     * 租户ID
     */
    @TableField(value = "tenant_id", fill = FieldFill.INSERT_UPDATE)
    protected String tenantId;

    /**
     * 删除标记,1:删除,0:未删除
     */
    @TableField("yn")
    @TableLogic(value = "0", delval = "id")
    protected Long yn;

    /**
     * 创建用户ID
     */
    @TableField(value = "create_id", fill = FieldFill.INSERT)
    protected String createId;

    /**
     * 创建用户名
     */
    @TableField(value = "create_name", fill = FieldFill.INSERT)
    protected String createName;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    protected LocalDateTime createTime;

    /**
     * 最后修改用户ID
     */
    @TableField(value = "update_id", fill = FieldFill.INSERT)
    protected String updateId;

    /**
     * 最后修改用户名
     */
    @TableField(value = "update_name", fill = FieldFill.INSERT)
    protected String updateName;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT)
    protected LocalDateTime updateTime;
}
