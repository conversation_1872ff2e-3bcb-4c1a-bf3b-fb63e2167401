package com.chinatelecom.gs.engine.core.model.controller;

import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.common.constants.Constants;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.core.model.service.PromptChatService;
import com.chinatelecom.gs.engine.robot.sdk.dto.PromptChatRequest;
import com.chinatelecom.gs.engine.robot.sdk.v2.spi.center.PlatformDialogCenterApi;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import jakarta.annotation.Resource;
import java.io.IOException;


@RestController
@Slf4j
@Tag(name = "模型对话 Controller")
@RequestMapping({Constants.BASE_PREFIX + Constants.WEB_PREFIX + "/prompt"})
public class PromptChatController {


    @Resource
    private PromptChatService promptChatService;

    @Operation(summary = "模型回答", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "模型回答", groupName = "模型对话")
    @PostMapping(path = "/chat", produces = {MediaType.TEXT_EVENT_STREAM_VALUE})
    @AuditLog(businessType = "模型对话", operType = "模型回答", operDesc = "模型回答", objId="#request.modelCode")
    public SseEmitter prompt(@Validated @RequestBody PromptChatRequest request) throws IOException {
        SseEmitter sseEmitter = new SseEmitter(PlatformDialogCenterApi.TIMEOUT);

        try {
            promptChatService.chatRequest(request, sseEmitter);
            return sseEmitter;
        } catch (Exception e) {
            log.error("prompt error", e);
            return null;
        }
    }

}
