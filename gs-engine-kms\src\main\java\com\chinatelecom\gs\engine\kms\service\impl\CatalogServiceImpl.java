package com.chinatelecom.gs.engine.kms.service.impl;

import com.chinatelecom.gs.engine.common.config.aspect.annotation.Lock;
import com.chinatelecom.gs.engine.common.config.property.GsGlobalConfig;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.utils.BizAssert;
import com.chinatelecom.gs.engine.common.utils.IPageUtils;
import com.chinatelecom.gs.engine.common.utils.StreamUtils;
import com.chinatelecom.gs.engine.core.manager.convert.ConfigConvert;
import com.chinatelecom.gs.engine.core.manager.service.BaseConfigAppService;
import com.chinatelecom.gs.engine.core.manager.vo.config.PublishConfig;
import com.chinatelecom.gs.engine.kms.base.model.CodeParam;
import com.chinatelecom.gs.engine.kms.base.service.impl.BaseAppServiceByCodeImpl;
import com.chinatelecom.gs.engine.kms.convert.vo.CatalogVoConverter;
import com.chinatelecom.gs.engine.kms.dto.KnowledgeBaseDTO;
import com.chinatelecom.gs.engine.kms.dto.catalog.CatalogCopyTreeDTO;
import com.chinatelecom.gs.engine.kms.enums.CatalogType;
import com.chinatelecom.gs.engine.kms.infra.po.CatalogPO;
import com.chinatelecom.gs.engine.kms.model.dto.CatalogDTO;
import com.chinatelecom.gs.engine.kms.model.dto.KnowledgeDTO;
import com.chinatelecom.gs.engine.kms.repository.CatalogRepository;
import com.chinatelecom.gs.engine.kms.repository.KnowledgeBaseRepository;
import com.chinatelecom.gs.engine.kms.repository.KnowledgeRepository;
import com.chinatelecom.gs.engine.kms.sdk.enums.PublishStatus;
import com.chinatelecom.gs.engine.kms.sdk.enums.TargetType;
import com.chinatelecom.gs.engine.kms.sdk.vo.catalog.CatalogCreateParam;
import com.chinatelecom.gs.engine.kms.sdk.vo.catalog.CatalogMoveParam;
import com.chinatelecom.gs.engine.kms.sdk.vo.catalog.CatalogQueryParam;
import com.chinatelecom.gs.engine.kms.sdk.vo.catalog.CatalogTreeVO;
import com.chinatelecom.gs.engine.kms.sdk.vo.catalog.CatalogUpdateParam;
import com.chinatelecom.gs.engine.kms.sdk.vo.catalog.CatalogVO;
import com.chinatelecom.gs.engine.kms.search.SearchService;
import com.chinatelecom.gs.engine.kms.service.CatalogService;
import com.chinatelecom.gs.engine.kms.service.CollectionAppService;
import com.chinatelecom.gs.engine.kms.service.KnowledgeAppService;
import com.chinatelecom.gs.engine.kms.service.KnowledgeBaseService;
import com.chinatelecom.gs.engine.kms.util.KnowledgePublishCheckUtils;
import com.chinatelecom.gs.engine.kms.util.PrivilegeEnumUtils;
import com.chinatelecom.gs.privilege.common.enums.PrivilegeEnum;
import com.chinatelelcom.gs.engine.sdk.common.Page;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static com.chinatelecom.gs.engine.common.constants.Constants.EMPTY_NODE;
import static com.chinatelecom.gs.engine.kms.search.IndexBuild.PARENT_CATALOG_CODE;

/**
 * <p>
 * 目录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-13
 */
@Service
@Validated
@Slf4j

public class CatalogServiceImpl extends BaseAppServiceByCodeImpl<CatalogRepository, CatalogQueryParam,
        CatalogVoConverter, CatalogPO, CatalogDTO, CatalogVO, CatalogCreateParam, CatalogUpdateParam>
        implements CatalogService {

    @Resource
    private CatalogRepository catalogRepository;

    @Resource
    private KnowledgeRepository knowledgeRepository;

    @Resource
    private KnowledgeBaseRepository knowledgeBaseRepository;

    @Resource
    private SearchService searchService;

    @Resource
    @Lazy
    private KnowledgeAppService knowledgeAppService;

    @Resource
    private CollectionAppService collectionAppService;

    @Resource
    private GsGlobalConfig gsGlobalConfig;

    @Resource
    private BaseConfigAppService configService;

    @Resource
    @Lazy
    private KnowledgeBaseService knowledgeBaseService;


    protected CatalogVoConverter converter() {
        return CatalogVoConverter.INSTANCE;
    }

    @Override
    public Page<CatalogVO> pageQuery(CatalogQueryParam query) {
        PrivilegeEnum hasPrivilege = knowledgeBaseService.checkKnowledgeBaseAuth(query.getKnowledgeBaseCode(), PrivilegeEnum.view);
        if (query.getParentCode() == null) {
            query.setParentCode(EMPTY_NODE);
        }
        List<String> publishStatusList = query.getPublishStatus();
        if (CollectionUtils.isNotEmpty(publishStatusList)) {
            query.setFilterPublishStatus(true);
        }

        //获取开关配置
        PublishConfig config = configService.getConfigOrDefault(ConfigConvert.PUBLISH_CONFIG, RequestContext.getTenantId());
        if(!config.getPublishSwitch()) {
            // 发布功能关闭时不显示删除未发布的数据
            if (CollectionUtils.isNotEmpty(publishStatusList)) {
                publishStatusList.remove(PublishStatus.DELETE_UNPUBLISH.name());
            } else {
                publishStatusList = new ArrayList<>();
                query.setPublishStatus(publishStatusList);
                publishStatusList.addAll(PublishStatus.NOT_DELETE.stream().map(PublishStatus::name).collect(Collectors.toList()));
            }
        }

        // 判断是否需要递归搜索所有子目录
        boolean needRecursiveSearch = hasFilterConditions(query);
        List<String> searchParentCodes = null;

        if (needRecursiveSearch) {
            // 获取当前目录下的所有子目录codes
            searchParentCodes = getAllSubCatalogCodes(query.getKnowledgeBaseCode(), query.getParentCode());
        }

        Page<CatalogVO> page = IPageUtils.convert(catalogRepository.page(query, searchParentCodes));
        if (CollectionUtils.isEmpty(page.getRecords())) {
            return page;
        }
        List<CatalogVO> catalogVOList = page.getRecords();

        List<String> knowledgeCodes = catalogVOList.stream()
                .filter(catalogVO -> CatalogType.KNOWLEDGE.getValue().equals(catalogVO.getType())
                        && StringUtils.isNotBlank(catalogVO.getKnowledgeCode()))
                .map(CatalogVO::getKnowledgeCode).collect(Collectors.toList());
        List<KnowledgeDTO> knowledgeList = knowledgeRepository.findByCodes(knowledgeCodes);
        Map<String, KnowledgeDTO> knowledgeMap = Maps.uniqueIndex(knowledgeList, KnowledgeDTO::getCode);

        List<String> catalogCodes = catalogVOList.stream().filter(c -> StringUtils.equals(CatalogType.TITLE.getValue(), c.getType()))
                .map(CatalogVO::getCode).collect(Collectors.toList());

        Map<String, Integer> titleCountMap = catalogRepository.countByTypeAndParentCodes(query.getKnowledgeBaseCode(),
                catalogCodes, CatalogType.TITLE);
        Map<String, Integer> knowledgeCountMap = catalogRepository.countByTypeAndParentCodes(query.getKnowledgeBaseCode(),
                catalogCodes, CatalogType.KNOWLEDGE);


        catalogVOList.forEach(catalogVO -> {
            if (knowledgeMap.containsKey(catalogVO.getKnowledgeCode())) {
                KnowledgeDTO knowledgeDTO = knowledgeMap.get(catalogVO.getKnowledgeCode());
                if(knowledgeDTO != null) {
                    catalogVO.setName(knowledgeDTO.getName());
                    catalogVO.setKnowledgeType(knowledgeDTO.getType());
                    catalogVO.setKnowledgeStatus(knowledgeDTO.getStatus());
                    catalogVO.setKnowledgeIndexingStatus(knowledgeDTO.getIndexingStatus());
                    catalogVO.setKnowledgeImageIndexingStatus(knowledgeDTO.getImageIndexingStatus());
                    catalogVO.setKnowledgeOn(knowledgeDTO.getOn());
                    catalogVO.setCollected(knowledgeDTO.getCollected());
                    catalogVO.setPublishStatus(knowledgeDTO.getPublishStatus());
                    catalogVO.setKnowledgeTip(knowledgeDTO.getKnowledgeTip());
                    catalogVO.setUpdateTime(knowledgeDTO.getUpdateTime());
                    catalogVO.setTaskId(knowledgeDTO.getTaskId());
                    catalogVO.setImageTaskId(knowledgeDTO.getImageTaskId());
                    catalogVO.setProgress(knowledgeDTO.getProgress());
                    catalogVO.setDevMessage(knowledgeDTO.getDevMessage());
                    catalogVO.setFileLength(knowledgeDTO.getFileLength());
                }
            }

            // 获取下一级目录数量
            catalogVO.setNextLevelCatalogCount(titleCountMap.getOrDefault(catalogVO.getCode(), 0));
            // 获取下一级知识数量
            catalogVO.setNextLevelKnowledgeCount(knowledgeCountMap.getOrDefault(catalogVO.getCode(), 0));
            catalogVO.setPermission(PrivilegeEnumUtils.convertPermission(hasPrivilege));
        });

        // 分别处理不同type的收藏状态
        collectionAppService.fetchCollectionCatalogVO(
                catalogVOList.stream()
                        .filter(vo -> CatalogType.TITLE.getValue().equals(vo.getType()))
                        .collect(Collectors.toList()),
                TargetType.Catalog
        );

        collectionAppService.fetchCollectionCatalogVO(
                catalogVOList.stream()
                        .filter(vo -> CatalogType.KNOWLEDGE.getValue().equals(vo.getType()))
                        .collect(Collectors.toList()),
                TargetType.Knowledge
        );

        return page;
    }

    @Override
    public List<CatalogTreeVO> treePath(String knowledgeBaseCode, String catalogCode) {
        List<CatalogDTO> nodes = treeToNode(knowledgeBaseCode, catalogCode, null);
        List<CatalogDTO> result = Lists.newArrayList();
        CatalogDTO.flatten(nodes, result);
        setInfo(result, true);
        return convertToTree(nodes.get(0).getChildren());
    }

    @Override
    public List<CatalogTreeVO> treePathByKnowledgeCode(String knowledgeBaseCode, String knowledgeCode) {
        CatalogDTO catalogDTO = catalogRepository.findByKnowledgeCode(knowledgeCode);
        BizAssert.notNull(catalogDTO, "AA012", "目录数据不存在");
        return treePath(knowledgeBaseCode, catalogDTO.getCode());
    }

    @Override
    public void deleteByKnowledgeCode(Collection<String> knowledgeCodes) {
        catalogRepository.deleteByKnowledgeCode(knowledgeCodes);
        //删除收藏
        collectionAppService.deleteByTargetCodesAllUser(knowledgeCodes, TargetType.Knowledge);
    }

    @Override
    public List<CatalogDTO> findCatalogPathList(String knowledgeBaseCode, String catalogCode) {
        CatalogDTO catalog = catalogRepository.findByCode(catalogCode);
        return findPath(knowledgeBaseCode, catalog, new ArrayList<>());
    }

    @Override
    public List<CatalogDTO> findCatalogPathListByKnowledgeCode(String knowledgeBaseCode, String knowledgeCode) {
        CatalogDTO catalogDTO = catalogRepository.findByKnowledgeCode(knowledgeCode);
        BizAssert.notNull(catalogDTO, "AA012", "目录数据不存在");
        return findCatalogPathList(knowledgeBaseCode, catalogDTO.getCode());
    }

    @Override
    public List<CatalogCopyTreeDTO> queryAllTree(String knowledgeBaseCode) {
        List<CatalogDTO> allCatalogList = catalogRepository.findByKnowledgeBaseCode(knowledgeBaseCode);
        if (CollectionUtils.isNotEmpty(allCatalogList)) {
            List<String> knowledgeCodes = allCatalogList.stream()
                    .filter(catalogDTO -> StringUtils.equals(catalogDTO.getType(), CatalogType.KNOWLEDGE.getValue()))
                    .map(CatalogDTO::getKnowledgeCode)
                    .collect(Collectors.toList());

            List<KnowledgeDTO> knowledgeList = knowledgeRepository.findCopyInfoByCodes(knowledgeBaseCode, knowledgeCodes);
            Map<String, KnowledgeDTO> knowledgeMap = Maps.uniqueIndex(knowledgeList, KnowledgeDTO::getCode);
            List<CatalogCopyTreeDTO> allTreeList = allCatalogList.stream()
                    .map(catalogDTO -> buildCatalogCopy(knowledgeMap, catalogDTO)).collect(Collectors.toList());

            // 构造树形结构
            Map<String, List<CatalogCopyTreeDTO>> parentMap = allTreeList.stream().collect(Collectors.groupingBy(CatalogCopyTreeDTO::getParentCode));
            CatalogCopyTreeDTO root = new CatalogCopyTreeDTO();
            root.setCode(EMPTY_NODE);
            generateTreeOne(root, parentMap);
            return root.getChild();
        }
        return Collections.emptyList();
    }


    private void generateTreeOne(CatalogCopyTreeDTO dto, Map<String, List<CatalogCopyTreeDTO>> treeMap) {
        String catalogCode = dto.getCode();
        List<CatalogCopyTreeDTO> sonList = treeMap.get(catalogCode);
        if (CollectionUtils.isNotEmpty(sonList)) {
            dto.setChild(sonList);
            for (CatalogCopyTreeDTO sonDTO : sonList) {
                generateTreeOne(sonDTO, treeMap);
            }
        }
    }

    private CatalogCopyTreeDTO buildCatalogCopy(Map<String, KnowledgeDTO> knowledgeMap, CatalogDTO catalogDTO) {
        CatalogCopyTreeDTO result = new CatalogCopyTreeDTO();
        result.setCode(catalogDTO.getCode());
        result.setName(catalogDTO.getName());
        CatalogType catalogType = CatalogType.fromValue(catalogDTO.getType());
        result.setType(catalogType);
        result.setParentCode(catalogDTO.getParentCode());

        if (CatalogType.KNOWLEDGE == catalogType) {
            KnowledgeDTO knowledgeDTO = knowledgeMap.get(catalogDTO.getKnowledgeCode());
            if (knowledgeDTO != null) {
                result.setKnowledgeCode(knowledgeDTO.getCode());
                result.setOriginalFileKey(knowledgeDTO.getOriginalFileKey());
            }
        }
        return result;
    }

    private List<CatalogDTO> findPath(String knowledgeBaseCode, CatalogDTO catalog, List<CatalogDTO> pathList) {
        if (catalog == null || !catalog.getKnowledgeBaseCode().equals(knowledgeBaseCode)) {
            log.error("知识库[{}]的目录[{}]不存在", knowledgeBaseCode, Objects.isNull(catalog) ? null : catalog.getCode());
            throw new BizException("AA014", "未找到目录");
        }

        if (pathList.size() > 1000) {
            BizAssert.throwBizException("A0003", "递归查询目录子数据超过最大递归限制");
        }

        if (!EMPTY_NODE.equals(catalog.getParentCode())) {
            CatalogDTO parent = catalogRepository.findByCode(catalog.getParentCode());
            pathList.add(0, catalog);
            findPath(knowledgeBaseCode, parent, pathList);
        } else {
            pathList.add(0, catalog);
        }

        return pathList;
    }

    /**
     * 更新目录的关联信息，包括收藏状态、关联知识等
     *
     * @param catalogs
     */
    public void setInfo(List<CatalogDTO> catalogs, boolean count) {
        if (CollectionUtils.isEmpty(catalogs)) {
            return;
        }

        final var knowledgeCodes = catalogs.stream().map(CatalogDTO::getKnowledgeCode).filter(Objects::nonNull).collect(Collectors.toList());
        final var knowledgeMap = knowledgeRepository.findByCodes(knowledgeCodes).stream().
                collect(Collectors.toMap(KnowledgeDTO::getCode, a -> a));
        catalogs.forEach(catalog -> {
            if (catalog.getKnowledgeCode() != null) {
                KnowledgeDTO knowledgeDTO = knowledgeMap.get(catalog.getKnowledgeCode());
                if (knowledgeDTO == null) {
                    return;
                }
                catalog.setKnowledgeType(knowledgeDTO.getType());
                catalog.setCollected(knowledgeDTO.getCollected());
                catalog.setKnowledgeStatus(knowledgeDTO.getStatus());
                catalog.setKnowledgeIndexingStatus(knowledgeDTO.getIndexingStatus());
                catalog.setKnowledgeImageIndexingStatus(knowledgeDTO.getImageIndexingStatus());
                catalog.setKnowledgeTip(knowledgeDTO.getKnowledgeTip());
                catalog.setKnowledgeOn(knowledgeDTO.getOn());
                catalog.setCoverFileUrl(knowledgeDTO.getCoverFileUrl());
            }
        });
    }

    private List<CatalogDTO> treeToNode(String knowledgeBaseCode, String catalogCode, List<CatalogDTO> children) {
        CatalogDTO catalogDTO = catalogRepository.findByCode(catalogCode);
        List<CatalogDTO> list = catalogRepository.findByParentCode(knowledgeBaseCode, catalogDTO.getParentCode());
        for (CatalogDTO node : list) {
            if (node.getCode().equals(catalogCode)) {
                node.setChildren(children);
            }
        }
        List<CatalogDTO> parents;
        if (catalogDTO.getParentCode().equals(EMPTY_NODE)) {
            CatalogDTO root = new CatalogDTO();
            root.setCode(EMPTY_NODE);
            root.setChildren(list);
            parents = ImmutableList.of(root);
        } else {
            parents = treeToNode(knowledgeBaseCode, catalogDTO.getParentCode(), list);
        }
        return parents;
    }

    private List<CatalogTreeVO> convertToTree(List<CatalogDTO> catalogs) {
        if (catalogs == null) {
            return ImmutableList.of();
        }
        return catalogs.stream().map(catalog -> {
            CatalogTreeVO vo = converter().convertTreeVO(catalog);
            vo.setSubCatalogs(convertToTree(catalog.getChildren()));
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    public CatalogVO get(String code) {
        CatalogVO catalogVO = super.get(code);
        BizAssert.notNull(catalogVO, "AA012", "目录数据不存在");
        // 获取下一级目录数量
        Integer nextLevelCatalogCount = catalogRepository.countByTypeAndParentCode(catalogVO.getKnowledgeBaseCode(), code, CatalogType.TITLE);
        catalogVO.setNextLevelCatalogCount(nextLevelCatalogCount);
        // 获取下一级知识数量
        Integer nextLevelKnowledgeCount = catalogRepository.countByTypeAndParentCode(catalogVO.getKnowledgeBaseCode(), code, CatalogType.KNOWLEDGE);
        catalogVO.setNextLevelKnowledgeCount(nextLevelKnowledgeCount);
        //设置目录所属知识库类型
        KnowledgeBaseDTO knowledgeBaseDTO = knowledgeBaseRepository.findByCode(catalogVO.getKnowledgeBaseCode());
        catalogVO.setKnowledgeBaseType(knowledgeBaseDTO.getType());
        catalogVO.setCollected(collectionAppService.isCollected(TargetType.Catalog, catalogVO.getCode()));
        return catalogVO;
    }

    @Override
    public List<CatalogVO> list(Collection<String> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return ImmutableList.of();
        }
        List<CatalogDTO> catalogDTOs = catalogRepository.findByCodes(codes);
        List<CatalogVO> catalogVOs = catalogDTOs.stream()
                .map(converter()::convertVO)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(catalogVOs)) {
            return ImmutableList.of();
        }
        String knowledgeBaseCode = catalogVOs.get(0).getKnowledgeBaseCode();

        List<String> titleCodes = catalogDTOs.stream().filter(dto -> CatalogType.TITLE.getValue().equals(dto.getType())).map(CatalogDTO::getCode).collect(Collectors.toList());
        Map<String, Integer> titleCountMap = catalogRepository.countByTypeAndParentCodes(knowledgeBaseCode,
                titleCodes, CatalogType.TITLE);
        Map<String, Integer> knowledgeCountMap = catalogRepository.countByTypeAndParentCodes(knowledgeBaseCode,
                titleCodes, CatalogType.KNOWLEDGE);

        for (CatalogVO catalogVO : catalogVOs) {
            // 获取下一级目录数量
            catalogVO.setNextLevelCatalogCount(titleCountMap.getOrDefault(catalogVO.getCode(), 0));
            // 获取下一级知识数量
            catalogVO.setNextLevelKnowledgeCount(knowledgeCountMap.getOrDefault(catalogVO.getCode(), 0));

            //设置目录所属知识库类型
            KnowledgeBaseDTO knowledgeBaseDTO = knowledgeBaseRepository.findByCode(catalogVO.getKnowledgeBaseCode());
            catalogVO.setKnowledgeBaseType(knowledgeBaseDTO.getType());
        }
        return catalogVOs;
    }

    @Transactional
    @Override
    @Lock(key = "'catalog_' + #create.getKnowledgeBaseCode()", leaseTime = 60, waitTime = 1, message="分组正在创建中...")
    public CatalogVO create(CatalogCreateParam create) {
        String catalogCode = catalogRepository.generateCode(StringUtils.EMPTY);
        knowledgeBaseService.checkKnowledgeBaseAuth(create.getKnowledgeBaseCode(), PrivilegeEnum.edit);

        checkKnowledgeBaseAndParent(create.getKnowledgeBaseCode(), create.getParentCode());
        CatalogDTO catalogDTO = converter().convertCreate(create);
        catalogDTO.setCode(catalogCode);
        if (catalogDTO.getKnowledgeCode() != null) {
            catalogDTO.setType(CatalogType.KNOWLEDGE.getValue());
        } else {
            catalogDTO.setType(CatalogType.TITLE.getValue());
        }
        checkCatalog(catalogDTO);
        int level = 1;
        String parentCode = create.getParentCode();
        if (parentCode == null) {
            parentCode = EMPTY_NODE;
        } else {
            CatalogDTO parent = catalogRepository.findByCode(parentCode);
            level = parent.getLevel() + 1;
        }
        catalogDTO.setParentCode(parentCode);
        catalogDTO.setLevel(level);
        if (create.getPrevCode() == null) {
            catalogDTO.setPrevCode(EMPTY_NODE);
        }
        catalogRepository.checkNameDuplication(catalogDTO);
        catalogRepository.save(catalogDTO);
        // 创建目录到指定为止
//        catalogDTO = moveTo(catalogDTO, create.getKnowledgeBaseCode(), create.getParentCode(), create.getPrevCode());
        return converter().convertVO(catalogDTO);
    }

    @Transactional
    @Override
    public boolean update(String code, CatalogUpdateParam update) {
        CatalogDTO catalogDTO = catalogRepository.findByCode(code);
        BizAssert.notNull(catalogDTO, "AA014", "目录数据不存在");
        knowledgeBaseService.checkKnowledgeBaseAuth(catalogDTO.getKnowledgeBaseCode(), PrivilegeEnum.edit);
        catalogDTO.setName(update.getName());
        catalogRepository.checkNameDuplication(catalogDTO);
        return super.update(code, update);
    }

    @Transactional
    @Override
    @Lock(key = "'catalog_' + #param.getKnowledgeBaseCode()", leaseTime = 60, waitTime = 1, message="分组正在移动中...")
    public void move(String code, CatalogMoveParam param) {
        CatalogDTO catalogDTO = catalogRepository.findByCode(code);
        if (catalogDTO == null) {
            throw new BizException("AA014", "未找到目录");
        }
        knowledgeBaseService.checkKnowledgeBaseAuth(catalogDTO.getKnowledgeBaseCode(), PrivilegeEnum.edit);
        if (Objects.equals(catalogDTO.getKnowledgeType(), CatalogType.KNOWLEDGE)) {
            KnowledgeDTO knowledgeDTO = knowledgeRepository.selectByCode(catalogDTO.getKnowledgeCode());
            KnowledgePublishCheckUtils.updatePublishStatusCheck(knowledgeDTO.getPublishStatus());
        }

        if (catalogDTO.getKnowledgeBaseCode().equals(param.getKnowledgeBaseCode())) {
            move(catalogDTO, param);
        } else {
            throw new BizException("AA044", "禁止跨知识库移动");
//            moveToDiffKb(catalogDTO, param);
        }
    }

    public void moveToDiffKb(CatalogDTO catalogDTO, CatalogMoveParam param) {
        move(catalogDTO, param);
    }

    /**
     * 移动目录
     *
     * @param catalogDTO 被移动的目录
     * @param param      参数
     */
    private void move(CatalogDTO catalogDTO, CatalogMoveParam param) {
        if (catalogDTO.getId().toString().equals(param.getParentCode()) || catalogDTO.getId().toString().equals(param.getPrevCode())) {
            throw new BizException("AA043", "目录正在更新中，请勿重复提交");
        }
        checkKnowledgeBaseAndParent(param.getKnowledgeBaseCode(), param.getParentCode());
        String originalParent = catalogDTO.getParentCode();
        String originalPrev = catalogDTO.getPrevCode();
        if (param.getParentCode() == null) {
            param.setParentCode(EMPTY_NODE);
        }
        if (param.getPrevCode() == null) {
            param.setPrevCode(EMPTY_NODE);
        }
        if (originalParent.equals(param.getParentCode()) && originalPrev.equals(param.getPrevCode()) &&
                catalogDTO.getKnowledgeBaseCode().equals(param.getKnowledgeBaseCode())) {
            return;
        }
        if (param.getWithChildren()) {
            // 查找所有目录及其子目录
            catalogDTO = findAllChildren(catalogDTO.getCode(), catalogDTO.getKnowledgeBaseCode());
            // 禁止移动到自己的子目录中
            Set<String> sonCatalogCodeSet = new HashSet<>();
            getAllSonCatalogCode(catalogDTO, sonCatalogCodeSet, 1);
            if (sonCatalogCodeSet.contains(param.getParentCode())) {
                BizAssert.throwBizException("AA045", "禁止将目录移动至自己子目录中");
            }

            // 移出目录
            moveOut(catalogDTO);
            // 移动目录及其子目录到指定位置
            moveTo(catalogDTO, param.getKnowledgeBaseCode(), param.getParentCode(), param.getPrevCode());
        } else {
            // 移出目录
            moveOut(catalogDTO);
            // 移动目录到指定位置
            moveTo(catalogDTO, param.getKnowledgeBaseCode(), param.getParentCode(), param.getPrevCode());
            // 将该目录的子目录向上移动一个层级
            catalogDTO = findAllChildren(catalogDTO.getCode(), catalogDTO.getKnowledgeBaseCode());
            if (CollectionUtils.isNotEmpty(catalogDTO.getChildren())) {
                final var children = catalogDTO.getChildren();
                children.forEach(child -> child.setParentCode(originalParent));
                children.get(0).setPrevCode(originalPrev);
                List<CatalogDTO> changed = catalogDTO.flatten();
                changed.remove(0);
                changed.forEach(c -> c.setLevel(c.getLevel() - 1));

//                CatalogDTO latter = repository.findByRelatedCode(catalogDTO.getKnowledgeBaseCode(), originalParent, originalPrev);
//                if (latter != null) {
//                    latter.setPrevCode(children.get(children.size() - 1).getCode());
//                    changed.add(latter);
//                }
                catalogRepository.saveOrUpdateBatch(changed);
            }
        }
        if (catalogDTO.getKnowledgeCode() != null) {
            Map<String, Object> updateInfo = new HashMap<>();
            updateInfo.put(PARENT_CATALOG_CODE, param.getParentCode());
            searchService.updateBaseInfoByKnowledgeCode(catalogDTO.getKnowledgeCode(), updateInfo);

            knowledgeRepository.updateUpdateTime(catalogDTO.getKnowledgeCode());
        }
    }

    private void getAllSonCatalogCode(CatalogDTO catalogDTO, Set<String> sonCatalogCodeSet, Integer level) {
        if (catalogDTO != null) {
            sonCatalogCodeSet.add(catalogDTO.getCode());
            if (level > 1000) {
                BizAssert.throwBizException("A0003", "递归查询目录子数据超过最大递归限制");
            }

            if (CollectionUtils.isNotEmpty(catalogDTO.getChildren())) {
                for (CatalogDTO son : catalogDTO.getChildren()) {
                    getAllSonCatalogCode(son, sonCatalogCodeSet, level + 1);
                }
            }
        }
    }

    /**
     * 判断查询参数中是否包含过滤条件
     */
    private boolean hasFilterConditions(CatalogQueryParam query) {
        return StringUtils.isNotBlank(query.getName()) ||
                CollectionUtils.isNotEmpty(query.getStatus()) ||
                query.getFilterPublishStatus();
    }

    /**
     * 获取指定目录下的所有子目录codes和当前code（包括多层级）
     */
    private List<String> getAllSubCatalogCodes(String knowledgeBaseCode, String parentCode) {
        // 获取当前目录下的所有子目录树
        CatalogDTO rootCatalog = findAllChildren(parentCode, knowledgeBaseCode);

        // 收集所有子目录的codes
        Set<String> allCodes = new HashSet<>();
        getAllSonCatalogCode(rootCatalog, allCodes, 0);

        // 添加当前目录code
        allCodes.add(parentCode);

        return new ArrayList<>(allCodes);
    }

    private void getAllSonTitleCode(CatalogDTO catalogDTO, Set<String> sonCatalogCodeSet, Integer level) {
        if (catalogDTO != null) {
            if ("title".equals(catalogDTO.getType())){
                sonCatalogCodeSet.add(catalogDTO.getCode());
            }
            if (level > 1000) {
                BizAssert.throwBizException("A0003", "递归查询目录子数据超过最大递归限制");
            }

            if (CollectionUtils.isNotEmpty(catalogDTO.getChildren())) {
                for (CatalogDTO son : catalogDTO.getChildren()) {
                    getAllSonTitleCode(son, sonCatalogCodeSet, level + 1);
                }
            }
        }
    }

    /**
     * 将目录从原来位置移除
     *
     * @param catalogDTO
     */
    private void moveOut(CatalogDTO catalogDTO) {
//        CatalogDTO latter = repository.findByRelatedCode(catalogDTO.getKnowledgeBaseCode(), catalogDTO.getParentCode(), catalogDTO.getCode());
//        if (latter != null) {
//            latter.setPrevCode(catalogDTO.getPrevCode());
//            repository.save(latter);
//        }
    }

    @Override
    public Set<String> findAllCatalogTitleCodes(String rootCode, String knowledgeBaseCode) {
        CatalogDTO rootDTO = this.findAllChildren(rootCode, knowledgeBaseCode);
        Set<String> allCode = new HashSet<>();
        getAllSonTitleCode(rootDTO, allCode, 1);
        return allCode;
    }

    /**
     * 查找根目录的所有子目录
     *
     * @param rootCode           根目录
     * @param knowledgeBaseCode  知识库Code
     * @return 根目录
     */
    private CatalogDTO findAllChildren(String rootCode, String knowledgeBaseCode) {
        return findAllChildren(rootCode, knowledgeBaseCode, null);
    }

    /**
     * 查找根目录的所有子目录
     *
     * @param rootCode          根目录
     * @param knowledgeBaseCode 知识库Code
     * @return 根目录
     */
    private CatalogDTO findAllChildren(String rootCode, String knowledgeBaseCode, Integer level) {
        List<CatalogDTO> all = catalogRepository.findByKnowledgeBaseCode(knowledgeBaseCode);
        Map<String, CatalogDTO> map = StreamUtils.toMap(all, CatalogDTO::getCode);
        Map<String, List<CatalogDTO>> parentMap = StreamUtils.groupBy(all, CatalogDTO::getParentCode);
        Queue<String> queue = Lists.newLinkedList();
        queue.offer(rootCode);
        CatalogDTO root;
        int baseLevel = 0;
        if (EMPTY_NODE.equals(rootCode)) {
            root = new CatalogDTO();
            map.put(rootCode, root);
        } else {
            root = map.get(rootCode);
            baseLevel = root.getLevel();
        }
        int maxLevel = level == null ? Integer.MAX_VALUE : baseLevel + level;
        while (!queue.isEmpty()) {
            String id = queue.poll();
            CatalogDTO catalogDTO = map.get(id);
            if (catalogDTO == null) {
                continue;
            }
            List<CatalogDTO> children = parentMap.getOrDefault(id, Lists.newArrayList());
//            children = sort(children);
            children = children.stream().filter(child -> {
                if (child.getLevel() > maxLevel) {
                    return false;
                }
//                if (filter.getType() != null && filter.getType() != child.getType()) {
//                    return false;
//                }
                return true;
            }).collect(Collectors.toList());
            catalogDTO.setChildren(children);
            for (CatalogDTO child : children) {
                queue.offer(child.getCode());
            }
        }
        return root;
    }

    /**
     * 按照prevCode排序
     *
     * @return 排序好的目录列表
     */
    private List<CatalogDTO> sort(List<CatalogDTO> children) {
        Map<String, CatalogDTO> map = StreamUtils.toMap(children, CatalogDTO::getPrevCode);
        String prevCode = EMPTY_NODE;
        List<CatalogDTO> sorted = Lists.newArrayList();
        while (!map.isEmpty()) {
            CatalogDTO catalogDTO = map.remove(prevCode);
            if (catalogDTO == null) {
                break;
            }
            sorted.add(catalogDTO);
            prevCode = catalogDTO.getCode();
        }
        return sorted;
    }

    /**
     * 将目录移动到指定位置。如果目录ID为空或者不存在，则表示新建目录
     *
     * @param catalogDTO      待移动的目录
     * @param knowledgeBaseCode 目标知识库
     * @param parentCode      目标父目录
     * @param prevCode        目标前一个目录
     */
    private CatalogDTO moveTo(CatalogDTO catalogDTO, String knowledgeBaseCode, String parentCode, String prevCode) {
        int level = 1;
        if (parentCode == null) {
            parentCode = EMPTY_NODE;
        } else if (!parentCode.equals(EMPTY_NODE)) {
            CatalogDTO parent = catalogRepository.findByCode(parentCode);
            level = parent.getLevel() + 1;
        }
        if (prevCode == null) {
            prevCode = EMPTY_NODE;
        }
        // 查到待移动目录的后置目录
//        CatalogDTO latter = repository.findByRelatedCode(knowledgeBaseCode, parentCode, prevCode);
        catalogDTO.setKnowledgeBaseCode(knowledgeBaseCode);
        catalogDTO.setPrevCode(prevCode);
        catalogDTO.setParentCode(parentCode);
        catalogDTO.setLevel(level);
        rebuildRelatedCode(catalogDTO.getLevel(), catalogDTO.getCode(), catalogDTO.getChildren());
        iterate(catalogDTO.getChildren(), child -> {
            child.setKnowledgeBaseCode(knowledgeBaseCode);
        });
        saveCatalogKnowledge(catalogDTO, knowledgeBaseCode);
        List<CatalogDTO> changed = catalogDTO.flatten();
//        if (latter != null) {
//            if (catalogDTO.getCode() == null) {
//                catalogDTO.setCode(repository.generateCode(StringUtils.EMPTY));
//            }
//            latter.setPrevCode(catalogDTO.getCode());
//            changed.add(latter);
//        }
        catalogRepository.saveOrUpdateBatch(changed);
        return catalogRepository.findByCode(catalogDTO.getCode());
    }

    /**
     * 保存目录及其子目录关联的知识
     */
    private void saveCatalogKnowledge(CatalogDTO catalogDTO, String knowledgeBaseCode) {
        iterate(ImmutableList.of(catalogDTO), child -> {
            if (child.getKnowledgeDTO() != null) {
                KnowledgeDTO knowledgeDTO = child.getKnowledgeDTO();
                knowledgeDTO.setKnowledgeBaseCode(knowledgeBaseCode);
                knowledgeDTO.setParentCatalogCode(child.getParentCode());
//                knowledgeDTO = knowledgeApplicationService.copy(knowledgeDTO);
                child.setKnowledgeCode(knowledgeDTO.getCode());
                child.setKnowledgeDTO(knowledgeDTO);
            }
        });
    }

    /**
     * 重置建立目录关系的code
     *
     * @param parentLevel 父目录层级
     * @param parentCode  父目录ID
     * @param catalogs    目录列表
     */
    private void rebuildRelatedCode(int parentLevel, String parentCode, List<CatalogDTO> catalogs) {
        if (CollectionUtils.isEmpty(catalogs)) {
            return;
        }
        String prev = EMPTY_NODE;
        for (CatalogDTO child : catalogs) {
            child.setParentCode(parentCode);
            child.setPrevCode(prev);
            child.setLevel(parentLevel + 1);
            prev = child.getCode();
            rebuildRelatedCode(child.getLevel(), child.getCode(), child.getChildren());
        }
    }

    /**
     * 遍历目录及其子目录
     */
    private void iterate(List<CatalogDTO> catalogs, Consumer<CatalogDTO> consumer) {
        if (CollectionUtils.isEmpty(catalogs)) {
            return;
        }
        for (CatalogDTO catalog : catalogs) {
            consumer.accept(catalog);
            iterate(catalog.getChildren(), consumer);
        }

    }

    private void checkKnowledgeBaseAndParent(String knowledgeBaseCode, String code) {
        KnowledgeBaseDTO knowledgeBaseDTO = knowledgeBaseRepository.findByCode(knowledgeBaseCode);
        if (knowledgeBaseDTO == null) {
            throw new BizException("AA011", "知识库不存在");
        }
        if (code != null) {
            CatalogDTO catalogDTO = catalogRepository.findByCode(code);
            if (!knowledgeBaseCode.equals(catalogDTO.getKnowledgeBaseCode())) {
                throw new BizException("AA042", "目录和目标知识库不一致");
            }
        }
    }

    private void checkCatalog(CatalogDTO catalogDTO) {
        if (catalogDTO.getName().length() > gsGlobalConfig.getKmsBizConfig().getFileNameMaxLength()) {
            throw new BizException("AA002", "文件名称超过长度限制");
        }
    }

    @Transactional
    @Override
    public boolean delete(CodeParam codeParam) {
        for (String code : codeParam.getCodes()) {
//            AppDTO appDTO = appRepository.getById(RequestContext.getCheckedAppId());
//            if (id.equals(appDTO.getDefaultCatalogId())) {
//                throw new BizException(ErrorCode.CATALOG_DEFAULT_CANNOT_DELETE.getCode(), ErrorCode.CATALOG_DEFAULT_CANNOT_DELETE.getDesc());
//            }
            CatalogDTO catalogDTO = catalogRepository.findByCode(code);
            if (catalogDTO != null) {
                knowledgeBaseService.checkKnowledgeBaseAuth(catalogDTO.getKnowledgeBaseCode(), PrivilegeEnum.edit);

                if (StringUtils.equals(CatalogType.KNOWLEDGE.getValue(), catalogDTO.getType())) {
                    knowledgeAppService.publishDeleteFlag(catalogDTO.getKnowledgeCode());
                } else {
                    // 检查目录下是否为空，为空才允许删除
                    CatalogDTO son = catalogRepository.findOneByParentCode(catalogDTO.getKnowledgeBaseCode(), catalogDTO.getCode());
                    BizAssert.isNull(son, "AA032", "目录下存在文档，禁止删除");
                    //删除收藏
                    collectionAppService.deleteByTargetCodesAllUser(ImmutableList.of(catalogDTO.getCode()), TargetType.Catalog);
                    catalogRepository.removeByCode(code);
                }
            }
        }
        return true;
    }

}
