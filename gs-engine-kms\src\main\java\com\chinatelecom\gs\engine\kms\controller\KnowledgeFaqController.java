package com.chinatelecom.gs.engine.kms.controller;

import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.common.utils.HtmlXssUtils;
import com.chinatelecom.gs.engine.kms.base.model.CodeParam;
import com.chinatelecom.gs.engine.kms.sdk.api.KnowledgeFaqApi;
import com.chinatelecom.gs.engine.kms.sdk.constants.KmsApis;
import com.chinatelecom.gs.engine.kms.sdk.enums.EnvType;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.faq.KnowledgeFaqCreateParam;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.faq.KnowledgeFaqQueryParam;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.faq.KnowledgeFaqUpdateParam;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.faq.KnowledgeFaqVO;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.publish.KnowledgeFaqProd;
import com.chinatelecom.gs.engine.kms.service.KnowledgeFaqAppService;
import com.chinatelecom.gs.engine.kms.service.KnowledgeFaqProdAppService;
import com.chinatelecom.gs.engine.sdk.common.annotation.ApiType;
import com.chinatelecom.gs.engine.sdk.common.annotation.HideFromApiTypes;
import com.chinatelelcom.gs.engine.sdk.common.Page;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;


/**
 * <p>
 * 问答表 Controller
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-05
 */
@RestController
@Slf4j
@Tag(name = "问答表 Controller")
@RequestMapping({KmsApis.KMS_API + KmsApis.KNOWLEDGE + KmsApis.FAQ,
        KmsApis.RPC + KmsApis.KNOWLEDGE + KmsApis.FAQ,
        KmsApis.OPENAPI + KmsApis.KNOWLEDGE + KmsApis.FAQ})
public class KnowledgeFaqController implements KnowledgeFaqApi {

    @Resource
    private KnowledgeFaqAppService knowledgeFaqAppService;

    @Resource
    private KnowledgeFaqProdAppService knowledgeFaqProdAppService;

    @Operation(summary = "问答表分页列表", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "问答表分页列表", groupName = "问答对管理")
    @AuditLog(businessType = "问答对管理", operType = "问答表分页列表", operDesc = "问答表分页列表", objId="#param.knowledgeCode")
    @PostMapping(KmsApis.PAGE_API)
    public Result<Page<KnowledgeFaqVO>> page(@Validated @RequestBody KnowledgeFaqQueryParam param) {
        return Result.success(knowledgeFaqAppService.pageQuery(param));
    }

    @Operation(summary = "问答表详情", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "问答表详情", groupName = "问答对管理")
    @AuditLog(businessType = "问答对管理", operType = "问答表详情", operDesc = "问答表详情", objId="#code")
    @GetMapping(KmsApis.CODE_PATH)
    public Result<KnowledgeFaqVO> get(@PathVariable("code") String code,
                                      @RequestParam(value = "env", required = false, defaultValue = "TEST") String env) {
        // 先获取测试环境数据
        KnowledgeFaqVO testVO = knowledgeFaqAppService.get(code);
        if (testVO == null) {
            return Result.success(null);
        }

        // 如果env为PROD，则用生产数据覆盖测试数据
        if (EnvType.PROD.name().equalsIgnoreCase(env)) {
            KnowledgeFaqProd prodFaq = knowledgeFaqProdAppService.queryPublishWithProd(testVO.getKnowledgeCode(), code);
            if (prodFaq != null) {
                testVO.setKnowledgeCode(prodFaq.getKnowledgeCode());
                testVO.setOn(prodFaq.getOn());
                testVO.setQuestion(prodFaq.getQuestion());
                testVO.setAnswerType(prodFaq.getAnswerType());
                testVO.setAnswer(prodFaq.getAnswer());
                testVO.setCorpus(prodFaq.getCorpus());
                testVO.setCorpusCount(prodFaq.getCorpus() != null ? prodFaq.getCorpus().size() : 0);
                testVO.setPublishStatus(prodFaq.getPublishStatus());
            }
        }
        return Result.success(testVO);
    }

    @Operation(summary = "问答表新增", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "问答表新增", groupName = "问答对管理")
    @AuditLog(businessType = "问答对管理", operType = "问答表新增", operDesc = "问答表新增", objId="#createParam.knowledgeCode")
    @PostMapping
    public Result<KnowledgeFaqVO> add(@Validated @RequestBody KnowledgeFaqCreateParam createParam) {
        if (StringUtils.isNotBlank(createParam.getAnswer())) {
            createParam.setAnswer(HtmlXssUtils.safe(createParam.getAnswer()));
        }
        KnowledgeFaqVO result = knowledgeFaqAppService.create(createParam);
        return Result.success(result);
    }

    @Operation(summary = "问答表更新", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "问答表更新", groupName = "问答对管理")
    @AuditLog(businessType = "问答对管理", operType = "问答表更新", operDesc = "问答表更新", objId="#code")
    @PutMapping(KmsApis.CODE_PATH)
    public Result<Boolean> update(@PathVariable("code") String code, @Validated @RequestBody KnowledgeFaqUpdateParam param) {
        if (StringUtils.isNotBlank(param.getAnswer())) {
            param.setAnswer(HtmlXssUtils.safe(param.getAnswer()));
        }
        return Result.success(knowledgeFaqAppService.update(code, param));
    }

    @Operation(summary = "问答表删除", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "引用调用", groupName = "问答对管理")
    @AuditLog(businessType = "问答对管理", operType = "引用调用", operDesc = "引用调用", objId="#codes.codes")
    @PostMapping(KmsApis.DELETE_API)
    public Result<Boolean> delete(@Validated @RequestBody CodeParam codes) {
        knowledgeFaqAppService.publishDeleteFlag(codes.getCodes());
        return Result.success(true);
    }

    @Operation(summary = "问答对开关", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "问答对开关", groupName = "问答对管理")
    @AuditLog(businessType = "问答对管理", operType = "问答对开关", operDesc = "问答对开关", objId="#code")
    @PutMapping(KmsApis.SWITCH_API + KmsApis.CODE_PATH)
    public Result<Boolean> switchOn(@PathVariable("code") String code, @RequestParam Boolean on) {
        Boolean result = knowledgeFaqAppService.switchOn(code, on);
        return Result.success(result);
    }

    @HideFromApiTypes(ApiType.OPENAPI)
    @Operation(summary = "问答文件导出", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "问答文件导出", groupName = "问答对管理")
    @AuditLog(businessType = "问答对管理", operType = "问答文件导出", operDesc = "问答文件导出", objId="#knowledgeCode")
    @GetMapping(KmsApis.EXPORT + KmsApis.KNOWLEDGE_CODE_PATH)
    public void export(@PathVariable("knowledgeCode") String knowledgeCode, HttpServletResponse response) {
        knowledgeFaqAppService.export(knowledgeCode, response);
    }



}


