package com.chinatelecom.gs.engine.robot.sdk.v2.spi.plugin;

import com.chinatelecom.gs.engine.robot.sdk.dto.Result;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.plugin.request.ExecuteInternalPluginApiRequest;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.plugin.response.PluginInfoResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = "gs-engine-robot", url = "${gs.kmsRpcConfig.gsUrl:}", contextId = "pluginInfoRpcApi", path = "/ais/plugin/rpc/plugin")
public interface PluginInfoRpcApi {

    @GetMapping("/queryPluginApiInfo")
    Result<PluginInfoResponse> queryPluginApiInfo(@RequestParam(value = "apiId") String apiId);

    @Operation(summary = "执行官方内部插件", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "插件管理")})})
    @PostMapping("/internal/executeApi")
    Result<Object> executeInternalApi(@RequestBody ExecuteInternalPluginApiRequest request);

    @GetMapping("/queryAllTenantId")
    Result<List<String>> queryAllTenantId();

    @GetMapping("/checkMallExist")
    Result<Boolean> checkMallExist(@RequestParam(value = "pluginId") String pluginId);

}
