CREATE TABLE `plugin_mcp_ps_meta_info` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `tenant_id` varchar(128) DEFAULT NULL COMMENT '租户id',
  `plugin_id` varchar(128) NOT NULL COMMENT '插件id',
  `ps_id` varchar(128) NOT NULL COMMENT '资源id',
  `ps_name` varchar(128) NOT NULL COMMENT '资源名称',
  `description` varchar(1000) DEFAULT NULL COMMENT '描述',
  `ps_type` int NOT NULL COMMENT '资源类型：1-prompt; 2-resource',
  `input_schema` varchar(1000) DEFAULT NULL COMMENT '输入模式',
  `quote_number` int DEFAULT '0' COMMENT '引用数',
  `enabled` int DEFAULT '1' COMMENT '是否启用',
  `debug_status` int DEFAULT NULL COMMENT '调试状态:0-调试失败, 1-调试成功',
  `version` int DEFAULT NULL COMMENT '版本号',
  `yn` bigint unsigned NOT NULL DEFAULT '0' COMMENT '删除状态：0-未删除；1-已删除',
  `create_id` varchar(128) NOT NULL DEFAULT '1' COMMENT '创建人id',
  `create_name` varchar(256) NOT NULL DEFAULT '‘’' COMMENT '创建人名称',
  `create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  `update_id` varchar(128) NOT NULL DEFAULT '1' COMMENT '更新人id',
  `update_name` varchar(256) NOT NULL DEFAULT '‘’' COMMENT '更新人名称',
  `update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
  `uri` varchar(128) DEFAULT NULL COMMENT 'resource uri',
  `mime_type` varchar(64) DEFAULT NULL COMMENT 'resource mimeType',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=165 DEFAULT CHARSET=utf8mb3 COMMENT='MCP插件资源和提示元数据表';

ALTER TABLE plugin_meta_info
ADD COLUMN `service_type` VARCHAR(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'http' COMMENT '插件service类型:http或者mcp',
ADD COLUMN `service_sub_type` VARCHAR(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'MCP插件类型:stdio或者sse',
ADD COLUMN `command` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'mcp-stdio插件调用命令',
ADD COLUMN `arguments` TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT 'mcp-stdio调用参数',
ADD COLUMN `envs` TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT 'mcp-stdio环境变量';

ALTER TABLE plugin_api_param MODIFY COLUMN `param_desc` TEXT;
