package com.chinatelecom.gs.engine.robot.sdk.rpc;

import com.chinatelecom.gs.engine.kms.sdk.constants.KmsApis;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.KnowledgeVO;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.base.KnowledgeBaseVO;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.chunk.ChunkDataVO;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.faq.KnowledgeFaqVO;
import com.chinatelecom.gs.engine.kms.sdk.vo.search.*;
import com.chinatelecom.gs.engine.robot.sdk.dto.AgentAssistKmsSearchRequest;
import com.chinatelecom.gs.engine.robot.sdk.dto.AgentAssistKmsSuggestRequest;
import com.chinatelecom.gs.engine.robot.sdk.dto.AgentKnowledgeFaqRequest;
import com.chinatelecom.gs.engine.robot.sdk.dto.AgentPageChunksParam;
import com.chinatelelcom.gs.engine.sdk.common.Page;
import com.chinatelelcom.gs.engine.sdk.common.PageImpl;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 */
@FeignClient(name = "gs-engine-robot", contextId = "AgentAssistKmsSearchApi", url = "${gs.kmsRpcConfig.gsUrl:}", path = "/ais/bot/rpc")
public interface AgentKmsSearchApi {
    /**
     * 辅助知识搜索接口
     * @param searchParam
     * @return
     */
    @PostMapping("/search/v1")
    SearchResp<? extends BaseItem> search(@RequestBody AgentAssistKmsSearchRequest searchParam);

    /**
     * 辅助知识搜索联想接口
     * @param searchParam
     * @return
     */
    @PostMapping("/suggest/v1")
    Result<SuggestVO> suggest(@RequestBody AgentAssistKmsSuggestRequest searchParam);

    /**
     * 辅助知识点详情查询接口
     * @param code 知识点编码
     * @return
     */
    @GetMapping(KmsApis.KNOWLEDGE + KmsApis.CODE_PATH)
    Result<KnowledgeVO> knowledgeDetail(@RequestParam("agentCode") String agentCode, @PathVariable("code") String code);

    /**
     * 辅助搜索结果高亮接口
     * @param highlightParam
     * @return
     */
    @PostMapping("/search/highlight")
    Result<HighlightVO> highlight(@RequestBody HighlightParam highlightParam);

    /**
     * 辅助查询文档分片分页接口
     * @param knowledgeCode
     * @param param
     * @return
     */
    @PostMapping(KmsApis.KNOWLEDGE + KmsApis.CHUNKS + KmsApis.PAGE_API + KmsApis.KNOWLEDGE_CODE_PATH)
    Result<PageImpl<ChunkDataVO>> pageChunks(@PathVariable("knowledgeCode") String knowledgeCode, @RequestBody AgentPageChunksParam param);

    /**
     * 辅助查询问答对分页接口
     * @param param
     * @return
     */
    @PostMapping(KmsApis.PAGE_API)
    Result<Page<KnowledgeFaqVO>> pageFaq(@Validated @RequestBody AgentKnowledgeFaqRequest param);

    /**
     * 机器人订阅知识库详情接口
     * @param agentCode 机器人编码
     * @param code 知识库编码
     * @param test 环境 1测试 0线上
     * @return 知识库详情
     */
    @Operation(summary = "查询知识库详情", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @GetMapping(KmsApis.KNOWLEDGE_BASE + KmsApis.GET_API)
    Result<KnowledgeBaseVO> knowledgeBaseDetail(@RequestParam("agentCode") String agentCode, @RequestParam("code") String code, @RequestParam("test") boolean test);

    @GetMapping(KmsApis.KNOWLEDGE + KmsApis.FAQ + KmsApis.CODE_PATH)
    Result<KnowledgeFaqVO> getFaq(@PathVariable("code") String code, @RequestParam(value = "agentCode") String agentCode,
                               @RequestParam(value = "env", required = false, defaultValue = "TEST") String env);
}
