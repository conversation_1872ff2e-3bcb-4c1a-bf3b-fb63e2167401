
drop table gs_base_config;

CREATE TABLE `gs_base_config`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `yn`          bigint unsigned NOT NULL DEFAULT '0' COMMENT '删除标记,0:未删除,其他表示已删除',
    `code`        varchar(255) NOT NULL COMMENT '业务配置编码',
    `name`        varchar(64)  NOT NULL COMMENT '名称',
    `dimension`   varchar(255) NOT NULL COMMENT '配置维度:系统维度、租户维度、应用维度、业务维度',
    `config_type`  varchar(255) NOT NULL COMMENT '配置类型',
    `business_no` varchar(255) NOT NULL COMMENT '业务场景配置唯一标识',
    `config_data`  text COMMENT '配置json格式value值',
    `obj_class`   varchar(1024)         DEFAULT NULL COMMENT '配置json的class对象',
    `description` varchar(255) NOT NULL COMMENT '业务配置场景说明',
    `create_id`   varchar(255) NOT NULL DEFAULT '0' COMMENT '创建用户ID',
    `create_name` varchar(255) NOT NULL DEFAULT '未知用户' COMMENT '创建用户名',
    `create_time` datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_id`   varchar(255) NOT NULL DEFAULT '0' COMMENT '最后修改用户ID',
    `update_name` varchar(255) NOT NULL DEFAULT '未知用户' COMMENT '最后修改用户名',
    `update_time` datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `idx_business_no_key_scene` (`business_no`,`config_type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='公共配置信息表';


