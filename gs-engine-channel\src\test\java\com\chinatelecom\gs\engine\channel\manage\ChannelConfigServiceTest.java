package com.chinatelecom.gs.engine.channel.manage;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinatelecom.gs.engine.channel.common.enums.ChannelTypeEnum;
import com.chinatelecom.gs.engine.channel.dao.po.ChannelConfigPO;
import com.chinatelecom.gs.engine.channel.dao.repository.ChannelConfigRepository;
import com.chinatelecom.gs.engine.channel.service.dto.ChannelInfoDTO;
import com.chinatelecom.gs.engine.robot.sdk.config.enums.ApiSecretType;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

public class ChannelConfigServiceTest {

    @InjectMocks
    private ChannelConfigService channelConfigService;

    @Mock
    private ChannelConfigRepository channelConfigRepository;

    @Mock
    private ChannelSecretManagerService channelSecretManagerService;

    @Mock
    private WebLinkConfigService webLinkConfigService;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    // 测试 getChannelConfigValue 成功情况
    @Test
    public void testGetChannelConfigValue_Success() {
        String channelId = "1";
        String configKey = "key1";
        String expectedValue = "value1";

        ChannelConfigPO mockPo = new ChannelConfigPO();
        mockPo.setConfigValue(expectedValue);

        when(channelConfigRepository.getOne(any(LambdaQueryWrapper.class))).thenReturn(mockPo);

        String result = channelConfigService.getChannelConfigValue(channelId, configKey);

        assertEquals(expectedValue, result);
        verify(channelConfigRepository, times(1)).getOne(any(LambdaQueryWrapper.class));
    }

    // 测试 getChannelConfigValue 失败（无数据）
    @Test
    public void testGetChannelConfigValue_NoData() {
        when(channelConfigRepository.getOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        assertThrows(NullPointerException.class, () -> {
            channelConfigService.getChannelConfigValue("1", "key1");
        });
    }

    // 测试 isConfigExist_QywxApp_ReturnsTrue
    @Test
    public void testIsConfigExist_QywxApp_ReturnsTrue() {
        ChannelInfoDTO dto = new ChannelInfoDTO();
        dto.setChannelId("1");
        dto.setChannelType(ChannelTypeEnum.QYWX_APP);

        when(channelConfigRepository.list(any(LambdaQueryWrapper.class))).thenReturn(Lists.newArrayList(new ChannelConfigPO()));

        boolean result = channelConfigService.isConfigExist(dto);
        assertTrue(result);
    }

    // 测试 isConfigExist_Api_ReturnsFalse
    @Test
    public void testIsConfigExist_Api_ReturnsFalse() {
        ChannelInfoDTO dto = new ChannelInfoDTO();
        dto.setAgentCode("agent1");
        dto.setChannelType(ChannelTypeEnum.API);

        when(channelSecretManagerService.isSecretExist("agent1", ApiSecretType.API)).thenReturn(false);

        boolean result = channelConfigService.isConfigExist(dto);
        assertFalse(result);
    }

    // 测试 isConfigExist_InvalidType_ThrowsException
    @Test
    public void testIsConfigExist_InvalidType_ThrowsException() {
        ChannelInfoDTO dto = new ChannelInfoDTO();
        dto.setChannelType(ChannelTypeEnum.TEST_WINDOW); // 设置为 null，而不是整个 dto 为 null

        BizException exception = assertThrows(BizException.class, () -> {
            channelConfigService.isConfigExist(dto);
        });

        assertEquals("A0054", exception.getCode());
        assertEquals("渠道种类不存在", exception.getMessage());
    }

    @Test
    public void testIsConfigExist_InvalidType_ThrowsExceptionNull() {
        ChannelInfoDTO dto = new ChannelInfoDTO();
        dto.setChannelType(null); // 设置为 null，而不是整个 dto 为 null

        BizException exception = assertThrows(BizException.class, () -> {
            channelConfigService.isConfigExist(dto);
        });

        assertEquals("A0054", exception.getCode());
        assertEquals("渠道种类不存在", exception.getMessage());
    }

    // 测试 getConfigValueWithId_Success
    @Test
    public void testGetConfigValueWithId_Success() {
        String id = "1";
        String expectedValue = "value1";

        ChannelConfigPO mockPo = new ChannelConfigPO();
        mockPo.setConfigValue(expectedValue);

        when(channelConfigRepository.getById(id)).thenReturn(mockPo);

        String result = channelConfigService.getConfigValueWithId(id);
        assertEquals(expectedValue, result);
    }

    // 测试 getConfigValueWithId_NoData
    @Test
    public void testGetConfigValueWithId_NoData() {
        when(channelConfigRepository.getById("1")).thenReturn(null);

        assertThrows(NullPointerException.class, () -> {
            channelConfigService.getConfigValueWithId("1");
        });
    }
}
