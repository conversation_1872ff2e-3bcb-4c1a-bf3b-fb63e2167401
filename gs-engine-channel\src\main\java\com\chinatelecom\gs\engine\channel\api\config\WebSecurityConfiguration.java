//package com.chinatelecom.csbotplatform.channel.api.config;
//
//import org.springframework.context.annotation.Configuration;
//import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
//import org.springframework.security.config.annotation.web.builders.HttpSecurity;
//import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
//import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
//
//import jakarta.annotation.Resource;
//
//
///**
// * <AUTHOR>
// * @date 2024/3/25 17:43
// * @desc
// */
//@Configuration
//@EnableWebSecurity
//public class WebSecurityConfiguration extends WebSecurityConfigurerAdapter {
//
//    @Resource
//    private CustomAuthenticationProvider customAuthenticationProvider;
//
//    @Override
//    protected void configure(HttpSecurity http) throws Exception {
//        http.authorizeRequests()
//                .antMatchers("/loginpage.html", "/login", "/error.html", "/health", "/qywx/**").permitAll()
//                .antMatchers("/manage/**").hasRole("admin")
//                .anyRequest()
//                .authenticated()
//                .and()
//                .formLogin(form -> form.loginPage("/loginpage.html").loginProcessingUrl("/login").failureForwardUrl("/error.html").defaultSuccessUrl("/doc.html"));
//        http.csrf().disable();
//    }
//
//    @Override
//    protected void configure(AuthenticationManagerBuilder auth) {
//        // 添加自定义认证逻辑
//        customAuthenticationProvider.setUsername("channelsadmin");
//        customAuthenticationProvider.setPassword("Ts@k8&b1u0d;KBe");
//        auth.authenticationProvider(customAuthenticationProvider);
//    }
//
//
//}
