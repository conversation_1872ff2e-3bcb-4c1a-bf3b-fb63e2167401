package com.chinatelecom.gs.engine.core.model.toolkit.adapter.ernie;

import com.chinatelecom.gs.engine.core.sdk.vo.llm.BaseLLMResponse;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description BaiduMessage
 * @date Create by 2023/11/16 15:39
 */
@Data
public class ErnieResponse implements BaseLLMResponse {

    private String id;

    private String object;

    private int created;

    private String result;

    private boolean is_truncated;

    private boolean need_clear_history;

    private String finish_reason;

    private Usage usage;


    @Data
    public static class Usage {
        private int prompt_tokens;
        private int completion_tokens;
        private int total_tokens;
    }

    /**
     * 获取输出内容
     *
     * @return
     */
    @Override
    public String outputContent() {
        return this.result;
    }

    /**
     * 获取输入token数
     *
     * @return
     */
    @Override
    public Integer promptTokens() {
        if(this.usage != null){
            return this.usage.prompt_tokens;
        }
        return null;
    }

    /**
     * 获取输出token数
     *
     * @return
     */
    @Override
    public Integer completionTokens() {
        if(this.usage != null){
            return this.usage.completion_tokens;
        }
        return null;
    }
}