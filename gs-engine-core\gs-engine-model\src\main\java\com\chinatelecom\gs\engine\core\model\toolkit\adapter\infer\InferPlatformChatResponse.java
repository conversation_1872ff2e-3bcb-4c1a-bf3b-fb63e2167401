package com.chinatelecom.gs.engine.core.model.toolkit.adapter.infer;

import com.chinatelecom.gs.engine.core.sdk.vo.llm.BaseLLMResponse;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

@Data
public class InferPlatformChatResponse implements BaseLLMResponse {

    protected String model;

    protected String object;

    protected Integer created;

    protected List<Choice> choices;

    @Data
    public static class Choice implements Serializable{
        private Integer index;

        private String finish_reason;

        private InferPlatformMessage message;

        private InferPlatformMessage delta;
    }

    @Data
    public static class Usage implements Serializable {
        private Integer prompt_tokens;
        private Integer total_tokens;
        private Integer completion_tokens;
    }

    /**
     * 获取输出内容
     *
     * @return
     */
    @Override
    public String outputContent() {
        StringBuilder outputBuilder = new StringBuilder();
        if(CollectionUtils.isNotEmpty(choices)){
            for(Choice choice:choices){
                if(Objects.nonNull(choice.getMessage())){
                    outputBuilder.append(choice.getMessage().getContent());
                }
                if(Objects.nonNull(choice.getDelta())){
                    outputBuilder.append(choice.getDelta().getContent());
                }
            }
        }
        return outputBuilder.toString();
    }
}
