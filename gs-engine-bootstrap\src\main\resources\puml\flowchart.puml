@startuml 鉴权流程
skinparam handwritten false
skinparam DefaultFontName Microsoft YaHei
skinparam DefaultFontSize 12
skinparam ArrowFontSize 11
skinparam ArrowThickness 1.5
skinparam ArrowColor #666666
skinparam RoundCorner 5
skinparam Shadowing false

skinparam ActivityBackgroundColor #FFFFFF
skinparam ActivityBorderColor #3498DB
skinparam ActivityBorderThickness 1

skinparam ActorBackgroundColor #FFFFFF
skinparam ActorBorderColor #3498DB

skinparam NoteBackgroundColor #FFF9C4
skinparam NoteBorderColor #FFD600

title 基于accessToken的API鉴权流程

actor "用户" as User
participant "共享页面" as Frontend
participant "API鉴权层" as AuthLayer
participant "OpenApiMessageController" as Controller
participant "WebLinkConfigService" as TokenService
participant "业务逻辑层" as BusinessLayer

note over TokenService: accessToken有效期：30分钟

== 获取API密钥和accessToken ==
User -> Frontend: 1. 输入共享码
Frontend -> Controller: 2. 调用获取API密钥接口\n/channel/openapi/message/link/apisecret/{channelId}
note right of Controller: 此接口无需鉴权
Controller -> TokenService: 3. 验证共享码并生成accessToken
TokenService --> Controller: 4. 返回API密钥和accessToken
Controller --> Frontend: 5. 返回API密钥和accessToken信息
Frontend -> Frontend: 6. 存储accessToken用于后续请求

== API调用鉴权流程 ==
alt SSE对话接口
    Frontend -> AuthLayer: 7a. 调用SSE对话接口 /channel/openapi/message/sseAuthChat
else 同步对话接口
    Frontend -> AuthLayer: 7b. 调用同步对话接口 /channel/openapi/message/syncAuthChat
else 查询机器人详情接口
    Frontend -> AuthLayer: 7c. 调用查询机器人详情 /channel/openapi/agent/queryAgent
else 文件下载接口
    Frontend -> AuthLayer: 7d. 调用文件下载接口 /channel/openapi/message/file/download
end

AuthLayer -> AuthLayer: 8. BaseCustomerFilter拦截请求
AuthLayer -> AuthLayer: 9. ApiContextInterceptor处理鉴权

alt accessToken有效
    AuthLayer -> TokenService: 10. 验证accessToken并获取数据
    TokenService --> AuthLayer: 11. 验证通过，返回用户和渠道信息
    AuthLayer -> AuthLayer: 12. 设置请求上下文信息(RequestContext)
    AuthLayer -> BusinessLayer: 13. 转发请求到业务处理层
    BusinessLayer --> Frontend: 14. 返回业务处理结果
else accessToken无效或过期
    AuthLayer --> Frontend: 15. 返回错误：AccessToken无效或已过期
    Frontend -> Frontend: 16. 提示用户重新获取accessToken
    Frontend -> Controller: 17. 重新调用获取API密钥接口
end

note right of AuthLayer: 若30分钟后accessToken过期，\n需要重新获取accessToken

@enduml