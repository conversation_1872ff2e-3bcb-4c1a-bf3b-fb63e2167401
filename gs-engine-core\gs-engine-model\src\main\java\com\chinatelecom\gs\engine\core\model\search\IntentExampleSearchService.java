package com.chinatelecom.gs.engine.core.model.search;

import com.chinatelecom.ai.search.client.AiSearchClient;
import com.chinatelecom.ai.search.query_dsl.FieldValue;
import com.chinatelecom.ai.search.query_dsl.KnnQuery;
import com.chinatelecom.ai.search.query_dsl.Query;
import com.chinatelecom.ai.search.req.SearchRequest;
import com.chinatelecom.ai.search.resp.SearchResponse;
import com.chinatelecom.ai.search.sdk.enums.SearchType;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.enums.LogStatusEnum;
import com.chinatelecom.gs.engine.common.enums.LogTypeEnum;
import com.chinatelecom.gs.engine.common.log.track.LogMessage;
import com.chinatelecom.gs.engine.common.log.track.LogMqProducer;
import com.chinatelecom.gs.engine.core.model.converter.SearchParamMapper;
import com.chinatelecom.gs.engine.core.model.entity.dto.AgentIntentConfigDTO;
import com.chinatelecom.gs.engine.core.model.respository.respository.AgentIntentConfigRepository;
import com.chinatelecom.gs.engine.core.model.respository.respository.AgentIntentExampleRepository;
import com.chinatelecom.gs.engine.core.model.respository.respository.AgentIntentManagerRepository;
import com.chinatelecom.gs.engine.core.sdk.enums.SearchStrategyEnum;
import com.chinatelecom.gs.engine.core.sdk.vo.IntentIndexData;
import com.chinatelecom.gs.engine.core.sdk.vo.IntentSearchVO;
import com.chinatelecom.gs.engine.sdk.SdkSearchResponse;
import com.chinatelecom.gs.workflow.core.workflow.core.utils.LogUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 意图例句搜索服务
 */
@Service
@Slf4j
public class IntentExampleSearchService {

    @Resource
    @Lazy
    private AiSearchClient aiSearchClient;

    @Resource
    private AgentIntentManagerRepository intentManagerRepository;

    @Resource
    private AgentIntentExampleRepository intentExampleRepository;

    @Resource
    private IntentIndexBuild intentIndexBuild;

    @Resource
    private AgentIntentConfigRepository configRepository;

    @Resource
    private LogMqProducer logMqProducer;

    public List<SdkSearchResponse.SdkRetrieveItem<IntentIndexData>> aiToSdkRetrieveItems(List<SearchResponse.RetrieveItem<IntentIndexData>> aiCollectItems) {
        return SearchParamMapper.INSTANCE.aiToSdkRetrieveItems(aiCollectItems);
    }


    /**
     * 搜索意图例句
     */
    public IntentSearchVO<IntentIndexData> search(String query, List<String> codes, String appCode) {
        RequestContext.setAppCode(appCode);
        IntentSearchVO<IntentIndexData> intentResponse = new IntentSearchVO<>();
        AgentIntentConfigDTO config = configRepository.selectAllOrDefault(RequestContext.getAppCode());
        SearchStrategyEnum searchStrategy = config.getSearchStrategy();

        SearchRequest searchRequest = null;
        SearchResponse<IntentIndexData> resp = null;
        LocalDateTime startTime = LocalDateTime.now();
        LogStatusEnum statusEnum = LogStatusEnum.SUCCESS;
        try {
            // 构建搜索请求
            searchRequest = buildSearchRequest(query, searchStrategy);

            // 添加全局过滤条件
            List<Query> globalFilters = buildPageChunkGlobalFilters(codes);
            searchRequest.setGlobalFilters(globalFilters);

            // 执行搜索
            resp = aiSearchClient.online().search(searchRequest, IntentIndexData.class);

            if(Objects.nonNull(resp) && Objects.nonNull(resp.getData())){
                SearchResponse.SearchResult<IntentIndexData> data = resp.getData();
                logItemsFilter(data.getItems());
                intentResponse.setTook(data.getTook());
                intentResponse.setTotal(data.getTotal());
                intentResponse.setItems(aiToSdkRetrieveItems(data.getItems()));
            }

        } catch (Exception e) {
            statusEnum = LogStatusEnum.FAILED;
            log.error("意图例句搜索失败，query: {}, codes: {}", query, codes, e);
        }finally {
            LogMessage logMessage = LogUtils.buildCommonLog(LogTypeEnum.ES_SEARCH, searchRequest != null ? searchRequest.toString() : "", resp, statusEnum, startTime, LocalDateTime.now(), "");
            logMessage.setName("意图ES检索");
            logMqProducer.sendLogMessage(logMessage);
        }
        return intentResponse;
    }

    /**
     * 记录前三条搜索结果的日志
     */
    protected static void logItemsFilter(List<SearchResponse.RetrieveItem<IntentIndexData>> items) {
        //打印前三条的得分和内容
        if (CollectionUtils.isNotEmpty(items)) {
            StringBuilder sb = new StringBuilder();
            int count = Math.min(3, items.size());
            for (int i = 0; i < count; i++) {
                SearchResponse.RetrieveItem<IntentIndexData> item = items.get(i);
                sb.append("score:").append(item.getScore())
                        .append(" title:").append(item.getSource().getTitle())
                        .append("\n"); // 构建日志信息
            }
            log.info("Top {} 召回数据信息:\n{}", count, sb);
        }
    }

    private List<Query> buildPageChunkGlobalFilters(List<String> codes) {
        List<Query> shouldList = Lists.newArrayList();

        if (CollectionUtils.isNotEmpty(codes)) {
            Query codeQuery = buildQuery(codes, IntentIndexBuild.INTENT_CODE);
            shouldList.add(codeQuery);
        }

        List<Query> mustList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(shouldList)) {
            Query paramFilter = Query.of(q -> q.bool(b -> b.should(shouldList)));
            mustList.add(paramFilter);
        }
        mustList.add(term(IntentIndexBuild.INTENT_ON, true));

        return mustList;
    }

    protected Query buildQuery(List<String> filterCodes, String fieldName) {
        List<FieldValue> values = filterCodes.stream().map(FieldValue::of).collect(Collectors.toList());
        return Query.of(q -> q.terms(t -> t.field(fieldName).terms(f -> f.value(values))));
    }

    protected Query term(String field, boolean value) {
        return Query.of(q -> q.term(t -> t.field(field).value(value)));
    }

    /**
     * 构建搜索请求
     *
     * @param query          搜索关键词
     * @param searchStrategy 搜索策略
     * @return SearchRequest对象
     */
    private SearchRequest buildSearchRequest(String query, SearchStrategyEnum searchStrategy) {
        // 构建基础查询
        Query contentQuery = Query.of(q -> q.match(m -> m
                .field("content")
                .query(query)
        ));

        Query boolQuery = Query.of(q -> q.bool(b -> b
                .must(Lists.newArrayList(
                        Query.of(subQ -> subQ.bool(subB -> subB
                                .should(Lists.newArrayList(contentQuery))
                        ))
                ))
        ));

        SearchRequest.SearchRequestBuilder builder = SearchRequest.builder()
                .instanceName(intentIndexBuild.indexName())
                .query(boolQuery)
                .type(SearchType.VECTOR)  // 默认使用向量搜索
                .page(1)
                .size(50)
                .knnMinScore(0.65)
                .firstRankExpression("atan_normalized_bm25(0.01)*0.5+knn_score()*0.5");

        // 根据搜索策略配置KNN
        KnnQuery knnQuery = KnnQuery.of(k -> k
                .field("contentVector") // 字段名。可以指定联合索引，例如"f3#idx_vector"，不指定则自动找到对应的向量索引
                .query(query)
                .numCandidates(1000) //候选集大小
                .k(100));

        builder.knn(knnQuery);

        SearchRequest searchRequest = builder.build();

        switch (searchStrategy) {
            case SEMANTICS:
                searchRequest.setQuery(Query.of(q -> q.matchNone(m -> m)));
                break;
            case KEYWORD:
                searchRequest.setKnn(null);
                break;
            default:
        }

        return searchRequest;
    }
} 