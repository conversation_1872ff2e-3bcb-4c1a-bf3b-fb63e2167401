package com.chinatelecom.gs.engine.core.corekit.controller.web;

import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.common.auth.KsMenuConfig;
import com.chinatelecom.gs.engine.common.auth.MenuConfig;
import com.chinatelecom.gs.engine.common.auth.PermissionTag;
import com.chinatelecom.gs.engine.common.constants.Apis;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.core.corekit.domain.request.SensitiveBasePageRequest;
import com.chinatelecom.gs.engine.core.corekit.domain.request.SensitiveBaseRequest;
import com.chinatelecom.gs.engine.core.corekit.domain.response.SensitiveBaseResponse;
import com.chinatelecom.gs.engine.core.corekit.service.SensitiveBaseService;
import com.chinatelelcom.gs.engine.sdk.common.Page;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.constraints.NotEmpty;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;


@Slf4j
@Tag(name = "敏感词库管理")
@RestController
@RequestMapping(Apis.BASE_PREFIX + Apis.WEB_API + "/sensitive_base")
public class SensitiveBaseController {

    @Resource
    private SensitiveBaseService sensitiveBaseService;

    @PermissionTag(code = {MenuConfig.SENSITIVE, KsMenuConfig.SENSITIVE})
    @PostMapping("/save")
    @Operation(summary = "敏感词库新增/编辑")
    @PlatformRestApi(name = "敏感词库新增/编辑", groupName = "敏感词库管理")
    @AuditLog(businessType = "敏感词库管理", operType = "敏感词库新增/编辑", operDesc = "敏感词库新增/编辑", objId="#request.sensitiveBase")
    public Result<Boolean> save(@Valid @RequestBody SensitiveBaseRequest request) {
        return Result.success(sensitiveBaseService.saveSensitiveBase(request));
    }

    @PermissionTag(code = {MenuConfig.SENSITIVE, KsMenuConfig.SENSITIVE})
    @PostMapping("/delete")
    @Operation(summary = "敏感词库删除")
    @PlatformRestApi(name = "敏感词库删除", groupName = "敏感词库管理")
    @AuditLog(businessType = "敏感词库管理", operType = "敏感词库删除", operDesc = "敏感词库删除", objId="#request.sensitiveBase")
    public Result<Boolean> delete(@Valid @RequestBody @NotEmpty(message = "Id不能为空") List<String> codes) {
        return Result.success(sensitiveBaseService.delete(codes));
    }

    @PermissionTag(code = {MenuConfig.SENSITIVE, KsMenuConfig.SENSITIVE})
    @GetMapping("/page")
    @Operation(summary = "敏感词库分页查询")
    @PlatformRestApi(name = "敏感词库分页查询", groupName = "敏感词库管理")
    @AuditLog(businessType = "敏感词库管理", operType = "敏感词库分页查询", operDesc = "敏感词库分页查询", objId="null")
    public Result<Page<SensitiveBaseResponse>> page(SensitiveBasePageRequest request){
        return Result.success(sensitiveBaseService.pageQuery(request));
    }
}
