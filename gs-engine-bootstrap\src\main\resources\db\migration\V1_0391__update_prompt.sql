update prompt_template set content='"""
### Task
根据主题，对大纲内容进行扩展，生成一份详细的Markdown格式的报告。

### Instructions
- **内容要求**：严格按照给定的大纲格式输出内容，报告应包括每个部分的详细描述，语言应专业、清晰，适合目标读者。请确保逻辑严谨，结构清晰，内容要全面完整。
- **层级结构**：
    1. 第一层级下的内容为概括性总结，呈现主要观点或结论，确保信息简明扼要。
    2. 第二层级的内容为详细的分析，提供具体的数据、案例或理论支持，深入探讨主题的各个方面。
    3. 第三层级的内容为更细致的说明或补充，进一步解释第二层级中的内容，提供更多的细节或例子，以保证报告内容全面完整。
- **格式要求**：
    1. 一级标题用例如1，2，3...等序号标明，标题以#开始，如#1。
    2. 二级标题用例如1.1，1.2...等序号标明，标题以##开始，如##1.1。
    3. 三级标题用例如1.1.1，1.1.2...等序号标明，标题以以###开始,如###1.1.1。
    4. 注意，不要输出大纲以外的标题,输入的大纲是一级标题开始，报告内容也严格按照大纲从一级标题开始，整体输出内容不要使用markdown的代码块包裹。


具体格式参考如下：
#1 一级标题
    报告内容...
##1.1 二级标题
    报告内容...
###1.1.1 三级标题
    报告内容...
##1.2 二级标题
    报告内容...
###1.2.1 三级标题
    报告内容...
#2 二级标题
    报告内容...
##2.1 二级标题
    报告内容...
###2.1.1 三级标题
    报告内容...


### Question
输入的大纲为：
${content}
根据指令要求和大纲，生成一份详细的报告
"""'
where `code`='CONTENT' AND app_code="0" limit 1;
