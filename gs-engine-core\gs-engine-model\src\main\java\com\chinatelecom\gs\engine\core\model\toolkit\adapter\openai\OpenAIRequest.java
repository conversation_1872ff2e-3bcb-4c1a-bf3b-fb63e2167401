package com.chinatelecom.gs.engine.core.model.toolkit.adapter.openai;

import com.chinatelecom.gs.engine.core.sdk.vo.llm.BaseLLMRequest;
import com.chinatelecom.gs.engine.core.sdk.vo.llm.LLMMessage;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

@Data
public class OpenAIRequest implements BaseLLMRequest {
    private double temperature =0.7;

    private double top_p =1;

    private boolean stream;

    private String model;

    private List<LLMMessage> messages;

    /**
     * 获取输入内容
     *
     * @return
     */
    @Override
    public String inputContent() {
        StringBuilder inputBuilder = new StringBuilder();
        if(CollectionUtils.isNotEmpty(messages)){
            for(LLMMessage message: messages){
                inputBuilder.append(message.getContent());
            }
        }
        return inputBuilder.toString();
    }
}
