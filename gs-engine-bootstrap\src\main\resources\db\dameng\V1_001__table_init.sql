CREATE TABLE "RECENT"
(
    "ID"   BIGINT IDENTITY(1,1) NOT NULL,
    "CODE" VARCHAR(64 char
) NOT NULL,
 "APP_CODE" VARCHAR(64 char) NOT NULL,
 "ACTION_TYPE" VARCHAR(20 char) NOT NULL,
 "TARGET_CODE" DECIMAL(20,0) NOT NULL,
 "TARGET_TYPE" VARCHAR(20 char) NOT NULL,
 "YN" DECIMAL(20,0) DEFAULT 0
 NOT NULL,
 "CREATE_ID" VARCHAR(255 char) DEFAULT '0'
 NOT NULL,
 "CREATE_NAME" VARCHAR(255 char) DEFAULT '未知用户'
 NOT NULL,
 "CREATE_TIME" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
 NOT NULL,
 "UPDATE_ID" VARCHAR(255 char) DEFAULT '0'
 NOT NULL,
 "UPDATE_NAME" VARCHAR(255 char) DEFAULT '未知用户'
 NOT NULL,
 "UPDATE_TIME" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
 NOT NULL
);
CREATE TABLE "ROBOT_AI_INTENT"
(
    "ID"        BIGINT IDENTITY(1,1) NOT NULL,
    "TENANT_ID" VARCHAR(128 char
) DEFAULT ''
 NULL,
 "APP_ID" VARCHAR(255 char) NULL,
 "MODEL_CODE" VARCHAR(255 char) NOT NULL,
 "INTENT_CODE" VARCHAR(255 char) NOT NULL,
 "INTENT_NAME" VARCHAR(2048 char) NOT NULL,
 "INTENT_ID" VARCHAR(255 char) NULL,
 "YN" DECIMAL(20,0) DEFAULT 0
 NOT NULL,
 "CREATE_ID" VARCHAR(100 char) DEFAULT '1'
 NOT NULL,
 "CREATE_NAME" VARCHAR(255 char) NOT NULL,
 "CREATE_TIME" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
 NOT NULL,
 "UPDATE_ID" VARCHAR(100 char) DEFAULT '1'
 NOT NULL,
 "UPDATE_NAME" VARCHAR(255 char) NOT NULL,
 "UPDATE_TIME" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
 NOT NULL
);
CREATE TABLE "ROBOT_AI_MODEL"
(
    "ID"        BIGINT IDENTITY(10,1) NOT NULL,
    "TENANT_ID" VARCHAR(128 char
) DEFAULT ''
 NULL,
 "APP_ID" VARCHAR(255 char) NULL,
 "MODEL_CODE" VARCHAR(255 char) NOT NULL,
 "MODEL_NAME" VARCHAR(255 char) NOT NULL,
 "MODEL_TYPE" VARCHAR(255 char) NOT NULL,
 "MODEL_STATE" VARCHAR(255 char) NULL,
 "DATA_FORMAT" VARCHAR(255 char) NULL,
 "DATA_DIMENSION" INT NULL,
 "API_KEY" VARCHAR(255 char) NULL,
 "INTENT_NUMBER" INT DEFAULT 0
 NOT NULL,
 "DESCRIPTION" VARCHAR(255 char) NULL,
 "EXTERNAL_MODEL_ID" VARCHAR(255 char) NULL,
 "EXTERNAL_MODEL_URL" VARCHAR(1024 char) NULL,
 "EXTERNAL_MODEL_CONFIG" VARCHAR(10000 char) NULL,
 "GLOBAL_FLAG" TINYINT DEFAULT 0
 NOT NULL,
 "MODEL_CONFIG" TEXT NULL,
 "YN" DECIMAL(20,0) DEFAULT 0
 NOT NULL,
 "CREATE_ID" VARCHAR(100 char) DEFAULT '1'
 NOT NULL,
 "CREATE_NAME" VARCHAR(255 char) NOT NULL,
 "CREATE_TIME" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
 NOT NULL,
 "UPDATE_ID" VARCHAR(100 char) DEFAULT '1'
 NOT NULL,
 "UPDATE_NAME" VARCHAR(255 char) NOT NULL,
 "UPDATE_TIME" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
 NOT NULL,
 "DISTINGUISH_MODEL_CONFIG" TINYINT DEFAULT 0
 NOT NULL,
 "MODEL_SECRET" VARCHAR(255 char) NULL,
 "MODEL_PROVIDER" VARCHAR(255 char) NULL,
 "THRESHOLD" DOUBLE NULL,
 "IS_DEFAULT" VARCHAR(100 char) DEFAULT '0'
 NULL,
 "MODEL_CALL_NAME" VARCHAR(255 char) DEFAULT ''
 NOT NULL
);
CREATE TABLE "SESSION_INFO"
(
    "ID"        BIGINT IDENTITY(1,1) NOT NULL,
    "TENANT_ID" VARCHAR(128 char
) DEFAULT ''
 NULL,
 "APP_ID" VARCHAR(128 char) DEFAULT ''
 NULL,
 "USER_ID" VARCHAR(128 char) DEFAULT ''
 NULL,
 "AGENT_CODE" VARCHAR(255 char) NULL,
 "SESSION_ID" VARCHAR(255 char) NULL,
 "YN" DECIMAL(20,0) DEFAULT 0
 NOT NULL,
 "CREATE_ID" VARCHAR(100 char) DEFAULT '1'
 NOT NULL,
 "CREATE_NAME" VARCHAR(255 char) NOT NULL,
 "CREATE_TIME" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
 NOT NULL,
 "UPDATE_ID" VARCHAR(100 char) DEFAULT '1'
 NOT NULL,
 "UPDATE_NAME" VARCHAR(255 char) NOT NULL,
 "UPDATE_TIME" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
 NOT NULL,
 "SESSION_NAME" VARCHAR(256 char) NULL,
 "IS_TEST" INT DEFAULT 0
 NULL,
 "ACTION_SCOPE" VARCHAR(255 char) NULL,
 "ACTION_SCOPE_ENCODE" VARCHAR(255 char) NULL
);
CREATE TABLE "TAG_RELATION"
(
    "ID"   BIGINT IDENTITY(1,1) NOT NULL,
    "YN"   DECIMAL(20, 0) DEFAULT 0
        NOT NULL,
    "CODE" VARCHAR(64 char
) NOT NULL,
 "APP_CODE" VARCHAR(64 char) NOT NULL,
 "TAG_CODE" VARCHAR(64 char) NOT NULL,
 "TARGET_CODE" VARCHAR(64 char) NOT NULL,
 "TARGET_TYPE" VARCHAR(20 char) NOT NULL,
 "CREATE_ID" VARCHAR(255 char) DEFAULT '0'
 NOT NULL,
 "CREATE_NAME" VARCHAR(255 char) DEFAULT '未知用户'
 NOT NULL,
 "CREATE_TIME" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
 NOT NULL,
 "UPDATE_ID" VARCHAR(255 char) DEFAULT '0'
 NOT NULL,
 "UPDATE_NAME" VARCHAR(255 char) DEFAULT '未知用户'
 NOT NULL,
 "UPDATE_TIME" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
 NOT NULL
);
CREATE TABLE "TAG_TREE_NODE"
(
    "ID"   BIGINT IDENTITY(1,1) NOT NULL,
    "YN"   DECIMAL(20, 0) DEFAULT 0
        NOT NULL,
    "CODE" VARCHAR(64 char
) NOT NULL,
 "APP_CODE" VARCHAR(64 char) NOT NULL,
 "NAME" VARCHAR(64 char) NOT NULL,
 "PARENT_CODE" VARCHAR(64 char) NOT NULL,
 "PATH" VARCHAR(255 char) NOT NULL,
 "DESCRIPTION" TEXT NULL,
 "CREATE_ID" VARCHAR(255 char) DEFAULT '0'
 NOT NULL,
 "CREATE_NAME" VARCHAR(255 char) DEFAULT '未知用户'
 NOT NULL,
 "CREATE_TIME" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
 NOT NULL,
 "UPDATE_ID" VARCHAR(255 char) DEFAULT '0'
 NOT NULL,
 "UPDATE_NAME" VARCHAR(255 char) DEFAULT '未知用户'
 NOT NULL,
 "UPDATE_TIME" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
 NOT NULL
);
CREATE TABLE "WORKFLOW_TASK"
(
    "ID"          BIGINT IDENTITY(1,1) NOT NULL,
    "WORKFLOW_ID" VARCHAR(100 char
) NOT NULL,
 "SESSION_ID" VARCHAR(100 char) NOT NULL,
 "MESSAGE_ID" VARCHAR(100 char) NOT NULL,
 "RUN_STATUS" INT DEFAULT 3
 NOT NULL,
 "YN" INT DEFAULT 0
 NOT NULL,
 "UPDATE_TIME" TIMESTAMP(0) NULL,
 "CREATE_TIME" TIMESTAMP(0) NULL,
 "CREATE_ID" VARCHAR(100 char) NULL,
 "CREATE_NAME" VARCHAR(100 char) NULL,
 "UPDATE_ID" VARCHAR(100 char) NULL,
 "UPDATE_NAME" VARCHAR(100 char) NULL,
 "APP_CODE" VARCHAR(100 char) NOT NULL
);
CREATE TABLE "WORKFLOW_TEST_LOG"
(
    "ID"          BIGINT IDENTITY(1,1) NOT NULL,
    "WORKFLOW_ID" VARCHAR(100 char
) NOT NULL,
 "NODE_ID" VARCHAR(100 char) NOT NULL,
 "NODE_NAME" VARCHAR(100 char) NOT NULL,
 "INPUT_PARAM" TEXT NULL,
 "UPDATE_TIME" TIMESTAMP(0) NOT NULL,
 "CREATE_TIME" TIMESTAMP(0) NOT NULL,
 "APP_CODE" VARCHAR(100 char) NOT NULL,
 "CREATE_ID" VARCHAR(100 char) NULL,
 "UPDATE_ID" VARCHAR(100 char) NULL,
 "CREATE_NAME" VARCHAR(100 char) NULL,
 "UPDATE_NAME" VARCHAR(100 char) NULL,
 "YN" INT NOT NULL,
 "RUN_STATUS" INT DEFAULT 0
 NOT NULL,
 "OUTPUT_PARAM" TEXT NULL,
 "BIZ_DATA" TEXT NULL,
 "COST" BIGINT NULL,
 "NODE_TYPE" VARCHAR(100 char) NULL,
 "ERROR" TEXT NULL,
 "MESSAGE_ID" VARCHAR(100 char) NULL
);
CREATE TABLE "WORKFLOW_VERSION_INFO"
(
    "ID"        BIGINT IDENTITY(1,1) NOT NULL,
    "TENANT_ID" VARCHAR(128 char
) DEFAULT ''
 NULL,
 "WORKFLOW_ID" VARCHAR(128 char) DEFAULT ''
 NOT NULL,
 "VERSION" BIGINT DEFAULT 1
 NOT NULL,
 "STATUS" VARCHAR(128 char) DEFAULT 'EDITABLE'
 NOT NULL,
 "RUNTIME_STATUS" VARCHAR(128 char) DEFAULT 'OFFLINE'
 NOT NULL,
 "VERSION_DESC" VARCHAR(1024 char) DEFAULT ''
 NOT NULL,
 "YN" INT DEFAULT 0
 NOT NULL,
 "APP_CODE" VARCHAR(100 char) DEFAULT '1'
 NOT NULL,
 "CREATE_ID" VARCHAR(100 char) DEFAULT '1'
 NOT NULL,
 "CREATE_NAME" VARCHAR(256 char) DEFAULT ''
 NOT NULL,
 "UPDATE_ID" VARCHAR(100 char) DEFAULT '1'
 NOT NULL,
 "UPDATE_NAME" VARCHAR(256 char) DEFAULT ''
 NOT NULL,
 "CREATE_TIME" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3)
 NOT NULL,
 "UPDATE_TIME" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3)
 NOT NULL
);
ALTER TABLE "RECENT"
    ADD CONSTRAINT NOT CLUSTER PRIMARY KEY ("ID");

ALTER TABLE "ROBOT_AI_INTENT"
    ADD CONSTRAINT NOT CLUSTER PRIMARY KEY ("ID");

ALTER TABLE "ROBOT_AI_MODEL"
    ADD CONSTRAINT NOT CLUSTER PRIMARY KEY ("ID");

ALTER TABLE "SESSION_INFO"
    ADD CONSTRAINT NOT CLUSTER PRIMARY KEY ("ID");

ALTER TABLE "TAG_RELATION"
    ADD CONSTRAINT NOT CLUSTER PRIMARY KEY ("ID");

ALTER TABLE "TAG_TREE_NODE"
    ADD CONSTRAINT NOT CLUSTER PRIMARY KEY ("ID");

ALTER TABLE "WORKFLOW_TASK"
    ADD CONSTRAINT NOT CLUSTER PRIMARY KEY ("ID");

ALTER TABLE "WORKFLOW_TEST_LOG"
    ADD CONSTRAINT NOT CLUSTER PRIMARY KEY ("ID");

ALTER TABLE "WORKFLOW_VERSION_INFO"
    ADD CONSTRAINT NOT CLUSTER PRIMARY KEY ("ID");

CREATE INDEX "IDX_APP_CREATE"
    ON "RECENT" ("APP_CODE", "CREATE_ID");
ALTER
INDEX "IDX_APP_CREATE" VISIBLE;

CREATE INDEX "IDX_CODE"
    ON "RECENT" ("CODE");
ALTER
INDEX "IDX_CODE" VISIBLE;

CREATE INDEX "IDX_TAG_RELATION_APP_TARGET"
    ON "TAG_RELATION" ("APP_CODE", "TARGET_CODE", "TARGET_TYPE");
ALTER
INDEX "IDX_TAG_RELATION_APP_TARGET" VISIBLE;

CREATE INDEX "IDX_TAG_RELATION_CODE"
    ON "TAG_RELATION" ("APP_CODE", "TAG_CODE");
ALTER
INDEX "IDX_TAG_RELATION_CODE" VISIBLE;

CREATE INDEX "IDX_TAG_RELATION_TARGET"
    ON "TAG_RELATION" ("YN", "TARGET_CODE", "TARGET_TYPE");
ALTER
INDEX "IDX_TAG_RELATION_TARGET" VISIBLE;

CREATE INDEX "IDX_TAG_TREE_NODE_CODE"
    ON "TAG_TREE_NODE" ("APP_CODE", "CODE");
ALTER
INDEX "IDX_TAG_TREE_NODE_CODE" VISIBLE;

CREATE INDEX "IDX_TAG_TREE_NODE_PATH"
    ON "TAG_TREE_NODE" ("APP_CODE", "PATH");
ALTER
INDEX "IDX_TAG_TREE_NODE_PATH" VISIBLE;

CREATE INDEX "WORKFLOW_TASK_INDEX"
    ON "WORKFLOW_TASK" ("WORKFLOW_ID", "APP_CODE");
ALTER
INDEX "WORKFLOW_TASK_INDEX" VISIBLE;

CREATE INDEX "WORKFLOW_TEST_LOG_INDEX"
    ON "WORKFLOW_TEST_LOG" ("WORKFLOW_ID", "MESSAGE_ID");
ALTER
INDEX "WORKFLOW_TEST_LOG_INDEX" VISIBLE;

ALTER TABLE "ROBOT_AI_MODEL"
    ADD CONSTRAINT "IDX_MODEL_CODE" UNIQUE ("MODEL_CODE");

ALTER TABLE "WORKFLOW_VERSION_INFO"
    ADD CONSTRAINT "UK_WORKFLOW_VERSION" UNIQUE ("WORKFLOW_ID", "VERSION", "YN");

COMMENT
ON TABLE "RECENT" IS '最近操作表';

COMMENT
ON COLUMN "RECENT"."ACTION_TYPE" IS '操作类型';

COMMENT
ON COLUMN "RECENT"."APP_CODE" IS '应用Code';

COMMENT
ON COLUMN "RECENT"."CODE" IS 'Code';

COMMENT
ON COLUMN "RECENT"."CREATE_ID" IS '创建用户ID';

COMMENT
ON COLUMN "RECENT"."CREATE_NAME" IS '创建用户名';

COMMENT
ON COLUMN "RECENT"."CREATE_TIME" IS '创建时间';

COMMENT
ON COLUMN "RECENT"."ID" IS '主键';

COMMENT
ON COLUMN "RECENT"."TARGET_CODE" IS '关联知识或知识库ID';

COMMENT
ON COLUMN "RECENT"."TARGET_TYPE" IS '关联类型';

COMMENT
ON COLUMN "RECENT"."UPDATE_ID" IS '最后修改用户ID';

COMMENT
ON COLUMN "RECENT"."UPDATE_NAME" IS '最后修改用户名';

COMMENT
ON COLUMN "RECENT"."UPDATE_TIME" IS '更新时间';

COMMENT
ON COLUMN "RECENT"."YN" IS '删除标记,0:未删除,其他表示已删除';

COMMENT
ON TABLE "ROBOT_AI_INTENT" IS '算法模型意图表';

COMMENT
ON COLUMN "ROBOT_AI_INTENT"."APP_ID" IS '应用id';

COMMENT
ON COLUMN "ROBOT_AI_INTENT"."CREATE_ID" IS '创建用户 ID';

COMMENT
ON COLUMN "ROBOT_AI_INTENT"."CREATE_NAME" IS '创建用户名称';

COMMENT
ON COLUMN "ROBOT_AI_INTENT"."CREATE_TIME" IS '创建用户时间';

COMMENT
ON COLUMN "ROBOT_AI_INTENT"."ID" IS '主键 ID';

COMMENT
ON COLUMN "ROBOT_AI_INTENT"."INTENT_CODE" IS '意图编码';

COMMENT
ON COLUMN "ROBOT_AI_INTENT"."INTENT_ID" IS '意图 ID(全局唯一)';

COMMENT
ON COLUMN "ROBOT_AI_INTENT"."INTENT_NAME" IS '意图名称';

COMMENT
ON COLUMN "ROBOT_AI_INTENT"."MODEL_CODE" IS '模型编码';

COMMENT
ON COLUMN "ROBOT_AI_INTENT"."TENANT_ID" IS '租户 ID';

COMMENT
ON COLUMN "ROBOT_AI_INTENT"."UPDATE_ID" IS '最后修改用户 ID';

COMMENT
ON COLUMN "ROBOT_AI_INTENT"."UPDATE_NAME" IS '最后修改用户名称';

COMMENT
ON COLUMN "ROBOT_AI_INTENT"."UPDATE_TIME" IS '更新时间';

COMMENT
ON COLUMN "ROBOT_AI_INTENT"."YN" IS '删除标记，0-未删除，其他表示已删除';

COMMENT
ON TABLE "ROBOT_AI_MODEL" IS '算法模型表';

COMMENT
ON COLUMN "ROBOT_AI_MODEL"."API_KEY" IS 'API 密钥';

COMMENT
ON COLUMN "ROBOT_AI_MODEL"."APP_ID" IS '应用id';

COMMENT
ON COLUMN "ROBOT_AI_MODEL"."CREATE_ID" IS '创建用户 ID';

COMMENT
ON COLUMN "ROBOT_AI_MODEL"."CREATE_NAME" IS '创建用户名称';

COMMENT
ON COLUMN "ROBOT_AI_MODEL"."CREATE_TIME" IS '创建时间';

COMMENT
ON COLUMN "ROBOT_AI_MODEL"."DATA_DIMENSION" IS '数据维度';

COMMENT
ON COLUMN "ROBOT_AI_MODEL"."DATA_FORMAT" IS '数据格式';

COMMENT
ON COLUMN "ROBOT_AI_MODEL"."DESCRIPTION" IS '描述信息';

COMMENT
ON COLUMN "ROBOT_AI_MODEL"."DISTINGUISH_MODEL_CONFIG" IS '是否区分模型配置(是否区分用户请求或离线文档)，0-不区分，1-区分';

COMMENT
ON COLUMN "ROBOT_AI_MODEL"."EXTERNAL_MODEL_CONFIG" IS '模型拓展配置';

COMMENT
ON COLUMN "ROBOT_AI_MODEL"."EXTERNAL_MODEL_ID" IS '外部模型 ID';

COMMENT
ON COLUMN "ROBOT_AI_MODEL"."EXTERNAL_MODEL_URL" IS '外部模型 URL';

COMMENT
ON COLUMN "ROBOT_AI_MODEL"."GLOBAL_FLAG" IS '是否为全局/是否是预置，0-否，1-是';

COMMENT
ON COLUMN "ROBOT_AI_MODEL"."ID" IS '主键 ID';

COMMENT
ON COLUMN "ROBOT_AI_MODEL"."INTENT_NUMBER" IS '意图数量';

COMMENT
ON COLUMN "ROBOT_AI_MODEL"."IS_DEFAULT" IS '是否默认模型，0:否，1:是';

COMMENT
ON COLUMN "ROBOT_AI_MODEL"."MODEL_CALL_NAME" IS '模型调用名称';

COMMENT
ON COLUMN "ROBOT_AI_MODEL"."MODEL_CODE" IS '模型编码';

COMMENT
ON COLUMN "ROBOT_AI_MODEL"."MODEL_CONFIG" IS '模型配置';

COMMENT
ON COLUMN "ROBOT_AI_MODEL"."MODEL_NAME" IS '模型名称';

COMMENT
ON COLUMN "ROBOT_AI_MODEL"."MODEL_PROVIDER" IS '大模型提供方';

COMMENT
ON COLUMN "ROBOT_AI_MODEL"."MODEL_SECRET" IS '大模型api secret';

COMMENT
ON COLUMN "ROBOT_AI_MODEL"."MODEL_STATE" IS '模型状态';

COMMENT
ON COLUMN "ROBOT_AI_MODEL"."MODEL_TYPE" IS '模型类型';

COMMENT
ON COLUMN "ROBOT_AI_MODEL"."TENANT_ID" IS '租户 ID';

COMMENT
ON COLUMN "ROBOT_AI_MODEL"."THRESHOLD" IS '阈值';

COMMENT
ON COLUMN "ROBOT_AI_MODEL"."UPDATE_ID" IS '最后修改用户 ID';

COMMENT
ON COLUMN "ROBOT_AI_MODEL"."UPDATE_NAME" IS '足后修改用户名称';

COMMENT
ON COLUMN "ROBOT_AI_MODEL"."UPDATE_TIME" IS '更新时间';

COMMENT
ON COLUMN "ROBOT_AI_MODEL"."YN" IS '删除标记，0-未删除，其他表示已删除';

COMMENT
ON TABLE "SESSION_INFO" IS '会话信息表';

COMMENT
ON COLUMN "SESSION_INFO"."ACTION_SCOPE" IS '作用域';

COMMENT
ON COLUMN "SESSION_INFO"."ACTION_SCOPE_ENCODE" IS '作用域编码';

COMMENT
ON COLUMN "SESSION_INFO"."AGENT_CODE" IS '机器编码';

COMMENT
ON COLUMN "SESSION_INFO"."APP_ID" IS '会话关联的应用 ID';

COMMENT
ON COLUMN "SESSION_INFO"."CREATE_ID" IS '创建用户 ID';

COMMENT
ON COLUMN "SESSION_INFO"."CREATE_NAME" IS '创建用户名称';

COMMENT
ON COLUMN "SESSION_INFO"."CREATE_TIME" IS '创建用户时间';

COMMENT
ON COLUMN "SESSION_INFO"."ID" IS '主键 ID';

COMMENT
ON COLUMN "SESSION_INFO"."IS_TEST" IS '是否为测试，0: 否，1: 是';

COMMENT
ON COLUMN "SESSION_INFO"."SESSION_ID" IS '会话ID';

COMMENT
ON COLUMN "SESSION_INFO"."SESSION_NAME" IS '会话名称';

COMMENT
ON COLUMN "SESSION_INFO"."TENANT_ID" IS '租户 ID';

COMMENT
ON COLUMN "SESSION_INFO"."UPDATE_ID" IS '最后修改用户 ID';

COMMENT
ON COLUMN "SESSION_INFO"."UPDATE_NAME" IS '最后修改用户名称';

COMMENT
ON COLUMN "SESSION_INFO"."UPDATE_TIME" IS '更新时间';

COMMENT
ON COLUMN "SESSION_INFO"."USER_ID" IS '会话关联的用户 ID';

COMMENT
ON COLUMN "SESSION_INFO"."YN" IS '删除标记，0-未删除，其他表示已删除';

COMMENT
ON TABLE "TAG_RELATION" IS '标签关联表';

COMMENT
ON COLUMN "TAG_RELATION"."APP_CODE" IS '应用Code';

COMMENT
ON COLUMN "TAG_RELATION"."CODE" IS 'Code';

COMMENT
ON COLUMN "TAG_RELATION"."CREATE_ID" IS '创建用户ID';

COMMENT
ON COLUMN "TAG_RELATION"."CREATE_NAME" IS '创建用户名';

COMMENT
ON COLUMN "TAG_RELATION"."CREATE_TIME" IS '创建时间';

COMMENT
ON COLUMN "TAG_RELATION"."ID" IS '主键';

COMMENT
ON COLUMN "TAG_RELATION"."TAG_CODE" IS '标签code';

COMMENT
ON COLUMN "TAG_RELATION"."TARGET_CODE" IS '关联知识或知识库code';

COMMENT
ON COLUMN "TAG_RELATION"."TARGET_TYPE" IS '关联类型';

COMMENT
ON COLUMN "TAG_RELATION"."UPDATE_ID" IS '最后修改用户ID';

COMMENT
ON COLUMN "TAG_RELATION"."UPDATE_NAME" IS '最后修改用户名';

COMMENT
ON COLUMN "TAG_RELATION"."UPDATE_TIME" IS '更新时间';

COMMENT
ON COLUMN "TAG_RELATION"."YN" IS '删除标记,0:未删除,其他表示已删除';

COMMENT
ON TABLE "TAG_TREE_NODE" IS '标签表';

COMMENT
ON COLUMN "TAG_TREE_NODE"."APP_CODE" IS '应用Code';

COMMENT
ON COLUMN "TAG_TREE_NODE"."CODE" IS 'Code';

COMMENT
ON COLUMN "TAG_TREE_NODE"."CREATE_ID" IS '创建用户ID';

COMMENT
ON COLUMN "TAG_TREE_NODE"."CREATE_NAME" IS '创建用户名';

COMMENT
ON COLUMN "TAG_TREE_NODE"."CREATE_TIME" IS '创建时间';

COMMENT
ON COLUMN "TAG_TREE_NODE"."DESCRIPTION" IS '标签描述';

COMMENT
ON COLUMN "TAG_TREE_NODE"."ID" IS '主键';

COMMENT
ON COLUMN "TAG_TREE_NODE"."NAME" IS '名称';

COMMENT
ON COLUMN "TAG_TREE_NODE"."PARENT_CODE" IS '父标签code';

COMMENT
ON COLUMN "TAG_TREE_NODE"."PATH" IS '标签路径';

COMMENT
ON COLUMN "TAG_TREE_NODE"."UPDATE_ID" IS '最后修改用户ID';

COMMENT
ON COLUMN "TAG_TREE_NODE"."UPDATE_NAME" IS '最后修改用户名';

COMMENT
ON COLUMN "TAG_TREE_NODE"."UPDATE_TIME" IS '更新时间';

COMMENT
ON COLUMN "TAG_TREE_NODE"."YN" IS '删除标记,0:未删除,其他表示已删除';

COMMENT
ON TABLE "WORKFLOW_TASK" IS '工作流任务执行状态';

COMMENT
ON COLUMN "WORKFLOW_TASK"."MESSAGE_ID" IS '消息id';

COMMENT
ON COLUMN "WORKFLOW_TASK"."RUN_STATUS" IS '运行状态0-成功，1-失败，3-运行中';

COMMENT
ON COLUMN "WORKFLOW_TASK"."SESSION_ID" IS 'sessionId';

COMMENT
ON COLUMN "WORKFLOW_TASK"."WORKFLOW_ID" IS '工作流id';

COMMENT
ON TABLE "WORKFLOW_TEST_LOG" IS '工作流调试日志';

COMMENT
ON COLUMN "WORKFLOW_TEST_LOG"."BIZ_DATA" IS '业务参数';

COMMENT
ON COLUMN "WORKFLOW_TEST_LOG"."COST" IS '执行耗时 s';

COMMENT
ON COLUMN "WORKFLOW_TEST_LOG"."CREATE_TIME" IS '创建时间';

COMMENT
ON COLUMN "WORKFLOW_TEST_LOG"."ERROR" IS '错误信息';

COMMENT
ON COLUMN "WORKFLOW_TEST_LOG"."INPUT_PARAM" IS '参数';

COMMENT
ON COLUMN "WORKFLOW_TEST_LOG"."NODE_ID" IS '节点id';

COMMENT
ON COLUMN "WORKFLOW_TEST_LOG"."NODE_NAME" IS '节点名称';

COMMENT
ON COLUMN "WORKFLOW_TEST_LOG"."NODE_TYPE" IS '节点类型';

COMMENT
ON COLUMN "WORKFLOW_TEST_LOG"."OUTPUT_PARAM" IS '输出参数';

COMMENT
ON COLUMN "WORKFLOW_TEST_LOG"."RUN_STATUS" IS '运行状态0-成功，1-运行失败';

COMMENT
ON COLUMN "WORKFLOW_TEST_LOG"."UPDATE_TIME" IS '保存时间';

COMMENT
ON COLUMN "WORKFLOW_TEST_LOG"."WORKFLOW_ID" IS '工作流id';

CREATE TABLE "AGENT_AI_MODEL"
(
    "ID"        BIGINT IDENTITY(1,1) NOT NULL,
    "TENANT_ID" VARCHAR(128 char
) DEFAULT ''
 NULL,
 "ROBOT_CODE" VARCHAR(255 char) NULL,
 "MODEL_CODE" VARCHAR(255 char) NOT NULL,
 "MODEL_NAME" VARCHAR(255 char) NOT NULL,
 "MODEL_TYPE" VARCHAR(255 char) NOT NULL,
 "MODEL_STATE" VARCHAR(255 char) NULL,
 "DATA_FORMAT" VARCHAR(255 char) NULL,
 "DATA_DIMENSION" INT NULL,
 "API_KEY" VARCHAR(255 char) NULL,
 "INTENT_NUMBER" INT DEFAULT 0
 NOT NULL,
 "DESCRIPTION" VARCHAR(255 char) NULL,
 "EXTERNAL_MODEL_ID" VARCHAR(255 char) NULL,
 "EXTERNAL_MODEL_URL" VARCHAR(1024 char) NULL,
 "EXTERNAL_MODEL_CONFIG" VARCHAR(32767) NULL,
 "GLOBAL_FLAG" TINYINT DEFAULT 0
 NOT NULL,
 "MODEL_CONFIG" TEXT NULL,
 "YN" DECIMAL(20,0) DEFAULT 0
 NOT NULL,
 "CREATE_ID" VARCHAR(100 char) DEFAULT '1'
 NOT NULL,
 "CREATE_NAME" VARCHAR(255 char) NOT NULL,
 "CREATE_TIME" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
 NOT NULL,
 "UPDATE_ID" VARCHAR(100 char) DEFAULT '1'
 NOT NULL,
 "UPDATE_NAME" VARCHAR(255 char) NOT NULL,
 "UPDATE_TIME" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
 NOT NULL,
 "DISTINGUISH_MODEL_CONFIG" TINYINT DEFAULT 0
 NOT NULL,
 "MODEL_SECRET" VARCHAR(255 char) NULL,
 "MODEL_PROVIDER" VARCHAR(255 char) NULL,
 "THRESHOLD" DOUBLE NULL,
 "APP_CODE" VARCHAR(64 char) NULL
);
CREATE TABLE "AGENT_BASIC_INFO"
(
    "ID"         BIGINT IDENTITY(1,1) NOT NULL,
    "AGENT_CODE" VARCHAR(255 char
) NOT NULL,
 "AGENT_NAME" VARCHAR(255 char) DEFAULT ''
 NOT NULL,
 "AGENT_PICTURE" VARCHAR(255 char) DEFAULT ''
 NOT NULL,
 "KMS_APP_CODE" VARCHAR(255 char) DEFAULT ''
 NOT NULL,
 "AGENT_DESC" VARCHAR(255 char) DEFAULT ''
 NOT NULL,
 "AGENT_REPLY" TEXT NULL,
 "AGENT_TYPE" VARCHAR(255 char) DEFAULT ''
 NOT NULL,
 "TEMPLATE_CATEGORY_ID" VARCHAR(255 char) DEFAULT ''
 NOT NULL,
 "STATUS" VARCHAR(100 char) DEFAULT '0'
 NOT NULL,
 "VERSION" VARCHAR(100 char) DEFAULT '0'
 NOT NULL,
 "ENABLE_DIALOG" VARCHAR(100 char) DEFAULT '0'
 NOT NULL,
 "YN" DECIMAL(20,0) DEFAULT 0
 NOT NULL,
 "CREATE_ID" VARCHAR(100 char) NOT NULL,
 "CREATE_NAME" VARCHAR(255 char) NOT NULL,
 "CREATE_TIME" TIMESTAMP(0) NOT NULL,
 "UPDATE_ID" VARCHAR(100 char) NOT NULL,
 "UPDATE_NAME" VARCHAR(255 char) NOT NULL,
 "UPDATE_TIME" TIMESTAMP(0) NOT NULL,
 "TENANT_ID" VARCHAR(128 char) DEFAULT '1'
 NULL,
 "SCOPE" VARCHAR(100 char) DEFAULT 'UNPUBLISHED'
 NOT NULL,
 "APP_CODE" VARCHAR(64 char) NULL,
 "IS_DEFAULT" VARCHAR(100 char) DEFAULT '0'
 NULL
);
CREATE TABLE "AGENT_CONFIG"
(
    "ID"         BIGINT IDENTITY(1,1) NOT NULL,
    "AGENT_CODE" VARCHAR(256 char
) DEFAULT ''
 NOT NULL,
 "CONFIG_KEY" VARCHAR(256 char) DEFAULT ''
 NOT NULL,
 "CONFIG_VALUE" TEXT NULL,
 "TYPE" BIGINT DEFAULT 1
 NOT NULL,
 "CATEGORY" BIGINT DEFAULT 1
 NOT NULL,
 "VERSION" BIGINT DEFAULT 1
 NOT NULL,
 "YN" DECIMAL(20,0) DEFAULT 0
 NOT NULL,
 "CREATE_ID" VARCHAR(100 char) DEFAULT '1'
 NOT NULL,
 "CREATE_NAME" VARCHAR(256 char) DEFAULT ''
 NOT NULL,
 "UPDATE_ID" VARCHAR(100 char) DEFAULT '1'
 NOT NULL,
 "UPDATE_NAME" VARCHAR(256 char) DEFAULT ''
 NOT NULL,
 "CREATE_TIME" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3)
 NOT NULL,
 "UPDATE_TIME" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3)
 NOT NULL,
 "TENANT_ID" VARCHAR(128 char) DEFAULT ''
 NULL,
 "APP_CODE" VARCHAR(64 char) NULL
);
CREATE TABLE "AGENT_DEFAULT_CONFIG"
(
    "ID"         BIGINT IDENTITY(55,1) NOT NULL,
    "CONFIG_KEY" VARCHAR(256 char
) DEFAULT ''
 NOT NULL,
 "CONFIG_VALUE" TEXT NULL,
 "TYPE" BIGINT DEFAULT 1
 NOT NULL,
 "CATEGORY" BIGINT DEFAULT 1
 NOT NULL,
 "YN" DECIMAL(20,0) DEFAULT 0
 NOT NULL,
 "CREATE_ID" VARCHAR(100 char) DEFAULT '1'
 NOT NULL,
 "CREATE_NAME" VARCHAR(256 char) DEFAULT ''
 NOT NULL,
 "UPDATE_ID" VARCHAR(100 char) DEFAULT '1'
 NOT NULL,
 "UPDATE_NAME" VARCHAR(256 char) DEFAULT ''
 NOT NULL,
 "CREATE_TIME" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3)
 NOT NULL,
 "UPDATE_TIME" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3)
 NOT NULL,
 "VERSION" BIGINT DEFAULT 1
 NOT NULL,
 "TENANT_ID" VARCHAR(128 char) DEFAULT ''
 NOT NULL
);
CREATE TABLE "AGENT_DIALOG_COUNT_RECORD"
(
    "ID"        BIGINT IDENTITY(1,1) NOT NULL,
    "TENANT_ID" VARCHAR(128 char
) DEFAULT ''
 NULL,
 "AGENT_CODE" VARCHAR(128 char) DEFAULT ''
 NOT NULL,
 "CREATE_TIME" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3)
 NOT NULL,
 "UPDATE_TIME" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3)
 NOT NULL
);
CREATE TABLE "AGENT_DIALOG_RECORD"
(
    "ID"        BIGINT IDENTITY(1,1) NOT NULL,
    "TENANT_ID" VARCHAR(128 char
) DEFAULT ''
 NULL,
 "AGENT_CODE" VARCHAR(128 char) DEFAULT ''
 NOT NULL,
 "AGENT_NAME" VARCHAR(128 char) DEFAULT ''
 NOT NULL,
 "SESSION_ID" VARCHAR(128 char) DEFAULT ''
 NOT NULL,
 "CUSTOMER_ID" VARCHAR(128 char) DEFAULT ''
 NOT NULL,
 "CHANNEL_CODE" VARCHAR(128 char) DEFAULT ''
 NOT NULL,
 "CHANNEL_NAME" VARCHAR(128 char) DEFAULT ''
 NOT NULL,
 "DIALOG_START_TIME" TIMESTAMP(3) NOT NULL,
 "DIALOG_END_TIME" TIMESTAMP(3) NOT NULL,
 "DIALOG_DURATION" INT DEFAULT 0
 NOT NULL,
 "TO_HUMAN" TINYINT DEFAULT 0
 NOT NULL,
 "CUSTOMER_MESSAGE_NUM" INT DEFAULT 0
 NOT NULL,
 "AGENT_MESSAGE_NUM" INT DEFAULT 0
 NOT NULL,
 "MATCH_NUM" INT DEFAULT 0
 NOT NULL,
 "UNKNOWN_NUM" INT DEFAULT 0
 NOT NULL,
 "CREATE_TIME" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3)
 NOT NULL,
 "UPDATE_TIME" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3)
 NOT NULL,
 "YN" DECIMAL(20,0) DEFAULT 0
 NOT NULL,
 "APP_CODE" VARCHAR(64 char) NULL
);
CREATE TABLE "AGENT_INDEX_CREATE_RECORD"
(
    "ID"        BIGINT IDENTITY(1,1) NOT NULL,
    "TENANT_ID" VARCHAR(128 char
) DEFAULT ''
 NOT NULL,
 "TYPE" VARCHAR(32 char) DEFAULT ''
 NOT NULL,
 "APP_CODE" VARCHAR(128 char) DEFAULT ''
 NOT NULL,
 "INDEX_NAME" VARCHAR(128 char) DEFAULT ''
 NOT NULL,
 "STATE" BIGINT DEFAULT 1
 NOT NULL,
 "FAIL_REASON" TEXT NULL,
 "CREATE_TIME" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3)
 NOT NULL,
 "YN" DECIMAL(20,0) DEFAULT 0
 NOT NULL,
 "UPDATE_TIME" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3)
 NOT NULL
);
CREATE TABLE "AGENT_KNOWLEDGE_BASE"
(
    "ID"         BIGINT IDENTITY(1,1) NOT NULL,
    "AGENT_CODE" VARCHAR(100 char
) NOT NULL,
 "KNOWLEDGE_BASE_CODE" VARCHAR(100 char) NULL,
 "YN" DECIMAL(20,0) DEFAULT 0
 NOT NULL,
 "CREATE_ID" VARCHAR(100 char) DEFAULT '1'
 NOT NULL,
 "CREATE_NAME" VARCHAR(256 char) DEFAULT ''
 NOT NULL,
 "UPDATE_ID" VARCHAR(100 char) DEFAULT '1'
 NOT NULL,
 "UPDATE_NAME" VARCHAR(256 char) DEFAULT ''
 NOT NULL,
 "CREATE_TIME" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3)
 NOT NULL,
 "UPDATE_TIME" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3)
 NOT NULL,
 "TENANT_ID" VARCHAR(128 char) DEFAULT ''
 NOT NULL,
 "TYPE" VARCHAR(100 char) NULL,
 "APP_CODE" VARCHAR(64 char) NULL,
 "REL_TYPE" VARCHAR(255 char) DEFAULT 'BOT'
 NOT NULL,
 "VERSION" VARCHAR(100 char) DEFAULT '1'
 NULL
);
CREATE TABLE "AGENT_PLUGIN_BIND"
(
    "ID"         BIGINT IDENTITY(38,1) NOT NULL,
    "AGENT_CODE" VARCHAR(128 char
) DEFAULT ''
 NOT NULL,
 "PLUGIN_ID" VARCHAR(128 char) NOT NULL,
 "API_ID" VARCHAR(128 char) NOT NULL,
 "CREATE_ID" VARCHAR(100 char) DEFAULT '1'
 NOT NULL,
 "CREATE_NAME" VARCHAR(256 char) DEFAULT ''
 NOT NULL,
 "UPDATE_ID" VARCHAR(100 char) DEFAULT '1'
 NOT NULL,
 "UPDATE_NAME" VARCHAR(256 char) DEFAULT ''
 NOT NULL,
 "CREATE_TIME" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3)
 NOT NULL,
 "UPDATE_TIME" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3)
 NOT NULL,
 "YN" DECIMAL(20,0) DEFAULT 0
 NOT NULL,
 "PLUGIN_NAME" VARCHAR(128 char) DEFAULT ''
 NOT NULL,
 "API_NAME" VARCHAR(128 char) DEFAULT ''
 NOT NULL,
 "AGENT_VERSION" INT DEFAULT 1
 NOT NULL
);
CREATE TABLE "AGENT_QUESTION_HIT_RECORD"
(
    "ID"        BIGINT IDENTITY(1991,1) NOT NULL,
    "TENANT_ID" VARCHAR(128 char
) DEFAULT ''
 NULL,
 "AGENT_CODE" VARCHAR(128 char) DEFAULT ''
 NOT NULL,
 "KMS_CODE" VARCHAR(128 char) DEFAULT ''
 NOT NULL,
 "KNOWLEDGE_ID" VARCHAR(32 char) DEFAULT ''
 NOT NULL,
 "KNOWLEDGE_TYPE" VARCHAR(32 char) NULL,
 "TITLE" VARCHAR(256 char) DEFAULT ''
 NOT NULL,
 "CHANNEL_CODE" VARCHAR(128 char) NULL,
 "SESSION_ID" VARCHAR(128 char) NULL,
 "MESSAGE_ID" VARCHAR(128 char) NULL,
 "YN" DECIMAL(20,0) DEFAULT 0
 NOT NULL,
 "CREATE_TIME" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3)
 NOT NULL,
 "UPDATE_TIME" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3)
 NOT NULL
);
CREATE TABLE "AGENT_VERSION_INFO"
(
    "ID"        BIGINT IDENTITY(1,1) NOT NULL,
    "TENANT_ID" VARCHAR(128 char
) DEFAULT ''
 NULL,
 "AGENT_CODE" VARCHAR(128 char) DEFAULT ''
 NOT NULL,
 "VERSION" BIGINT DEFAULT 1
 NOT NULL,
 "STATUS" VARCHAR(128 char) DEFAULT 'EDITABLE'
 NOT NULL,
 "RUNTIME_STATUS" VARCHAR(128 char) DEFAULT 'OFFLINE'
 NOT NULL,
 "VERSION_DESC" VARCHAR(1024 char) DEFAULT ''
 NOT NULL,
 "MIGRATE_INDEX_STATUS" TINYINT DEFAULT 0
 NOT NULL,
 "YN" SMALLINT DEFAULT 0
 NOT NULL,
 "CREATE_ID" VARCHAR(100 char) DEFAULT '1'
 NOT NULL,
 "CREATE_NAME" VARCHAR(256 char) DEFAULT ''
 NOT NULL,
 "UPDATE_ID" VARCHAR(100 char) DEFAULT '1'
 NOT NULL,
 "UPDATE_NAME" VARCHAR(256 char) DEFAULT ''
 NOT NULL,
 "CREATE_TIME" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3)
 NOT NULL,
 "UPDATE_TIME" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3)
 NOT NULL,
 "APP_CODE" VARCHAR(64 char) NULL
);
CREATE TABLE "AGENT_WORKFLOW_BIND"
(
    "ID"         BIGINT IDENTITY(21,1) NOT NULL,
    "AGENT_CODE" VARCHAR(255 char
) NOT NULL,
 "WORKFLOW_ID" VARCHAR(255 char) NOT NULL,
 "AGENT_VERSION" INT NOT NULL,
 "YN" INT DEFAULT 0
 NOT NULL,
 "APP_CODE" VARCHAR(255 char) NOT NULL,
 "TENANT_ID" VARCHAR(255 char) NULL,
 "CREATE_ID" VARCHAR(255 char) NOT NULL,
 "CREATE_NAME" VARCHAR(255 char) NOT NULL,
 "CREATE_TIME" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
 NOT NULL,
 "UPDATE_ID" VARCHAR(255 char) NOT NULL,
 "UPDATE_NAME" VARCHAR(255 char) NOT NULL,
 "UPDATE_TIME" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
 NOT NULL,
 "WORKFLOW_CN_NAME" VARCHAR(255 char) NOT NULL
);
CREATE TABLE "APP"
(
    "ID"   BIGINT IDENTITY(1,1) NOT NULL,
    "YN"   DECIMAL(20, 0) DEFAULT 0
        NOT NULL,
    "NAME" VARCHAR(64 char
) NOT NULL,
 "CODE" VARCHAR(64 char) NOT NULL,
 "PERSONAL" BIT NOT NULL,
 "DEFAULT_KNOWLEDGE_BASE_CODE" VARCHAR(64 char) NULL,
 "DEFAULT_CATALOG_CODE" VARCHAR(64 char) NULL,
 "TENANT_ID" VARCHAR(64 char) NOT NULL,
 "BOT_CODE" VARCHAR(64 char) NULL,
 "CREATE_ID" VARCHAR(255 char) DEFAULT '0'
 NOT NULL,
 "CREATE_NAME" VARCHAR(255 char) DEFAULT '未知用户'
 NOT NULL,
 "CREATE_TIME" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
 NOT NULL,
 "UPDATE_ID" VARCHAR(255 char) DEFAULT '0'
 NOT NULL,
 "UPDATE_NAME" VARCHAR(255 char) DEFAULT '未知用户'
 NOT NULL,
 "UPDATE_TIME" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
 NOT NULL,
 "DS_BOT_CODE" VARCHAR(64 char) NULL
);
CREATE TABLE "APP_ROLE"
(
    "ID"   BIGINT IDENTITY(1,1) NOT NULL,
    "CODE" VARCHAR(64 char
) NOT NULL,
 "APP_CODE" VARCHAR(64 char) NOT NULL,
 "TENANT_ID" VARCHAR(64 char) NOT NULL,
 "ROLE_TYPE" VARCHAR(64 char) NOT NULL,
 "ROLE_ORDER" INT DEFAULT 0
 NOT NULL,
 "YN" BIT DEFAULT 0
 NOT NULL,
 "CREATE_ID" VARCHAR(255 char) DEFAULT '0'
 NOT NULL,
 "CREATE_NAME" VARCHAR(255 char) DEFAULT '未知用户'
 NOT NULL,
 "CREATE_TIME" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
 NOT NULL,
 "UPDATE_ID" VARCHAR(255 char) DEFAULT '0'
 NOT NULL,
 "UPDATE_NAME" VARCHAR(255 char) DEFAULT '未知用户'
 NOT NULL,
 "UPDATE_TIME" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
 NOT NULL
);
CREATE TABLE "ATTACHMENT"
(
    "ID"       BIGINT IDENTITY(1,1) NOT NULL,
    "YN"       DECIMAL(20, 0) DEFAULT 0
        NOT NULL,
    "APP_CODE" VARCHAR(64 char
) NOT NULL,
 "CODE" VARCHAR(255 char) NOT NULL,
 "FILE_NAME" VARCHAR(1024 char) NULL,
 "TYPE" VARCHAR(20 char) NOT NULL,
 "ATTACHABLE_TYPE" VARCHAR(24 char) NOT NULL,
 "ATTACHABLE_CODE" VARCHAR(255 char) NOT NULL,
 "FILE_KEY" VARCHAR(255 char) NULL,
 "CREATE_ID" VARCHAR(255 char) DEFAULT '0'
 NOT NULL,
 "CREATE_NAME" VARCHAR(255 char) DEFAULT '未知用户'
 NOT NULL,
 "CREATE_TIME" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
 NOT NULL,
 "UPDATE_ID" VARCHAR(255 char) DEFAULT '0'
 NOT NULL,
 "UPDATE_NAME" VARCHAR(255 char) DEFAULT '未知用户'
 NOT NULL,
 "UPDATE_TIME" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
 NOT NULL
);
CREATE TABLE "BOT_WORKFLOW"
(
    "ID"          BIGINT IDENTITY(1,1) NOT NULL,
    "WORKFLOW_ID" VARCHAR(255 char
) NOT NULL,
 "WORKFLOW_NAME" VARCHAR(255 char) NOT NULL,
 "WORKFLOW_CN_NAME" VARCHAR(255 char) NOT NULL,
 "ICON_URL" VARCHAR(255 char) NULL,
 "SCENE" VARCHAR(255 char) NULL,
 "CANVAS" TEXT NULL,
 "VERSION" INT NOT NULL,
 "DESCRIPTION" VARCHAR(255 char) NOT NULL,
 "DEBUG_STATUS" INT NOT NULL,
 "STATUS" INT NOT NULL,
 "BOT_REF_COUNT" INT NOT NULL,
 "YN" INT DEFAULT 0
 NOT NULL,
 "APP_CODE" VARCHAR(255 char) NOT NULL,
 "TENANT_ID" VARCHAR(255 char) NULL,
 "CREATE_ID" VARCHAR(255 char) NOT NULL,
 "CREATE_NAME" VARCHAR(255 char) NOT NULL,
 "CREATE_TIME" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3)
 NOT NULL,
 "UPDATE_ID" VARCHAR(255 char) NOT NULL,
 "UPDATE_NAME" VARCHAR(255 char) NOT NULL,
 "UPDATE_TIME" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3)
 NOT NULL
);
CREATE TABLE "BOT_WORKFLOW_EDGE"
(
    "ID"          BIGINT IDENTITY(1,1) NOT NULL,
    "WORKFLOW_ID" VARCHAR(255 char
) NOT NULL,
 "SOURCE_NODE_ID" VARCHAR(255 char) NOT NULL,
 "SOURCE_PORT_ID" VARCHAR(255 char) NULL,
 "TARGET_NODE_ID" VARCHAR(255 char) NOT NULL,
 "TARGET_PORT_ID" VARCHAR(255 char) NULL,
 "VERSION" INT NOT NULL,
 "YN" INT DEFAULT 0
 NOT NULL,
 "CREATE_ID" VARCHAR(255 char) NOT NULL,
 "CREATE_NAME" VARCHAR(255 char) NOT NULL,
 "CREATE_TIME" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3)
 NOT NULL,
 "UPDATE_ID" VARCHAR(255 char) NOT NULL,
 "UPDATE_NAME" VARCHAR(255 char) NOT NULL,
 "UPDATE_TIME" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3)
 NOT NULL,
 "APP_CODE" VARCHAR(255 char) NOT NULL
);
CREATE TABLE "BOT_WORKFLOW_NODE"
(
    "ID"          BIGINT IDENTITY(1,1) NOT NULL,
    "WORKFLOW_ID" VARCHAR(255 char
) NOT NULL,
 "NODE_ID" VARCHAR(255 char) NOT NULL,
 "NODE_TYPE" VARCHAR(255 char) NOT NULL,
 "DATA" TEXT NOT NULL,
 "VERSION" INT NOT NULL,
 "YN" INT DEFAULT 0
 NOT NULL,
 "CREATE_ID" VARCHAR(255 char) NOT NULL,
 "CREATE_NAME" VARCHAR(255 char) NOT NULL,
 "CREATE_TIME" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3)
 NOT NULL,
 "UPDATE_ID" VARCHAR(255 char) NOT NULL,
 "UPDATE_NAME" VARCHAR(255 char) NOT NULL,
 "UPDATE_TIME" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3)
 NOT NULL,
 "APP_CODE" VARCHAR(255 char) NOT NULL
);
CREATE TABLE "BOT_WORKFLOW_SCENE"
(
    "ID"          INT IDENTITY(1,1) NOT NULL,
    "WORKFLOW_ID" VARCHAR(255 char
) NOT NULL,
 "SCENE" VARCHAR(255 char) NOT NULL,
 "YN" INT DEFAULT 0
 NOT NULL,
 "APP_CODE" VARCHAR(255 char) NOT NULL,
 "TENANT_ID" VARCHAR(255 char) DEFAULT ''
 NULL,
 "CREATE_ID" VARCHAR(255 char) NOT NULL,
 "CREATE_NAME" VARCHAR(255 char) NOT NULL,
 "CREATE_TIME" TIMESTAMP(3) NOT NULL,
 "UPDATE_ID" VARCHAR(255 char) NOT NULL,
 "UPDATE_NAME" VARCHAR(255 char) NOT NULL,
 "UPDATE_TIME" TIMESTAMP(3) NOT NULL
);
CREATE TABLE "CATALOG"
(
    "ID"   BIGINT IDENTITY(1,1) NOT NULL,
    "NAME" VARCHAR(520 char
) NOT NULL,
 "CODE" VARCHAR(64 char) NOT NULL,
 "DESCRIPTION" VARCHAR(2048 char) NULL,
 "APP_CODE" VARCHAR(64 char) NOT NULL,
 "KNOWLEDGE_BASE_CODE" VARCHAR(64 char) NOT NULL,
 "PARENT_CODE" VARCHAR(64 char) NOT NULL,
 "PREV_CODE" VARCHAR(64 char) NOT NULL,
 "TYPE" VARCHAR(20 char) NOT NULL,
 "LEVEL" INT NOT NULL,
 "KNOWLEDGE_CODE" VARCHAR(64 char) NULL,
 "YN" DECIMAL(20,0) DEFAULT 0
 NOT NULL,
 "CREATE_ID" VARCHAR(255 char) DEFAULT '0'
 NOT NULL,
 "CREATE_NAME" VARCHAR(255 char) DEFAULT '未知用户'
 NOT NULL,
 "CREATE_TIME" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
 NOT NULL,
 "UPDATE_ID" VARCHAR(255 char) DEFAULT '0'
 NOT NULL,
 "UPDATE_NAME" VARCHAR(255 char) DEFAULT '未知用户'
 NOT NULL,
 "UPDATE_TIME" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
 NOT NULL
);
CREATE TABLE "CHANNEL_API_SECRET"
(
    "ID"        BIGINT IDENTITY(224,1) NOT NULL,
    "TENANT_ID" VARCHAR(128 char
) NOT NULL,
 "APP_ID" VARCHAR(128 char) NULL,
 "SECRET_ID" VARCHAR(128 char) NULL,
 "NAME" VARCHAR(255 char) NULL,
 "SECRET" VARCHAR(255 char) NULL,
 "PROMPT_ID" VARCHAR(128 char) NULL,
 "YN" INT DEFAULT 0
 NOT NULL,
 "CREATE_ID" VARCHAR(100 char) DEFAULT '1'
 NOT NULL,
 "CREATE_NAME" VARCHAR(90 char) NULL,
 "CREATE_TIME" TIMESTAMP(0) NULL,
 "UPDATE_ID" VARCHAR(100 char) DEFAULT '1'
 NOT NULL,
 "UPDATE_NAME" VARCHAR(90 char) NULL,
 "UPDATE_TIME" TIMESTAMP(0) NULL,
 "CHANNEL_ID" VARCHAR(256 char) DEFAULT ''
 NOT NULL,
 "SECRET_TYPE" VARCHAR(8 char) DEFAULT 'API'
 NULL,
 "APP_CODE" VARCHAR(255 char) NULL
);
CREATE TABLE "CHANNEL_CONFIG_INFO"
(
    "ID"        BIGINT IDENTITY(98,1) NOT NULL,
    "TENANT_ID" VARCHAR(128 char
) DEFAULT ''
 NOT NULL,
 "TENANT_NAME" VARCHAR(256 char) DEFAULT ''
 NOT NULL,
 "CHANNEL_ID" VARCHAR(256 char) DEFAULT ''
 NOT NULL,
 "CONFIG_KEY" VARCHAR(256 char) DEFAULT ''
 NOT NULL,
 "CONFIG_VALUE" CLOB NOT NULL,
 "YN" DECIMAL(20,0) DEFAULT 0
 NOT NULL,
 "CREATE_ID" VARCHAR(100 char) DEFAULT ''
 NOT NULL,
 "CREATE_NAME" VARCHAR(256 char) DEFAULT ''
 NOT NULL,
 "UPDATE_ID" VARCHAR(100 char) DEFAULT ''
 NOT NULL,
 "UPDATE_NAME" VARCHAR(256 char) DEFAULT ''
 NOT NULL,
 "CREATE_TIME" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3)
 NOT NULL,
 "UPDATE_TIME" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3)
 NOT NULL
);
CREATE TABLE "CHANNEL_INFO"
(
    "ID"        BIGINT IDENTITY(629,1) NOT NULL,
    "TENANT_ID" VARCHAR(128 char
) DEFAULT ''
 NOT NULL,
 "TENANT_NAME" VARCHAR(256 char) DEFAULT ''
 NOT NULL,
 "AGENT_CODE" VARCHAR(255 char) NOT NULL,
 "CHANNEL_ID" VARCHAR(256 char) DEFAULT ''
 NOT NULL,
 "CHANNEL_NAME" VARCHAR(256 char) DEFAULT ''
 NOT NULL,
 "CHANNEL_TYPE" VARCHAR(64 char) NOT NULL,
 "YN" DECIMAL(20,0) DEFAULT 0
 NOT NULL,
 "CREATE_ID" VARCHAR(100 char) DEFAULT ''
 NOT NULL,
 "CREATE_NAME" VARCHAR(256 char) DEFAULT ''
 NOT NULL,
 "UPDATE_ID" VARCHAR(100 char) DEFAULT ''
 NOT NULL,
 "UPDATE_NAME" VARCHAR(256 char) DEFAULT ''
 NOT NULL,
 "CREATE_TIME" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3)
 NOT NULL,
 "UPDATE_TIME" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3)
 NOT NULL,
 "ENABLE" TINYINT DEFAULT 0
 NOT NULL
);
CREATE TABLE "CHANNEL_MEDIA"
(
    "ID"        BIGINT IDENTITY(1,1) NOT NULL,
    "TENANT_ID" VARCHAR(128 char
) DEFAULT ''
 NOT NULL,
 "TENANT_NAME" VARCHAR(256 char) DEFAULT ''
 NOT NULL,
 "CORP_ID" VARCHAR(256 char) DEFAULT ''
 NOT NULL,
 "MEDIA_TYPE" VARCHAR(64 char) DEFAULT ''
 NOT NULL,
 "MEDIA_URL" VARCHAR(256 char) NOT NULL,
 "MEDIA_ID" VARCHAR(128 char) NOT NULL,
 "EXPIRE_TIME" DECIMAL(20,0) DEFAULT 0
 NOT NULL,
 "YN" DECIMAL(20,0) DEFAULT 0
 NOT NULL,
 "CREATE_ID" VARCHAR(100 char) DEFAULT ''
 NOT NULL,
 "CREATE_NAME" VARCHAR(256 char) DEFAULT ''
 NOT NULL,
 "UPDATE_ID" VARCHAR(100 char) DEFAULT ''
 NOT NULL,
 "UPDATE_NAME" VARCHAR(256 char) DEFAULT ''
 NOT NULL,
 "CREATE_TIME" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3)
 NOT NULL,
 "UPDATE_TIME" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3)
 NOT NULL
);
CREATE TABLE "CHANNEL_MESSAGE_RECORD"
(
    "ID"        BIGINT IDENTITY(1,1) NOT NULL,
    "TENANT_ID" VARCHAR(128 char
) DEFAULT ''
 NOT NULL,
 "TENANT_NAME" VARCHAR(256 char) DEFAULT ''
 NOT NULL,
 "CHANNEL_ID" VARCHAR(256 char) DEFAULT ''
 NOT NULL,
 "ROBOT_CODE" VARCHAR(256 char) DEFAULT ''
 NOT NULL,
 "SESSION_ID" VARCHAR(256 char) NOT NULL,
 "USER_ID" VARCHAR(256 char) NOT NULL,
 "MESSAGE_ID" VARCHAR(256 char) NOT NULL,
 "MESSAGE" VARCHAR(6000 char) NOT NULL,
 "MESSAGE_TYPE" VARCHAR(64 char) NULL,
 "MSG_DIRECTION" VARCHAR(32 char) NOT NULL,
 "YN" DECIMAL(20,0) DEFAULT 0
 NOT NULL,
 "CREATE_ID" VARCHAR(100 char) DEFAULT ''
 NOT NULL,
 "CREATE_NAME" VARCHAR(256 char) DEFAULT ''
 NOT NULL,
 "UPDATE_ID" VARCHAR(100 char) DEFAULT ''
 NOT NULL,
 "UPDATE_NAME" VARCHAR(256 char) DEFAULT ''
 NOT NULL,
 "CREATE_TIME" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3)
 NOT NULL,
 "UPDATE_TIME" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3)
 NOT NULL
);
CREATE TABLE "COLLECTION"
(
    "ID"   BIGINT IDENTITY(1,1) NOT NULL,
    "YN"   DECIMAL(20, 0) DEFAULT 0
        NOT NULL,
    "CODE" VARCHAR(64 char
) NOT NULL,
 "APP_CODE" VARCHAR(64 char) NOT NULL,
 "TARGET_CODE" VARCHAR(64 char) NOT NULL,
 "TARGET_TYPE" VARCHAR(20 char) NOT NULL,
 "CREATE_ID" VARCHAR(255 char) DEFAULT '0'
 NOT NULL,
 "CREATE_NAME" VARCHAR(255 char) DEFAULT '未知用户'
 NOT NULL,
 "CREATE_TIME" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
 NOT NULL,
 "UPDATE_ID" VARCHAR(255 char) DEFAULT '0'
 NOT NULL,
 "UPDATE_NAME" VARCHAR(255 char) DEFAULT '未知用户'
 NOT NULL,
 "UPDATE_TIME" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
 NOT NULL
);
CREATE TABLE "FAQ_SIMILAR_QUESTION"
(
    "ID"   BIGINT IDENTITY(1,1) NOT NULL,
    "YN"   DECIMAL(20, 0) DEFAULT 0
        NOT NULL,
    "CODE" VARCHAR(255 char
) NOT NULL,
 "APP_CODE" VARCHAR(64 char) NOT NULL,
 "KNOWLEDGE_CODE" VARCHAR(255 char) NOT NULL,
 "KNOWLEDGE_BASE_CODE" VARCHAR(64 char) NOT NULL,
 "FAQ_CODE" VARCHAR(255 char) NOT NULL,
 "SIMILAR_QUESTION" VARCHAR(500 char) NOT NULL,
 "CREATE_ID" VARCHAR(255 char) DEFAULT '0'
 NOT NULL,
 "CREATE_NAME" VARCHAR(255 char) DEFAULT '未知用户'
 NOT NULL,
 "CREATE_TIME" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
 NOT NULL,
 "UPDATE_ID" VARCHAR(255 char) DEFAULT '0'
 NOT NULL,
 "UPDATE_NAME" VARCHAR(255 char) DEFAULT '未知用户'
 NOT NULL,
 "UPDATE_TIME" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
 NOT NULL
);
CREATE TABLE "FILE"
(
    "ID"       BIGINT IDENTITY(1,1) NOT NULL,
    "YN"       DECIMAL(20, 0) DEFAULT 0
        NOT NULL,
    "APP_CODE" VARCHAR(64 char
) NOT NULL,
 "CODE" VARCHAR(255 char) NOT NULL,
 "FILE_KEY" VARCHAR(255 char) NOT NULL,
 "MD5" VARCHAR(255 char) NOT NULL,
 "TOTAL_SIZE" DECIMAL(20,0) NOT NULL,
 "ACCESS_TIME" TIMESTAMP(0) NOT NULL,
 "CREATE_ID" VARCHAR(255 char) DEFAULT '0'
 NOT NULL,
 "CREATE_NAME" VARCHAR(255 char) DEFAULT '未知用户'
 NOT NULL,
 "CREATE_TIME" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
 NOT NULL,
 "UPDATE_ID" VARCHAR(255 char) DEFAULT '0'
 NOT NULL,
 "UPDATE_NAME" VARCHAR(255 char) DEFAULT '未知用户'
 NOT NULL,
 "UPDATE_TIME" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
 NOT NULL
);
CREATE TABLE "GS_BASE_CONFIG"
(
    "ID"   BIGINT IDENTITY(2,1) NOT NULL,
    "YN"   DECIMAL(20, 0) DEFAULT 0
        NOT NULL,
    "CODE" VARCHAR(255 char
) NOT NULL,
 "NAME" VARCHAR(64 char) NOT NULL,
 "DIMENSION" VARCHAR(255 char) NOT NULL,
 "CONFIG_TYPE" VARCHAR(255 char) NOT NULL,
 "BUSINESS_NO" VARCHAR(255 char) NOT NULL,
 "CONFIG_DATA" TEXT NULL,
 "OBJ_CLASS" VARCHAR(1024 char) NULL,
 "DESCRIPTION" VARCHAR(255 char) NULL,
 "CREATE_ID" VARCHAR(255 char) DEFAULT '0'
 NOT NULL,
 "CREATE_NAME" VARCHAR(255 char) DEFAULT '未知用户'
 NOT NULL,
 "CREATE_TIME" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
 NOT NULL,
 "UPDATE_ID" VARCHAR(255 char) DEFAULT '0'
 NOT NULL,
 "UPDATE_NAME" VARCHAR(255 char) DEFAULT '未知用户'
 NOT NULL,
 "UPDATE_TIME" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
 NOT NULL
);
CREATE TABLE "GS_SESSION_FILE"
(
    "ID"       BIGINT IDENTITY(1,1) NOT NULL,
    "BIZ_TYPE" VARCHAR(32 char
) NULL,
 "BIZ_CODE" VARCHAR(255 char) NULL,
 "SESSION_ID" VARCHAR(255 char) NULL,
 "FILE_CODE" VARCHAR(255 char) NULL,
 "FILE_TYPE" VARCHAR(32 char) NULL,
 "FILE_NAME" VARCHAR(255 char) NULL,
 "FILE_OSS_URL" VARCHAR(255 char) NULL,
 "EXTRA_DATA" TEXT NULL,
 "PARSE_SPLIT" VARCHAR(32 char) NULL,
 "ORIGINAL_FILE_KEY" VARCHAR(255 char) NULL,
 "FILE_CONTENT" CLOB NULL,
 "FILE_CONTENT_LENGTH" BIGINT NULL,
 "TENANT_ID" VARCHAR(128 char) NULL,
 "CREATE_ID" VARCHAR(100 char) NULL,
 "CREATE_NAME" VARCHAR(225 char) NULL,
 "CREATE_TIME" TIMESTAMP(0) NULL,
 "UPDATE_ID" VARCHAR(255 char) NULL,
 "UPDATE_NAME" VARCHAR(255 char) NULL,
 "UPDATE_TIME" TIMESTAMP(0) NULL,
 "YN" INT DEFAULT 0
 NULL
);
CREATE TABLE "KNOWLEDGE"
(
    "ID"       BIGINT IDENTITY(1,1) NOT NULL,
    "YN"       DECIMAL(20, 0) DEFAULT 0
        NOT NULL,
    "APP_CODE" VARCHAR(64 char
) NOT NULL,
 "NAME" VARCHAR(520 char) NOT NULL,
 "CODE" VARCHAR(64 char) NOT NULL,
 "KNOWLEDGE_BASE_CODE" DECIMAL(20,0) NOT NULL,
 "TYPE" VARCHAR(20 char) NOT NULL,
 "ON" BIT DEFAULT 1
 NOT NULL,
 "STATUS" VARCHAR(20 char) NOT NULL,
 "INDEXING_STATUS" VARCHAR(20 char) DEFAULT 'pending'
 NOT NULL,
 "IMAGE_INDEXING_STATUS" VARCHAR(20 char) DEFAULT 'success'
 NOT NULL,
 "ORIGINAL_FILE_KEY" VARCHAR(1024 char) NULL,
 "VIEW_FILE_KEY" VARCHAR(1024 char) NULL,
 "VIEW_COUNT" DECIMAL(20,0) DEFAULT 0
 NOT NULL,
 "KNOWLEDGE_TIP" VARCHAR(128 char) NULL,
 "SLICE_FILE_INFO" VARCHAR(160 char) NULL,
 "PUBLISH_STATUS" VARCHAR(255 char) DEFAULT 'UNPUBLISHED'
 NOT NULL,
 "PUBLISH_TIME" TIMESTAMP(0) NULL,
 "PUBLISH_COUNT" INT DEFAULT 0
 NOT NULL,
 "COVER_FILE_KEY" VARCHAR(255 char) NULL,
 "DESCRIPTION" TEXT NULL,
 "CREATE_ID" VARCHAR(255 char) DEFAULT '0'
 NOT NULL,
 "CREATE_NAME" VARCHAR(255 char) DEFAULT '未知用户'
 NOT NULL,
 "CREATE_TIME" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
 NOT NULL,
 "UPDATE_ID" VARCHAR(255 char) DEFAULT '0'
 NOT NULL,
 "UPDATE_NAME" VARCHAR(255 char) DEFAULT '未知用户'
 NOT NULL,
 "UPDATE_TIME" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
 NOT NULL
);
CREATE TABLE "KNOWLEDGE_AUDIT"
(
    "ID"   BIGINT IDENTITY(1,1) NOT NULL,
    "YN"   DECIMAL(20, 0) DEFAULT 0
        NOT NULL,
    "CODE" VARCHAR(64 char
) NOT NULL,
 "APP_CODE" VARCHAR(64 char) NOT NULL,
 "NAME" VARCHAR(2048 char) NOT NULL,
 "DESCRIPTION" TEXT NULL,
 "KNOWLEDGE_BASE_CODE" VARCHAR(64 char) NOT NULL,
 "EXTERNAL_PROCESS_CODE" VARCHAR(64 char) NOT NULL,
 "EXTERNAL_INSTANCE_CODE" VARCHAR(64 char) NOT NULL,
 "AUDIT_TYPE" VARCHAR(64 char) NOT NULL,
 "AUDIT_START_TIME" TIMESTAMP(0) NOT NULL,
 "AUDIT_END_TIME" TIMESTAMP(0) NULL,
 "AUDIT_PARAM" TEXT NOT NULL,
 "AUDIT_STATUS" VARCHAR(64 char) NOT NULL,
 "AUDIT_RESULT" VARCHAR(2048 char) NULL,
 "CREATE_ID" VARCHAR(255 char) DEFAULT '0'
 NOT NULL,
 "CREATE_NAME" VARCHAR(255 char) DEFAULT '未知用户'
 NOT NULL,
 "CREATE_TIME" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
 NOT NULL,
 "UPDATE_ID" VARCHAR(255 char) DEFAULT '0'
 NOT NULL,
 "UPDATE_NAME" VARCHAR(255 char) DEFAULT '未知用户'
 NOT NULL,
 "UPDATE_TIME" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
 NOT NULL
);
CREATE TABLE "KNOWLEDGE_AUDIT_DATA"
(
    "ID"   BIGINT IDENTITY(1,1) NOT NULL,
    "YN"   DECIMAL(20, 0) DEFAULT 0
        NOT NULL,
    "CODE" VARCHAR(64 char
) NOT NULL,
 "APP_CODE" VARCHAR(64 char) NOT NULL,
 "AUDIT_CODE" VARCHAR(64 char) NOT NULL,
 "DATA_TYPE" VARCHAR(64 char) NOT NULL,
 "DATA_CODE" VARCHAR(64 char) NOT NULL,
 "DATA_TITLE" VARCHAR(520 char) NOT NULL,
 "DATA_STATUS" VARCHAR(64 char) NOT NULL,
 "DATA_CONTENT" TEXT NULL,
 "CREATE_ID" VARCHAR(255 char) DEFAULT '0'
 NOT NULL,
 "CREATE_NAME" VARCHAR(255 char) DEFAULT '未知用户'
 NOT NULL,
 "CREATE_TIME" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
 NOT NULL,
 "UPDATE_ID" VARCHAR(255 char) DEFAULT '0'
 NOT NULL,
 "UPDATE_NAME" VARCHAR(255 char) DEFAULT '未知用户'
 NOT NULL,
 "UPDATE_TIME" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
 NOT NULL
);
CREATE TABLE "KNOWLEDGE_BASE"
(
    "ID"   BIGINT IDENTITY(1,1) NOT NULL,
    "NAME" VARCHAR(64 char
) NOT NULL,
 "CODE" VARCHAR(64 char) NOT NULL,
 "TYPE" VARCHAR(16 char) DEFAULT 'FILE'
 NOT NULL,
 "DESC" VARCHAR(2048 char) NULL,
 "ON" BIT DEFAULT 1
 NOT NULL,
 "CONFIG" TEXT NULL,
 "APP_CODE" VARCHAR(64 char) NOT NULL,
 "ICON" VARCHAR(64 char) NULL,
 "COVER_KEY" VARCHAR(100 char) NULL,
 "YN" DECIMAL(20,0) DEFAULT 0
 NOT NULL,
 "CREATE_ID" VARCHAR(255 char) DEFAULT '0'
 NOT NULL,
 "CREATE_NAME" VARCHAR(255 char) DEFAULT '未知用户'
 NOT NULL,
 "CREATE_TIME" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
 NOT NULL,
 "UPDATE_ID" VARCHAR(255 char) DEFAULT '0'
 NOT NULL,
 "UPDATE_NAME" VARCHAR(255 char) DEFAULT '未知用户'
 NOT NULL,
 "UPDATE_TIME" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
 NOT NULL
);
CREATE TABLE "KNOWLEDGE_ESSENTIAL"
(
    "ID"       BIGINT IDENTITY(1,1) NOT NULL,
    "YN"       DECIMAL(20, 0) DEFAULT 0
        NOT NULL,
    "APP_CODE" VARCHAR(64 char
) NOT NULL,
 "CODE" VARCHAR(255 char) NOT NULL,
 "KNOWLEDGE_CODE" VARCHAR(64 char) NOT NULL,
 "TITLE" VARCHAR(512 char) NOT NULL,
 "CONTENT" TEXT NOT NULL,
 "SOURCE" TEXT NOT NULL,
 "PARENT_CODE" VARCHAR(64 char) NULL,
 "CREATE_ID" VARCHAR(255 char) DEFAULT '0'
 NOT NULL,
 "CREATE_NAME" VARCHAR(255 char) DEFAULT '未知用户'
 NOT NULL,
 "CREATE_TIME" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
 NOT NULL,
 "UPDATE_ID" VARCHAR(255 char) DEFAULT '0'
 NOT NULL,
 "UPDATE_NAME" VARCHAR(255 char) DEFAULT '未知用户'
 NOT NULL,
 "UPDATE_TIME" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
 NOT NULL
);
CREATE TABLE "KNOWLEDGE_EXTRACT"
(
    "ID"             BIGINT IDENTITY(1,1) NOT NULL,
    "KNOWLEDGE_CODE" VARCHAR(255 char
) NOT NULL,
 "STATUS" VARCHAR(50 char) DEFAULT '1'
 NOT NULL,
 "FAILURE_REASON" VARCHAR(1000 char) NULL,
 "EXTRACT_STATUS" VARCHAR(10 char) DEFAULT '0'
 NOT NULL,
 "CREATE_ID" VARCHAR(255 char) DEFAULT '1'
 NOT NULL,
 "CREATE_NAME" VARCHAR(255 char) DEFAULT '未知用户'
 NOT NULL,
 "CREATE_TIME" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
 NOT NULL,
 "UPDATE_ID" VARCHAR(255 char) DEFAULT '0'
 NOT NULL,
 "UPDATE_TIME" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
 NOT NULL,
 "UPDATE_NAME" VARCHAR(255 char) DEFAULT '未知用户'
 NOT NULL,
 "YN" BIGINT DEFAULT 0
 NOT NULL,
 "CODE" VARCHAR(255 char) NOT NULL,
 "APP_CODE" VARCHAR(64 char) NOT NULL
);
CREATE TABLE "KNOWLEDGE_EXTRA_INFO"
(
    "ID"       BIGINT IDENTITY(1,1) NOT NULL,
    "YN"       DECIMAL(20, 0) DEFAULT 0
        NOT NULL,
    "APP_CODE" VARCHAR(64 char
) NOT NULL,
 "CODE" VARCHAR(64 char) NOT NULL,
 "EXTRA_KEY" VARCHAR(64 char) DEFAULT 'DEFAULT'
 NOT NULL,
 "KNOWLEDGE_CODE" DECIMAL(20,0) NOT NULL,
 "CONTENT" CLOB NOT NULL,
 "TYPE" VARCHAR(20 char) NOT NULL,
 "CREATE_ID" VARCHAR(255 char) DEFAULT '0'
 NOT NULL,
 "CREATE_NAME" VARCHAR(255 char) DEFAULT '未知用户'
 NOT NULL,
 "CREATE_TIME" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
 NOT NULL,
 "UPDATE_ID" VARCHAR(255 char) DEFAULT '0'
 NOT NULL,
 "UPDATE_NAME" VARCHAR(255 char) DEFAULT '未知用户'
 NOT NULL,
 "UPDATE_TIME" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
 NOT NULL
);
CREATE TABLE "KNOWLEDGE_FAQ"
(
    "ID"   BIGINT IDENTITY(1,1) NOT NULL,
    "YN"   DECIMAL(20, 0) DEFAULT 0
        NOT NULL,
    "CODE" VARCHAR(255 char
) NOT NULL,
 "APP_CODE" VARCHAR(64 char) NOT NULL,
 "KNOWLEDGE_CODE" VARCHAR(255 char) NOT NULL,
 "KNOWLEDGE_BASE_CODE" VARCHAR(64 char) NOT NULL,
 "ON" BIT DEFAULT 1
 NOT NULL,
 "QUESTION" VARCHAR(1024 char) NOT NULL,
 "ANSWER_TYPE" VARCHAR(32 char) NOT NULL,
 "ANSWER" TEXT NOT NULL,
 "IS_AUTO" BIT DEFAULT 0
 NOT NULL,
 "PUBLISH_STATUS" VARCHAR(255 char) DEFAULT 'UNPUBLISHED'
 NOT NULL,
 "PUBLISH_TIME" TIMESTAMP(0) NULL,
 "PUBLISH_COUNT" INT DEFAULT 0
 NOT NULL,
 "CREATE_ID" VARCHAR(255 char) DEFAULT '0'
 NOT NULL,
 "CREATE_NAME" VARCHAR(255 char) DEFAULT '未知用户'
 NOT NULL,
 "CREATE_TIME" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
 NOT NULL,
 "UPDATE_ID" VARCHAR(255 char) DEFAULT '0'
 NOT NULL,
 "UPDATE_NAME" VARCHAR(255 char) DEFAULT '未知用户'
 NOT NULL,
 "UPDATE_TIME" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
 NOT NULL
);
CREATE TABLE "KNOWLEDGE_FAQ_PROD"
(
    "ID"   BIGINT IDENTITY(1,1) NOT NULL,
    "YN"   DECIMAL(20, 0) DEFAULT 0
        NOT NULL,
    "CODE" VARCHAR(255 char
) NOT NULL,
 "APP_CODE" VARCHAR(64 char) NOT NULL,
 "KNOWLEDGE_CODE" VARCHAR(255 char) NOT NULL,
 "CONTENT" CLOB NULL,
 "CREATE_ID" VARCHAR(255 char) DEFAULT '0'
 NOT NULL,
 "CREATE_NAME" VARCHAR(255 char) DEFAULT '未知用户'
 NOT NULL,
 "CREATE_TIME" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
 NOT NULL,
 "UPDATE_ID" VARCHAR(255 char) DEFAULT '0'
 NOT NULL,
 "UPDATE_NAME" VARCHAR(255 char) DEFAULT '未知用户'
 NOT NULL,
 "UPDATE_TIME" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
 NOT NULL
);
CREATE TABLE "KNOWLEDGE_FAQ_TEMP"
(
    "ID"   BIGINT IDENTITY(1,1) NOT NULL,
    "YN"   BIGINT DEFAULT 0
        NOT NULL,
    "CODE" VARCHAR(255 char
) NOT NULL,
 "KNOWLEDGE_CODE" VARCHAR(255 char) NOT NULL,
 "ON" BIT DEFAULT 1
 NOT NULL,
 "QUESTION" VARCHAR(1024 char) NOT NULL,
 "ANSWER_TYPE" VARCHAR(32 char) NOT NULL,
 "ANSWER" TEXT NOT NULL,
 "CORPUS" TEXT NULL,
 "CORPUS_COUNT" INT NULL,
 "EXTRACT_CODE" VARCHAR(255 char) NULL,
 "CREATE_ID" VARCHAR(255 char) DEFAULT '0'
 NOT NULL,
 "CREATE_NAME" VARCHAR(255 char) DEFAULT '未知用户'
 NOT NULL,
 "CREATE_TIME" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
 NOT NULL,
 "UPDATE_ID" VARCHAR(255 char) DEFAULT '0'
 NOT NULL,
 "UPDATE_TIME" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
 NOT NULL,
 "UPDATE_NAME" VARCHAR(255 char) DEFAULT '未知用户'
 NOT NULL,
 "STORE_TIME" TIMESTAMP(0) NULL,
 "APP_CODE" VARCHAR(64 char) NOT NULL,
 "STORE_STATUS" VARCHAR(8 char) DEFAULT '0'
 NOT NULL,
 "IS_AUTO" TINYINT DEFAULT 0
 NOT NULL,
 "SOURCE" TEXT NULL,
 "TARGET_KNOWLEDGE_CODE" VARCHAR(255 char) NULL
);
CREATE TABLE "KNOWLEDGE_PROD"
(
    "ID"       BIGINT IDENTITY(1,1) NOT NULL,
    "YN"       DECIMAL(20, 0) DEFAULT 0
        NOT NULL,
    "APP_CODE" VARCHAR(64 char
) NOT NULL,
 "CODE" VARCHAR(64 char) NOT NULL,
 "CONTENT" CLOB NULL,
 "CREATE_ID" VARCHAR(255 char) DEFAULT '0'
 NOT NULL,
 "CREATE_NAME" VARCHAR(255 char) DEFAULT '未知用户'
 NOT NULL,
 "CREATE_TIME" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
 NOT NULL,
 "UPDATE_ID" VARCHAR(255 char) DEFAULT '0'
 NOT NULL,
 "UPDATE_NAME" VARCHAR(255 char) DEFAULT '未知用户'
 NOT NULL,
 "UPDATE_TIME" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
 NOT NULL
);
CREATE TABLE "KNOWLEDGE_REPORT"
(
    "ID"   BIGINT IDENTITY(1,1) NOT NULL,
    "NAME" VARCHAR(512 char
) NOT NULL,
 "CODE" VARCHAR(64 char) NOT NULL,
 "APP_CODE" VARCHAR(64 char) NOT NULL,
 "THEME_TEXT" VARCHAR(512 char) NOT NULL,
 "CONTENT" CLOB NULL,
 "TYPE" VARCHAR(64 char) NOT NULL,
 "OUTLINE_DATA" TEXT NULL,
 "CHOSE" VARCHAR(10000 char) NULL,
 "YN" DECIMAL(20,0) DEFAULT 0
 NOT NULL,
 "CREATE_ID" VARCHAR(255 char) DEFAULT '0'
 NOT NULL,
 "CREATE_NAME" VARCHAR(255 char) DEFAULT '未知用户'
 NOT NULL,
 "CREATE_TIME" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
 NOT NULL,
 "UPDATE_ID" VARCHAR(255 char) DEFAULT '0'
 NOT NULL,
 "UPDATE_NAME" VARCHAR(255 char) DEFAULT '未知用户'
 NOT NULL,
 "UPDATE_TIME" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
 NOT NULL
);
CREATE TABLE "PLUGIN_API_META_INFO"
(
    "ID"        BIGINT IDENTITY(81,1) NOT NULL,
    "TENANT_ID" VARCHAR(128 char
) DEFAULT ''
 NOT NULL,
 "API_ID" VARCHAR(128 char) NOT NULL,
 "PLUGIN_ID" VARCHAR(128 char) NOT NULL,
 "API_NAME" VARCHAR(128 char) NOT NULL,
 "API_DESC" VARCHAR(1000 char) NOT NULL,
 "PATH" VARCHAR(128 char) NOT NULL,
 "METHOD" VARCHAR(128 char) NOT NULL,
 "SERVICE_STATUS" INT NOT NULL,
 "DEBUG_STATUS" INT NOT NULL,
 "QUOTE_NUMBER" INT DEFAULT 0
 NULL,
 "ENABLED" INT DEFAULT 1
 NULL,
 "VERSION" INT NOT NULL,
 "CREATE_ID" VARCHAR(100 char) DEFAULT '1'
 NOT NULL,
 "CREATE_NAME" VARCHAR(256 char) DEFAULT ''
 NOT NULL,
 "UPDATE_ID" VARCHAR(100 char) DEFAULT '1'
 NOT NULL,
 "UPDATE_NAME" VARCHAR(256 char) DEFAULT ''
 NOT NULL,
 "CREATE_TIME" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3)
 NOT NULL,
 "UPDATE_TIME" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3)
 NOT NULL,
 "YN" DECIMAL(20,0) DEFAULT 0
 NOT NULL,
 "APP_CODE" VARCHAR(128 char) DEFAULT ''
 NOT NULL
);
CREATE TABLE "PLUGIN_API_PARAM"
(
    "ID"        BIGINT IDENTITY(918,1) NOT NULL,
    "TENANT_ID" VARCHAR(128 char
) DEFAULT ''
 NOT NULL,
 "PARAM_ID" VARCHAR(128 char) NOT NULL,
 "FATHER_PARAM_ID" VARCHAR(128 char) NULL,
 "PLUGIN_ID" VARCHAR(128 char) NOT NULL,
 "API_ID" VARCHAR(128 char) NOT NULL,
 "NAME" VARCHAR(128 char) NOT NULL,
 "PARAM_DESC" VARCHAR(128 char) NOT NULL,
 "REQ_RSP_TYPE" INT NOT NULL,
 "LOCATION" INT NULL,
 "REQUIRED" INT NULL,
 "DEFAULT_VALUE" VARCHAR(1000 char) NULL,
 "PARAM_TYPE" INT DEFAULT 0
 NULL,
 "ENABLED" INT DEFAULT 1
 NULL,
 "VERSION" INT NOT NULL,
 "CREATE_ID" VARCHAR(100 char) DEFAULT '1'
 NOT NULL,
 "CREATE_NAME" VARCHAR(256 char) DEFAULT ''
 NOT NULL,
 "UPDATE_ID" VARCHAR(100 char) DEFAULT '1'
 NOT NULL,
 "UPDATE_NAME" VARCHAR(256 char) DEFAULT ''
 NOT NULL,
 "CREATE_TIME" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3)
 NOT NULL,
 "UPDATE_TIME" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3)
 NOT NULL,
 "YN" DECIMAL(20,0) DEFAULT 0
 NOT NULL,
 "APP_CODE" VARCHAR(255 char) DEFAULT ''
 NOT NULL
);
CREATE TABLE "PLUGIN_BIND_CARD"
(
    "ID"        BIGINT IDENTITY(13,1) NOT NULL,
    "TENANT_ID" VARCHAR(128 char
) DEFAULT ''
 NOT NULL,
 "API_ID" VARCHAR(128 char) NULL,
 "PLUGIN_ID" VARCHAR(128 char) NULL,
 "AGENT_CODE" VARCHAR(128 char) NOT NULL,
 "BIND_TYPE" INT NOT NULL,
 "MAX_LENGTH" INT NULL,
 "BIND_ARRAY" VARCHAR(128 char) NULL,
 "BIND_MAPPING_RULE" TEXT NOT NULL,
 "VERSION" INT NOT NULL,
 "CREATE_ID" VARCHAR(100 char) DEFAULT '1'
 NOT NULL,
 "CREATE_NAME" VARCHAR(256 char) DEFAULT ''
 NOT NULL,
 "UPDATE_ID" VARCHAR(100 char) DEFAULT '1'
 NOT NULL,
 "UPDATE_NAME" VARCHAR(256 char) DEFAULT ''
 NOT NULL,
 "CREATE_TIME" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3)
 NOT NULL,
 "UPDATE_TIME" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3)
 NOT NULL,
 "YN" DECIMAL(20,0) DEFAULT 0
 NOT NULL,
 "BUSINESS_TYPE" INT NOT NULL,
 "WORKFLOW_ID" VARCHAR(128 char) NULL,
 "NODE_ID" VARCHAR(128 char) NULL
);
CREATE TABLE "PLUGIN_MALL_CATEGORIES"
(
    "ID"            BIGINT IDENTITY(2,1) NOT NULL,
    "CATEGORY_CODE" VARCHAR(128 char
) DEFAULT ''
 NOT NULL,
 "CATEGORY_NAME" VARCHAR(128 char) DEFAULT ''
 NOT NULL,
 "CATEGORY_ICON" VARCHAR(128 char) DEFAULT ''
 NOT NULL,
 "CREATE_ID" VARCHAR(100 char) DEFAULT '1'
 NOT NULL,
 "CREATE_NAME" VARCHAR(256 char) DEFAULT ''
 NOT NULL,
 "UPDATE_ID" VARCHAR(100 char) DEFAULT '1'
 NOT NULL,
 "UPDATE_NAME" VARCHAR(256 char) DEFAULT ''
 NOT NULL,
 "CREATE_TIME" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3)
 NOT NULL,
 "UPDATE_TIME" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3)
 NOT NULL,
 "YN" DECIMAL(20,0) DEFAULT 0
 NOT NULL
);
CREATE TABLE "PLUGIN_MALL_INFO"
(
    "ID"        BIGINT IDENTITY(9,1) NOT NULL,
    "PLUGIN_ID" VARCHAR(128 char
) NOT NULL,
 "CATEGORY_CODE" VARCHAR(128 char) DEFAULT ''
 NOT NULL,
 "CATEGORY_NAME" VARCHAR(128 char) DEFAULT ''
 NOT NULL,
 "PLUGIN_SOURCE" TINYINT DEFAULT 0
 NULL,
 "CREATE_ID" VARCHAR(100 char) DEFAULT '1'
 NOT NULL,
 "CREATE_NAME" VARCHAR(256 char) DEFAULT ''
 NOT NULL,
 "UPDATE_ID" VARCHAR(100 char) DEFAULT '1'
 NOT NULL,
 "UPDATE_NAME" VARCHAR(256 char) DEFAULT ''
 NOT NULL,
 "CREATE_TIME" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3)
 NOT NULL,
 "UPDATE_TIME" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3)
 NOT NULL,
 "YN" DECIMAL(20,0) DEFAULT 0
 NOT NULL
);
CREATE TABLE "PLUGIN_META_INFO"
(
    "ID"        BIGINT IDENTITY(81,1) NOT NULL,
    "TENANT_ID" VARCHAR(128 char
) DEFAULT ''
 NOT NULL,
 "PLUGIN_ID" VARCHAR(128 char) NOT NULL,
 "PLUGIN_NAME" VARCHAR(128 char) NOT NULL,
 "PLUGIN_DESC" VARCHAR(1000 char) NOT NULL,
 "CREATE_API_TYPE" INT DEFAULT 1
 NOT NULL,
 "URL" VARCHAR(128 char) NOT NULL,
 "PLUGIN_ICON" VARCHAR(255 char) NOT NULL,
 "AUTH_TYPE" INT NOT NULL,
 "LOCATION" INT NULL,
 "SERVICE_KEY" VARCHAR(128 char) NULL,
 "SERVICE_TOKEN" TEXT NULL,
 "OAUTH_INFO" TEXT NULL,
 "COMMON_HEADERS" TEXT NULL,
 "STATUS" INT NOT NULL,
 "VERSION" INT NOT NULL,
 "CREATE_ID" VARCHAR(100 char) DEFAULT '1'
 NOT NULL,
 "CREATE_NAME" VARCHAR(256 char) DEFAULT ''
 NOT NULL,
 "UPDATE_ID" VARCHAR(100 char) DEFAULT '1'
 NOT NULL,
 "UPDATE_NAME" VARCHAR(256 char) DEFAULT ''
 NOT NULL,
 "CREATE_TIME" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3)
 NOT NULL,
 "UPDATE_TIME" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3)
 NOT NULL,
 "YN" DECIMAL(20,0) DEFAULT 0
 NOT NULL,
 "APP_CODE" VARCHAR(255 char) DEFAULT ''
 NOT NULL
);
CREATE TABLE "PLUGIN_VERSION_INFO"
(
    "ID"        BIGINT IDENTITY(128,1) NOT NULL,
    "TENANT_ID" VARCHAR(128 char
) DEFAULT ''
 NOT NULL,
 "PLUGIN_ID" VARCHAR(128 char) DEFAULT ''
 NOT NULL,
 "VERSION" BIGINT DEFAULT 1
 NOT NULL,
 "STATUS" VARCHAR(128 char) DEFAULT 'EDITABLE'
 NOT NULL,
 "RUNTIME_STATUS" VARCHAR(128 char) DEFAULT 'OFFLINE'
 NOT NULL,
 "VERSION_DESC" VARCHAR(1024 char) DEFAULT ''
 NOT NULL,
 "YN" SMALLINT DEFAULT 0
 NOT NULL,
 "CREATE_ID" VARCHAR(100 char) DEFAULT '1'
 NOT NULL,
 "CREATE_NAME" VARCHAR(256 char) DEFAULT ''
 NOT NULL,
 "UPDATE_ID" VARCHAR(100 char) DEFAULT '1'
 NOT NULL,
 "UPDATE_NAME" VARCHAR(256 char) DEFAULT ''
 NOT NULL,
 "CREATE_TIME" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3)
 NOT NULL,
 "UPDATE_TIME" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3)
 NOT NULL
);
CREATE TABLE "PROMPT_TEMPLATE"
(
    "ID"   BIGINT IDENTITY(16,1) NOT NULL,
    "YN"   DECIMAL(20, 0) DEFAULT 0
        NOT NULL,
    "CODE" VARCHAR(64 char
) NOT NULL,
 "APP_CODE" VARCHAR(64 char) NOT NULL,
 "NAME" VARCHAR(64 char) NOT NULL,
 "DESCRIPTION" VARCHAR(512 char) NULL,
 "CONTENT" TEXT NULL,
 "MODEL_PARAM" VARCHAR(2048 char) NULL,
 "CREATE_ID" VARCHAR(255 char) DEFAULT '0'
 NOT NULL,
 "CREATE_NAME" VARCHAR(255 char) DEFAULT '未知用户'
 NOT NULL,
 "CREATE_TIME" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
 NOT NULL,
 "UPDATE_ID" VARCHAR(255 char) DEFAULT '0'
 NOT NULL,
 "UPDATE_NAME" VARCHAR(255 char) DEFAULT '未知用户'
 NOT NULL,
 "UPDATE_TIME" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP()
 NOT NULL
);
ALTER TABLE "AGENT_AI_MODEL"
    ADD CONSTRAINT NOT CLUSTER PRIMARY KEY ("ID");

ALTER TABLE "AGENT_BASIC_INFO"
    ADD CONSTRAINT NOT CLUSTER PRIMARY KEY ("ID");

ALTER TABLE "AGENT_CONFIG"
    ADD CONSTRAINT NOT CLUSTER PRIMARY KEY ("ID");

ALTER TABLE "AGENT_DEFAULT_CONFIG"
    ADD CONSTRAINT NOT CLUSTER PRIMARY KEY ("ID");

ALTER TABLE "AGENT_DIALOG_COUNT_RECORD"
    ADD CONSTRAINT NOT CLUSTER PRIMARY KEY ("ID");

ALTER TABLE "AGENT_DIALOG_RECORD"
    ADD CONSTRAINT NOT CLUSTER PRIMARY KEY ("ID");

ALTER TABLE "AGENT_INDEX_CREATE_RECORD"
    ADD CONSTRAINT NOT CLUSTER PRIMARY KEY ("ID");

ALTER TABLE "AGENT_KNOWLEDGE_BASE"
    ADD CONSTRAINT NOT CLUSTER PRIMARY KEY ("ID");

ALTER TABLE "AGENT_PLUGIN_BIND"
    ADD CONSTRAINT NOT CLUSTER PRIMARY KEY ("ID");

ALTER TABLE "AGENT_QUESTION_HIT_RECORD"
    ADD CONSTRAINT NOT CLUSTER PRIMARY KEY ("ID");

ALTER TABLE "AGENT_VERSION_INFO"
    ADD CONSTRAINT NOT CLUSTER PRIMARY KEY ("ID");

ALTER TABLE "AGENT_WORKFLOW_BIND"
    ADD CONSTRAINT NOT CLUSTER PRIMARY KEY ("ID");

ALTER TABLE "APP"
    ADD CONSTRAINT NOT CLUSTER PRIMARY KEY ("ID");

ALTER TABLE "APP_ROLE"
    ADD CONSTRAINT NOT CLUSTER PRIMARY KEY ("ID");

ALTER TABLE "ATTACHMENT"
    ADD CONSTRAINT NOT CLUSTER PRIMARY KEY ("ID");

ALTER TABLE "BOT_WORKFLOW"
    ADD CONSTRAINT NOT CLUSTER PRIMARY KEY ("ID");

ALTER TABLE "BOT_WORKFLOW_EDGE"
    ADD CONSTRAINT NOT CLUSTER PRIMARY KEY ("ID");

ALTER TABLE "BOT_WORKFLOW_NODE"
    ADD CONSTRAINT NOT CLUSTER PRIMARY KEY ("ID");

ALTER TABLE "BOT_WORKFLOW_SCENE"
    ADD CONSTRAINT NOT CLUSTER PRIMARY KEY ("ID");

ALTER TABLE "CATALOG"
    ADD CONSTRAINT NOT CLUSTER PRIMARY KEY ("ID");

ALTER TABLE "CHANNEL_API_SECRET"
    ADD CONSTRAINT NOT CLUSTER PRIMARY KEY ("ID");

ALTER TABLE "CHANNEL_CONFIG_INFO"
    ADD CONSTRAINT NOT CLUSTER PRIMARY KEY ("ID");

ALTER TABLE "CHANNEL_INFO"
    ADD CONSTRAINT NOT CLUSTER PRIMARY KEY ("ID");

ALTER TABLE "CHANNEL_MEDIA"
    ADD CONSTRAINT NOT CLUSTER PRIMARY KEY ("ID");

ALTER TABLE "CHANNEL_MESSAGE_RECORD"
    ADD CONSTRAINT NOT CLUSTER PRIMARY KEY ("ID");

ALTER TABLE "COLLECTION"
    ADD CONSTRAINT NOT CLUSTER PRIMARY KEY ("ID");

ALTER TABLE "FAQ_SIMILAR_QUESTION"
    ADD CONSTRAINT NOT CLUSTER PRIMARY KEY ("ID");

ALTER TABLE "FILE"
    ADD CONSTRAINT NOT CLUSTER PRIMARY KEY ("ID");

ALTER TABLE "GS_BASE_CONFIG"
    ADD CONSTRAINT NOT CLUSTER PRIMARY KEY ("ID");

ALTER TABLE "GS_SESSION_FILE"
    ADD CONSTRAINT NOT CLUSTER PRIMARY KEY ("ID");

ALTER TABLE "KNOWLEDGE"
    ADD CONSTRAINT NOT CLUSTER PRIMARY KEY ("ID");

ALTER TABLE "KNOWLEDGE_AUDIT"
    ADD CONSTRAINT NOT CLUSTER PRIMARY KEY ("ID");

ALTER TABLE "KNOWLEDGE_AUDIT_DATA"
    ADD CONSTRAINT NOT CLUSTER PRIMARY KEY ("ID");

ALTER TABLE "KNOWLEDGE_BASE"
    ADD CONSTRAINT NOT CLUSTER PRIMARY KEY ("ID");

ALTER TABLE "KNOWLEDGE_ESSENTIAL"
    ADD CONSTRAINT NOT CLUSTER PRIMARY KEY ("ID");

ALTER TABLE "KNOWLEDGE_EXTRACT"
    ADD CONSTRAINT NOT CLUSTER PRIMARY KEY ("ID");

ALTER TABLE "KNOWLEDGE_EXTRA_INFO"
    ADD CONSTRAINT NOT CLUSTER PRIMARY KEY ("ID");

ALTER TABLE "KNOWLEDGE_FAQ"
    ADD CONSTRAINT NOT CLUSTER PRIMARY KEY ("ID");

ALTER TABLE "KNOWLEDGE_FAQ_PROD"
    ADD CONSTRAINT NOT CLUSTER PRIMARY KEY ("ID");

ALTER TABLE "KNOWLEDGE_FAQ_TEMP"
    ADD CONSTRAINT NOT CLUSTER PRIMARY KEY ("ID");

ALTER TABLE "KNOWLEDGE_PROD"
    ADD CONSTRAINT NOT CLUSTER PRIMARY KEY ("ID");

ALTER TABLE "KNOWLEDGE_REPORT"
    ADD CONSTRAINT NOT CLUSTER PRIMARY KEY ("ID");

ALTER TABLE "PLUGIN_API_META_INFO"
    ADD CONSTRAINT NOT CLUSTER PRIMARY KEY ("ID");

ALTER TABLE "PLUGIN_API_PARAM"
    ADD CONSTRAINT NOT CLUSTER PRIMARY KEY ("ID");

ALTER TABLE "PLUGIN_BIND_CARD"
    ADD CONSTRAINT NOT CLUSTER PRIMARY KEY ("ID");

ALTER TABLE "PLUGIN_MALL_CATEGORIES"
    ADD CONSTRAINT NOT CLUSTER PRIMARY KEY ("ID");

ALTER TABLE "PLUGIN_MALL_INFO"
    ADD CONSTRAINT NOT CLUSTER PRIMARY KEY ("ID");

ALTER TABLE "PLUGIN_META_INFO"
    ADD CONSTRAINT NOT CLUSTER PRIMARY KEY ("ID");

ALTER TABLE "PLUGIN_VERSION_INFO"
    ADD CONSTRAINT NOT CLUSTER PRIMARY KEY ("ID");

ALTER TABLE "PROMPT_TEMPLATE"
    ADD CONSTRAINT NOT CLUSTER PRIMARY KEY ("ID");

CREATE INDEX "IDX_AGENT_CODE"
    ON "AGENT_BASIC_INFO" ("AGENT_CODE");
ALTER
INDEX "IDX_AGENT_CODE" VISIBLE;

CREATE INDEX "APP_CODE_KEY"
    ON "AGENT_INDEX_CREATE_RECORD" ("APP_CODE");
ALTER
INDEX "APP_CODE_KEY" VISIBLE;

CREATE INDEX "INDEX_KEY"
    ON "AGENT_INDEX_CREATE_RECORD" ("INDEX_NAME");
ALTER
INDEX "INDEX_KEY" VISIBLE;

CREATE INDEX "INDEX83841708971900"
    ON "AGENT_PLUGIN_BIND" ("AGENT_CODE", "API_ID", "YN");
ALTER
INDEX "INDEX83841708971900" VISIBLE;

CREATE INDEX "IDX_APP_CODE"
    ON "AGENT_WORKFLOW_BIND" ("APP_CODE");
ALTER
INDEX "IDX_APP_CODE" VISIBLE;

CREATE INDEX "IDX_CREATE_TIME"
    ON "AGENT_WORKFLOW_BIND" ("CREATE_TIME");
ALTER
INDEX "IDX_CREATE_TIME" VISIBLE;

CREATE INDEX "IDX_WORKFLOW_ID"
    ON "AGENT_WORKFLOW_BIND" ("WORKFLOW_ID");
ALTER
INDEX "IDX_WORKFLOW_ID" VISIBLE;

CREATE INDEX "INDEX83841718928800"
    ON "AGENT_WORKFLOW_BIND" ("AGENT_CODE");
ALTER
INDEX "INDEX83841718928800" VISIBLE;

CREATE INDEX "IDX_APP_NAME"
    ON "APP" ("TENANT_ID", "NAME");
ALTER
INDEX "IDX_APP_NAME" VISIBLE;

CREATE INDEX "INDEX83841774071800"
    ON "APP" ("CODE");
ALTER
INDEX "INDEX83841774071800" VISIBLE;

CREATE INDEX "IDX_ATTACHMENT_FILE_KEY"
    ON "ATTACHMENT" ("FILE_KEY");
ALTER
INDEX "IDX_ATTACHMENT_FILE_KEY" VISIBLE;

CREATE INDEX "BOT_WORKFLOW_APP_CODE_INDEX"
    ON "BOT_WORKFLOW" ("APP_CODE");
ALTER
INDEX "BOT_WORKFLOW_APP_CODE_INDEX" VISIBLE;

CREATE INDEX "BOT_WORKFLOW_ID_INDEX"
    ON "BOT_WORKFLOW" ("WORKFLOW_ID", "VERSION");
ALTER
INDEX "BOT_WORKFLOW_ID_INDEX" VISIBLE;

CREATE INDEX "BOT_WORKFLOW_EDGE_ID_INDEX"
    ON "BOT_WORKFLOW_EDGE" ("WORKFLOW_ID", "APP_CODE", "VERSION");
ALTER
INDEX "BOT_WORKFLOW_EDGE_ID_INDEX" VISIBLE;

CREATE INDEX "BOT_WORKFLOW_NODE_INDEX"
    ON "BOT_WORKFLOW_NODE" ("WORKFLOW_ID", "NODE_ID", "APP_CODE", "VERSION");
ALTER
INDEX "BOT_WORKFLOW_NODE_INDEX" VISIBLE;

CREATE INDEX "IDX_CATALOG"
    ON "CATALOG" ("CODE");
ALTER
INDEX "IDX_CATALOG" VISIBLE;

CREATE INDEX "IDX_CATALOG_KNOWLEDGE"
    ON "CATALOG" ("KNOWLEDGE_CODE");
ALTER
INDEX "IDX_CATALOG_KNOWLEDGE" VISIBLE;

CREATE INDEX "IDX_CATALOG_RELATION"
    ON "CATALOG" ("KNOWLEDGE_CODE", "PARENT_CODE", "PREV_CODE");
ALTER
INDEX "IDX_CATALOG_RELATION" VISIBLE;

CREATE INDEX "IDX_COLLECTION_APP_TARGET"
    ON "COLLECTION" ("APP_CODE", "TARGET_CODE", "TARGET_TYPE");
ALTER
INDEX "IDX_COLLECTION_APP_TARGET" VISIBLE;

CREATE INDEX "IDX_COLLECTION_CODE"
    ON "COLLECTION" ("APP_CODE", "CODE");
ALTER
INDEX "IDX_COLLECTION_CODE" VISIBLE;

CREATE INDEX "IDX_COLLECTION_TARGET"
    ON "COLLECTION" ("YN", "TARGET_CODE", "TARGET_TYPE");
ALTER
INDEX "IDX_COLLECTION_TARGET" VISIBLE;

CREATE INDEX "IDX_FAQ_SIMILAR_KNOWLEDGE_CODE"
    ON "FAQ_SIMILAR_QUESTION" ("APP_CODE", "KNOWLEDGE_CODE");
ALTER
INDEX "IDX_FAQ_SIMILAR_KNOWLEDGE_CODE" VISIBLE;

CREATE INDEX "IDX_FAQ_SIMILAR_QUESTION"
    ON "FAQ_SIMILAR_QUESTION" ("SIMILAR_QUESTION");
ALTER
INDEX "IDX_FAQ_SIMILAR_QUESTION" VISIBLE;

CREATE INDEX "IDX_FAQ_SIMILAR_QUESTION_CODE"
    ON "FAQ_SIMILAR_QUESTION" ("APP_CODE", "CODE");
ALTER
INDEX "IDX_FAQ_SIMILAR_QUESTION_CODE" VISIBLE;

CREATE INDEX "FILE_CODE_INDEX"
    ON "GS_SESSION_FILE" ("FILE_CODE", "SESSION_ID");
ALTER
INDEX "FILE_CODE_INDEX" VISIBLE;

CREATE INDEX "SESSION_ID_INDEX"
    ON "GS_SESSION_FILE" ("SESSION_ID");
ALTER
INDEX "SESSION_ID_INDEX" VISIBLE;

CREATE INDEX "INDEX83841967584200"
    ON "KNOWLEDGE_BASE" ("CODE");
ALTER
INDEX "INDEX83841967584200" VISIBLE;

CREATE INDEX "INDEX83841976025100"
    ON "KNOWLEDGE_BASE" ("APP_CODE", "CODE");
ALTER
INDEX "INDEX83841976025100" VISIBLE;

CREATE INDEX "IDX_KNOWLEDGE_CODE"
    ON "KNOWLEDGE_ESSENTIAL" ("KNOWLEDGE_CODE");
ALTER
INDEX "IDX_KNOWLEDGE_CODE" VISIBLE;

CREATE INDEX "IDX_EXTRACT_YN_CODE"
    ON "KNOWLEDGE_EXTRACT" ("APP_CODE", "CODE");
ALTER
INDEX "IDX_EXTRACT_YN_CODE" VISIBLE;

CREATE INDEX "IDX_KNOWLEDGE_EXTRA_INFO_KNOWLEDGE"
    ON "KNOWLEDGE_EXTRA_INFO" ("KNOWLEDGE_CODE", "EXTRA_KEY");
ALTER
INDEX "IDX_KNOWLEDGE_EXTRA_INFO_KNOWLEDGE" VISIBLE;

CREATE INDEX "IDX_KNOWLEDGE_FAQ_CODE"
    ON "KNOWLEDGE_FAQ" ("APP_CODE", "CODE");
ALTER
INDEX "IDX_KNOWLEDGE_FAQ_CODE" VISIBLE;

CREATE INDEX "IDX_KNOWLEDGE_FAQ_KNOWLEDGE_CODE"
    ON "KNOWLEDGE_FAQ" ("APP_CODE", "KNOWLEDGE_CODE");
ALTER
INDEX "IDX_KNOWLEDGE_FAQ_KNOWLEDGE_CODE" VISIBLE;

CREATE INDEX "IDX_KNOWLEDGE_FAQ_KNOWLEDGE_ID"
    ON "KNOWLEDGE_FAQ" ("KNOWLEDGE_CODE");
ALTER
INDEX "IDX_KNOWLEDGE_FAQ_KNOWLEDGE_ID" VISIBLE;

CREATE INDEX "INDEX83842054246600"
    ON "KNOWLEDGE_FAQ_PROD" ("KNOWLEDGE_CODE");
ALTER
INDEX "INDEX83842054246600" VISIBLE;

CREATE INDEX "KNOWLEDGE_CODE"
    ON "KNOWLEDGE_FAQ_PROD" ("CODE");
ALTER
INDEX "KNOWLEDGE_CODE" VISIBLE;

CREATE INDEX "INDEX83842072758500"
    ON "KNOWLEDGE_PROD" ("CODE");
ALTER
INDEX "INDEX83842072758500" VISIBLE;

CREATE INDEX "INDEX83842081541300"
    ON "KNOWLEDGE_REPORT" ("CODE");
ALTER
INDEX "INDEX83842081541300" VISIBLE;

CREATE INDEX "INDEX83842089995900"
    ON "KNOWLEDGE_REPORT" ("APP_CODE", "CODE");
ALTER
INDEX "INDEX83842089995900" VISIBLE;

CREATE INDEX "API_ID_KEY"
    ON "PLUGIN_API_META_INFO" ("API_ID", "PLUGIN_ID", "VERSION", "YN");
ALTER
INDEX "API_ID_KEY" VISIBLE;

CREATE INDEX "PARAM_ID_KEY"
    ON "PLUGIN_API_PARAM" ("PARAM_ID", "PLUGIN_ID", "API_ID", "VERSION", "YN");
ALTER
INDEX "PARAM_ID_KEY" VISIBLE;

CREATE INDEX "INDEX83842119964700"
    ON "PLUGIN_BIND_CARD" ("API_ID", "PLUGIN_ID", "AGENT_CODE", "YN");
ALTER
INDEX "INDEX83842119964700" VISIBLE;

CREATE INDEX "PLUGIN_ID_KEY"
    ON "PLUGIN_MALL_INFO" ("PLUGIN_ID", "CATEGORY_CODE", "YN");
ALTER
INDEX "PLUGIN_ID_KEY" VISIBLE;

CREATE INDEX "INDEX83842143035100"
    ON "PLUGIN_META_INFO" ("PLUGIN_ID", "VERSION", "YN");
ALTER
INDEX "INDEX83842143035100" VISIBLE;

CREATE INDEX "IDX_PROMPT_TEMPLATE_CODE"
    ON "PROMPT_TEMPLATE" ("APP_CODE", "CODE");
ALTER
INDEX "IDX_PROMPT_TEMPLATE_CODE" VISIBLE;

CREATE INDEX "IDX_PROMPT_TEMPLATE_NAME"
    ON "PROMPT_TEMPLATE" ("APP_CODE", "NAME");
ALTER
INDEX "IDX_PROMPT_TEMPLATE_NAME" VISIBLE;

CREATE INDEX "BOT_WORKFLOW_VERSION_ID_INDEX"
    ON "WORKFLOW_VERSION_INFO" ("WORKFLOW_ID", "VERSION");
ALTER
INDEX "BOT_WORKFLOW_VERSION_ID_INDEX" VISIBLE;

ALTER TABLE "AGENT_AI_MODEL"
    ADD CONSTRAINT "AI_MODEL_CONFIG" CHECK ("EXTERNAL_MODEL_CONFIG" IS JSON ) ENABLE;

ALTER TABLE "AGENT_CONFIG"
    ADD CONSTRAINT "CONFIG_KEY_UNIQUE" UNIQUE ("AGENT_CODE", "CONFIG_KEY", "VERSION", "YN");

ALTER TABLE "AGENT_DIALOG_COUNT_RECORD"
    ADD CONSTRAINT "AGENT_CODE_KEY" UNIQUE ("AGENT_CODE");

ALTER TABLE "AGENT_DIALOG_RECORD"
    ADD CONSTRAINT "SESSION_ID_KEY" UNIQUE ("SESSION_ID", "YN");

ALTER TABLE "AGENT_QUESTION_HIT_RECORD"
    ADD CONSTRAINT "UK_MESSAGE_ID" UNIQUE ("KNOWLEDGE_ID", "MESSAGE_ID");

ALTER TABLE "AGENT_VERSION_INFO"
    ADD CONSTRAINT "UK_AGENT_VERSION" UNIQUE ("AGENT_CODE", "VERSION", "YN");

ALTER TABLE "APP_ROLE"
    ADD CONSTRAINT "UK_APP_ROLE" UNIQUE ("CODE", "APP_CODE", "ROLE_TYPE");

ALTER TABLE "CHANNEL_CONFIG_INFO"
    ADD CONSTRAINT "UK_CHANNELS_CONFIG" UNIQUE ("CHANNEL_ID", "CONFIG_KEY", "YN");

ALTER TABLE "CHANNEL_INFO"
    ADD CONSTRAINT "UK_CHANNEL_ID" UNIQUE ("CHANNEL_ID", "YN");

ALTER TABLE "CHANNEL_MEDIA"
    ADD CONSTRAINT "UK_CORP_ID_MEDIA" UNIQUE ("CORP_ID", "MEDIA_URL", "YN", "MEDIA_ID");

ALTER TABLE "GS_BASE_CONFIG"
    ADD CONSTRAINT "IDX_BUSINESS_NO_KEY_SCENE" UNIQUE ("BUSINESS_NO", "CONFIG_TYPE");

ALTER TABLE "KNOWLEDGE"
    ADD CONSTRAINT "UK_CODE" UNIQUE ("CODE");

ALTER TABLE "KNOWLEDGE_AUDIT"
    ADD UNIQUE ("CODE");

ALTER TABLE "KNOWLEDGE_AUDIT_DATA"
    ADD UNIQUE ("CODE");

ALTER TABLE "KNOWLEDGE_FAQ_TEMP"
    ADD CONSTRAINT "KNOWLEDGE_FAQ_TEMP_UN" UNIQUE ("CODE");

ALTER TABLE "PLUGIN_VERSION_INFO"
    ADD CONSTRAINT "UK_PLUGIN_VERSION" UNIQUE ("PLUGIN_ID", "VERSION", "YN");

COMMENT
ON COLUMN "AGENT_BASIC_INFO"."AGENT_CODE" IS 'agent 编码';

COMMENT
ON COLUMN "AGENT_BASIC_INFO"."AGENT_DESC" IS 'agent 描述';

COMMENT
ON COLUMN "AGENT_BASIC_INFO"."AGENT_NAME" IS 'agent 名称';

COMMENT
ON COLUMN "AGENT_BASIC_INFO"."AGENT_PICTURE" IS 'agent 头像';

COMMENT
ON COLUMN "AGENT_BASIC_INFO"."AGENT_REPLY" IS '人设与回复';

COMMENT
ON COLUMN "AGENT_BASIC_INFO"."AGENT_TYPE" IS 'agent 类型';

COMMENT
ON COLUMN "AGENT_BASIC_INFO"."APP_CODE" IS '应用Code';

COMMENT
ON COLUMN "AGENT_BASIC_INFO"."IS_DEFAULT" IS '是否默认小智机器人，0:否，1:是';

COMMENT
ON COLUMN "AGENT_BASIC_INFO"."KMS_APP_CODE" IS '知识库空间 id';

COMMENT
ON COLUMN "AGENT_BASIC_INFO"."TEMPLATE_CATEGORY_ID" IS 'agent 类型';

COMMENT
ON COLUMN "AGENT_BASIC_INFO"."YN" IS '记录删除标识';

COMMENT
ON TABLE "AGENT_DEFAULT_CONFIG" IS 'agent 默认配置表';

COMMENT
ON COLUMN "AGENT_DEFAULT_CONFIG"."CATEGORY" IS '配置分类';

COMMENT
ON COLUMN "AGENT_DEFAULT_CONFIG"."CONFIG_KEY" IS '配置内容的 key';

COMMENT
ON COLUMN "AGENT_DEFAULT_CONFIG"."CONFIG_VALUE" IS '配置详细内容';

COMMENT
ON COLUMN "AGENT_DEFAULT_CONFIG"."CREATE_ID" IS '创建用户 ID';

COMMENT
ON COLUMN "AGENT_DEFAULT_CONFIG"."CREATE_NAME" IS '记录创建人名称';

COMMENT
ON COLUMN "AGENT_DEFAULT_CONFIG"."CREATE_TIME" IS '记录创建时间';

COMMENT
ON COLUMN "AGENT_DEFAULT_CONFIG"."ID" IS '自增 id';

COMMENT
ON COLUMN "AGENT_DEFAULT_CONFIG"."TYPE" IS '配置类型';

COMMENT
ON COLUMN "AGENT_DEFAULT_CONFIG"."UPDATE_ID" IS '最后修改用户 ID';

COMMENT
ON COLUMN "AGENT_DEFAULT_CONFIG"."UPDATE_NAME" IS '最后一次记录操作人';

COMMENT
ON COLUMN "AGENT_DEFAULT_CONFIG"."UPDATE_TIME" IS '记录最后一次更新时间';

COMMENT
ON COLUMN "AGENT_DEFAULT_CONFIG"."VERSION" IS '配置版本';

COMMENT
ON COLUMN "AGENT_DEFAULT_CONFIG"."YN" IS '记录删除标识';

COMMENT
ON TABLE "AGENT_DIALOG_COUNT_RECORD" IS '会话统计记录表';

COMMENT
ON COLUMN "AGENT_DIALOG_COUNT_RECORD"."AGENT_CODE" IS 'agent 编码';

COMMENT
ON COLUMN "AGENT_DIALOG_COUNT_RECORD"."CREATE_TIME" IS '记录创建时间';

COMMENT
ON COLUMN "AGENT_DIALOG_COUNT_RECORD"."ID" IS '自增 id';

COMMENT
ON COLUMN "AGENT_DIALOG_COUNT_RECORD"."UPDATE_TIME" IS '记录最后一次更新时间';

COMMENT
ON TABLE "AGENT_INDEX_CREATE_RECORD" IS '索引创建记录表';

COMMENT
ON COLUMN "AGENT_INDEX_CREATE_RECORD"."APP_CODE" IS '编码';

COMMENT
ON COLUMN "AGENT_INDEX_CREATE_RECORD"."CREATE_TIME" IS '记录创建时间';

COMMENT
ON COLUMN "AGENT_INDEX_CREATE_RECORD"."FAIL_REASON" IS '失败原因';

COMMENT
ON COLUMN "AGENT_INDEX_CREATE_RECORD"."ID" IS '自增 id';

COMMENT
ON COLUMN "AGENT_INDEX_CREATE_RECORD"."INDEX_NAME" IS '索引名称';

COMMENT
ON COLUMN "AGENT_INDEX_CREATE_RECORD"."STATE" IS '是否成功,1:成功，0：失败';

COMMENT
ON COLUMN "AGENT_INDEX_CREATE_RECORD"."TENANT_ID" IS '支撑平台租户 id';

COMMENT
ON COLUMN "AGENT_INDEX_CREATE_RECORD"."TYPE" IS '类型';

COMMENT
ON COLUMN "AGENT_INDEX_CREATE_RECORD"."UPDATE_TIME" IS '记录最后一次更新时间';

COMMENT
ON COLUMN "AGENT_INDEX_CREATE_RECORD"."YN" IS '记录删除标识';

COMMENT
ON TABLE "AGENT_KNOWLEDGE_BASE" IS 'agent 和知识库关联表';

COMMENT
ON COLUMN "AGENT_KNOWLEDGE_BASE"."APP_CODE" IS '应用Code';

COMMENT
ON COLUMN "AGENT_KNOWLEDGE_BASE"."CREATE_ID" IS '记录创建人 id';

COMMENT
ON COLUMN "AGENT_KNOWLEDGE_BASE"."CREATE_NAME" IS '记录创建人名称';

COMMENT
ON COLUMN "AGENT_KNOWLEDGE_BASE"."CREATE_TIME" IS '记录创建时间';

COMMENT
ON COLUMN "AGENT_KNOWLEDGE_BASE"."TYPE" IS '知识库类型';

COMMENT
ON COLUMN "AGENT_KNOWLEDGE_BASE"."UPDATE_ID" IS '最后修改用户 ID';

COMMENT
ON COLUMN "AGENT_KNOWLEDGE_BASE"."UPDATE_NAME" IS '最后一次记录操作人';

COMMENT
ON COLUMN "AGENT_KNOWLEDGE_BASE"."UPDATE_TIME" IS '记录最后一次更新时间';

COMMENT
ON COLUMN "AGENT_KNOWLEDGE_BASE"."YN" IS '记录删除标识';

COMMENT
ON TABLE "AGENT_PLUGIN_BIND" IS '插件订阅关系表';

COMMENT
ON COLUMN "AGENT_PLUGIN_BIND"."AGENT_CODE" IS 'agent编码';

COMMENT
ON COLUMN "AGENT_PLUGIN_BIND"."API_ID" IS '工具id';

COMMENT
ON COLUMN "AGENT_PLUGIN_BIND"."CREATE_ID" IS '创建用户ID';

COMMENT
ON COLUMN "AGENT_PLUGIN_BIND"."CREATE_NAME" IS '记录创建人名称';

COMMENT
ON COLUMN "AGENT_PLUGIN_BIND"."CREATE_TIME" IS '记录创建时间';

COMMENT
ON COLUMN "AGENT_PLUGIN_BIND"."ID" IS '自增 id';

COMMENT
ON COLUMN "AGENT_PLUGIN_BIND"."PLUGIN_ID" IS '插件id';

COMMENT
ON COLUMN "AGENT_PLUGIN_BIND"."UPDATE_ID" IS '最后修改用户ID';

COMMENT
ON COLUMN "AGENT_PLUGIN_BIND"."UPDATE_NAME" IS '最后一次记录操作人';

COMMENT
ON COLUMN "AGENT_PLUGIN_BIND"."UPDATE_TIME" IS '记录最后一次更新时间';

COMMENT
ON COLUMN "AGENT_PLUGIN_BIND"."YN" IS '记录删除标识';

COMMENT
ON COLUMN "AGENT_WORKFLOW_BIND"."AGENT_CODE" IS 'agentCode';

COMMENT
ON COLUMN "AGENT_WORKFLOW_BIND"."AGENT_VERSION" IS '版本';

COMMENT
ON COLUMN "AGENT_WORKFLOW_BIND"."APP_CODE" IS '空间id';

COMMENT
ON COLUMN "AGENT_WORKFLOW_BIND"."ID" IS '自增 id';

COMMENT
ON COLUMN "AGENT_WORKFLOW_BIND"."WORKFLOW_ID" IS '工作流id';

COMMENT
ON TABLE "APP" IS '应用表';

COMMENT
ON COLUMN "APP"."BOT_CODE" IS '应用下的机器人Code';

COMMENT
ON COLUMN "APP"."CODE" IS 'Code';

COMMENT
ON COLUMN "APP"."CREATE_ID" IS '创建用户ID';

COMMENT
ON COLUMN "APP"."CREATE_NAME" IS '创建用户名';

COMMENT
ON COLUMN "APP"."CREATE_TIME" IS '创建时间';

COMMENT
ON COLUMN "APP"."DEFAULT_CATALOG_CODE" IS '默认目录Code';

COMMENT
ON COLUMN "APP"."DEFAULT_KNOWLEDGE_BASE_CODE" IS '默认知识库Code';

COMMENT
ON COLUMN "APP"."DS_BOT_CODE" IS 'ds机器人编码';

COMMENT
ON COLUMN "APP"."ID" IS '主键';

COMMENT
ON COLUMN "APP"."NAME" IS '名称';

COMMENT
ON COLUMN "APP"."PERSONAL" IS '是否属于个人';

COMMENT
ON COLUMN "APP"."TENANT_ID" IS '租户ID';

COMMENT
ON COLUMN "APP"."UPDATE_ID" IS '最后修改用户ID';

COMMENT
ON COLUMN "APP"."UPDATE_NAME" IS '最后修改用户名';

COMMENT
ON COLUMN "APP"."UPDATE_TIME" IS '更新时间';

COMMENT
ON COLUMN "APP"."YN" IS '删除标记,0:未删除,其他表示已删除';

COMMENT
ON TABLE "APP_ROLE" IS '应用权限表';

COMMENT
ON COLUMN "APP_ROLE"."APP_CODE" IS '应用Code';

COMMENT
ON COLUMN "APP_ROLE"."CODE" IS '权限Code';

COMMENT
ON COLUMN "APP_ROLE"."CREATE_ID" IS '创建用户ID';

COMMENT
ON COLUMN "APP_ROLE"."CREATE_NAME" IS '创建用户名';

COMMENT
ON COLUMN "APP_ROLE"."CREATE_TIME" IS '创建时间';

COMMENT
ON COLUMN "APP_ROLE"."ID" IS '主键';

COMMENT
ON COLUMN "APP_ROLE"."ROLE_ORDER" IS '权限排序';

COMMENT
ON COLUMN "APP_ROLE"."ROLE_TYPE" IS '权限类型';

COMMENT
ON COLUMN "APP_ROLE"."TENANT_ID" IS '租户ID';

COMMENT
ON COLUMN "APP_ROLE"."UPDATE_ID" IS '最后修改用户ID';

COMMENT
ON COLUMN "APP_ROLE"."UPDATE_NAME" IS '最后修改用户名';

COMMENT
ON COLUMN "APP_ROLE"."UPDATE_TIME" IS '更新时间';

COMMENT
ON COLUMN "APP_ROLE"."YN" IS '删除标记,0:未删除,其他表示已删除';

COMMENT
ON TABLE "ATTACHMENT" IS '附件表';

COMMENT
ON COLUMN "ATTACHMENT"."APP_CODE" IS '应用Code';

COMMENT
ON COLUMN "ATTACHMENT"."ATTACHABLE_CODE" IS '关联数据编码';

COMMENT
ON COLUMN "ATTACHMENT"."ATTACHABLE_TYPE" IS '关联数据类型';

COMMENT
ON COLUMN "ATTACHMENT"."CODE" IS '业务配置编码';

COMMENT
ON COLUMN "ATTACHMENT"."CREATE_ID" IS '创建用户ID';

COMMENT
ON COLUMN "ATTACHMENT"."CREATE_NAME" IS '创建用户名';

COMMENT
ON COLUMN "ATTACHMENT"."CREATE_TIME" IS '创建时间';

COMMENT
ON COLUMN "ATTACHMENT"."FILE_KEY" IS '文件Key';

COMMENT
ON COLUMN "ATTACHMENT"."FILE_NAME" IS '文件名称';

COMMENT
ON COLUMN "ATTACHMENT"."ID" IS '主键';

COMMENT
ON COLUMN "ATTACHMENT"."TYPE" IS '文件类型';

COMMENT
ON COLUMN "ATTACHMENT"."UPDATE_ID" IS '最后修改用户ID';

COMMENT
ON COLUMN "ATTACHMENT"."UPDATE_NAME" IS '最后修改用户名';

COMMENT
ON COLUMN "ATTACHMENT"."UPDATE_TIME" IS '更新时间';

COMMENT
ON COLUMN "ATTACHMENT"."YN" IS '删除标记,0:未删除,其他表示已删除';

COMMENT
ON COLUMN "BOT_WORKFLOW"."APP_CODE" IS '空间id';

COMMENT
ON COLUMN "BOT_WORKFLOW"."BOT_REF_COUNT" IS '引用数';

COMMENT
ON COLUMN "BOT_WORKFLOW"."CANVAS" IS '画布';

COMMENT
ON COLUMN "BOT_WORKFLOW"."DEBUG_STATUS" IS '调试状态';

COMMENT
ON COLUMN "BOT_WORKFLOW"."DESCRIPTION" IS '工作流描述';

COMMENT
ON COLUMN "BOT_WORKFLOW"."ICON_URL" IS '工作流图标';

COMMENT
ON COLUMN "BOT_WORKFLOW"."ID" IS '自增 id';

COMMENT
ON COLUMN "BOT_WORKFLOW"."SCENE" IS '场景分类';

COMMENT
ON COLUMN "BOT_WORKFLOW"."STATUS" IS '状态, 1-未发布,2-已发布,3-修复待发布';

COMMENT
ON COLUMN "BOT_WORKFLOW"."VERSION" IS '版本';

COMMENT
ON COLUMN "BOT_WORKFLOW"."WORKFLOW_CN_NAME" IS '工作流应用名称';

COMMENT
ON COLUMN "BOT_WORKFLOW"."WORKFLOW_ID" IS '工作流id';

COMMENT
ON COLUMN "BOT_WORKFLOW"."WORKFLOW_NAME" IS '工作流名称';

COMMENT
ON COLUMN "BOT_WORKFLOW_EDGE"."ID" IS '自增 id';

COMMENT
ON COLUMN "BOT_WORKFLOW_NODE"."ID" IS '自增 id';

COMMENT
ON COLUMN "BOT_WORKFLOW_SCENE"."APP_CODE" IS '空间id';

COMMENT
ON COLUMN "BOT_WORKFLOW_SCENE"."SCENE" IS '工作流id';

COMMENT
ON COLUMN "BOT_WORKFLOW_SCENE"."WORKFLOW_ID" IS '工作流id';

COMMENT
ON TABLE "CATALOG" IS '目录表';

COMMENT
ON COLUMN "CATALOG"."APP_CODE" IS '应用Code';

COMMENT
ON COLUMN "CATALOG"."CODE" IS 'Code';

COMMENT
ON COLUMN "CATALOG"."CREATE_ID" IS '创建用户ID';

COMMENT
ON COLUMN "CATALOG"."CREATE_NAME" IS '创建用户名';

COMMENT
ON COLUMN "CATALOG"."CREATE_TIME" IS '创建时间';

COMMENT
ON COLUMN "CATALOG"."DESCRIPTION" IS '描述';

COMMENT
ON COLUMN "CATALOG"."ID" IS '主键';

COMMENT
ON COLUMN "CATALOG"."KNOWLEDGE_BASE_CODE" IS '所属知识库Code';

COMMENT
ON COLUMN "CATALOG"."KNOWLEDGE_CODE" IS '关联知识';

COMMENT
ON COLUMN "CATALOG"."LEVEL" IS '层级';

COMMENT
ON COLUMN "CATALOG"."NAME" IS '名称';

COMMENT
ON COLUMN "CATALOG"."PARENT_CODE" IS '父目录Code';

COMMENT
ON COLUMN "CATALOG"."PREV_CODE" IS '同层级前置目录Code';

COMMENT
ON COLUMN "CATALOG"."TYPE" IS '目录类型';

COMMENT
ON COLUMN "CATALOG"."UPDATE_ID" IS '最后修改用户ID';

COMMENT
ON COLUMN "CATALOG"."UPDATE_NAME" IS '最后修改用户名';

COMMENT
ON COLUMN "CATALOG"."UPDATE_TIME" IS '更新时间';

COMMENT
ON COLUMN "CATALOG"."YN" IS '删除标记,0:未删除,其他表示已删除';

COMMENT
ON TABLE "CHANNEL_API_SECRET" IS 'API秘钥管理';

COMMENT
ON COLUMN "CHANNEL_API_SECRET"."APP_ID" IS '应用id';

COMMENT
ON COLUMN "CHANNEL_API_SECRET"."CHANNEL_ID" IS '渠道id';

COMMENT
ON COLUMN "CHANNEL_API_SECRET"."CREATE_ID" IS '创建用户ID';

COMMENT
ON COLUMN "CHANNEL_API_SECRET"."CREATE_NAME" IS '创建人名称';

COMMENT
ON COLUMN "CHANNEL_API_SECRET"."CREATE_TIME" IS '创建时间';

COMMENT
ON COLUMN "CHANNEL_API_SECRET"."ID" IS '主键';

COMMENT
ON COLUMN "CHANNEL_API_SECRET"."NAME" IS '名称';

COMMENT
ON COLUMN "CHANNEL_API_SECRET"."PROMPT_ID" IS '提示词模板';

COMMENT
ON COLUMN "CHANNEL_API_SECRET"."SECRET" IS '秘钥';

COMMENT
ON COLUMN "CHANNEL_API_SECRET"."SECRET_ID" IS '秘钥id';

COMMENT
ON COLUMN "CHANNEL_API_SECRET"."SECRET_TYPE" IS '秘钥类型';

COMMENT
ON COLUMN "CHANNEL_API_SECRET"."TENANT_ID" IS '租户id';

COMMENT
ON COLUMN "CHANNEL_API_SECRET"."UPDATE_ID" IS '最后修改用户ID';

COMMENT
ON COLUMN "CHANNEL_API_SECRET"."UPDATE_NAME" IS '更新人名称';

COMMENT
ON COLUMN "CHANNEL_API_SECRET"."UPDATE_TIME" IS '更新时间';

COMMENT
ON COLUMN "CHANNEL_API_SECRET"."YN" IS '是否有效;是否有效：0-有效，非0-无效';

COMMENT
ON TABLE "CHANNEL_CONFIG_INFO" IS '渠道配置信息表';

COMMENT
ON COLUMN "CHANNEL_CONFIG_INFO"."CHANNEL_ID" IS '渠道id';

COMMENT
ON COLUMN "CHANNEL_CONFIG_INFO"."CONFIG_KEY" IS '配置key';

COMMENT
ON COLUMN "CHANNEL_CONFIG_INFO"."CONFIG_VALUE" IS '配置值';

COMMENT
ON COLUMN "CHANNEL_CONFIG_INFO"."CREATE_ID" IS '记录创建人id';

COMMENT
ON COLUMN "CHANNEL_CONFIG_INFO"."CREATE_NAME" IS '记录创建人名称';

COMMENT
ON COLUMN "CHANNEL_CONFIG_INFO"."CREATE_TIME" IS '记录创建时间';

COMMENT
ON COLUMN "CHANNEL_CONFIG_INFO"."ID" IS '自增id';

COMMENT
ON COLUMN "CHANNEL_CONFIG_INFO"."TENANT_ID" IS '支撑平台租户id';

COMMENT
ON COLUMN "CHANNEL_CONFIG_INFO"."TENANT_NAME" IS '支撑平台租户名称';

COMMENT
ON COLUMN "CHANNEL_CONFIG_INFO"."UPDATE_ID" IS '最后一次操作人id';

COMMENT
ON COLUMN "CHANNEL_CONFIG_INFO"."UPDATE_NAME" IS '最后一次记录操作人';

COMMENT
ON COLUMN "CHANNEL_CONFIG_INFO"."UPDATE_TIME" IS '记录最后一次更新时间';

COMMENT
ON COLUMN "CHANNEL_CONFIG_INFO"."YN" IS '记录删除标识';

COMMENT
ON TABLE "CHANNEL_INFO" IS '渠道基础信息表';

COMMENT
ON COLUMN "CHANNEL_INFO"."AGENT_CODE" IS '机器人code（已更名为agent_code）';

COMMENT
ON COLUMN "CHANNEL_INFO"."CHANNEL_ID" IS '渠道id';

COMMENT
ON COLUMN "CHANNEL_INFO"."CHANNEL_NAME" IS '渠道名称';

COMMENT
ON COLUMN "CHANNEL_INFO"."CHANNEL_TYPE" IS '渠道类型';

COMMENT
ON COLUMN "CHANNEL_INFO"."CREATE_ID" IS '记录创建人id';

COMMENT
ON COLUMN "CHANNEL_INFO"."CREATE_NAME" IS '记录创建人名称';

COMMENT
ON COLUMN "CHANNEL_INFO"."CREATE_TIME" IS '记录创建时间';

COMMENT
ON COLUMN "CHANNEL_INFO"."ENABLE" IS '是否启用';

COMMENT
ON COLUMN "CHANNEL_INFO"."ID" IS '自增id';

COMMENT
ON COLUMN "CHANNEL_INFO"."TENANT_ID" IS '支撑平台租户id';

COMMENT
ON COLUMN "CHANNEL_INFO"."TENANT_NAME" IS '支撑平台租户名称';

COMMENT
ON COLUMN "CHANNEL_INFO"."UPDATE_ID" IS '最后一次操作人id';

COMMENT
ON COLUMN "CHANNEL_INFO"."UPDATE_NAME" IS '最后一次记录操作人';

COMMENT
ON COLUMN "CHANNEL_INFO"."UPDATE_TIME" IS '记录最后一次更新时间';

COMMENT
ON COLUMN "CHANNEL_INFO"."YN" IS '记录删除标识';

COMMENT
ON TABLE "CHANNEL_MEDIA" IS '渠道媒体信息表';

COMMENT
ON COLUMN "CHANNEL_MEDIA"."CORP_ID" IS '企业id';

COMMENT
ON COLUMN "CHANNEL_MEDIA"."CREATE_ID" IS '记录创建人id';

COMMENT
ON COLUMN "CHANNEL_MEDIA"."CREATE_NAME" IS '记录创建人名称';

COMMENT
ON COLUMN "CHANNEL_MEDIA"."CREATE_TIME" IS '记录创建时间';

COMMENT
ON COLUMN "CHANNEL_MEDIA"."EXPIRE_TIME" IS '媒体过期时间';

COMMENT
ON COLUMN "CHANNEL_MEDIA"."ID" IS '自增id';

COMMENT
ON COLUMN "CHANNEL_MEDIA"."MEDIA_ID" IS '媒体id';

COMMENT
ON COLUMN "CHANNEL_MEDIA"."MEDIA_TYPE" IS '媒体类型';

COMMENT
ON COLUMN "CHANNEL_MEDIA"."MEDIA_URL" IS '媒体访问地址';

COMMENT
ON COLUMN "CHANNEL_MEDIA"."TENANT_ID" IS '支撑平台租户id';

COMMENT
ON COLUMN "CHANNEL_MEDIA"."TENANT_NAME" IS '支撑平台租户名称';

COMMENT
ON COLUMN "CHANNEL_MEDIA"."UPDATE_ID" IS '最后一次操作人id';

COMMENT
ON COLUMN "CHANNEL_MEDIA"."UPDATE_NAME" IS '最后一次记录操作人';

COMMENT
ON COLUMN "CHANNEL_MEDIA"."UPDATE_TIME" IS '记录最后一次更新时间';

COMMENT
ON COLUMN "CHANNEL_MEDIA"."YN" IS '记录删除标识';

COMMENT
ON TABLE "CHANNEL_MESSAGE_RECORD" IS '消息记录表';

COMMENT
ON COLUMN "CHANNEL_MESSAGE_RECORD"."CHANNEL_ID" IS '渠道id';

COMMENT
ON COLUMN "CHANNEL_MESSAGE_RECORD"."CREATE_ID" IS '记录创建人id';

COMMENT
ON COLUMN "CHANNEL_MESSAGE_RECORD"."CREATE_NAME" IS '记录创建人名称';

COMMENT
ON COLUMN "CHANNEL_MESSAGE_RECORD"."CREATE_TIME" IS '记录创建时间';

COMMENT
ON COLUMN "CHANNEL_MESSAGE_RECORD"."ID" IS '自增id';

COMMENT
ON COLUMN "CHANNEL_MESSAGE_RECORD"."MESSAGE" IS '消息内容';

COMMENT
ON COLUMN "CHANNEL_MESSAGE_RECORD"."MESSAGE_ID" IS '消息id';

COMMENT
ON COLUMN "CHANNEL_MESSAGE_RECORD"."MSG_DIRECTION" IS '消息方向，USER-用户输入；BOT-机器人输出';

COMMENT
ON COLUMN "CHANNEL_MESSAGE_RECORD"."ROBOT_CODE" IS '机器人编码';

COMMENT
ON COLUMN "CHANNEL_MESSAGE_RECORD"."SESSION_ID" IS '会话id';

COMMENT
ON COLUMN "CHANNEL_MESSAGE_RECORD"."TENANT_ID" IS '支撑平台租户id';

COMMENT
ON COLUMN "CHANNEL_MESSAGE_RECORD"."TENANT_NAME" IS '支撑平台租户名称';

COMMENT
ON COLUMN "CHANNEL_MESSAGE_RECORD"."UPDATE_ID" IS '最后一次操作人id';

COMMENT
ON COLUMN "CHANNEL_MESSAGE_RECORD"."UPDATE_NAME" IS '最后一次记录操作人';

COMMENT
ON COLUMN "CHANNEL_MESSAGE_RECORD"."UPDATE_TIME" IS '记录最后一次更新时间';

COMMENT
ON COLUMN "CHANNEL_MESSAGE_RECORD"."USER_ID" IS '用户id';

COMMENT
ON COLUMN "CHANNEL_MESSAGE_RECORD"."YN" IS '记录删除标识';

COMMENT
ON TABLE "COLLECTION" IS '收藏表';

COMMENT
ON COLUMN "COLLECTION"."APP_CODE" IS '应用Code';

COMMENT
ON COLUMN "COLLECTION"."CODE" IS 'Code';

COMMENT
ON COLUMN "COLLECTION"."CREATE_ID" IS '创建用户ID';

COMMENT
ON COLUMN "COLLECTION"."CREATE_NAME" IS '创建用户名';

COMMENT
ON COLUMN "COLLECTION"."CREATE_TIME" IS '创建时间';

COMMENT
ON COLUMN "COLLECTION"."ID" IS '主键';

COMMENT
ON COLUMN "COLLECTION"."TARGET_CODE" IS '关联知识或知识库code';

COMMENT
ON COLUMN "COLLECTION"."TARGET_TYPE" IS '关联类型';

COMMENT
ON COLUMN "COLLECTION"."UPDATE_ID" IS '最后修改用户ID';

COMMENT
ON COLUMN "COLLECTION"."UPDATE_NAME" IS '最后修改用户名';

COMMENT
ON COLUMN "COLLECTION"."UPDATE_TIME" IS '更新时间';

COMMENT
ON COLUMN "COLLECTION"."YN" IS '删除标记,0:未删除,其他表示已删除';

COMMENT
ON TABLE "FAQ_SIMILAR_QUESTION" IS '相似问表';

COMMENT
ON COLUMN "FAQ_SIMILAR_QUESTION"."APP_CODE" IS '应用Code';

COMMENT
ON COLUMN "FAQ_SIMILAR_QUESTION"."CODE" IS '业务配置编码';

COMMENT
ON COLUMN "FAQ_SIMILAR_QUESTION"."CREATE_ID" IS '创建用户ID';

COMMENT
ON COLUMN "FAQ_SIMILAR_QUESTION"."CREATE_NAME" IS '创建用户名';

COMMENT
ON COLUMN "FAQ_SIMILAR_QUESTION"."CREATE_TIME" IS '创建时间';

COMMENT
ON COLUMN "FAQ_SIMILAR_QUESTION"."FAQ_CODE" IS '关联问答对code';

COMMENT
ON COLUMN "FAQ_SIMILAR_QUESTION"."ID" IS '主键';

COMMENT
ON COLUMN "FAQ_SIMILAR_QUESTION"."KNOWLEDGE_BASE_CODE" IS '所属知识库Code';

COMMENT
ON COLUMN "FAQ_SIMILAR_QUESTION"."KNOWLEDGE_CODE" IS '关联知识code';

COMMENT
ON COLUMN "FAQ_SIMILAR_QUESTION"."SIMILAR_QUESTION" IS '问题';

COMMENT
ON COLUMN "FAQ_SIMILAR_QUESTION"."UPDATE_ID" IS '最后修改用户ID';

COMMENT
ON COLUMN "FAQ_SIMILAR_QUESTION"."UPDATE_NAME" IS '最后修改用户名';

COMMENT
ON COLUMN "FAQ_SIMILAR_QUESTION"."UPDATE_TIME" IS '更新时间';

COMMENT
ON COLUMN "FAQ_SIMILAR_QUESTION"."YN" IS '删除标记,0:未删除,其他表示已删除';

COMMENT
ON TABLE "FILE" IS '文件缓存表';

COMMENT
ON COLUMN "FILE"."ACCESS_TIME" IS '最近访问时间';

COMMENT
ON COLUMN "FILE"."APP_CODE" IS '应用Code';

COMMENT
ON COLUMN "FILE"."CODE" IS '业务配置编码';

COMMENT
ON COLUMN "FILE"."CREATE_ID" IS '创建用户ID';

COMMENT
ON COLUMN "FILE"."CREATE_NAME" IS '创建用户名';

COMMENT
ON COLUMN "FILE"."CREATE_TIME" IS '创建时间';

COMMENT
ON COLUMN "FILE"."FILE_KEY" IS '文件路径';

COMMENT
ON COLUMN "FILE"."ID" IS '主键';

COMMENT
ON COLUMN "FILE"."MD5" IS 'md5';

COMMENT
ON COLUMN "FILE"."TOTAL_SIZE" IS '文件大小';

COMMENT
ON COLUMN "FILE"."UPDATE_ID" IS '最后修改用户ID';

COMMENT
ON COLUMN "FILE"."UPDATE_NAME" IS '最后修改用户名';

COMMENT
ON COLUMN "FILE"."UPDATE_TIME" IS '更新时间';

COMMENT
ON COLUMN "FILE"."YN" IS '删除标记,0:未删除,其他表示已删除';

COMMENT
ON TABLE "GS_BASE_CONFIG" IS '公共配置信息表';

COMMENT
ON COLUMN "GS_BASE_CONFIG"."BUSINESS_NO" IS '业务场景配置唯一标识';

COMMENT
ON COLUMN "GS_BASE_CONFIG"."CODE" IS '业务配置编码';

COMMENT
ON COLUMN "GS_BASE_CONFIG"."CONFIG_DATA" IS '配置json格式value值';

COMMENT
ON COLUMN "GS_BASE_CONFIG"."CONFIG_TYPE" IS '配置类型';

COMMENT
ON COLUMN "GS_BASE_CONFIG"."CREATE_ID" IS '创建用户ID';

COMMENT
ON COLUMN "GS_BASE_CONFIG"."CREATE_NAME" IS '创建用户名';

COMMENT
ON COLUMN "GS_BASE_CONFIG"."CREATE_TIME" IS '创建时间';

COMMENT
ON COLUMN "GS_BASE_CONFIG"."DIMENSION" IS '配置维度:系统维度、租户维度、应用维度、业务维度';

COMMENT
ON COLUMN "GS_BASE_CONFIG"."ID" IS '主键';

COMMENT
ON COLUMN "GS_BASE_CONFIG"."NAME" IS '名称';

COMMENT
ON COLUMN "GS_BASE_CONFIG"."OBJ_CLASS" IS '配置json的class对象';

COMMENT
ON COLUMN "GS_BASE_CONFIG"."UPDATE_ID" IS '最后修改用户ID';

COMMENT
ON COLUMN "GS_BASE_CONFIG"."UPDATE_NAME" IS '最后修改用户名';

COMMENT
ON COLUMN "GS_BASE_CONFIG"."UPDATE_TIME" IS '更新时间';

COMMENT
ON COLUMN "GS_BASE_CONFIG"."YN" IS '删除标记,0:未删除,其他表示已删除';

COMMENT
ON TABLE "GS_SESSION_FILE" IS '会话文件';

COMMENT
ON COLUMN "GS_SESSION_FILE"."BIZ_CODE" IS '业务编码';

COMMENT
ON COLUMN "GS_SESSION_FILE"."BIZ_TYPE" IS '业务类型';

COMMENT
ON COLUMN "GS_SESSION_FILE"."CREATE_ID" IS '创建用户 ID';

COMMENT
ON COLUMN "GS_SESSION_FILE"."CREATE_NAME" IS '创建用户名称';

COMMENT
ON COLUMN "GS_SESSION_FILE"."CREATE_TIME" IS '创建用户时间';

COMMENT
ON COLUMN "GS_SESSION_FILE"."EXTRA_DATA" IS '扩展配置';

COMMENT
ON COLUMN "GS_SESSION_FILE"."FILE_CODE" IS '文件编码';

COMMENT
ON COLUMN "GS_SESSION_FILE"."FILE_CONTENT" IS '文件文本';

COMMENT
ON COLUMN "GS_SESSION_FILE"."FILE_CONTENT_LENGTH" IS '文件文本长度';

COMMENT
ON COLUMN "GS_SESSION_FILE"."FILE_NAME" IS '文件名称';

COMMENT
ON COLUMN "GS_SESSION_FILE"."FILE_OSS_URL" IS 'oss地址';

COMMENT
ON COLUMN "GS_SESSION_FILE"."FILE_TYPE" IS '文件类型';

COMMENT
ON COLUMN "GS_SESSION_FILE"."ID" IS '主键 ID';

COMMENT
ON COLUMN "GS_SESSION_FILE"."ORIGINAL_FILE_KEY" IS '文件原始内容文件key';

COMMENT
ON COLUMN "GS_SESSION_FILE"."PARSE_SPLIT" IS '是否解析分片 0-否 1-是';

COMMENT
ON COLUMN "GS_SESSION_FILE"."SESSION_ID" IS '会话ID';

COMMENT
ON COLUMN "GS_SESSION_FILE"."TENANT_ID" IS '租户 ID';

COMMENT
ON COLUMN "GS_SESSION_FILE"."UPDATE_ID" IS '最后修改用户 ID';

COMMENT
ON COLUMN "GS_SESSION_FILE"."UPDATE_NAME" IS '最后修改用户名称';

COMMENT
ON COLUMN "GS_SESSION_FILE"."UPDATE_TIME" IS '更新时间';

COMMENT
ON COLUMN "GS_SESSION_FILE"."YN" IS '删除标记，0-未删除，其他表示已删除';

COMMENT
ON TABLE "KNOWLEDGE_AUDIT_DATA" IS '知识审核内容表';

COMMENT
ON COLUMN "KNOWLEDGE_AUDIT_DATA"."APP_CODE" IS '应用Code';

COMMENT
ON COLUMN "KNOWLEDGE_AUDIT_DATA"."AUDIT_CODE" IS '审核编码';

COMMENT
ON COLUMN "KNOWLEDGE_AUDIT_DATA"."CODE" IS 'code';

COMMENT
ON COLUMN "KNOWLEDGE_AUDIT_DATA"."CREATE_ID" IS '创建用户ID';

COMMENT
ON COLUMN "KNOWLEDGE_AUDIT_DATA"."CREATE_NAME" IS '创建用户名';

COMMENT
ON COLUMN "KNOWLEDGE_AUDIT_DATA"."CREATE_TIME" IS '创建时间';

COMMENT
ON COLUMN "KNOWLEDGE_AUDIT_DATA"."DATA_CODE" IS '审核数据编码';

COMMENT
ON COLUMN "KNOWLEDGE_AUDIT_DATA"."DATA_CONTENT" IS '审核数据内容';

COMMENT
ON COLUMN "KNOWLEDGE_AUDIT_DATA"."DATA_STATUS" IS '审核数据状态';

COMMENT
ON COLUMN "KNOWLEDGE_AUDIT_DATA"."DATA_TITLE" IS '审核数据标题';

COMMENT
ON COLUMN "KNOWLEDGE_AUDIT_DATA"."DATA_TYPE" IS '审核数据类型';

COMMENT
ON COLUMN "KNOWLEDGE_AUDIT_DATA"."ID" IS '主键';

COMMENT
ON COLUMN "KNOWLEDGE_AUDIT_DATA"."UPDATE_ID" IS '最后修改用户ID';

COMMENT
ON COLUMN "KNOWLEDGE_AUDIT_DATA"."UPDATE_NAME" IS '最后修改用户名';

COMMENT
ON COLUMN "KNOWLEDGE_AUDIT_DATA"."UPDATE_TIME" IS '更新时间';

COMMENT
ON COLUMN "KNOWLEDGE_AUDIT_DATA"."YN" IS '删除标记,0:未删除,其他表示已删除';

COMMENT
ON TABLE "KNOWLEDGE_BASE" IS '知识库表';

COMMENT
ON COLUMN "KNOWLEDGE_BASE"."APP_CODE" IS '应用Code';

COMMENT
ON COLUMN "KNOWLEDGE_BASE"."CODE" IS 'Code';

COMMENT
ON COLUMN "KNOWLEDGE_BASE"."CONFIG" IS '高级配置';

COMMENT
ON COLUMN "KNOWLEDGE_BASE"."COVER_KEY" IS '封面文件key';

COMMENT
ON COLUMN "KNOWLEDGE_BASE"."CREATE_ID" IS '创建用户ID';

COMMENT
ON COLUMN "KNOWLEDGE_BASE"."CREATE_NAME" IS '创建用户名';

COMMENT
ON COLUMN "KNOWLEDGE_BASE"."CREATE_TIME" IS '创建时间';

COMMENT
ON COLUMN "KNOWLEDGE_BASE"."DESC" IS '描述';

COMMENT
ON COLUMN "KNOWLEDGE_BASE"."ICON" IS '图标';

COMMENT
ON COLUMN "KNOWLEDGE_BASE"."ID" IS '主键';

COMMENT
ON COLUMN "KNOWLEDGE_BASE"."NAME" IS '名称';

COMMENT
ON COLUMN "KNOWLEDGE_BASE"."ON" IS '开关';

COMMENT
ON COLUMN "KNOWLEDGE_BASE"."TYPE" IS '类型';

COMMENT
ON COLUMN "KNOWLEDGE_BASE"."UPDATE_ID" IS '最后修改用户ID';

COMMENT
ON COLUMN "KNOWLEDGE_BASE"."UPDATE_NAME" IS '最后修改用户名';

COMMENT
ON COLUMN "KNOWLEDGE_BASE"."UPDATE_TIME" IS '更新时间';

COMMENT
ON COLUMN "KNOWLEDGE_BASE"."YN" IS '删除标记,0:未删除,其他表示已删除';

COMMENT
ON TABLE "KNOWLEDGE_ESSENTIAL" IS '知识要素表';

COMMENT
ON COLUMN "KNOWLEDGE_ESSENTIAL"."APP_CODE" IS '应用Code';

COMMENT
ON COLUMN "KNOWLEDGE_ESSENTIAL"."CODE" IS '业务配置编码';

COMMENT
ON COLUMN "KNOWLEDGE_ESSENTIAL"."CONTENT" IS '内容';

COMMENT
ON COLUMN "KNOWLEDGE_ESSENTIAL"."CREATE_ID" IS '创建用户ID';

COMMENT
ON COLUMN "KNOWLEDGE_ESSENTIAL"."CREATE_NAME" IS '创建用户名';

COMMENT
ON COLUMN "KNOWLEDGE_ESSENTIAL"."CREATE_TIME" IS '创建时间';

COMMENT
ON COLUMN "KNOWLEDGE_ESSENTIAL"."ID" IS '主键';

COMMENT
ON COLUMN "KNOWLEDGE_ESSENTIAL"."KNOWLEDGE_CODE" IS '所属知识编码';

COMMENT
ON COLUMN "KNOWLEDGE_ESSENTIAL"."PARENT_CODE" IS '父节点Code';

COMMENT
ON COLUMN "KNOWLEDGE_ESSENTIAL"."SOURCE" IS '来源';

COMMENT
ON COLUMN "KNOWLEDGE_ESSENTIAL"."TITLE" IS '标题';

COMMENT
ON COLUMN "KNOWLEDGE_ESSENTIAL"."UPDATE_ID" IS '最后修改用户ID';

COMMENT
ON COLUMN "KNOWLEDGE_ESSENTIAL"."UPDATE_NAME" IS '最后修改用户名';

COMMENT
ON COLUMN "KNOWLEDGE_ESSENTIAL"."UPDATE_TIME" IS '更新时间';

COMMENT
ON COLUMN "KNOWLEDGE_ESSENTIAL"."YN" IS '删除标记,0:未删除,其他表示已删除';

COMMENT
ON TABLE "KNOWLEDGE_EXTRACT" IS '知识抽取主表';

COMMENT
ON COLUMN "KNOWLEDGE_EXTRACT"."CODE" IS '业务code';

COMMENT
ON COLUMN "KNOWLEDGE_EXTRACT"."CREATE_ID" IS '创建人id';

COMMENT
ON COLUMN "KNOWLEDGE_EXTRACT"."CREATE_NAME" IS '创建人';

COMMENT
ON COLUMN "KNOWLEDGE_EXTRACT"."CREATE_TIME" IS '创建时间';

COMMENT
ON COLUMN "KNOWLEDGE_EXTRACT"."EXTRACT_STATUS" IS '0：抽取中,1:抽取失败，2：抽取成功';

COMMENT
ON COLUMN "KNOWLEDGE_EXTRACT"."FAILURE_REASON" IS '失败原因';

COMMENT
ON COLUMN "KNOWLEDGE_EXTRACT"."KNOWLEDGE_CODE" IS '知识id';

COMMENT
ON COLUMN "KNOWLEDGE_EXTRACT"."STATUS" IS '0：废弃，1：使用中';

COMMENT
ON COLUMN "KNOWLEDGE_EXTRACT"."UPDATE_ID" IS '修改人标识';

COMMENT
ON COLUMN "KNOWLEDGE_EXTRACT"."UPDATE_NAME" IS '修改人名字';

COMMENT
ON COLUMN "KNOWLEDGE_EXTRACT"."UPDATE_TIME" IS '修改时间';

COMMENT
ON COLUMN "KNOWLEDGE_EXTRACT"."YN" IS '0:未删除,其他表示已删除';

COMMENT
ON TABLE "KNOWLEDGE_EXTRA_INFO" IS '知识额外信息表';

COMMENT
ON COLUMN "KNOWLEDGE_EXTRA_INFO"."APP_CODE" IS '应用Code';

COMMENT
ON COLUMN "KNOWLEDGE_EXTRA_INFO"."CODE" IS 'Code';

COMMENT
ON COLUMN "KNOWLEDGE_EXTRA_INFO"."CONTENT" IS '内容';

COMMENT
ON COLUMN "KNOWLEDGE_EXTRA_INFO"."CREATE_ID" IS '创建用户ID';

COMMENT
ON COLUMN "KNOWLEDGE_EXTRA_INFO"."CREATE_NAME" IS '创建用户名';

COMMENT
ON COLUMN "KNOWLEDGE_EXTRA_INFO"."CREATE_TIME" IS '创建时间';

COMMENT
ON COLUMN "KNOWLEDGE_EXTRA_INFO"."EXTRA_KEY" IS '存储任务编号';

COMMENT
ON COLUMN "KNOWLEDGE_EXTRA_INFO"."ID" IS '主键';

COMMENT
ON COLUMN "KNOWLEDGE_EXTRA_INFO"."KNOWLEDGE_CODE" IS '所属知识编码';

COMMENT
ON COLUMN "KNOWLEDGE_EXTRA_INFO"."TYPE" IS '类型';

COMMENT
ON COLUMN "KNOWLEDGE_EXTRA_INFO"."UPDATE_ID" IS '最后修改用户ID';

COMMENT
ON COLUMN "KNOWLEDGE_EXTRA_INFO"."UPDATE_NAME" IS '最后修改用户名';

COMMENT
ON COLUMN "KNOWLEDGE_EXTRA_INFO"."UPDATE_TIME" IS '更新时间';

COMMENT
ON COLUMN "KNOWLEDGE_EXTRA_INFO"."YN" IS '删除标记,0:未删除,其他表示已删除';

COMMENT
ON TABLE "KNOWLEDGE_FAQ" IS '问答表';

COMMENT
ON COLUMN "KNOWLEDGE_FAQ"."ANSWER" IS '回答';

COMMENT
ON COLUMN "KNOWLEDGE_FAQ"."ANSWER_TYPE" IS '回答类型';

COMMENT
ON COLUMN "KNOWLEDGE_FAQ"."APP_CODE" IS '应用Code';

COMMENT
ON COLUMN "KNOWLEDGE_FAQ"."CODE" IS '业务配置编码';

COMMENT
ON COLUMN "KNOWLEDGE_FAQ"."CREATE_ID" IS '创建用户ID';

COMMENT
ON COLUMN "KNOWLEDGE_FAQ"."CREATE_NAME" IS '创建用户名';

COMMENT
ON COLUMN "KNOWLEDGE_FAQ"."CREATE_TIME" IS '创建时间';

COMMENT
ON COLUMN "KNOWLEDGE_FAQ"."ID" IS '主键';

COMMENT
ON COLUMN "KNOWLEDGE_FAQ"."IS_AUTO" IS '是否自动抽取';

COMMENT
ON COLUMN "KNOWLEDGE_FAQ"."KNOWLEDGE_BASE_CODE" IS '所属知识库Code';

COMMENT
ON COLUMN "KNOWLEDGE_FAQ"."KNOWLEDGE_CODE" IS '关联知识code';

COMMENT
ON COLUMN "KNOWLEDGE_FAQ"."ON" IS '开关';

COMMENT
ON COLUMN "KNOWLEDGE_FAQ"."PUBLISH_COUNT" IS '发布次数';

COMMENT
ON COLUMN "KNOWLEDGE_FAQ"."PUBLISH_STATUS" IS '发布状态';

COMMENT
ON COLUMN "KNOWLEDGE_FAQ"."PUBLISH_TIME" IS '发布时间';

COMMENT
ON COLUMN "KNOWLEDGE_FAQ"."QUESTION" IS '问题';

COMMENT
ON COLUMN "KNOWLEDGE_FAQ"."UPDATE_ID" IS '最后修改用户ID';

COMMENT
ON COLUMN "KNOWLEDGE_FAQ"."UPDATE_NAME" IS '最后修改用户名';

COMMENT
ON COLUMN "KNOWLEDGE_FAQ"."UPDATE_TIME" IS '更新时间';

COMMENT
ON COLUMN "KNOWLEDGE_FAQ"."YN" IS '删除标记,0:未删除,其他表示已删除';

COMMENT
ON TABLE "KNOWLEDGE_FAQ_PROD" IS '问答线上表';

COMMENT
ON COLUMN "KNOWLEDGE_FAQ_PROD"."APP_CODE" IS '应用Code';

COMMENT
ON COLUMN "KNOWLEDGE_FAQ_PROD"."CODE" IS '业务配置编码';

COMMENT
ON COLUMN "KNOWLEDGE_FAQ_PROD"."CONTENT" IS '发布内容';

COMMENT
ON COLUMN "KNOWLEDGE_FAQ_PROD"."CREATE_ID" IS '创建用户ID';

COMMENT
ON COLUMN "KNOWLEDGE_FAQ_PROD"."CREATE_NAME" IS '创建用户名';

COMMENT
ON COLUMN "KNOWLEDGE_FAQ_PROD"."CREATE_TIME" IS '创建时间';

COMMENT
ON COLUMN "KNOWLEDGE_FAQ_PROD"."ID" IS '主键';

COMMENT
ON COLUMN "KNOWLEDGE_FAQ_PROD"."KNOWLEDGE_CODE" IS '关联知识code';

COMMENT
ON COLUMN "KNOWLEDGE_FAQ_PROD"."UPDATE_ID" IS '最后修改用户ID';

COMMENT
ON COLUMN "KNOWLEDGE_FAQ_PROD"."UPDATE_NAME" IS '最后修改用户名';

COMMENT
ON COLUMN "KNOWLEDGE_FAQ_PROD"."UPDATE_TIME" IS '更新时间';

COMMENT
ON COLUMN "KNOWLEDGE_FAQ_PROD"."YN" IS '删除标记,0:未删除,其他表示已删除';

COMMENT
ON TABLE "KNOWLEDGE_PROD" IS '知识线上表';

COMMENT
ON COLUMN "KNOWLEDGE_PROD"."APP_CODE" IS '应用Code';

COMMENT
ON COLUMN "KNOWLEDGE_PROD"."CODE" IS '知识code';

COMMENT
ON COLUMN "KNOWLEDGE_PROD"."CONTENT" IS '发布内容';

COMMENT
ON COLUMN "KNOWLEDGE_PROD"."CREATE_ID" IS '创建用户ID';

COMMENT
ON COLUMN "KNOWLEDGE_PROD"."CREATE_NAME" IS '创建用户名';

COMMENT
ON COLUMN "KNOWLEDGE_PROD"."CREATE_TIME" IS '创建时间';

COMMENT
ON COLUMN "KNOWLEDGE_PROD"."ID" IS '主键';

COMMENT
ON COLUMN "KNOWLEDGE_PROD"."UPDATE_ID" IS '最后修改用户ID';

COMMENT
ON COLUMN "KNOWLEDGE_PROD"."UPDATE_NAME" IS '最后修改用户名';

COMMENT
ON COLUMN "KNOWLEDGE_PROD"."UPDATE_TIME" IS '更新时间';

COMMENT
ON COLUMN "KNOWLEDGE_PROD"."YN" IS '删除标记,0:未删除,其他表示已删除';

COMMENT
ON TABLE "KNOWLEDGE_REPORT" IS '知识报告表';

COMMENT
ON COLUMN "KNOWLEDGE_REPORT"."APP_CODE" IS '应用Code';

COMMENT
ON COLUMN "KNOWLEDGE_REPORT"."CHOSE" IS '智能问答选择项';

COMMENT
ON COLUMN "KNOWLEDGE_REPORT"."CODE" IS 'Code';

COMMENT
ON COLUMN "KNOWLEDGE_REPORT"."CONTENT" IS '文章内容';

COMMENT
ON COLUMN "KNOWLEDGE_REPORT"."CREATE_ID" IS '创建用户ID';

COMMENT
ON COLUMN "KNOWLEDGE_REPORT"."CREATE_NAME" IS '创建用户名';

COMMENT
ON COLUMN "KNOWLEDGE_REPORT"."CREATE_TIME" IS '创建时间';

COMMENT
ON COLUMN "KNOWLEDGE_REPORT"."ID" IS '主键';

COMMENT
ON COLUMN "KNOWLEDGE_REPORT"."NAME" IS '名称';

COMMENT
ON COLUMN "KNOWLEDGE_REPORT"."OUTLINE_DATA" IS '大纲结构';

COMMENT
ON COLUMN "KNOWLEDGE_REPORT"."THEME_TEXT" IS '用户原始主题';

COMMENT
ON COLUMN "KNOWLEDGE_REPORT"."TYPE" IS '文章内容格式';

COMMENT
ON COLUMN "KNOWLEDGE_REPORT"."UPDATE_ID" IS '最后修改用户ID';

COMMENT
ON COLUMN "KNOWLEDGE_REPORT"."UPDATE_NAME" IS '最后修改用户名';

COMMENT
ON COLUMN "KNOWLEDGE_REPORT"."UPDATE_TIME" IS '更新时间';

COMMENT
ON COLUMN "KNOWLEDGE_REPORT"."YN" IS '删除标记,0:未删除,其他表示已删除';

COMMENT
ON TABLE "PLUGIN_API_META_INFO" IS '插件工具元数据表';

COMMENT
ON COLUMN "PLUGIN_API_META_INFO"."API_DESC" IS '工具描述';

COMMENT
ON COLUMN "PLUGIN_API_META_INFO"."API_ID" IS '工具id';

COMMENT
ON COLUMN "PLUGIN_API_META_INFO"."API_NAME" IS '工具名称';

COMMENT
ON COLUMN "PLUGIN_API_META_INFO"."CREATE_ID" IS '创建用户ID';

COMMENT
ON COLUMN "PLUGIN_API_META_INFO"."CREATE_NAME" IS '记录创建人名称';

COMMENT
ON COLUMN "PLUGIN_API_META_INFO"."CREATE_TIME" IS '记录创建时间';

COMMENT
ON COLUMN "PLUGIN_API_META_INFO"."DEBUG_STATUS" IS '调试状态:0-调试失败, 1-调试成功';

COMMENT
ON COLUMN "PLUGIN_API_META_INFO"."ENABLED" IS '是否启用';

COMMENT
ON COLUMN "PLUGIN_API_META_INFO"."ID" IS '自增 id';

COMMENT
ON COLUMN "PLUGIN_API_META_INFO"."METHOD" IS '方法类型';

COMMENT
ON COLUMN "PLUGIN_API_META_INFO"."PATH" IS '接口path';

COMMENT
ON COLUMN "PLUGIN_API_META_INFO"."PLUGIN_ID" IS '插件id';

COMMENT
ON COLUMN "PLUGIN_API_META_INFO"."QUOTE_NUMBER" IS '引用数, 包含agent引用数和工作流引用数';

COMMENT
ON COLUMN "PLUGIN_API_META_INFO"."SERVICE_STATUS" IS '服务状态:0-下线, 1-上线';

COMMENT
ON COLUMN "PLUGIN_API_META_INFO"."TENANT_ID" IS '租户id';

COMMENT
ON COLUMN "PLUGIN_API_META_INFO"."UPDATE_ID" IS '最后修改用户ID';

COMMENT
ON COLUMN "PLUGIN_API_META_INFO"."UPDATE_NAME" IS '最后一次记录操作人';

COMMENT
ON COLUMN "PLUGIN_API_META_INFO"."UPDATE_TIME" IS '记录最后一次更新时间';

COMMENT
ON COLUMN "PLUGIN_API_META_INFO"."VERSION" IS '版本号';

COMMENT
ON COLUMN "PLUGIN_API_META_INFO"."YN" IS '记录删除标识';

COMMENT
ON TABLE "PLUGIN_API_PARAM" IS '插件工具参数表';

COMMENT
ON COLUMN "PLUGIN_API_PARAM"."API_ID" IS '工具id';

COMMENT
ON COLUMN "PLUGIN_API_PARAM"."CREATE_ID" IS '创建用户ID';

COMMENT
ON COLUMN "PLUGIN_API_PARAM"."CREATE_NAME" IS '记录创建人名称';

COMMENT
ON COLUMN "PLUGIN_API_PARAM"."CREATE_TIME" IS '记录创建时间';

COMMENT
ON COLUMN "PLUGIN_API_PARAM"."DEFAULT_VALUE" IS '默认值';

COMMENT
ON COLUMN "PLUGIN_API_PARAM"."ENABLED" IS '是否启用';

COMMENT
ON COLUMN "PLUGIN_API_PARAM"."FATHER_PARAM_ID" IS '父插件id';

COMMENT
ON COLUMN "PLUGIN_API_PARAM"."ID" IS '自增 id';

COMMENT
ON COLUMN "PLUGIN_API_PARAM"."LOCATION" IS '参数位量置,1:query; 2: header; 3: path; 4: body';

COMMENT
ON COLUMN "PLUGIN_API_PARAM"."NAME" IS '参数名称';

COMMENT
ON COLUMN "PLUGIN_API_PARAM"."PARAM_DESC" IS '参数描述';

COMMENT
ON COLUMN "PLUGIN_API_PARAM"."PARAM_ID" IS '参数id';

COMMENT
ON COLUMN "PLUGIN_API_PARAM"."PARAM_TYPE" IS '参数类型，1：请求体, 2: 响应体';

COMMENT
ON COLUMN "PLUGIN_API_PARAM"."PLUGIN_ID" IS '插件id';

COMMENT
ON COLUMN "PLUGIN_API_PARAM"."REQUIRED" IS '是否必填，false：否；true：是';

COMMENT
ON COLUMN "PLUGIN_API_PARAM"."REQ_RSP_TYPE" IS '参数类型,1: string; 2: integer; 3: boolean; 4: array; 5: object';

COMMENT
ON COLUMN "PLUGIN_API_PARAM"."TENANT_ID" IS '租户id';

COMMENT
ON COLUMN "PLUGIN_API_PARAM"."UPDATE_ID" IS '最后修改用户ID';

COMMENT
ON COLUMN "PLUGIN_API_PARAM"."UPDATE_NAME" IS '最后一次记录操作人';

COMMENT
ON COLUMN "PLUGIN_API_PARAM"."UPDATE_TIME" IS '记录最后一次更新时间';

COMMENT
ON COLUMN "PLUGIN_API_PARAM"."VERSION" IS '版本号';

COMMENT
ON COLUMN "PLUGIN_API_PARAM"."YN" IS '记录删除标识';

COMMENT
ON TABLE "PLUGIN_BIND_CARD" IS '工具卡片绑定表';

COMMENT
ON COLUMN "PLUGIN_BIND_CARD"."AGENT_CODE" IS 'agentCode';

COMMENT
ON COLUMN "PLUGIN_BIND_CARD"."API_ID" IS '工具id';

COMMENT
ON COLUMN "PLUGIN_BIND_CARD"."BIND_ARRAY" IS '绑定的数组来源';

COMMENT
ON COLUMN "PLUGIN_BIND_CARD"."BIND_MAPPING_RULE" IS '绑定字段, json对象';

COMMENT
ON COLUMN "PLUGIN_BIND_CARD"."BIND_TYPE" IS '绑定类型，1:单张卡片，2:竖向列表';

COMMENT
ON COLUMN "PLUGIN_BIND_CARD"."CREATE_ID" IS '创建用户ID';

COMMENT
ON COLUMN "PLUGIN_BIND_CARD"."CREATE_NAME" IS '记录创建人名称';

COMMENT
ON COLUMN "PLUGIN_BIND_CARD"."CREATE_TIME" IS '记录创建时间';

COMMENT
ON COLUMN "PLUGIN_BIND_CARD"."ID" IS '自增 id';

COMMENT
ON COLUMN "PLUGIN_BIND_CARD"."MAX_LENGTH" IS '卡片列表最大长度';

COMMENT
ON COLUMN "PLUGIN_BIND_CARD"."PLUGIN_ID" IS '插件id';

COMMENT
ON COLUMN "PLUGIN_BIND_CARD"."TENANT_ID" IS '租户id';

COMMENT
ON COLUMN "PLUGIN_BIND_CARD"."UPDATE_ID" IS '最后修改用户ID';

COMMENT
ON COLUMN "PLUGIN_BIND_CARD"."UPDATE_NAME" IS '最后一次记录操作人';

COMMENT
ON COLUMN "PLUGIN_BIND_CARD"."UPDATE_TIME" IS '记录最后一次更新时间';

COMMENT
ON COLUMN "PLUGIN_BIND_CARD"."VERSION" IS '版本号';

COMMENT
ON COLUMN "PLUGIN_BIND_CARD"."YN" IS '记录删除标识';

COMMENT
ON TABLE "PLUGIN_MALL_CATEGORIES" IS '插件分类表';

COMMENT
ON COLUMN "PLUGIN_MALL_CATEGORIES"."CATEGORY_CODE" IS '插件分类编码';

COMMENT
ON COLUMN "PLUGIN_MALL_CATEGORIES"."CATEGORY_ICON" IS '插件分类图标';

COMMENT
ON COLUMN "PLUGIN_MALL_CATEGORIES"."CATEGORY_NAME" IS '插件分类名称';

COMMENT
ON COLUMN "PLUGIN_MALL_CATEGORIES"."CREATE_ID" IS '创建用户ID';

COMMENT
ON COLUMN "PLUGIN_MALL_CATEGORIES"."CREATE_NAME" IS '记录创建人名称';

COMMENT
ON COLUMN "PLUGIN_MALL_CATEGORIES"."CREATE_TIME" IS '记录创建时间';

COMMENT
ON COLUMN "PLUGIN_MALL_CATEGORIES"."ID" IS '自增 id';

COMMENT
ON COLUMN "PLUGIN_MALL_CATEGORIES"."UPDATE_ID" IS '最后修改用户ID';

COMMENT
ON COLUMN "PLUGIN_MALL_CATEGORIES"."UPDATE_NAME" IS '最后一次记录操作人';

COMMENT
ON COLUMN "PLUGIN_MALL_CATEGORIES"."UPDATE_TIME" IS '记录最后一次更新时间';

COMMENT
ON COLUMN "PLUGIN_MALL_CATEGORIES"."YN" IS '记录删除标识';

COMMENT
ON TABLE "PLUGIN_MALL_INFO" IS '插件商店表';

COMMENT
ON COLUMN "PLUGIN_MALL_INFO"."CATEGORY_CODE" IS '插件分类';

COMMENT
ON COLUMN "PLUGIN_MALL_INFO"."CATEGORY_NAME" IS '插件分类名称';

COMMENT
ON COLUMN "PLUGIN_MALL_INFO"."CREATE_ID" IS '创建用户ID';

COMMENT
ON COLUMN "PLUGIN_MALL_INFO"."CREATE_NAME" IS '记录创建人名称';

COMMENT
ON COLUMN "PLUGIN_MALL_INFO"."CREATE_TIME" IS '记录创建时间';

COMMENT
ON COLUMN "PLUGIN_MALL_INFO"."ID" IS '自增 id';

COMMENT
ON COLUMN "PLUGIN_MALL_INFO"."PLUGIN_ID" IS '插件id';

COMMENT
ON COLUMN "PLUGIN_MALL_INFO"."PLUGIN_SOURCE" IS '插件来源，0：自建 1：灵耀官方';

COMMENT
ON COLUMN "PLUGIN_MALL_INFO"."UPDATE_ID" IS '最后修改用户ID';

COMMENT
ON COLUMN "PLUGIN_MALL_INFO"."UPDATE_NAME" IS '最后一次记录操作人';

COMMENT
ON COLUMN "PLUGIN_MALL_INFO"."UPDATE_TIME" IS '记录最后一次更新时间';

COMMENT
ON COLUMN "PLUGIN_MALL_INFO"."YN" IS '记录删除标识';

COMMENT
ON TABLE "PLUGIN_META_INFO" IS '插件元数据表';

COMMENT
ON COLUMN "PLUGIN_META_INFO"."AUTH_TYPE" IS '校验方式';

COMMENT
ON COLUMN "PLUGIN_META_INFO"."COMMON_HEADERS" IS '公共header参数';

COMMENT
ON COLUMN "PLUGIN_META_INFO"."CREATE_API_TYPE" IS '插件内工具创建方式';

COMMENT
ON COLUMN "PLUGIN_META_INFO"."CREATE_ID" IS '创建用户ID';

COMMENT
ON COLUMN "PLUGIN_META_INFO"."CREATE_NAME" IS '记录创建人名称';

COMMENT
ON COLUMN "PLUGIN_META_INFO"."CREATE_TIME" IS '记录创建时间';

COMMENT
ON COLUMN "PLUGIN_META_INFO"."ID" IS '自增 id';

COMMENT
ON COLUMN "PLUGIN_META_INFO"."LOCATION" IS '校验参数所在位置, 0-请求头，1-url';

COMMENT
ON COLUMN "PLUGIN_META_INFO"."OAUTH_INFO" IS 'oauth鉴权参数';

COMMENT
ON COLUMN "PLUGIN_META_INFO"."PLUGIN_DESC" IS '插件描述';

COMMENT
ON COLUMN "PLUGIN_META_INFO"."PLUGIN_ICON" IS '插件图标';

COMMENT
ON COLUMN "PLUGIN_META_INFO"."PLUGIN_ID" IS '插件id';

COMMENT
ON COLUMN "PLUGIN_META_INFO"."PLUGIN_NAME" IS '插件名称';

COMMENT
ON COLUMN "PLUGIN_META_INFO"."SERVICE_KEY" IS '参数key';

COMMENT
ON COLUMN "PLUGIN_META_INFO"."SERVICE_TOKEN" IS 'token';

COMMENT
ON COLUMN "PLUGIN_META_INFO"."STATUS" IS '状态';

COMMENT
ON COLUMN "PLUGIN_META_INFO"."TENANT_ID" IS '租户id';

COMMENT
ON COLUMN "PLUGIN_META_INFO"."UPDATE_ID" IS '最后修改用户ID';

COMMENT
ON COLUMN "PLUGIN_META_INFO"."UPDATE_NAME" IS '最后一次记录操作人';

COMMENT
ON COLUMN "PLUGIN_META_INFO"."UPDATE_TIME" IS '记录最后一次更新时间';

COMMENT
ON COLUMN "PLUGIN_META_INFO"."URL" IS 'url前缀';

COMMENT
ON COLUMN "PLUGIN_META_INFO"."VERSION" IS '版本号';

COMMENT
ON COLUMN "PLUGIN_META_INFO"."YN" IS '记录删除标识';

COMMENT
ON TABLE "PROMPT_TEMPLATE" IS 'prompt模板表';

COMMENT
ON COLUMN "PROMPT_TEMPLATE"."APP_CODE" IS '应用Code';

COMMENT
ON COLUMN "PROMPT_TEMPLATE"."CODE" IS 'Code';

COMMENT
ON COLUMN "PROMPT_TEMPLATE"."CONTENT" IS '内容';

COMMENT
ON COLUMN "PROMPT_TEMPLATE"."CREATE_ID" IS '创建用户ID';

COMMENT
ON COLUMN "PROMPT_TEMPLATE"."CREATE_NAME" IS '创建用户名';

COMMENT
ON COLUMN "PROMPT_TEMPLATE"."CREATE_TIME" IS '创建时间';

COMMENT
ON COLUMN "PROMPT_TEMPLATE"."DESCRIPTION" IS '描述';

COMMENT
ON COLUMN "PROMPT_TEMPLATE"."ID" IS '主键';

COMMENT
ON COLUMN "PROMPT_TEMPLATE"."MODEL_PARAM" IS '模型参数';

COMMENT
ON COLUMN "PROMPT_TEMPLATE"."NAME" IS '名称';

COMMENT
ON COLUMN "PROMPT_TEMPLATE"."UPDATE_ID" IS '最后修改用户ID';

COMMENT
ON COLUMN "PROMPT_TEMPLATE"."UPDATE_NAME" IS '最后修改用户名';

COMMENT
ON COLUMN "PROMPT_TEMPLATE"."UPDATE_TIME" IS '更新时间';

COMMENT
ON COLUMN "PROMPT_TEMPLATE"."YN" IS '删除标记,0:未删除,其他表示已删除';

COMMENT
ON TABLE "WORKFLOW_VERSION_INFO" IS 'workflow版本信息表';

COMMENT
ON COLUMN "WORKFLOW_VERSION_INFO"."APP_CODE" IS '空间id';

COMMENT
ON COLUMN "WORKFLOW_VERSION_INFO"."CREATE_ID" IS '创建用户 ID';

COMMENT
ON COLUMN "WORKFLOW_VERSION_INFO"."CREATE_NAME" IS '记录创建人名称';

COMMENT
ON COLUMN "WORKFLOW_VERSION_INFO"."CREATE_TIME" IS '记录创建时间';

COMMENT
ON COLUMN "WORKFLOW_VERSION_INFO"."ID" IS '自增 id';

COMMENT
ON COLUMN "WORKFLOW_VERSION_INFO"."RUNTIME_STATUS" IS '运行状态';

COMMENT
ON COLUMN "WORKFLOW_VERSION_INFO"."STATUS" IS '版本状态';

COMMENT
ON COLUMN "WORKFLOW_VERSION_INFO"."TENANT_ID" IS '支撑平台租户 id';

COMMENT
ON COLUMN "WORKFLOW_VERSION_INFO"."UPDATE_ID" IS '最后修改用户 ID';

COMMENT
ON COLUMN "WORKFLOW_VERSION_INFO"."UPDATE_NAME" IS '最后一次记录操作人';

COMMENT
ON COLUMN "WORKFLOW_VERSION_INFO"."UPDATE_TIME" IS '记录最后一次更新时间';

COMMENT
ON COLUMN "WORKFLOW_VERSION_INFO"."VERSION" IS '版本号';

COMMENT
ON COLUMN "WORKFLOW_VERSION_INFO"."VERSION_DESC" IS '版本描述';

COMMENT
ON COLUMN "WORKFLOW_VERSION_INFO"."WORKFLOW_ID" IS '工作流id';

COMMENT
ON COLUMN "WORKFLOW_VERSION_INFO"."YN" IS '记录删除标识';

CREATE INDEX "INDEX83841661361300"
    ON "AGENT_AI_MODEL" ("MODEL_CODE");
ALTER
INDEX "INDEX83841661361300" VISIBLE;

CREATE INDEX "INDEX83843902904200"
    ON "AGENT_CONFIG" ("AGENT_CODE");
ALTER
INDEX "INDEX83843902904200" VISIBLE;

CREATE INDEX "BOT_CODE_KEY"
    ON "AGENT_DIALOG_RECORD" ("AGENT_CODE", "YN");
ALTER
INDEX "BOT_CODE_KEY" VISIBLE;

CREATE INDEX "INDEX83843927105200"
    ON "AGENT_QUESTION_HIT_RECORD" ("AGENT_CODE", "YN");
ALTER
INDEX "INDEX83843927105200" VISIBLE;

CREATE INDEX "INDEX83843935531900"
    ON "AGENT_VERSION_INFO" ("AGENT_CODE");
ALTER
INDEX "INDEX83843935531900" VISIBLE;

CREATE INDEX "IDX_KNOWLEDGE_NAME"
    ON "KNOWLEDGE" ("KNOWLEDGE_BASE_CODE", "NAME");
ALTER
INDEX "IDX_KNOWLEDGE_NAME" VISIBLE;

CREATE INDEX "IDX_EXTERNAL_INSTANCE_CODE"
    ON "KNOWLEDGE_AUDIT" ("EXTERNAL_INSTANCE_CODE");
ALTER
INDEX "IDX_EXTERNAL_INSTANCE_CODE" VISIBLE;

CREATE INDEX "IDX_FAQ_TEMP_YN_CODE"
    ON "KNOWLEDGE_FAQ_TEMP" ("APP_CODE", "CODE");
ALTER
INDEX "IDX_FAQ_TEMP_YN_CODE" VISIBLE;

CREATE INDEX "IDX_PLUGIN_ID"
    ON "PLUGIN_VERSION_INFO" ("PLUGIN_ID");
ALTER
INDEX "IDX_PLUGIN_ID" VISIBLE;

COMMENT
ON TABLE "AGENT_AI_MODEL" IS '算法模型表';

COMMENT
ON COLUMN "AGENT_AI_MODEL"."API_KEY" IS 'apikey';

COMMENT
ON COLUMN "AGENT_AI_MODEL"."APP_CODE" IS '应用Code';

COMMENT
ON COLUMN "AGENT_AI_MODEL"."CREATE_ID" IS '创建用户 ID';

COMMENT
ON COLUMN "AGENT_AI_MODEL"."CREATE_NAME" IS '创建用户名';

COMMENT
ON COLUMN "AGENT_AI_MODEL"."CREATE_TIME" IS '创建时间';

COMMENT
ON COLUMN "AGENT_AI_MODEL"."DATA_DIMENSION" IS '数据维度';

COMMENT
ON COLUMN "AGENT_AI_MODEL"."DATA_FORMAT" IS '数据格式';

COMMENT
ON COLUMN "AGENT_AI_MODEL"."DESCRIPTION" IS '描述';

COMMENT
ON COLUMN "AGENT_AI_MODEL"."DISTINGUISH_MODEL_CONFIG" IS '是否区分模型配置（是否区分用户请求或离线文档），0-不区分，1-区分';

COMMENT
ON COLUMN "AGENT_AI_MODEL"."EXTERNAL_MODEL_CONFIG" IS '模型拓展配置';

COMMENT
ON COLUMN "AGENT_AI_MODEL"."EXTERNAL_MODEL_ID" IS '外部模型 id';

COMMENT
ON COLUMN "AGENT_AI_MODEL"."EXTERNAL_MODEL_URL" IS '外部模型访问地址';

COMMENT
ON COLUMN "AGENT_AI_MODEL"."GLOBAL_FLAG" IS '是否是全局/是否是预置（0-非全局，1-全局）';

COMMENT
ON COLUMN "AGENT_AI_MODEL"."ID" IS '主键';

COMMENT
ON COLUMN "AGENT_AI_MODEL"."INTENT_NUMBER" IS '意图个数';

COMMENT
ON COLUMN "AGENT_AI_MODEL"."MODEL_CODE" IS '模型编码';

COMMENT
ON COLUMN "AGENT_AI_MODEL"."MODEL_CONFIG" IS '模型配置';

COMMENT
ON COLUMN "AGENT_AI_MODEL"."MODEL_NAME" IS '模型名称';

COMMENT
ON COLUMN "AGENT_AI_MODEL"."MODEL_PROVIDER" IS '大模型提供方';

COMMENT
ON COLUMN "AGENT_AI_MODEL"."MODEL_SECRET" IS '大模型 api secret';

COMMENT
ON COLUMN "AGENT_AI_MODEL"."MODEL_STATE" IS '模型状态';

COMMENT
ON COLUMN "AGENT_AI_MODEL"."MODEL_TYPE" IS '模型类型';

COMMENT
ON COLUMN "AGENT_AI_MODEL"."ROBOT_CODE" IS '机器人 code';

COMMENT
ON COLUMN "AGENT_AI_MODEL"."THRESHOLD" IS '分类模型阈值';

COMMENT
ON COLUMN "AGENT_AI_MODEL"."UPDATE_ID" IS '最后修改用户 ID';

COMMENT
ON COLUMN "AGENT_AI_MODEL"."UPDATE_NAME" IS '最后修改用户名';

COMMENT
ON COLUMN "AGENT_AI_MODEL"."UPDATE_TIME" IS '更新时间';

COMMENT
ON COLUMN "AGENT_AI_MODEL"."YN" IS '删除标记,0:未删除,其他表示已删除';

COMMENT
ON TABLE "AGENT_CONFIG" IS 'agent 配置表';

COMMENT
ON COLUMN "AGENT_CONFIG"."AGENT_CODE" IS '配置内容的 key';

COMMENT
ON COLUMN "AGENT_CONFIG"."APP_CODE" IS '应用Code';

COMMENT
ON COLUMN "AGENT_CONFIG"."CATEGORY" IS '配置分类';

COMMENT
ON COLUMN "AGENT_CONFIG"."CONFIG_KEY" IS '配置内容的 key';

COMMENT
ON COLUMN "AGENT_CONFIG"."CONFIG_VALUE" IS '配置详细内容';

COMMENT
ON COLUMN "AGENT_CONFIG"."CREATE_ID" IS '创建用户 ID';

COMMENT
ON COLUMN "AGENT_CONFIG"."CREATE_NAME" IS '记录创建人名称';

COMMENT
ON COLUMN "AGENT_CONFIG"."CREATE_TIME" IS '记录创建时间';

COMMENT
ON COLUMN "AGENT_CONFIG"."ID" IS '自增 id';

COMMENT
ON COLUMN "AGENT_CONFIG"."TYPE" IS '配置类型';

COMMENT
ON COLUMN "AGENT_CONFIG"."UPDATE_ID" IS '最后修改用户 ID';

COMMENT
ON COLUMN "AGENT_CONFIG"."UPDATE_NAME" IS '最后一次记录操作人';

COMMENT
ON COLUMN "AGENT_CONFIG"."UPDATE_TIME" IS '记录最后一次更新时间';

COMMENT
ON COLUMN "AGENT_CONFIG"."VERSION" IS '配置版本';

COMMENT
ON COLUMN "AGENT_CONFIG"."YN" IS '记录删除标识';

COMMENT
ON TABLE "AGENT_DIALOG_RECORD" IS '会话记录表';

COMMENT
ON COLUMN "AGENT_DIALOG_RECORD"."AGENT_CODE" IS '机器人 code';

COMMENT
ON COLUMN "AGENT_DIALOG_RECORD"."AGENT_MESSAGE_NUM" IS '机器人消息数';

COMMENT
ON COLUMN "AGENT_DIALOG_RECORD"."AGENT_NAME" IS '机器人名称';

COMMENT
ON COLUMN "AGENT_DIALOG_RECORD"."APP_CODE" IS '应用Code';

COMMENT
ON COLUMN "AGENT_DIALOG_RECORD"."CHANNEL_CODE" IS '入口 code';

COMMENT
ON COLUMN "AGENT_DIALOG_RECORD"."CHANNEL_NAME" IS '入口名称';

COMMENT
ON COLUMN "AGENT_DIALOG_RECORD"."CREATE_TIME" IS '记录创建时间';

COMMENT
ON COLUMN "AGENT_DIALOG_RECORD"."CUSTOMER_ID" IS '客户 id';

COMMENT
ON COLUMN "AGENT_DIALOG_RECORD"."CUSTOMER_MESSAGE_NUM" IS '客户消息数';

COMMENT
ON COLUMN "AGENT_DIALOG_RECORD"."DIALOG_DURATION" IS '会话时长';

COMMENT
ON COLUMN "AGENT_DIALOG_RECORD"."DIALOG_END_TIME" IS '会话结束时间';

COMMENT
ON COLUMN "AGENT_DIALOG_RECORD"."DIALOG_START_TIME" IS '会话开始时间';

COMMENT
ON COLUMN "AGENT_DIALOG_RECORD"."ID" IS '自增 id';

COMMENT
ON COLUMN "AGENT_DIALOG_RECORD"."MATCH_NUM" IS '命中意图数';

COMMENT
ON COLUMN "AGENT_DIALOG_RECORD"."SESSION_ID" IS '会话 id';

COMMENT
ON COLUMN "AGENT_DIALOG_RECORD"."TO_HUMAN" IS '是否转人工';

COMMENT
ON COLUMN "AGENT_DIALOG_RECORD"."UNKNOWN_NUM" IS '未命中意图数';

COMMENT
ON COLUMN "AGENT_DIALOG_RECORD"."UPDATE_TIME" IS '记录最后一次更新时间';

COMMENT
ON COLUMN "AGENT_DIALOG_RECORD"."YN" IS '记录删除标识';

COMMENT
ON TABLE "AGENT_QUESTION_HIT_RECORD" IS '知识点命中记录表';

COMMENT
ON COLUMN "AGENT_QUESTION_HIT_RECORD"."AGENT_CODE" IS '机器人编码';

COMMENT
ON COLUMN "AGENT_QUESTION_HIT_RECORD"."CHANNEL_CODE" IS '入口编码';

COMMENT
ON COLUMN "AGENT_QUESTION_HIT_RECORD"."CREATE_TIME" IS '记录创建时间';

COMMENT
ON COLUMN "AGENT_QUESTION_HIT_RECORD"."ID" IS '自增id';

COMMENT
ON COLUMN "AGENT_QUESTION_HIT_RECORD"."KMS_CODE" IS '知识库ID';

COMMENT
ON COLUMN "AGENT_QUESTION_HIT_RECORD"."KNOWLEDGE_ID" IS '知识ID';

COMMENT
ON COLUMN "AGENT_QUESTION_HIT_RECORD"."KNOWLEDGE_TYPE" IS '知识类型';

COMMENT
ON COLUMN "AGENT_QUESTION_HIT_RECORD"."MESSAGE_ID" IS '消息ID';

COMMENT
ON COLUMN "AGENT_QUESTION_HIT_RECORD"."SESSION_ID" IS '会话ID';

COMMENT
ON COLUMN "AGENT_QUESTION_HIT_RECORD"."TITLE" IS '知识标题';

COMMENT
ON COLUMN "AGENT_QUESTION_HIT_RECORD"."UPDATE_TIME" IS '记录最后一次更新时间';

COMMENT
ON COLUMN "AGENT_QUESTION_HIT_RECORD"."YN" IS '记录删除标识';

COMMENT
ON COLUMN "AGENT_VERSION_INFO"."AGENT_CODE" IS 'bot id,以支撑平台的应用 id 做机器人 id';

COMMENT
ON COLUMN "AGENT_VERSION_INFO"."APP_CODE" IS '应用Code';

COMMENT
ON COLUMN "AGENT_VERSION_INFO"."CREATE_ID" IS '创建用户 ID';

COMMENT
ON COLUMN "AGENT_VERSION_INFO"."CREATE_NAME" IS '记录创建人名称';

COMMENT
ON COLUMN "AGENT_VERSION_INFO"."CREATE_TIME" IS '记录创建时间';

COMMENT
ON COLUMN "AGENT_VERSION_INFO"."ID" IS '自增 id';

COMMENT
ON COLUMN "AGENT_VERSION_INFO"."MIGRATE_INDEX_STATUS" IS '迁移索引状态(0-为迁移完成，1-迁移完成)';

COMMENT
ON COLUMN "AGENT_VERSION_INFO"."RUNTIME_STATUS" IS '运行状态';

COMMENT
ON COLUMN "AGENT_VERSION_INFO"."STATUS" IS '版本状态';

COMMENT
ON COLUMN "AGENT_VERSION_INFO"."TENANT_ID" IS '支撑平台租户 id';

COMMENT
ON COLUMN "AGENT_VERSION_INFO"."UPDATE_ID" IS '最后修改用户 ID';

COMMENT
ON COLUMN "AGENT_VERSION_INFO"."UPDATE_NAME" IS '最后一次记录操作人';

COMMENT
ON COLUMN "AGENT_VERSION_INFO"."UPDATE_TIME" IS '记录最后一次更新时间';

COMMENT
ON COLUMN "AGENT_VERSION_INFO"."VERSION" IS '版本号';

COMMENT
ON COLUMN "AGENT_VERSION_INFO"."VERSION_DESC" IS '版本描述';

COMMENT
ON COLUMN "AGENT_VERSION_INFO"."YN" IS '记录删除标识';

COMMENT
ON TABLE "KNOWLEDGE" IS '知识表';

COMMENT
ON COLUMN "KNOWLEDGE"."APP_CODE" IS '应用Code';

COMMENT
ON COLUMN "KNOWLEDGE"."CODE" IS 'Code';

COMMENT
ON COLUMN "KNOWLEDGE"."COVER_FILE_KEY" IS '封面key';

COMMENT
ON COLUMN "KNOWLEDGE"."CREATE_ID" IS '创建用户ID';

COMMENT
ON COLUMN "KNOWLEDGE"."CREATE_NAME" IS '创建用户名';

COMMENT
ON COLUMN "KNOWLEDGE"."CREATE_TIME" IS '创建时间';

COMMENT
ON COLUMN "KNOWLEDGE"."DESCRIPTION" IS '描述';

COMMENT
ON COLUMN "KNOWLEDGE"."ID" IS '主键';

COMMENT
ON COLUMN "KNOWLEDGE"."IMAGE_INDEXING_STATUS" IS '图片索引状态';

COMMENT
ON COLUMN "KNOWLEDGE"."INDEXING_STATUS" IS '索引状态';

COMMENT
ON COLUMN "KNOWLEDGE"."KNOWLEDGE_BASE_CODE" IS '所属知识库编码';

COMMENT
ON COLUMN "KNOWLEDGE"."KNOWLEDGE_TIP" IS '消息提示';

COMMENT
ON COLUMN "KNOWLEDGE"."NAME" IS '名称';

COMMENT
ON COLUMN "KNOWLEDGE"."ON" IS '开关';

COMMENT
ON COLUMN "KNOWLEDGE"."ORIGINAL_FILE_KEY" IS '原始文件key';

COMMENT
ON COLUMN "KNOWLEDGE"."PUBLISH_COUNT" IS '发布次数';

COMMENT
ON COLUMN "KNOWLEDGE"."PUBLISH_STATUS" IS '发布状态';

COMMENT
ON COLUMN "KNOWLEDGE"."PUBLISH_TIME" IS '发布时间';

COMMENT
ON COLUMN "KNOWLEDGE"."SLICE_FILE_INFO" IS '文件切片信息';

COMMENT
ON COLUMN "KNOWLEDGE"."STATUS" IS '状态';

COMMENT
ON COLUMN "KNOWLEDGE"."TYPE" IS '类型';

COMMENT
ON COLUMN "KNOWLEDGE"."UPDATE_ID" IS '最后修改用户ID';

COMMENT
ON COLUMN "KNOWLEDGE"."UPDATE_NAME" IS '最后修改用户名';

COMMENT
ON COLUMN "KNOWLEDGE"."UPDATE_TIME" IS '更新时间';

COMMENT
ON COLUMN "KNOWLEDGE"."VIEW_COUNT" IS '浏览次数';

COMMENT
ON COLUMN "KNOWLEDGE"."VIEW_FILE_KEY" IS '预览文件key';

COMMENT
ON COLUMN "KNOWLEDGE"."YN" IS '删除标记,0:未删除,其他表示已删除';

COMMENT
ON TABLE "KNOWLEDGE_AUDIT" IS '知识审核表';

COMMENT
ON COLUMN "KNOWLEDGE_AUDIT"."APP_CODE" IS '应用Code';

COMMENT
ON COLUMN "KNOWLEDGE_AUDIT"."AUDIT_END_TIME" IS '审核结束时间';

COMMENT
ON COLUMN "KNOWLEDGE_AUDIT"."AUDIT_PARAM" IS '审核参数';

COMMENT
ON COLUMN "KNOWLEDGE_AUDIT"."AUDIT_RESULT" IS '审核结果';

COMMENT
ON COLUMN "KNOWLEDGE_AUDIT"."AUDIT_START_TIME" IS '审核发起时间';

COMMENT
ON COLUMN "KNOWLEDGE_AUDIT"."AUDIT_STATUS" IS '审核状态';

COMMENT
ON COLUMN "KNOWLEDGE_AUDIT"."AUDIT_TYPE" IS '送审类型,DOC:文档变更,FAQ:问答对变更';

COMMENT
ON COLUMN "KNOWLEDGE_AUDIT"."CODE" IS 'code';

COMMENT
ON COLUMN "KNOWLEDGE_AUDIT"."CREATE_ID" IS '创建用户ID';

COMMENT
ON COLUMN "KNOWLEDGE_AUDIT"."CREATE_NAME" IS '创建用户名';

COMMENT
ON COLUMN "KNOWLEDGE_AUDIT"."CREATE_TIME" IS '创建时间';

COMMENT
ON COLUMN "KNOWLEDGE_AUDIT"."DESCRIPTION" IS '审核流程描述';

COMMENT
ON COLUMN "KNOWLEDGE_AUDIT"."EXTERNAL_INSTANCE_CODE" IS '外部审批系统的流程实例编码';

COMMENT
ON COLUMN "KNOWLEDGE_AUDIT"."EXTERNAL_PROCESS_CODE" IS '外部审批系统的流程编码';

COMMENT
ON COLUMN "KNOWLEDGE_AUDIT"."ID" IS '主键';

COMMENT
ON COLUMN "KNOWLEDGE_AUDIT"."KNOWLEDGE_BASE_CODE" IS '所属知识库Code';

COMMENT
ON COLUMN "KNOWLEDGE_AUDIT"."NAME" IS '审核流程名称';

COMMENT
ON COLUMN "KNOWLEDGE_AUDIT"."UPDATE_ID" IS '最后修改用户ID';

COMMENT
ON COLUMN "KNOWLEDGE_AUDIT"."UPDATE_NAME" IS '最后修改用户名';

COMMENT
ON COLUMN "KNOWLEDGE_AUDIT"."UPDATE_TIME" IS '更新时间';

COMMENT
ON COLUMN "KNOWLEDGE_AUDIT"."YN" IS '删除标记,0:未删除,其他表示已删除';

COMMENT
ON TABLE "KNOWLEDGE_FAQ_TEMP" IS '问题对临时表';

COMMENT
ON COLUMN "KNOWLEDGE_FAQ_TEMP"."ANSWER" IS '回答';

COMMENT
ON COLUMN "KNOWLEDGE_FAQ_TEMP"."ANSWER_TYPE" IS '回答类型';

COMMENT
ON COLUMN "KNOWLEDGE_FAQ_TEMP"."CODE" IS '业务配置编码';

COMMENT
ON COLUMN "KNOWLEDGE_FAQ_TEMP"."CORPUS" IS '相似问提';

COMMENT
ON COLUMN "KNOWLEDGE_FAQ_TEMP"."CORPUS_COUNT" IS '相似问个数';

COMMENT
ON COLUMN "KNOWLEDGE_FAQ_TEMP"."CREATE_ID" IS '创建人id';

COMMENT
ON COLUMN "KNOWLEDGE_FAQ_TEMP"."CREATE_NAME" IS '创建人';

COMMENT
ON COLUMN "KNOWLEDGE_FAQ_TEMP"."CREATE_TIME" IS '创建时间';

COMMENT
ON COLUMN "KNOWLEDGE_FAQ_TEMP"."EXTRACT_CODE" IS '抽取id';

COMMENT
ON COLUMN "KNOWLEDGE_FAQ_TEMP"."KNOWLEDGE_CODE" IS '关联知识code';

COMMENT
ON COLUMN "KNOWLEDGE_FAQ_TEMP"."ON" IS '开关';

COMMENT
ON COLUMN "KNOWLEDGE_FAQ_TEMP"."QUESTION" IS '问题';

COMMENT
ON COLUMN "KNOWLEDGE_FAQ_TEMP"."STORE_STATUS" IS '入库状态 0：未入库 1：已入库';

COMMENT
ON COLUMN "KNOWLEDGE_FAQ_TEMP"."STORE_TIME" IS '入库时间';

COMMENT
ON COLUMN "KNOWLEDGE_FAQ_TEMP"."UPDATE_ID" IS '创建人标识';

COMMENT
ON COLUMN "KNOWLEDGE_FAQ_TEMP"."UPDATE_NAME" IS '修改人名字';

COMMENT
ON COLUMN "KNOWLEDGE_FAQ_TEMP"."UPDATE_TIME" IS '修改时间';

COMMENT
ON COLUMN "KNOWLEDGE_FAQ_TEMP"."YN" IS '删除标记';

COMMENT
ON TABLE "PLUGIN_VERSION_INFO" IS '插件版本信息表';

COMMENT
ON COLUMN "PLUGIN_VERSION_INFO"."CREATE_ID" IS '创建用户 ID';

COMMENT
ON COLUMN "PLUGIN_VERSION_INFO"."CREATE_NAME" IS '记录创建人名称';

COMMENT
ON COLUMN "PLUGIN_VERSION_INFO"."CREATE_TIME" IS '记录创建时间';

COMMENT
ON COLUMN "PLUGIN_VERSION_INFO"."ID" IS '自增 id';

COMMENT
ON COLUMN "PLUGIN_VERSION_INFO"."PLUGIN_ID" IS '插件id';

COMMENT
ON COLUMN "PLUGIN_VERSION_INFO"."RUNTIME_STATUS" IS '运行状态';

COMMENT
ON COLUMN "PLUGIN_VERSION_INFO"."STATUS" IS '版本状态';

COMMENT
ON COLUMN "PLUGIN_VERSION_INFO"."TENANT_ID" IS '支撑平台租户 id';

COMMENT
ON COLUMN "PLUGIN_VERSION_INFO"."UPDATE_ID" IS '最后修改用户 ID';

COMMENT
ON COLUMN "PLUGIN_VERSION_INFO"."UPDATE_NAME" IS '最后一次记录操作人';

COMMENT
ON COLUMN "PLUGIN_VERSION_INFO"."UPDATE_TIME" IS '记录最后一次更新时间';

COMMENT
ON COLUMN "PLUGIN_VERSION_INFO"."VERSION" IS '版本号';

COMMENT
ON COLUMN "PLUGIN_VERSION_INFO"."VERSION_DESC" IS '版本描述';

COMMENT
ON COLUMN "PLUGIN_VERSION_INFO"."YN" IS '记录删除标识';

