package com.chinatelecom.gs.engine.robot.sdk.constant;

/**
 * 公共常量
 *
 * <AUTHOR>
 * @date 2023/5/18
 */
public class CommonConstant {

    private CommonConstant() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 密码加密的盐
     **/
    public static final String SALT = "telecom_ai";

    /**
     * 内部调用header-key
     **/
    public static final String INTERNAL_CALL = "internal_call";

    /**
     * 内部调用header-value
     **/
    public static final String INTERNAL_CALL_VALUE = "internal_call_pass_through";

    /**
     * 租户id的请求头的key
     */
    public static final String TENANT_ID_HEADER_NAME = "x-tenantId";


    public static final String USER_INFO_HEADER_NAME = "x-userInfo-header";


    /**
     * 租户名称的请求头的key
     */
    public static final String TENANT_NAME_HEADER_NAME = "x-tenantName";

    /**
     * 用户id的请求头的key
     */
    public static final String USER_ID_HEADER_NAME = "x-userId";

    /**
     * 用户名称的请求头的key
     */
    public static final String USER_NAME_HEADER_NAME = "x-tenantId";
    /**
     * 异常日志监控，环境
     **/
    public static final String MONITOR_ENVIRONMENT = "MONITOR_ENVIRONMENT";

    /**
     * 异常日志监控，应用名称
     **/
    public static final String APP_NAME = "APP_NAME";

    /**
     * 异常日志监控，服务地址
     **/
    public static final String SERVER_IP = "ServerIP";

    /**
     * 异常日志监控，企微webhook
     **/
    public static final String MONITOR_WEBHOOK = "MONITOR_WEBHOOK";

    /**
     * 异常日志监控，是否开启预警
     **/
    public static final String MONITOR_ENABLE = "MONITOR_ENABLE";

    /**
     * 异常日志监控，消息会发送到预警中心，预警中心在发送到企微群
     **/
    public static final String MONITOR_CENTER = "MONITOR_CENTER";

    /**
     * 异常日志监控，预警中心code
     **/
    public static final String MONITOR_CODE = "MONITOR_CODE";

    /**
     * 异常日志监控，预警中心secretkey
     **/
    public static final String MONITOR_SECRETKEY = "MONITOR_SECRETKEY";

    /**
     * 异常日志监控，通知成员
     **/
    public static final String MONITOR_MEMBER = "MONITOR_MEMBER";

    /**
     * markdown
     **/
    public static final String MARKDOWN = "markdown";

    /**
     * 异常日志监控，异常堆栈信息
     **/
    public static final String STACK = "STACK";

    /**
     * 异常日志监控，时间格式化
     **/
    public static final String MONITOR_DATE_PATTERN = "yyyy-MM-dd HH:mm:ss.SSS";

    /**
     * 预警markdown模版
     **/
    public static final String SEND_WARN_MARKDOWN = """
            **环境:**  <font color="warning">%s</font>\s
             	 \
            **时间:**  <font color="warning">%s</font>\s
             	 \
            **应用名称:** <font color="warning">%s</font>\s
             	 \
            **服务地址:** <font color="warning">%s</font>\s
             	 \
            **线程名称:** <font color="warning">%s</font>\s
             	 \
            **异常位置:** <font color="warning">%s</font>\s
             	 \
            **异常信息:** <font color="warning">%s</font> """;
    /**
     * 会话日志埋点es索引名
     */
    public static final String ES_LOG_MESSAGE_INDEX_NAME = "gs_dialog_trace_log_message";

    /**
     * 知识库搜索埋点es索引名
     */
    public static final String KMS_SEARCH_ES_LOG_MESSAGE_INDEX_NAME = "kms_search_es_log_message_index_name";


}
