package com.chinatelecom.gs.engine.core.entity.service.impl;

import com.chinatelecom.gs.engine.core.model.toolkit.StreamingChatLanguageModel;
import com.chinatelecom.gs.engine.core.model.toolkit.core.dto.LLMRequest;
import com.chinatelecom.gs.engine.core.model.toolkit.core.dto.ModelProviderEnum;
import com.chinatelecom.gs.engine.core.model.toolkit.core.dto.Response;
import com.chinatelecom.gs.engine.core.sdk.rpc.ModelServiceClient;
import com.chinatelecom.gs.engine.core.sdk.vo.ModelPageListParam;
import com.chinatelecom.gs.engine.core.sdk.vo.llm.ToolCall;
import com.chinatelecom.gs.engine.core.sdk.vo.llm.WrapLLMMessage;
import com.chinatelecom.gs.engine.kms.sdk.vo.prompt.PromptTemplateVO;
import com.chinatelecom.gs.engine.kms.service.PromptTemplateAppService;
import com.chinatelecom.gs.engine.robot.sdk.dto.SimpleChatLog;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.entity.ParamRecognitionRequest;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.entity.ParamRecognitionResponse;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.entity.ParamValueVO;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.ExecutorService;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@Slf4j
class ParamRecognitionServiceImplTest {

    @InjectMocks
    private ParamRecognitionServiceImpl paramRecognitionService;

    @Mock
    private PromptTemplateAppService promptTemplateAppService;

    @Mock
    private ModelServiceClient remoteServiceClient;

    @Mock
    private StreamingChatLanguageModel streamingChatLanguageModel;

    @Mock
    private ExecutorService commonExecutorService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testPredict_WithEmptyParams_ReturnsNull() {
        // Given
        ParamRecognitionRequest request = new ParamRecognitionRequest();
        request.setParams(Collections.emptyList());

        // When
        ParamRecognitionResponse response = paramRecognitionService.predict(request);

        // Then
        assertNull(response);
    }

    @Test
    void testPredict_WithNullParams_ReturnsNull() {
        // Given
        ParamRecognitionRequest request = new ParamRecognitionRequest();
        request.setParams(null);

        // When
        ParamRecognitionResponse response = paramRecognitionService.predict(request);

        // Then
        assertNull(response);
    }

    @Test
    void testPredict_WithFunctionCallTrue_CallsDoLLMFunctionRecognize() {
        // Given
        ParamRecognitionRequest request = new ParamRecognitionRequest();
        List<ParamRecognitionRequest.Param> params = new ArrayList<>();
        ParamRecognitionRequest.Param param = new ParamRecognitionRequest.Param();
        param.setName("testParam");
        param.setDescription("Test parameter");
        params.add(param);
        request.setParams(params);
        request.setFunctionCall(true);
        request.setQuery("Test query");

        List<ParamValueVO> mockParamValues = new ArrayList<>();
        ParamValueVO paramValue = new ParamValueVO();
        paramValue.setName("testParam");
        paramValue.setValue("testValue");
        mockParamValues.add(paramValue);

        // Mock the protected method
        ParamRecognitionServiceImpl spyService = Mockito.spy(paramRecognitionService);
        doReturn(mockParamValues).when(spyService).doLLMFunctionRecognize(request);

        // When
        ParamRecognitionResponse response = spyService.predict(request);

        // Then
        assertNotNull(response);
        assertEquals(1, response.getParamValues().size());
        assertEquals("testParam", response.getParamValues().get(0).getName());
        assertEquals("testValue", response.getParamValues().get(0).getValue());
    }

    @Test
    void testPredict_WithFunctionCallFalse_CallsDoLLMPromptRecognize() {
        // Given
        ParamRecognitionRequest request = new ParamRecognitionRequest();
        List<ParamRecognitionRequest.Param> params = new ArrayList<>();
        ParamRecognitionRequest.Param param = new ParamRecognitionRequest.Param();
        param.setName("testParam");
        param.setDescription("Test parameter");
        params.add(param);
        request.setParams(params);
        request.setFunctionCall(false);
        request.setQuery("Test query");

        List<ParamValueVO> mockParamValues = new ArrayList<>();
        ParamValueVO paramValue = new ParamValueVO();
        paramValue.setName("testParam");
        paramValue.setValue("testValue");
        mockParamValues.add(paramValue);

        // Mock the protected method
        ParamRecognitionServiceImpl spyService = Mockito.spy(paramRecognitionService);
        doReturn(mockParamValues).when(spyService).doLLMPromptRecognize(request);

        // When
        ParamRecognitionResponse response = spyService.predict(request);

        // Then
        assertNotNull(response);
        assertEquals(1, response.getParamValues().size());
        assertEquals("testParam", response.getParamValues().get(0).getName());
        assertEquals("testValue", response.getParamValues().get(0).getValue());
    }

    @Test
    void testDoLLMFunctionRecognize_WithValidRequest_ReturnsParamValues() {
        // Given
        ParamRecognitionRequest request = new ParamRecognitionRequest();
        request.setQuery("Test query");
        request.setMessageId("testMessageId");
        request.setModelCode("testModelCode");

        List<ParamRecognitionRequest.Param> params = new ArrayList<>();
        ParamRecognitionRequest.Param param = new ParamRecognitionRequest.Param();
        param.setName("testParam");
        param.setDescription("Test parameter");
        params.add(param);
        request.setParams(params);

        // Mock prompt template
        PromptTemplateVO promptTemplate = new PromptTemplateVO();
        promptTemplate.setContent("Test prompt content with {{query}}");
        when(promptTemplateAppService.get("REPLY_RECOGNIZE_FUNCTION_CALL_PROMPT_TEMPLATE")).thenReturn(promptTemplate);

        // Mock model service client
        ModelPageListParam modelParam = new ModelPageListParam();
        modelParam.setModelProvider("TEST_PROVIDER");
        modelParam.setApiKey("testApiKey");
        modelParam.setModelSecret("testSecret");
        modelParam.setModelCallName("testModelName");
        when(remoteServiceClient.queryByModelCode("testModelCode")).thenReturn(modelParam);

        // Mock LLM response with tool calls
        WrapLLMMessage llmMessage = new WrapLLMMessage();
        List<ToolCall> toolCalls = new ArrayList<>();
        ToolCall toolCall = new ToolCall();
        com.chinatelecom.gs.engine.core.sdk.vo.llm.Function function = new com.chinatelecom.gs.engine.core.sdk.vo.llm.Function();
        function.setArguments("{\"testParam\":\"testValue\"}");
        toolCall.setFunction(function);
        toolCalls.add(toolCall);
        llmMessage.setToolCalls(toolCalls);

        Response<WrapLLMMessage> llmResponse = new Response<>(llmMessage);
        try {
            when(streamingChatLanguageModel.syncGenerateMessage(any(LLMRequest.class))).thenReturn(llmResponse);
        } catch (Exception e) {
            fail("Exception should not be thrown");
        }

        // When
        List<ParamValueVO> result = paramRecognitionService.doLLMFunctionRecognize(request);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("testParam", result.get(0).getName());
        assertEquals("testValue", result.get(0).getValue());
    }

    @Test
    void testDoLLMFunctionRecognize_WithPromptResponse_ReturnsParamValues() {
        // Given
        ParamRecognitionRequest request = new ParamRecognitionRequest();
        request.setQuery("Test query");
        request.setMessageId("testMessageId");
        request.setModelCode("testModelCode");

        List<ParamRecognitionRequest.Param> params = new ArrayList<>();
        ParamRecognitionRequest.Param param = new ParamRecognitionRequest.Param();
        param.setName("testParam");
        param.setDescription("Test parameter");
        params.add(param);
        request.setParams(params);

        // Mock prompt template
        PromptTemplateVO promptTemplate = new PromptTemplateVO();
        promptTemplate.setContent("Test prompt content with {{query}}");
        when(promptTemplateAppService.get("REPLY_RECOGNIZE_FUNCTION_CALL_PROMPT_TEMPLATE")).thenReturn(promptTemplate);

        // Mock model service client
        ModelPageListParam modelParam = new ModelPageListParam();
        modelParam.setModelProvider("TEST_PROVIDER");
        modelParam.setApiKey("testApiKey");
        modelParam.setModelSecret("testSecret");
        modelParam.setModelCallName("testModelName");
        when(remoteServiceClient.queryByModelCode("testModelCode")).thenReturn(modelParam);

        // Mock LLM response with content (no tool calls)
        WrapLLMMessage llmMessage = new WrapLLMMessage();
        llmMessage.setContent("{\"params\":{\"testParam\":\"testValue\"}}");
        llmMessage.setToolCalls(Collections.emptyList());

        Response<WrapLLMMessage> llmResponse = new Response<>(llmMessage);
        try {
            when(streamingChatLanguageModel.syncGenerateMessage(any(LLMRequest.class))).thenReturn(llmResponse);
        } catch (Exception e) {
            fail("Exception should not be thrown");
        }

        // When
        List<ParamValueVO> result = paramRecognitionService.doLLMFunctionRecognize(request);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("testParam", result.get(0).getName());
        assertEquals("testValue", result.get(0).getValue());
    }

    @Test
    void testBuildLLMRequest_WithValidModelCode_ReturnsLLMRequest() {
        // Given
        String modelCode = "testModelCode";
        String prompt = "Test prompt";

        ModelPageListParam modelParam = new ModelPageListParam();
        modelParam.setModelProvider("TEST_PROVIDER");
        modelParam.setApiKey("testApiKey");
        modelParam.setModelSecret("testSecret");
        modelParam.setModelCallName("testModelName");
        when(remoteServiceClient.queryByModelCode(modelCode)).thenReturn(modelParam);

        // When
        LLMRequest result = paramRecognitionService.buildLLMRequest(modelCode, prompt, null, null, null, false);

        // Then
        assertNotNull(result);
        assertEquals(prompt, result.getText());
        assertEquals(0, result.getStreaming()); // false -> 0
        assertEquals(ModelProviderEnum.from("TEST_PROVIDER"), result.getLlmModelInfo().getProvider());
        assertEquals("testApiKey", result.getLlmModelInfo().getModelApi());
        assertEquals("testSecret", result.getLlmModelInfo().getModelSecret());
        assertEquals("testModelName", result.getLlmModelInfo().getModelName());
    }

    @Test
    void testBuildLLMRequest_WithInvalidModelCode_ReturnsNull() {
        // Given
        String modelCode = "invalidModelCode";
        String prompt = "Test prompt";
        when(remoteServiceClient.queryByModelCode(modelCode)).thenReturn(null);

        // When
        LLMRequest result = paramRecognitionService.buildLLMRequest(modelCode, prompt, null, null, null, false);

        // Then
        assertNull(result);
    }

    @Test
    void testConvertHistories_WithEmptyList_ReturnsEmptyList() {
        // When
        List<WrapLLMMessage> result = paramRecognitionService.convertHistories(Collections.emptyList());

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testConvertHistories_WithNullList_ReturnsEmptyList() {
        // When
        List<WrapLLMMessage> result = paramRecognitionService.convertHistories(null);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testConvertHistories_WithValidMessages_ReturnsConvertedMessages() {
        // Given
        List<SimpleChatLog> historyMessages = new ArrayList<>();
        SimpleChatLog chatLog = new SimpleChatLog();
        chatLog.setContent("Test content");
        chatLog.setRole("user");
        historyMessages.add(chatLog);

        // When
        List<WrapLLMMessage> result = paramRecognitionService.convertHistories(historyMessages);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("Test content", result.get(0).getContent());
        assertEquals("user", result.get(0).getRole());
    }

    @Test
    void testDoLlmReplyRecognize_WithInvalidResponse_ReturnsNull() {
        // Given
        ParamRecognitionRequest request = new ParamRecognitionRequest();
        request.setQuery("Test query");
        request.setModelCode("testModelCode");

        ParamRecognitionRequest.Param param = new ParamRecognitionRequest.Param();
        param.setName("testParam");
        param.setDescription("Test parameter");

        // Mock prompt template
        PromptTemplateVO promptTemplate = new PromptTemplateVO();
        promptTemplate.setContent("Test prompt with {{question}} and {{reply}}");
        when(promptTemplateAppService.get("REPLY_RECOGNIZE_PROMPT_TEMPLATE")).thenReturn(promptTemplate);

        // Mock LLM response
        Response<String> llmResponse = new Response<>("否");
        try {
            when(streamingChatLanguageModel.syncGenerate(any(LLMRequest.class))).thenReturn(llmResponse);
        } catch (Exception e) {
            fail("Exception should not be thrown");
        }

        // When
        ParamValueVO result = paramRecognitionService.doLlmReplyRecognize(request, param);

        // Then
        assertNull(result);
    }

    @Test
    void testDoLlmReplyRecognize_WithMissingPromptTemplate_ThrowsBizException() {
        // Given
        ParamRecognitionRequest request = new ParamRecognitionRequest();
        ParamRecognitionRequest.Param param = new ParamRecognitionRequest.Param();
        param.setName("testParam");
        param.setDescription("Test parameter");
        when(promptTemplateAppService.get("REPLY_RECOGNIZE_PROMPT_TEMPLATE")).thenReturn(null);

        // When & Then
        BizException exception = assertThrows(BizException.class, () -> {
            paramRecognitionService.doLlmReplyRecognize(request, param);
        });
        assertEquals("BA010", exception.getCode());
        assertEquals("未找到采集回复识别大模型prompt配置", exception.getMessage());
    }

    @Test
    void testLlmExecute_WithValidRequest_ReturnsResponse() {
        // Given
        String modelCode = "testModelCode";
        String prompt = "Test prompt";

        ModelPageListParam modelParam = new ModelPageListParam();
        modelParam.setModelProvider("TEST_PROVIDER");
        modelParam.setApiKey("testApiKey");
        modelParam.setModelSecret("testSecret");
        modelParam.setModelCallName("testModelName");
        when(remoteServiceClient.queryByModelCode(modelCode)).thenReturn(modelParam);

        Response<String> llmResponse = new Response<>("Test response");
        try {
            when(streamingChatLanguageModel.syncGenerate(any(LLMRequest.class))).thenReturn(llmResponse);
        } catch (Exception e) {
            fail("Exception should not be thrown");
        }

        // When
        String result = paramRecognitionService.llmExecute(modelCode, prompt);

        // Then
        assertEquals("Test response", result);
    }

    @Test
    void testLlmExecute_WithBizException_ThrowsBizException() {
        // Given
        String modelCode = "testModelCode";
        String prompt = "Test prompt";

        ModelPageListParam modelParam = new ModelPageListParam();
        modelParam.setModelProvider("TEST_PROVIDER");
        modelParam.setApiKey("testApiKey");
        modelParam.setModelSecret("testSecret");
        modelParam.setModelCallName("testModelName");
        when(remoteServiceClient.queryByModelCode(modelCode)).thenReturn(modelParam);

        BizException bizException = new BizException("TEST_ERROR", "Test error message");
        try {
            when(streamingChatLanguageModel.syncGenerate(any(LLMRequest.class))).thenThrow(bizException);
        } catch (Exception e) {
            fail("Exception should not be thrown");
        }

        // When & Then
        BizException exception = assertThrows(BizException.class, () -> {
            paramRecognitionService.llmExecute(modelCode, prompt);
        });
        assertEquals("TEST_ERROR", exception.getCode());
        assertEquals("Test error message", exception.getMessage());
    }
}
