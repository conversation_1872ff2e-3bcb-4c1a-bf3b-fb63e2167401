UPDATE plugin_meta_info SET plugin_icon = '/ais/plugin/web/pluginMall/defaultIcon/doc.png' where plugin_id = '1022408486262673408';

UPDATE plugin_meta_info SET plugin_icon = '/ais/plugin/web/pluginMall/defaultIcon/video.png' where plugin_id = '1022407489129811968';

UPDATE plugin_meta_info SET plugin_icon = '/ais/plugin/web/pluginMall/defaultIcon/audio.png'where plugin_id = '1022404434082402304';

UPDATE plugin_meta_info SET plugin_icon = '/ais/plugin/web/pluginMall/defaultIcon/qa.png' where plugin_id = '1022400335559528448';

INSERT INTO plugin_api_param
( tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '10226934525004390401', NULL, '1022408486262673408', '1022408782879657984', 'taskType', '解析任务类型:text:普通文档解析，LLMOCR:大模型解析', 1, 1, 0, 'TEXT', 1, 1, 2, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-20 10:18:39.801', '2025-04-01 07:22:11.905', 0, '');
INSERT INTO plugin_api_param
( tenant_id, param_id, father_param_id, plugin_id, api_id, name, param_desc, req_rsp_type, location, required, default_value, param_type, enabled, version, create_id, create_name, update_id, update_name, create_time, update_time, yn, app_code)
VALUES( '', '10226934525004390401', NULL, '1022408486262673408', '1022408782879657984', 'taskType', '解析任务类型:text:普通文档解析，LLMOCR:大模型解析', 1, 1, 0, 'TEXT', 1, 1, 1, '8577916997213683712', 'admin', '8577916997213683712', 'admin', '2025-03-20 10:18:39.801', '2025-04-01 07:22:29', 0, '');

