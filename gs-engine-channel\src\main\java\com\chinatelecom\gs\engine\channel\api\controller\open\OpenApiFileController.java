package com.chinatelecom.gs.engine.channel.api.controller.open;

import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.kms.service.SessionFileService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;


/**
 * <AUTHOR>
 */
@Slf4j
@Tag(name = "会话文件下载")
@RestController
@RequestMapping(value = "/plugin" + "/temporary" + "/file")
public class OpenApiFileController {

    @Resource
    private SessionFileService sessionFileService;

    @Operation(summary = "会话文件下载", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "会话文件下载", groupName = "对话框挂载文件")
    @GetMapping(value = "/download", produces = MediaType.APPLICATION_JSON_VALUE)
    @AuditLog(businessType = "会话文件下载", operType = "会话文件下载", operDesc = "会话文件下载", objId="#fileKey")
    public void download(@Param("fileKey") String fileKey, @Param("date") Long date, @Param("token") String token, HttpServletResponse response) {
        sessionFileService.download(fileKey, date, token, response);
    }
}
