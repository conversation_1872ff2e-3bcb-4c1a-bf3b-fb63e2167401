package com.chinatelecom.gs.engine.kms.controller;


import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.common.auth.KsMenuConfig;
import com.chinatelecom.gs.engine.common.auth.PermissionTag;
import com.chinatelecom.gs.engine.common.config.aspect.annotation.DebugLog;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.kms.base.model.CodeParam;
import com.chinatelecom.gs.engine.kms.sdk.constants.KmsApis;
import com.chinatelecom.gs.engine.kms.sdk.vo.word.*;
import com.chinatelecom.gs.engine.kms.service.WordTemplateAppService;
import com.chinatelecom.gs.engine.sdk.common.annotation.ApiType;
import com.chinatelecom.gs.engine.sdk.common.annotation.HideFromApiTypes;
import com.chinatelelcom.gs.engine.sdk.common.Page;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;


/**
 * <p>
 * word模板表 Controller
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-20
 */
@RestController
@Slf4j
@Tag(name = "word模板表 Controller")
@HideFromApiTypes(ApiType.OPENAPI)
@RequestMapping({KmsApis.KMS_API + KmsApis.WORD_TEMPLATE_API, KmsApis.OPENAPI + KmsApis.WORD_TEMPLATE_API, KmsApis.RPC + KmsApis.WORD_TEMPLATE_API})
public class WordTemplateController {

    @Resource
    private WordTemplateAppService wordTemplateAppService;

    @Operation(summary = "word模板表分页列表", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @DebugLog(operation = "word模板表分页列表")
    @PlatformRestApi(name = "word模板表分页列表", groupName = "word模板管理")
    @AuditLog(businessType = "word模板管理", operType = "word模板表分页列表", operDesc = "word模板表分页列表", objId="null")
    @PostMapping(KmsApis.PAGE_API)
    @PermissionTag(code = {KsMenuConfig.WORD_TPL, KsMenuConfig.WORD_TPL_1})
    public Result<Page<WordTemplateVO>> page(@Validated @RequestBody WordTemplateQueryParam param) {
        return Result.success(wordTemplateAppService.pageQuery(param));
    }

    @Operation(summary = "word模板表详情", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @DebugLog(operation = "word模板表详情")
    @PlatformRestApi(name = "word模板表详情", groupName = "word模板管理")
    @AuditLog(businessType = "word模板管理", operType = "word模板表详情", operDesc = "word模板表详情", objId="#code")
    @GetMapping(KmsApis.CODE_PATH)
    @PermissionTag(code = {KsMenuConfig.WORD_TPL, KsMenuConfig.WORD_TPL_1})
    public Result<WordTemplateVO> get(@PathVariable("code") String code) {
        return Result.success(wordTemplateAppService.get(code));
    }

    @Operation(summary = "word模板表新增", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @DebugLog(operation = "word模板表新增")
    @PlatformRestApi(name = "word模板表新增", groupName = "word模板管理")
    @AuditLog(businessType = "word模板管理", operType = "word模板表新增", operDesc = "word模板表新增", objId="#_RESULT_.data.code")
    @PostMapping
    @PermissionTag(code = {KsMenuConfig.WORD_TPL, KsMenuConfig.WORD_TPL_1})
    public Result<WordTemplateVO> add(@Validated @RequestBody WordTemplateCreateParam createParam) {
        return Result.success(wordTemplateAppService.create(createParam));
    }


    @Operation(summary = "word模板表更新", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @DebugLog(operation = "word模板表更新")
    @PlatformRestApi(name = "word模板表更新", groupName = "word模板管理")
    @AuditLog(businessType = "word模板管理", operType = "word模板表更新", operDesc = "word模板表更新", objId="#code")
    @PutMapping(KmsApis.CODE_PATH)
    @PermissionTag(code = {KsMenuConfig.WORD_TPL, KsMenuConfig.WORD_TPL_1})
    public Result<Boolean> update(@PathVariable("code") String code, @Validated @RequestBody WordTemplateUpdateParam param) {
        return Result.success(wordTemplateAppService.update(code, param));
    }


    @Operation(summary = "word模板表删除", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @DebugLog(operation = "word模板表删除")
    @PlatformRestApi(name = "word模板表删除", groupName = "word模板管理")
    @AuditLog(businessType = "word模板管理", operType = "word模板表删除", operDesc = "word模板表删除", objId="#codes.code")
    @PostMapping(KmsApis.DELETE_API)
    @PermissionTag(code = {KsMenuConfig.WORD_TPL, KsMenuConfig.WORD_TPL_1})
    public Result<Boolean> delete(@Validated @RequestBody CodeParam codes) {
        return Result.success(wordTemplateAppService.delete(codes));
    }

    @Operation(summary = "渲染公文模板", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PostMapping(KmsApis.RENDER)
    @PlatformRestApi(name = "渲染公文模板", groupName = "word模板管理")
    @AuditLog(businessType = "word模板管理", operType = "渲染公文模板", operDesc = "渲染公文模板", objId="#wordRenderReq.code")
    @DebugLog(operation = "渲染公文模板")
    @PermissionTag(code = {KsMenuConfig.WORD_TPL, KsMenuConfig.WORD_TPL_1})
    public void renderWordTpl(HttpServletResponse response, @RequestBody WordRenderParam wordRenderReq) {
        wordTemplateAppService.renderWordTpl(response, wordRenderReq);
    }

}


