package com.chinatelecom.gs.engine.kms.controller;

import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.common.config.aspect.annotation.DebugLog;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.kms.sdk.constants.KmsApis;
import com.chinatelecom.gs.engine.kms.sdk.vo.extra.KnowledgeExtraInfoCreateParam;
import com.chinatelecom.gs.engine.kms.sdk.vo.extra.KnowledgeExtraInfoVO;
import com.chinatelecom.gs.engine.kms.sdk.vo.extra.KnowledgeExtraQueryMultiParam;
import com.chinatelecom.gs.engine.kms.sdk.vo.extra.KnowledgeExtraQueryOneParam;
import com.chinatelecom.gs.engine.kms.service.KnowledgeExtraInfoAppService;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024年12月13日
 */
@RestController
@Tag(name = "知识扩展属性管理 Controller")
@RequestMapping({KmsApis.KMS_API + KmsApis.KNOWLEDGE + KmsApis.EXTRA_API,
        KmsApis.RPC + KmsApis.KNOWLEDGE + KmsApis.EXTRA_API,
        KmsApis.OPENAPI + KmsApis.KNOWLEDGE + KmsApis.EXTRA_API})
public class KnowledgeExtraInfoController {

    @Resource
    private KnowledgeExtraInfoAppService knowledgeExtraInfoAppService;


    @Operation(summary = "获取知识扩展信息", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "获取知识扩展信息", groupName = "知识扩展属性管理")
    @AuditLog(businessType = "知识扩展属性管理", operType = "获取知识扩展信息", operDesc = "获取知识扩展信息", objId="#knowledgeCode")
    @DebugLog(operation = "获取知识扩展信息")
    @GetMapping(KmsApis.KNOWLEDGE_CODE_PATH)
    public Result<KnowledgeExtraInfoVO> get(@PathVariable("knowledgeCode") String knowledgeCode, @Validated @RequestBody KnowledgeExtraQueryOneParam param) {
        param.setKnowledgeCode(knowledgeCode);
        KnowledgeExtraInfoVO vo = knowledgeExtraInfoAppService.queryByParam(param);
        return Result.success(vo);
    }


    @Operation(summary = "知识扩展信息新增或修改", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "知识扩展信息新增或修改", groupName = "知识扩展属性管理")
    @AuditLog(businessType = "知识扩展属性管理", operType = "知识扩展信息新增或修改", operDesc = "知识扩展信息新增或修改", objId="#createParam.knowledgeCode")
    @DebugLog(operation = "知识扩展信息新增或修改")
    @PostMapping
    public Result<Boolean> addOrUpdate(@Validated @RequestBody KnowledgeExtraInfoCreateParam createParam) {
        knowledgeExtraInfoAppService.createOrUpdate(createParam);
        return Result.success(true);
    }

    @Operation(summary = "批量获取知识扩展信息", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "批量获取知识扩展信息", groupName = "知识扩展属性管理")
    @AuditLog(businessType = "知识扩展属性管理", operType = "批量获取知识扩展信息", operDesc = "批量获取知识扩展信息", objId="#param.knowledgeCode")
    @DebugLog(operation = "批量获取知识扩展信息")
    @PostMapping(KmsApis.BATCH)
    public Result<List<KnowledgeExtraInfoVO>> getBatch(
            @Validated @RequestBody KnowledgeExtraQueryMultiParam param) {
        List<KnowledgeExtraInfoVO> voList = knowledgeExtraInfoAppService.queryByMultiParam(param);
        return Result.success(voList);
    }

}
