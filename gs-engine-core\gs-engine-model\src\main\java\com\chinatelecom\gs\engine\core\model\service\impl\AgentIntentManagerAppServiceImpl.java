package com.chinatelecom.gs.engine.core.model.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.chinatelecom.gs.engine.common.config.mybatis.IgnoreAppCode;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.enums.DimensionEnum;
import com.chinatelecom.gs.engine.common.infra.base.impl.BaseExtendServiceImpl;
import com.chinatelecom.gs.engine.common.utils.*;
import com.chinatelecom.gs.engine.core.model.converter.IntentConfigVoConverter;
import com.chinatelecom.gs.engine.core.model.converter.IntentConverter;
import com.chinatelecom.gs.engine.core.model.entity.dto.AgentIntentConfigDTO;
import com.chinatelecom.gs.engine.core.model.entity.dto.AgentIntentExampleDTO;
import com.chinatelecom.gs.engine.core.model.entity.dto.AgentIntentManagerDTO;
import com.chinatelecom.gs.engine.core.model.entity.param.EmbeddingInfo;
import com.chinatelecom.gs.engine.core.model.entity.po.AgentIntentManagerPO;
import com.chinatelecom.gs.engine.core.model.entity.vo.*;
import com.chinatelecom.gs.engine.core.model.enums.ModelConfigTypeEnum;
import com.chinatelecom.gs.engine.core.model.enums.ModelTypeEnum;
import com.chinatelecom.gs.engine.core.model.mapper.AgentIntentManagerMapper;
import com.chinatelecom.gs.engine.core.model.respository.respository.AgentIntentConfigRepository;
import com.chinatelecom.gs.engine.core.model.respository.respository.AgentIntentExampleRepository;
import com.chinatelecom.gs.engine.core.model.respository.respository.AgentIntentManagerRepository;
import com.chinatelecom.gs.engine.core.model.search.IntentExampleSearchService;
import com.chinatelecom.gs.engine.core.model.search.IntentSearchService;
import com.chinatelecom.gs.engine.core.model.search.model.IntentIndexCreateConfig;
import com.chinatelecom.gs.engine.core.model.service.AgentIntentManagerAppService;
import com.chinatelecom.gs.engine.core.model.service.IntentIndexService;
import com.chinatelecom.gs.engine.core.sdk.request.AgentIntentConfigRequest;
import com.chinatelecom.gs.engine.core.sdk.request.CodeRequest;
import com.chinatelecom.gs.engine.core.sdk.request.IntentQueryRequest;
import com.chinatelecom.gs.engine.core.sdk.rpc.ModelServiceClient;
import com.chinatelecom.gs.engine.core.sdk.vo.*;
import com.chinatelecom.gs.engine.kms.sdk.enums.SystemFileType;
import com.chinatelelcom.gs.engine.sdk.common.Page;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import com.google.common.collect.ImmutableList;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 意图管理表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-10
 */
@Service
@Slf4j
@RefreshScope
public class AgentIntentManagerAppServiceImpl extends BaseExtendServiceImpl<AgentIntentManagerMapper, AgentIntentManagerPO> implements AgentIntentManagerAppService {

    @Resource
    private ResourceLoader resourceLoader;

    @Resource
    private AgentIntentManagerRepository repository;

    @Resource
    private AgentIntentConfigRepository configRepository;

    @Resource
    private AgentIntentExampleRepository exampleRepository;

    @Resource
    private ModelServiceClient modelServiceClient;

    @Resource
    private IntentSearchService intentSearchService;

    @Resource
    private IntentExampleSearchService intentExampleSearchService;

    @Resource
    private TransactionCommitHandler transactionCommitHandler;

    @Resource
    private IntentIndexService intentIndexService;

    @Value("${intent-init.name:打断意图}")
    private String interruptIntentName;

    protected IntentConverter converter() {
        return IntentConverter.INSTANCE;
    }

    protected IntentConfigVoConverter configConverter() {
        return IntentConfigVoConverter.INSTANCE;
    }

    @Override
    public Page<AgentIntentResponse> pageQuery(AgentIntentManagerPageQuery request) {
        IPage<AgentIntentManagerDTO> page = new PageDTO<>(request.getPageNum(), request.getPageSize());
        LambdaQueryWrapper<AgentIntentManagerPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StringUtils.isNotBlank(request.getName()), AgentIntentManagerPO::getName, request.getName());
        queryWrapper.eq(request.getOn() != null, AgentIntentManagerPO::getOn, request.getOn());
        queryWrapper.orderByDesc(AgentIntentManagerPO::getUpdateTime);
        IPage<AgentIntentManagerDTO> data = repository.page(page, queryWrapper);
        // 查询每个意图的意图例句
        fetchSimilarExamples(data.getRecords());
        return IPageUtils.convert(page, dto -> converter().convertVO(dto));

    }

    private void fetchSimilarExamples(List<AgentIntentManagerDTO> list) {
        list.forEach(dto -> {
            List<AgentIntentExampleDTO> examples = exampleRepository.selectByIntentCode(dto.getCode());
            dto.setExamples(examples.stream().map(AgentIntentExampleDTO::getExample).collect(Collectors.toList()));
        });
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public AgentIntentResponse create(AgentIntentManagerRequest createParam, boolean isPreset) {
        BizAssert.notNull(createParam, "A0001", "参数不能为空");
        boolean isSkip = skipCheck(createParam, new ArrayList<>());
        BizAssert.assertFalse(isSkip, "A0074", "模型或模型意图不存在");

        AgentIntentManagerDTO dto = converter().convert(createParam);
        checkAndDuplicateIntent(dto);
        dto.setCode(IdGenerator.getId(StringUtils.EMPTY));
        dto.setIsPreset(isPreset);
        repository.save(dto);
        saveExamples(dto);

        this.pushIntentSearch(dto);
        return converter().convertVO(dto);
    }

    private void pushIntentSearch(AgentIntentManagerDTO dto) {
        try {
            // 确保索引已创建
            intentIndexService.ensureIndexCreated();

            // 推送数据到索引
            intentSearchService.pushIntentData(dto);
        } catch (Exception e) {
            log.error("推送意图数据失败", e);
            throw new BizException("AA013", "推送意图数据失败，请稍后重试");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean update(String code, AgentIntentManagerRequest param) {
        BizAssert.notNull(code, "A0001", "code不能为空");

        AgentIntentManagerDTO dto = repository.selectByCode(code);
        BizAssert.notNull(dto, "A0070", "该意图不存在");
        if ((dto.getIsPreset()) && !dto.getName().equals(param.getName())) {
            throw new BizException("A0088", "预置意图不可修改名称");
        }
        fetchSimilarExamples(Collections.singletonList(dto));

        // 比较所有属性是否相同
        if (dto.equals(converter().convert(param))) {
            log.info("意图{}未修改", dto.getName());
            return true;
        }

        BeanUtils.copyProperties(param, dto);
        dto.setCode(code);
        boolean isSkip = skipCheck(param, new ArrayList<>());
        BizAssert.assertFalse(isSkip, "A0074", "模型或模型意图不存在");
        checkAndDuplicateIntent(dto);

        // 更新意图例句
        exampleRepository.deleteByIntentCode(code);
        saveExamples(dto);

        transactionCommitHandler.handle(() -> updateIntentIndex(dto));
        return repository.updateByCode(dto);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean delete(CodeRequest codes) {
        BizAssert.notNull(codes.getCodes(), "A0001", "codes不能为空");
        Integer count = repository.countIsPreset(codes.getCodes());
        BizAssert.assertFalse(count > 0, "A0001", "codes不能为空");
        // 删除意图例句
        for (String code : codes.getCodes()) {
            exampleRepository.deleteByIntentCode(code);
        }

        boolean b = repository.removeByCodes(codes.getCodes());
        transactionCommitHandler.handle(() -> deleteIntentIndex(codes));
        return b;
    }

    /**
     * 保存意图的意图例句
     */
    private void saveExamples(AgentIntentManagerDTO dto) {
        if (CollectionUtils.isEmpty(dto.getExamples())) {
            return;
        }

        List<AgentIntentExampleDTO> examples = new ArrayList<>();
        for (String example : dto.getExamples()) {
            AgentIntentExampleDTO exampleDTO = new AgentIntentExampleDTO();
            exampleDTO.setCode(IdGenerator.getId(StringUtils.EMPTY));
            exampleDTO.setIntentCode(dto.getCode());
            exampleDTO.setExample(example);
            examples.add(exampleDTO);
        }

        exampleRepository.saveBatch(examples);
    }

    /**
     * 检查意图名称和意图例句是否重复
     */
    private void checkAndDuplicateIntent(AgentIntentManagerDTO dto) {
        List<String> questions = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dto.getExamples())) {
            questions.addAll(dto.getExamples());
        }
        questions.add(dto.getName());

        if (questions.stream().anyMatch(question -> question.length() > 80)) {
            throw new BizException("AD045", "意图或例句超过最大长度80的限制");
        }

        // 检查是否存在重复的意图例句
        if (hasDuplicates(questions)) {
            String message = "意图名称和意图例句存在重复，请检查！" + dto.getName();
            throw new BizException("A0072", new String[]{message}, message);

        }

        // 检查意图名称是否已存在
        AgentIntentManagerDTO existingIntent = repository.selectByName(dto.getName());
        if (existingIntent != null && !existingIntent.getCode().equals(dto.getCode())) {
            String message = "意图名称已存在，请检查：" + dto.getName();
            throw new BizException("A0072", new String[]{message}, message);
        }

        // 检查意图例句是否已存在于其他意图中
        if (CollectionUtils.isNotEmpty(dto.getExamples())) {
            List<AgentIntentExampleDTO> existingExamples = exampleRepository.findByExamples(dto.getExamples());
            existingExamples = existingExamples.stream()
                    .filter(example -> !example.getIntentCode().equals(dto.getCode()))
                    .collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(existingExamples)) {
                StringBuilder message = new StringBuilder("意图例句已存在于其他意图中，请检查：");
                for (AgentIntentExampleDTO example : existingExamples) {
                    AgentIntentManagerDTO intent = repository.selectByCode(example.getIntentCode());
                    message.append(intent.getName())
                            .append("(")
                            .append(example.getExample())
                            .append(") ");
                }
                throw new BizException("A0071", new String[]{message.toString()}, message.toString());

            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean switchOn(BatchSwitchParam param) {
        // 批量开关
        BizAssert.notNull(param, "A0001", "参数不能为空");
        LambdaUpdateWrapper<AgentIntentManagerPO> updateWrapper = new LambdaUpdateWrapper<>();
        if (!CollectionUtils.isEmpty(param.getCodes())) {
            updateWrapper.in(AgentIntentManagerPO::getCode, param.getCodes());
        } else {
            //不传code为全部开关
            updateWrapper.in(AgentIntentManagerPO::getAppCode, RequestContext.getAppCode());
            //查询所有的codes 赋值给param.getCodes() 用于es更新开关
            List<String> allCodes = repository.selectAllCodes();
            param.setCodes(allCodes);
        }
        updateWrapper.set(AgentIntentManagerPO::getOn, param.getOn());
        boolean b = this.update(updateWrapper);
        // 更新意图索引
        transactionCommitHandler.handle(() -> intentSearchService.intentSwitchOn(param.getCodes(), param.getOn()));
        return b;
    }

    @Override
    public void getSystemFile(SystemFileType systemFileType, HttpServletResponse response) {
        try {
            final var resource = resourceLoader.getResource("classpath:system/" + systemFileType.getFile());
            String downloadFileName = systemFileType.getFile();
            InputStream inputStream = resource.getInputStream();
            HttpFileUtils.download(inputStream, downloadFileName, true, response);
        } catch (IOException e) {
            log.error("下载文件异常", e);
            throw new BizException(e, "AA017", "下载文件异常");
        }
    }

    @Override
    public AgentIntentResponse detail(String code) {
        BizAssert.notEmpty(code, "A0001", "code不能为空");

        AgentIntentManagerDTO dto = repository.selectByCode(code);
        BizAssert.notNull(dto, "A0070", "该意图不存在");
        fetchSimilarExamples(Collections.singletonList(dto));
        return converter().convertVO(dto);
    }

    @Override
    public void export(IntentQueryRequest param, HttpServletResponse response) {
        // 查询数据
        List<AgentIntentManagerDTO> list;
        LambdaQueryWrapper<AgentIntentManagerPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByDesc(AgentIntentManagerPO::getUpdateTime);
        if (param.getCodes() == null) {
            list = repository.listAll();
        } else {
            list = repository.listByCodesDesc(param.getCodes());
        }
        fetchSimilarExamples(list);
        // 转换为导出对象
        List<AgentIntentManagerExportResponse> exportList = list.stream()
                .map(dto -> {
                    AgentIntentManagerExportResponse r = new AgentIntentManagerExportResponse();
                    BeanUtils.copyProperties(dto, r);
                    // 处理意图例句和规则的换行显示
                    r.setExamplesList(dto.getExamples());
                    r.setRulesList(dto.getRules());
                    return r;
                })
                .collect(Collectors.toList());

        try (OutputStream outputStream = response.getOutputStream()) {
            // 创建导出参数
            ExportParams params = new ExportParams("意图列表", "sheet1", ExcelType.XSSF);
            // 导出Excel文件
            Workbook workbook = ExcelExportUtil.exportExcel(params, AgentIntentManagerExportResponse.class, exportList);

            // 设置响应头
            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("意图列表.xlsx", "UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);

            // 写入响应流
            workbook.write(outputStream);
            outputStream.flush();
            workbook.close();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String uploadModelIntent(ModelIntentUploadRequest request) {

        // 通过modelServiceClient获取模型的全部意图
        List<ModelIntentParam> modelIntentParams = modelServiceClient.queryIntentList(request.getModelCode());
        BizAssert.notEmpty(modelIntentParams, "A0070", "未找到模型相关意图");

        // 转换ModelIntentParam为AgentIntentManagerRequest
        List<AgentIntentManagerRequest> modelIntents = modelIntentParams.stream()
                .map(param -> {
                    AgentIntentManagerRequest dto = new AgentIntentManagerRequest();
                    dto.setName(param.getIntentName());
                    dto.setModelIntentCode(param.getIntentCode());
                    dto.setModelCode(request.getModelCode());
                    return dto;
                })
                .collect(Collectors.toList());

        // 如果modelIntentCode为空，导入全部意图
        List<AgentIntentManagerRequest> intentsToImport;
        if (CollectionUtils.isEmpty(request.getModelIntentCode())) {
            intentsToImport = modelIntents;
        } else {
            // 否则只导入指定的意图
            intentsToImport = modelIntents.stream()
                    .filter(intent -> request.getModelIntentCode().contains(intent.getModelIntentCode()))
                    .collect(Collectors.toList());
            BizAssert.notEmpty(intentsToImport, "A0070", "未找到指定的模型意图");
        }

        //批量创建
        List<SkipIntentQuestion> createResult = createBatch(intentsToImport);
        // 打印提示
        String message = getUserTip(intentsToImport, createResult);
        log.info(message);

        return message;
    }

    @Override
    public Boolean hasActiveIntents() {
        LambdaQueryWrapper<AgentIntentManagerPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AgentIntentManagerPO::getOn, true);
        return repository.count(wrapper) > 0;
    }

    @SneakyThrows
    @Override
    public String upload(MultipartFile file) {
        ImportParams params = new ImportParams();
        params.setTitleRows(1); // 设置标题行数量，如果你的数据从第二行开始，这里设置为1（可选）
        params.setHeadRows(1);  // 设置头信息行数，如果你的列名在第一行，这里设置为1（可选）
        List<IntentExcelImportRequest> list = ExcelImportUtil.importExcel(file.getInputStream(), IntentExcelImportRequest.class, params);

        // 将Excel导入的数据转换为AgentIntentManagerRequest
        List<AgentIntentManagerRequest> requests = list.stream().map(item -> {
            AgentIntentManagerRequest request = new AgentIntentManagerRequest();
            request.setName(item.getName());
            request.setDescription(item.getDescription());
            request.setModelCode(item.getModelCode());
            request.setModelIntentCode(item.getModelIntentCode());

            // 将例句字符串按换行符分割为列表
            if (StringUtils.isNotBlank(item.getExamples())) {
                List<String> examples = Arrays.asList(item.getExamples().split("\\r?\\n"));
                request.setExamples(examples);
            }

            // 将规则字符串按换行符分割为列表
            if (StringUtils.isNotBlank(item.getRules())) {
                List<String> rules = Arrays.asList(item.getRules().split("\\r?\\n"));
                request.setRules(rules);
            }

            return request;
        }).collect(Collectors.toList());


        //批量创建
        List<SkipIntentQuestion> createResult = createBatch(requests);
        // 打印提示
        String message = getUserTip(requests, createResult);
        log.info(message);

        return message;
    }

    public List<SkipIntentQuestion> createBatch(List<AgentIntentManagerRequest> list) {
        List<SkipIntentQuestion> createResult = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            for (AgentIntentManagerRequest request : list) {
                try {
                    if (skipCheck(request, createResult)) continue; // 校验失败就跳过
                    SkipIntentQuestion result = skipCreate(request);
                    if (result != null) {
                        createResult.add(result);
                    }
                } catch (Exception e) {
                    log.error("导入意图失败，name: {}", request.getName(), e);
                    createResult.add(convert(converter().convert(request), SkipIntentQuestion.QuestionType.master));
                }
            }
        }
        return createResult;
    }

    private boolean skipCheck(AgentIntentManagerRequest request, List<SkipIntentQuestion> createResult) {
        // 校验意图名称、描述和例句长度
        if (request.getName().length() > 80 ||
                (request.getDescription() != null && request.getDescription().length() > 500) ||
                (request.getExamples() != null && request.getExamples().stream().anyMatch(example -> example.length() > 80))) {
            log.warn("意图名称、描述或例句超过最大长度80的限制，跳过意图导入，name: {}", request.getName());
            addSkipIntentQuestion(request.getName(), SkipIntentQuestion.QuestionType.length, createResult);
            return true;
        }

        // modelCode校验
        if (StringUtils.isNotBlank(request.getModelCode())) {
            ModelPageListParam model = modelServiceClient.queryByModelCode(request.getModelCode());
            if (model == null) {
                log.warn("ModelCode不存在，跳过意图导入，name: {}, modelCode: {}", request.getName(), request.getModelCode());
                addSkipIntentQuestion(request.getName(), SkipIntentQuestion.QuestionType.model, createResult);
                return true;
            }
            List<ModelIntentParam> modelIntentParams = modelServiceClient.queryIntentList(request.getModelCode());
            if (CollectionUtils.isNotEmpty(modelIntentParams)) {
                boolean hasIntent = modelIntentParams.stream().anyMatch(intent -> intent.getIntentCode().equals(request.getModelIntentCode()));
                if (!hasIntent) {
                    log.warn("意图Code不存在，跳过意图导入，name: {}, modelIntentCode: {}, modelCode: {}", request.getName(), request.getModelIntentCode(), request.getModelCode()); // 日志中增加 modelCode 上下文
                    addSkipIntentQuestion(request.getName(), SkipIntentQuestion.QuestionType.model, createResult);
                    return true;
                }
            } else {
                log.warn("ModelCode下无意图，跳过意图导入，name: {}, modelCode: {}", request.getName(), request.getModelCode());
                addSkipIntentQuestion(request.getName(), SkipIntentQuestion.QuestionType.model, createResult);
                return true;
            }
        }
        return false;
    }

    /**
     * 添加跳过意图问题到结果列表
     *
     * @param question     问题名称
     * @param questionType 问题类型
     * @param createResult 结果列表
     */
    private void addSkipIntentQuestion(String question, SkipIntentQuestion.QuestionType questionType, List<SkipIntentQuestion> createResult) {
        SkipIntentQuestion skipIntentQuestion = new SkipIntentQuestion();
        skipIntentQuestion.setQuestion(question);
        skipIntentQuestion.setQuestionType(questionType);
        createResult.add(skipIntentQuestion);
    }

    private SkipIntentQuestion convert(AgentIntentManagerDTO dto, SkipIntentQuestion.QuestionType type) {
        if (dto == null) {
            return null;
        }
        SkipIntentQuestion skipQuestion = new SkipIntentQuestion();
        skipQuestion.setIntentCode(dto.getCode());
        skipQuestion.setQuestion(dto.getName());
        skipQuestion.setQuestionType(type);
        return skipQuestion;
    }

    private String getUserTip(List<AgentIntentManagerRequest> createParams, List<SkipIntentQuestion> skipQuestions) {
        if (CollectionUtils.isNotEmpty(skipQuestions)) {
            log.warn("意图导入部分数据被跳过，跳过详情：{}", JsonUtils.toJsonString(skipQuestions));
            Map<SkipIntentQuestion.QuestionType, List<SkipIntentQuestion>> map = skipQuestions.stream()
                    .collect(Collectors.groupingBy(SkipIntentQuestion::getQuestionType));

            List<SkipIntentQuestion> skipMaster = map.getOrDefault(SkipIntentQuestion.QuestionType.master, ImmutableList.of());
            List<SkipIntentQuestion> skipSimilar = map.getOrDefault(SkipIntentQuestion.QuestionType.similar, ImmutableList.of());
            List<SkipIntentQuestion> skipModel = map.getOrDefault(SkipIntentQuestion.QuestionType.model, ImmutableList.of());
            List<SkipIntentQuestion> lengthModel = map.getOrDefault(SkipIntentQuestion.QuestionType.length, ImmutableList.of());
            List<SkipIntentQuestion> otherModel = map.getOrDefault(SkipIntentQuestion.QuestionType.other, ImmutableList.of());


            StringBuilder message = new StringBuilder();
            message.append("导入%d个意图，".formatted(createParams.size()));

            if (CollectionUtils.isNotEmpty(skipMaster)) {
                message.append("跳过意图数：%d（意图名称重复），".formatted(skipMaster.size()));
                for (SkipIntentQuestion skip : skipMaster) {
                    message.append(skip.getQuestion()).append(" ");
                }
            }

            if (CollectionUtils.isNotEmpty(lengthModel)) {
                message.append("跳过意图数：%d（意图名称、描述或例句超过最大长度80的限制），".formatted(lengthModel.size()));
                for (SkipIntentQuestion skip : lengthModel) {
                    message.append(skip.getQuestion()).append(" ");
                }
            }
            if (CollectionUtils.isNotEmpty(otherModel)) {
                message.append("跳过意图数：%d（异常数据），".formatted(otherModel.size()));
                for (SkipIntentQuestion skip : otherModel) {
                    message.append(skip.getQuestion()).append(" ");
                }
            }

            if (CollectionUtils.isNotEmpty(skipModel)) {
                message.append("跳过意图数：%d（模型或模型意图不存在），".formatted(skipModel.size()));
                for (SkipIntentQuestion skip : skipMaster) {
                    message.append(skip.getQuestion()).append(" ");
                }
            }


            if (CollectionUtils.isNotEmpty(skipSimilar)) {
                message.append("跳过意图例句数：%d（意图例句重复），".formatted(skipSimilar.size()));
                for (SkipIntentQuestion skip : skipSimilar) {
                    message.append(skip.getQuestion()).append(" ");
                }

            }
            //过滤出 除QuestionType.similar的跳过数量
            long skipCount = skipQuestions.stream().filter(skip -> !skip.getQuestionType().equals(SkipIntentQuestion.QuestionType.similar)).count();

            message.append("请检查导入的意图。");
            if (createParams.size() == skipCount) {
                throw new BizException("A0073", new String[]{message.toString()}, message.toString());
            }
            return message.toString();
        }
        return "导入成功，总计导入%d个意图".formatted(createParams.size());
    }

    public SkipIntentQuestion skipCreate(AgentIntentManagerRequest createParam) {
        AgentIntentManagerDTO dto = converter().convert(createParam);
        if (StringUtils.isNotBlank(createParam.getCode())) {
            AgentIntentManagerDTO existDto = repository.selectByCode(createParam.getCode());
            BizAssert.isNull(existDto, "A0003", "编码重复");
        } else {
            dto.setCode(IdGenerator.getId(StringUtils.EMPTY));
        }

        // 检查名称是否重复
        AgentIntentManagerDTO existName = repository.selectByName(dto.getName());
        if (existName != null) {
            log.error("意图名称重复，中断入库。intentCode:{} 名称：{}", existName.getCode(), existName.getName());
            return convert(existName, SkipIntentQuestion.QuestionType.master);
        }

        // 检查意图例句是否重复
        Map<String, SkipIntentQuestion> skipQuestionMap = this.skipExamples(dto);
        if (!skipQuestionMap.isEmpty()) {
            // 如果有重复的意图例句，返回第一个重复的记录
            return skipQuestionMap.values().iterator().next();
        }

        repository.save(dto);
        saveExamples(dto);
        this.pushIntentSearch(dto);

        return null;
    }

    protected Map<String, SkipIntentQuestion> skipExamples(AgentIntentManagerDTO dto) {
        Set<String> examples = new HashSet<>();
        if (CollectionUtils.isNotEmpty(dto.getExamples())) {
            examples.addAll(dto.getExamples());
        }

        Map<String, SkipIntentQuestion> skipQuestionMap = new HashMap<>();

        // 检查意图例句是否在其他意图中已存在
        List<AgentIntentExampleDTO> existingExamples = exampleRepository.findByExamples(new ArrayList<>(examples));
        for (AgentIntentExampleDTO example : existingExamples) {
            // 排除当前意图的意图例句
            if (!example.getIntentCode().equals(dto.getCode())) {
                SkipIntentQuestion skip = new SkipIntentQuestion();
                skip.setIntentCode(example.getIntentCode());
                skip.setQuestion(example.getExample());
                skip.setQuestionType(SkipIntentQuestion.QuestionType.similar);
                skipQuestionMap.put(example.getExample(), skip);
                examples.remove(example.getExample());
            }
        }

        // 更新DTO中的意图例句列表，移除重复的
        dto.setExamples(new ArrayList<>(examples));
        return skipQuestionMap;
    }

    @Override
    @IgnoreAppCode
    public List<AgentIntentResponse> queryList(CodeRequest codes) {
        BizAssert.notNull(codes.getCodes(), "A0001", "codes不能为空");
        List<AgentIntentManagerDTO> list = repository.listByCodes(codes.getCodes());

        return list.stream()
                .map(converter()::convertVO).collect(Collectors.toList());
    }

    @Override
    public AgentIntentResponse queryInterruptIntent() {
        LambdaQueryWrapper<AgentIntentManagerPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AgentIntentManagerPO::getOn, true);
        queryWrapper.eq(AgentIntentManagerPO::getYn, 0);
        queryWrapper.eq(AgentIntentManagerPO::getIsPreset, true);
        queryWrapper.eq(AgentIntentManagerPO::getName, interruptIntentName);
        AgentIntentManagerDTO agentIntentManagerDTO = repository.selectOne(queryWrapper);
        return converter().convertVO(agentIntentManagerDTO);
    }

    @Override
    public AgentIntentConfigResponse getIntentConfigOrDefault(String appCode) {
        // 获取所有配置
        AgentIntentConfigDTO config = configRepository.selectAllOrDefault(appCode);
        BizAssert.notNull(config, "AD019", "未找到任何配置");
        // 转换为响应对象并返回
        return configConverter().convertVO(config);
    }

    @Override
    public Boolean creatOrUpdateConfig(AgentIntentConfigRequest param) {
        AgentIntentConfigDTO dto = configConverter().convertDTO(param);
        if ((dto.getUseRerank() && dto.getRerankModelCode().isEmpty()) || dto.getEmbeddingModelCode().isEmpty()) {
            throw new BizException("A0037", "模型不能为空");
        }
        if (dto.getUseRerank()) {
            checkModelCode(dto.getRerankModelCode());
        } else {
            dto.setRerankModelCode(StringUtils.EMPTY);
        }
        checkModelCode(dto.getEmbeddingModelCode());
        dto.setDimension(DimensionEnum.TENANT);
        AgentIntentConfigDTO record = configRepository.selectByAppCode();

        boolean flag;
        if (record == null) {
            dto.setCode(IdGenerator.getId(StringUtils.EMPTY));
            flag = configRepository.save(dto);
        } else {
            dto.setId(record.getId());
            dto.setCode(record.getCode());

            flag = configRepository.saveOrUpdate(dto);
        }

        // 检查模型是否发生变化并进行索引迁移
        try {
            changeIntentIndex(record, dto);
        } catch (Exception e) {
            log.error("意图索引迁移失败", e);
            throw new BizException("AA013", "意图索引迁移失败，请稍后重试");
        }

        return flag;
    }

    /**
     * 检查模型变更并更新索引
     */
    private void changeIntentIndex(AgentIntentConfigDTO oldConfig, AgentIntentConfigDTO newConfig) {
        if (oldConfig != null && newConfig != null) {
            IntentIndexCreateConfig oldCreateIndex = buildIntentIndexConfig(oldConfig);
            IntentIndexCreateConfig newCreateIndex = buildIntentIndexConfig(newConfig);

            if (oldCreateIndex != null && newCreateIndex != null) {
                boolean modelChanged = !StringUtils.equals(oldConfig.getEmbeddingModelCode(), newConfig.getEmbeddingModelCode());

                if (modelChanged) {
                    intentSearchService.reindexAndMigrate(newCreateIndex);
                    log.info("模型变更，更新重构意图索引：{} -> {}", oldConfig.getEmbeddingModelCode(), newConfig.getEmbeddingModelCode());
                }
            } else {
                log.warn("更新重构意图索引失败，请检查模型配置是否正确");
            }
        }
    }

    /**
     * 构建意图索引配置
     */
    private IntentIndexCreateConfig buildIntentIndexConfig(AgentIntentConfigDTO config) {
        if (config == null) {
            return null;
        }

        IntentIndexCreateConfig indexConfig = new IntentIndexCreateConfig();
        String modelCode = config.getEmbeddingModelCode();
        if (StringUtils.isBlank(modelCode)) {
            return null;
        }
        // 获取向量模型信息
        ModelPageListParam embeddingModel = modelServiceClient.queryByModelCode(modelCode);
        if (embeddingModel == null || !ModelTypeEnum.EMBEDDING.getCode().equals(embeddingModel.getModelType())) {
            return null;
        }
        EmbeddingInfo embeddingInfo = getEmbeddingInfo(embeddingModel);
        String embeddingOfflineName = embeddingInfo.getEmbeddingName(ModelConfigTypeEnum.OFFLINEDOCUMENTSMODELCONFIG);
        if (com.alibaba.excel.util.StringUtils.isNotBlank(embeddingOfflineName)) {
            indexConfig.setVectorDocAnalyzer(embeddingOfflineName);
        }

        String embeddingQueryName = embeddingInfo.getEmbeddingName(ModelConfigTypeEnum.USERREQUEST);
        if (com.alibaba.excel.util.StringUtils.isNotBlank(embeddingQueryName)) {
            indexConfig.setVectorQueryAnalyzer(embeddingQueryName);
        }


        return indexConfig;
    }

    private boolean hasDuplicates(List<String> list) {
        long distinctCount = list.stream().distinct().count();
        return distinctCount != list.size();
    }

    private void updateIntentIndex(AgentIntentManagerDTO dto) {
        String code = dto.getCode();
        try {
            // 删除索引
            intentSearchService.deleteIntentData(code);
            // 重新索引
            intentSearchService.pushIntentData(dto);
            log.info("意图索引更新成功，code:{}", code);
        } catch (Exception e) {
            log.error("意图索引更新失败，code:{}", code, e);
            throw new BizException("AA013", "意图索引更新失败，请稍后重试");
        }
    }


    private void deleteIntentIndex(CodeRequest codes) {
        try {
            // 批量删除索引
            for (String code : codes.getCodes()) {
                intentSearchService.deleteIntentData(code);
            }
            log.info("意图索引删除成功，codes:{}", codes.getCodes());
        } catch (Exception e) {
            log.error("意图索引删除失败，codes:{}", codes.getCodes(), e);
            throw new BizException("AA013", "意图索引删除失败，请稍后重试");
        }
    }

    @Override
    public IntentSearchVO<IntentIndexData> queryIntent(IntentQueryRequest request, String appCode) {
        return intentExampleSearchService.search(request.getQuery(), request.getCodes(), appCode);
    }

    public EmbeddingInfo getEmbeddingInfo(ModelPageListParam embedding) {
        BizAssert.notNull(embedding, "AA039", "未配置向量模型");
        String modelConfig = embedding.getExternalModelConfig();
        List<EmbeddingInfo.EmbeddingConfigInfo> embeddingConfigInfos = JsonUtils.parseObjectArray(modelConfig, EmbeddingInfo.EmbeddingConfigInfo.class);
        EmbeddingInfo embeddingInfo = new EmbeddingInfo();
        embeddingInfo.setModelCode(embedding.getModelCode());
        embeddingInfo.setModelName(embedding.getModelName());
        embeddingInfo.setEmbeddingConfigInfos(embeddingConfigInfos);
        return embeddingInfo;
    }

    private void checkModelCode(String modelCode) {
        if (StringUtils.isNotBlank(modelCode)) {
            ModelPageListParam model = modelServiceClient.queryByModelCode(modelCode);
            BizAssert.notNull(model, "A0037", "模型编码不存在");
        }
    }
}
