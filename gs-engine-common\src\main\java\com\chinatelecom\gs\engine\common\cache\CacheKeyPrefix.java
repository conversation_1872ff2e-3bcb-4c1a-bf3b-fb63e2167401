package com.chinatelecom.gs.engine.common.cache;

public final class CacheKeyPrefix {

    // 防止实例化
    private CacheKeyPrefix() {
    }

//    // 公共缓存前缀
//    public static final String DATA_CACHE = "dataCache:";
//    public static final String USER_CACHE = "userCache:";
//    public static final String PRODUCT_CACHE = "productCache:";
//    public static final String ORDER_CACHE = "orderCache:";
//    public static final String PROMPT_TEMPLATE_CACHE = "promptTemplateCache:";
//
//    // 系统级别的缓存前缀
//    public static final String SYSTEM_CONFIG_CACHE = "systemConfigCache:";
//    public static final String PERMISSION_CACHE = "permissionCache:";
//
//    // 临时缓存（例如会话相关）
//    public static final String SESSION_CACHE = "sessionCache:";
//    public static final String TEMPORARY_DATA_CACHE = "temporaryDataCache:";
//
//    // 特定业务逻辑缓存前缀
//    public static final String REPORT_CACHE = "reportCache:";
//    public static final String STATISTICS_CACHE = "statisticsCache:";

    public static final String KMS_CACHE = "kmsCache:";

}