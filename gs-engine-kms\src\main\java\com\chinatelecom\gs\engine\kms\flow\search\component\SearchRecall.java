package com.chinatelecom.gs.engine.kms.flow.search.component;

import com.chinatelecom.ai.search.query_dsl.KnnQuery;
import com.chinatelecom.ai.search.query_dsl.Query;
import com.chinatelecom.ai.search.req.SearchRequest;
import com.chinatelecom.ai.search.resp.SearchResponse;
import com.chinatelecom.gs.engine.common.config.property.GsGlobalConfig;
import com.chinatelecom.gs.engine.common.context.LocalContext;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.enums.LogStatusEnum;
import com.chinatelecom.gs.engine.common.enums.LogTypeEnum;
import com.chinatelecom.gs.engine.common.enums.SearchTemplateType;
import com.chinatelecom.gs.engine.common.log.track.LogMessage;
import com.chinatelecom.gs.engine.common.log.track.LogMqProducer;
import com.chinatelecom.gs.engine.common.utils.JsonUtils;
import com.chinatelecom.gs.engine.common.utils.TemplateUtil;
import com.chinatelecom.gs.engine.kms.convert.common.CommonConverter;
import com.chinatelecom.gs.engine.kms.dto.KnowledgeBaseDTO;
import com.chinatelecom.gs.engine.kms.flow.search.SearchContext;
import com.chinatelecom.gs.engine.kms.flow.search.dto.KsSearchResult;
import com.chinatelecom.gs.engine.kms.flow.search.dto.SearchParamTemplate;
import com.chinatelecom.gs.engine.kms.flow.search.dto.SearchRecallWrapper;
import com.chinatelecom.gs.engine.kms.model.dto.KnowledgeDTO;
import com.chinatelecom.gs.engine.kms.repository.KnowledgeRepository;
import com.chinatelecom.gs.engine.kms.sdk.enums.KnowledgeBaseType;
import com.chinatelecom.gs.engine.kms.sdk.enums.KnowledgeType;
import com.chinatelecom.gs.engine.kms.sdk.enums.RecallType;
import com.chinatelecom.gs.engine.kms.sdk.enums.SceneType;
import com.chinatelecom.gs.engine.kms.sdk.vo.search.SearchParam;
import com.chinatelecom.gs.engine.kms.search.IndexBuild;
import com.chinatelecom.gs.engine.kms.search.model.BaseIndexData;
import com.chinatelecom.gs.workflow.core.workflow.core.utils.LogUtils;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Maps;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import freemarker.template.Template;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import jakarta.annotation.Resource;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CountDownLatch;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@LiteflowComponent(id = "SearchRecall", name = "搜索召回")
public class SearchRecall extends BaseRecall {

    /**
     * 拼音正则搜索
     */
    public static final Pattern SPELL_PATTERN = Pattern.compile("^[a-zA-Z\\d\\s\\p{Punct}]*$", Pattern.MULTILINE);

    private final Map<SearchTemplateType, Template> templateMap = Maps.newHashMap();

    private final Map<RecallType, String> rankExpressionMap = Maps.newHashMap();

    @Resource
    private KnowledgeRepository knowledgeRepository;

    @Resource
    private LogMqProducer logMqProducer;


    public SearchRecall(GsGlobalConfig gsGlobalConfig) {
        // 遍历 KmsProperty 中的搜索模板，并将其转换为模板对象存储在 templateMap 中
        gsGlobalConfig.getSearch().getSearchTemplate().forEach((indexType, template) -> {
            templateMap.put(indexType, TemplateUtil.createTemplate(template));
        });

        gsGlobalConfig.getSearch().getRankExpression().forEach((recallType, expression) -> {
            rankExpressionMap.put(recallType, expression);
        });
    }

    @Override
    public void process() throws Exception {
        SearchParam searchParam = this.getRequestData();
        SearchContext searchContext = this.getContextBean(SearchContext.class);
        SearchParam.Filter filter = searchParam.getFilter();
        List<SearchParam.KnowledgeFilter> knowledgeFilters = filter.getKnowledgeFilters();
        Map<String, KnowledgeBaseDTO> searchKnowledgeBaseMap = searchContext.getKnowledgeBaseDTOMap();

        // 并发从search中进行召回
        List<KsSearchResult> indexDataResults = Collections.synchronizedList(new ArrayList<>());
        CountDownLatch latch = new CountDownLatch(knowledgeFilters.size());
        knowledgeFilters.forEach(knowledgeFilter -> {
            searchFlowPoolExecutor.submit(() -> {
                try {
                    log.info("【开始从知识库召回】 knowledgeBaseCode: {}", knowledgeFilter.getKnowledgeBaseCode());
                    searchKnowledgeBase(searchParam, knowledgeFilter, indexDataResults, searchKnowledgeBaseMap);
                } catch (Exception e) {
                    log.error("knowledge base search fail {}", knowledgeFilter.getKnowledgeBaseCode(), e);
                } finally {
                    latch.countDown();
                }
            });
        });
        latch.await();

        List<SearchRecallWrapper<BaseIndexData>> retrieveItems = buildSearchRecallByKSR(indexDataResults);
        searchContext.appendRecallResult(retrieveItems, true);
    }

    /**
     * 在一个知识库中召回数据
     */
    private void searchKnowledgeBase(SearchParam searchParam, SearchParam.KnowledgeFilter knowledgeFilter,
                                     List<KsSearchResult> indexDataResults,
                                     Map<String, KnowledgeBaseDTO> searchKnowledgeBaseMap) {
        // 获取知识库信息 - 知识库过滤已在KnowledgeBaseFilter中完成
        KnowledgeBaseDTO knowledgeBaseDTO = searchKnowledgeBaseMap.get(knowledgeFilter.getKnowledgeBaseCode());
        if (knowledgeBaseDTO == null) {
            // 如果知识库不在过滤后的Map中，说明已被过滤掉
            return;
        }

        /**
         * 构造搜索请求
         */
        SearchRequest searchRequest = buildSearchRequest(searchParam, knowledgeFilter, knowledgeBaseDTO);

        RecallType recallType = searchParam.getRecallType();
        if (Objects.equals(recallType, RecallType.separate_both)) {
            KnnQuery knn = searchRequest.getKnn();

            // 进行两路召回
            // 倒排召回
            searchRequest.setKnn(null);
            String rankExpression = rankExpressionMap.get(RecallType.keyword);
            if (StringUtils.isNotBlank(rankExpression) && Objects.equals(searchParam.getSceneType(), SceneType.chunk)) {
                searchRequest.setFirstRankExpression(rankExpression);
            }
            executeSearchTask(searchParam, searchRequest, indexDataResults, RecallType.keyword);

            // 向量召回
            searchRequest.setKnn(knn);
            searchRequest.setQuery(Query.of(q -> q.matchNone(m -> m)));
            rankExpression = rankExpressionMap.get(RecallType.knn);
            if (StringUtils.isNotBlank(rankExpression) && Objects.equals(searchParam.getSceneType(), SceneType.chunk)) {
                searchRequest.setFirstRankExpression(rankExpression);
            }
            executeSearchTask(searchParam, searchRequest, indexDataResults, RecallType.knn );
        } else {
            executeSearchTask(searchParam, searchRequest, indexDataResults, recallType);
        }
    }

    private SearchResponse<BaseIndexData> executeSearchTask(SearchParam searchParam, SearchRequest searchRequest,
                                                            List<KsSearchResult> indexDataResults, RecallType recallType) {
        SearchResponse<BaseIndexData> resp = null;
        LocalDateTime startTime = LocalDateTime.now();
        LogStatusEnum statusEnum = LogStatusEnum.SUCCESS;
        try {
            log.info("向搜索发送请求，request: {}", searchRequest);
            resp = aiSearchClient.online().search(searchRequest, BaseIndexData.class);
            if (resp != null) {
                KsSearchResult ksr = KsSearchResult.builder().searchResponse(resp).searchRequest(searchRequest).recallType(recallType).build();
                indexDataResults.add(ksr);
                log.info("搜索响应结果，response: {}", JsonUtils.toJsonString(resp));
            }
        } catch (IOException e) {
            statusEnum = LogStatusEnum.FAILED;
            throw new BizException(e, "C0002", ArrayUtils.EMPTY_OBJECT_ARRAY, "请求搜索接口失败：{}", e.getMessage());
        } finally {
            String messageId = StringUtils.isNotBlank(LocalContext.getMessageId()) ? LocalContext.getMessageId() : RequestContext.getMessageId();
            if (StringUtils.isNotBlank(messageId)) {
                LogMessage logMessage = LogUtils.buildCommonLog(LogTypeEnum.ES_SEARCH, searchRequest.toString(), resp, statusEnum, startTime, LocalDateTime.now(), "");
                String searchType = Objects.isNull(searchParam.getRecallType()) ? "" : searchParam.getRecallType().name();
                logMessage.setName("知识库ES检索-" + searchType);
                logMqProducer.sendLogMessage(logMessage);
            }
        }
        return resp;
    }

    /**
     * 构造搜索请求
     * @param searchParam
     * @param knowledgeFilter
     * @param knowledgeBaseDTO
     * @return
     */
    private SearchRequest buildSearchRequest(SearchParam searchParam, SearchParam.KnowledgeFilter knowledgeFilter, KnowledgeBaseDTO knowledgeBaseDTO) {
        SearchTemplateType searchTemplateType = getSearchTemplateType(searchParam, knowledgeBaseDTO);
        SearchRequest searchRequest = buildSearchRequest(searchParam, searchTemplateType);
        String indexName = IndexBuild.indexName(knowledgeFilter.getKnowledgeBaseCode(), searchParam.getEnv());
        searchRequest.setInstanceName(indexName);
        searchRequest.setGlobalFilters(buildFilters(knowledgeFilter, searchParam));
        if (StringUtils.isBlank(searchParam.getQuery())) {
            searchParam.setRecallType(RecallType.filter);
        }
        boolean isPreciseRetrieval = searchParam.getRecallStrategy().isPreciseRetrieval();
        // 是否是图片的精确检索，精确检索的评分是0，会导致挂载时被过滤调，这里兼容两种处理
        if (isPreciseRetrieval) {
            List<String> knowledgeCodes = knowledgeFilter.getKnowledgeCodes();
            if (CollectionUtils.isNotEmpty(knowledgeCodes) && knowledgeCodes.size() == 1) {
                //如果是图片类型 knowledgeCodes 仅包含一个元素，则不使用向量搜索
                KnowledgeDTO knowledge = knowledgeRepository.selectByCode(knowledgeCodes.get(0));
                if (knowledge != null && KnowledgeType.IMAGE == knowledge.getType()) {
                    log.info("知识图片全部查询knowledgeCode：{}", knowledgeCodes.get(0));
                    searchRequest.setQuery(null);
                    searchRequest.setKnn(null);
                    searchRequest.setFirstRankExpression(null);
                    searchRequest.setRank(null);
                }
            }
        }

        RecallType recallType = searchParam.getRecallType();
        if (recallType != null) {
            switch (recallType) {
                case knn:
                    searchRequest.setQuery(Query.of(q -> q.matchNone(m -> m)));
                    break;
                case keyword:
                    searchRequest.setKnn(null);
                    break;
                case filter:
                    searchRequest.setKnn(null);
                    searchRequest.setQuery(null);
                    break;
                default:
            }
            String rankExpression = rankExpressionMap.get(recallType);
            // 分数配置仅对分片召回场景生效
            if (StringUtils.isNotBlank(rankExpression)) {
                searchRequest.setFirstRankExpression(rankExpression);
            }
        }
        return searchRequest;
    }

    private SearchRequest buildSearchRequest(SearchParam searchParam, SearchTemplateType searchTemplateType) {
        SearchParamTemplate searchParamTemplate = CommonConverter.INSTANCE.convert(searchParam);
        Template requestTemplate = templateMap.get(searchTemplateType);
        String request = TemplateUtil.applyObj(requestTemplate, searchParamTemplate);

        // 构建搜索请求
        SearchRequest searchRequest;
        try {
            if (StringUtils.isBlank(request)) {
                searchRequest = new SearchRequest();
            } else {
                searchRequest = aiSearchClient.deserialize(request, SearchRequest._DESERIALIZER);
            }
        } catch (Exception e) {
            log.error("反序列搜索请求失败, ", e);
            throw new BizException(e, "B0001", "反序列搜索请求失败");
        }
        return searchRequest;
    }

    /**
     * 是否是拼音搜索
     * @param searchParam
     * @return
     */
    private boolean isSpell(SearchParam searchParam) {
        boolean isSpell = false;
        String query = searchParam.getQuery();
        Boolean spellSwitch = searchParam.getRecallStrategy().getSpellSwitch();
        if (StringUtils.isNotBlank(query) && BooleanUtils.isTrue(spellSwitch)) {
            Matcher matcher = SPELL_PATTERN.matcher(query);
            if (matcher.matches()) {
                isSpell = true;
            }
        }
        return isSpell;
    }

    List<Query> buildFilters(SearchParam.KnowledgeFilter knowledgeFilter, SearchParam searchParam) {
        List<Query> mustList = buildMustFilters(knowledgeFilter, searchParam);

        List<Query> mustNotList = buildMustNotFilters(knowledgeFilter, searchParam);

        //查询分片、FAQ数据
        Query fileIndexQuery = Query.of(q -> q.term(t -> t.field(IndexBuild.INDEX).value(0)));
        mustNotList.add(fileIndexQuery);

        List<Query> globalFilters = ImmutableList.of(Query.of(q -> q.bool(b -> b.must(mustList).mustNot(mustNotList))));
        return globalFilters;
    }

    private SearchTemplateType getSearchTemplateType(SearchParam searchParam, KnowledgeBaseDTO knowledgeBaseDTO) {
        SearchTemplateType templateType;
        KnowledgeBaseType type = knowledgeBaseDTO.getType();
        SceneType sceneType = searchParam.getSceneType();
        boolean spell = isSpell(searchParam);

        if (StringUtils.isBlank(searchParam.getQuery())) {
            templateType = sceneType == SceneType.chunk ? SearchTemplateType.filter : SearchTemplateType.search_filter;
        } else {
            if (type == KnowledgeBaseType.FAQ) {
                if (sceneType == SceneType.chunk) {
                    templateType = spell ? SearchTemplateType.spell : SearchTemplateType.faq;
                } else {
                    templateType = spell ? SearchTemplateType.search_spell : SearchTemplateType.search_faq;
                }
            } else {
                if (sceneType == SceneType.chunk) {
                    templateType = spell ? SearchTemplateType.spell : SearchTemplateType.doc;
                } else {
                    templateType = spell ? SearchTemplateType.search_spell : SearchTemplateType.search_doc;
                }
            }
        }
        return templateType;
    }
}
