package com.chinatelecom.gs.engine.channel.application.listener;

import com.chinatelecom.gs.engine.channel.common.enums.ChannelTypeEnum;
import com.chinatelecom.gs.engine.channel.dao.po.ChannelInfoPO;
import com.chinatelecom.gs.engine.channel.dao.repository.ChannelInfoRepository;
import com.chinatelecom.gs.engine.channel.manage.ChannelSecretManagerService;
import com.chinatelecom.gs.engine.channel.manage.WebLinkConfigService;
import com.chinatelecom.gs.engine.channel.service.dto.ChannelInfoDTO;
import com.chinatelecom.gs.engine.robot.sdk.config.enums.ApiSecretType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: xktang
 * @date: 2024/7/17 上午9:48
 * @version: 1.0
 */
@Slf4j
@Component
public class ChannelOffSiteSwitchOpenEventListener implements ApplicationListener<ChannelOffSiteSwitchOpenEvent> {

    private static final String DEFAULT_SECRET_NAME = "未命名";

    private final WebLinkConfigService webLinkConfigService;
    private final ChannelSecretManagerService channelSecretManagerService;
    private final ChannelInfoRepository channelInfoRepository;

    public ChannelOffSiteSwitchOpenEventListener(ChannelInfoRepository channelInfoRepository, WebLinkConfigService webLinkConfigService, ChannelSecretManagerService channelSecretManagerService) {
        this.channelInfoRepository = channelInfoRepository;
        this.webLinkConfigService = webLinkConfigService;
        this.channelSecretManagerService = channelSecretManagerService;
    }

    @Override
    public void onApplicationEvent(ChannelOffSiteSwitchOpenEvent event) {
        String source = event.getSource();
        log.info("Received channel off site switch open event ==> {}", source);
        this.ifNeedInitOffSiteChannel(source);
    }

    private void ifNeedInitOffSiteChannel(String agentCode) {
        Map<ChannelTypeEnum, ChannelInfoDTO> existChannelMap = new HashMap<>();
        List<ChannelInfoDTO> list = channelInfoRepository.getChannelInfoList(agentCode, ChannelTypeEnum.OFF_SITE_CHANNEL);
        if (!CollectionUtils.isEmpty(list)) {
            if (list.size() == ChannelTypeEnum.values().length) {
                log.info("not need init off site channel : {}", agentCode);
                return;
            }
            existChannelMap = list.stream().collect(Collectors.toMap(ChannelInfoDTO::getChannelType, Function.identity(), (a, b) -> a));
        }
        // 将不存在的渠道进行初始化
        List<ChannelInfoPO> initChannelList = new ArrayList<>();
        ChannelTypeEnum[] channelTypeEnums = ChannelTypeEnum.values();
        for (ChannelTypeEnum channelTypeEnum : channelTypeEnums) {
            ChannelInfoDTO dto = existChannelMap.get(channelTypeEnum);
            if (dto == null) {
                ChannelInfoPO defaultChannel = ChannelInfoRepository.getDefaultChannel(agentCode, channelTypeEnum);
                initChannelList.add(defaultChannel);
            }
        }
        if (!CollectionUtils.isEmpty(initChannelList)) {
            log.info("init off site channel {}", initChannelList);
            channelInfoRepository.saveBatch(initChannelList);
            initOffSiteChannelConfig(initChannelList);
        }
    }

    private void initOffSiteChannelConfig(List<ChannelInfoPO> channelInfoPOList) {
        for (ChannelInfoPO channelInfoPO : channelInfoPOList) {
            final String channelId = channelInfoPO.getChannelId();
            final String channelName = channelInfoPO.getChannelName();
            final ChannelTypeEnum channelType = channelInfoPO.getChannelType();
            final String agentCode = channelInfoPO.getAgentCode();
            if (ChannelTypeEnum.WEB_LINK == channelType) {
                log.info("{} ==> init web link channel config", agentCode);
                webLinkConfigService.generateWebLinkConfig(channelName, channelId, agentCode);
            } else if (ChannelTypeEnum.API == channelType) {
                log.info("{} ==> init default api channel config", agentCode);
                channelSecretManagerService.create(agentCode, channelId, DEFAULT_SECRET_NAME, ApiSecretType.API);
            }
        }
    }
}
