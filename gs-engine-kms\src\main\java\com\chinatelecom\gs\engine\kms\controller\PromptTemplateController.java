package com.chinatelecom.gs.engine.kms.controller;


import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.common.config.property.GsGlobalConfig;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.common.platform.StatOpenApi;
import com.chinatelecom.gs.engine.kms.base.model.CodeParam;
import com.chinatelecom.gs.engine.kms.common.SseEmitterUTF8;
import com.chinatelecom.gs.engine.kms.sdk.api.PromptAppServiceApi;
import com.chinatelecom.gs.engine.kms.sdk.constants.KmsApis;
import com.chinatelecom.gs.engine.kms.sdk.vo.prompt.*;
import com.chinatelecom.gs.engine.kms.service.PromptApplicationService;
import com.chinatelecom.gs.engine.kms.service.PromptTemplateAppService;
import com.chinatelecom.gs.engine.sdk.common.annotation.ApiType;
import com.chinatelecom.gs.engine.sdk.common.annotation.HideFromApiTypes;
import com.chinatelelcom.gs.engine.sdk.common.Page;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import jakarta.annotation.Resource;
import java.io.IOException;
import java.util.List;


/**
 * <p>
 * prompt模板表 Controller
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-11
 */
@RestController
@Slf4j
@Tag(name = "prompt模板表 Controller")
@RequestMapping({KmsApis.KMS_API + KmsApis.PROMPT, KmsApis.RPC + KmsApis.PROMPT, KmsApis.OPENAPI + KmsApis.PROMPT})
public class PromptTemplateController implements PromptAppServiceApi {

    @Resource
    private PromptTemplateAppService promptTemplateAppService;

    @Resource
    private PromptApplicationService promptApplicationService;

    @Resource
    private GsGlobalConfig gsGlobalConfig;

    @Value("${app.safeCheck.fenceScript:当前问答触发安全围栏，请换个说法试试！感谢您的理解！}")
    private String fenceScript;

    @Operation(summary = "prompt模板表分页列表", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "prompt模板表分页列表", groupName = "prompt管理")
    @StatOpenApi(name = "prompt模板表分页列表", groupName = "prompt管理")
    @AuditLog(businessType = "prompt管理", operType = "prompt模板表分页列表", operDesc = "prompt模板表分页列表", objId="null")
    @PostMapping(KmsApis.PAGE_API)
    public Result<Page<PromptTemplateVO>> page(@Validated @RequestBody PromptTemplateQueryParam param) {
        return Result.success(promptTemplateAppService.pageQuery(param));
    }

    @Operation(summary = "prompt模板表详情", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "prompt模板表详情", groupName = "prompt管理")
    @StatOpenApi(name = "prompt模板表详情", groupName = "prompt管理")
    @AuditLog(businessType = "prompt管理", operType = "prompt模板表详情", operDesc = "prompt模板表详情", objId="#code")
    @GetMapping(KmsApis.CODE_PATH)
    public Result<PromptTemplateVO> get(@PathVariable("code") String code) {
        return Result.success(promptTemplateAppService.get(code));
    }

    @Operation(summary = "prompt模板表新增", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "prompt模板表新增", groupName = "prompt管理")
    @StatOpenApi(name = "prompt模板表新增", groupName = "prompt管理")
    @AuditLog(businessType = "prompt管理", operType = "prompt模板表新增", operDesc = "prompt模板表新增", objId="null")
    @PostMapping
    public Result<Boolean> add(@Validated @RequestBody PromptTemplateCreateParam createParam) {
        promptTemplateAppService.create(createParam);
        return Result.success(true);
    }


    @Operation(summary = "prompt模板表更新", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "prompt模板表更新", groupName = "prompt管理")
    @StatOpenApi(name = "prompt模板表更新", groupName = "prompt管理")
    @AuditLog(businessType = "prompt管理", operType = "prompt模板表更新", operDesc = "prompt模板表更新", objId="#code")
    @PutMapping(KmsApis.CODE_PATH)
    public Result<Boolean> update(@PathVariable("code") String code, @Validated @RequestBody PromptTemplateUpdateParam param) {
        return Result.success(promptTemplateAppService.update(code, param));
    }


    @Operation(summary = "prompt模板表删除", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "prompt模板表删除", groupName = "prompt管理")
    @StatOpenApi(name = "prompt模板表删除", groupName = "prompt管理")
    @AuditLog(businessType = "prompt管理", operType = "prompt模板表删除", operDesc = "prompt模板表删除", objId="#codes.codes")
    @PostMapping(KmsApis.DELETE_API)
    public Result<Boolean> delete(@Validated @RequestBody CodeParam codes) {
        return Result.success(promptTemplateAppService.delete(codes));
    }

    @Operation(summary = "模型模板回答", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "模型模板回答", groupName = "prompt管理")
    @StatOpenApi(name = "模型模板回答", groupName = "prompt管理")
    @AuditLog(businessType = "prompt管理", operType = "模型模板回答", operDesc = "模型模板回答", objId="#promptRequest.code")
    @PostMapping(path = KmsApis.MODEL, produces = {MediaType.TEXT_EVENT_STREAM_VALUE})
    public SseEmitter prompt(@Validated @RequestBody PromptRequest promptRequest) throws IOException {
        SseEmitterUTF8 sseEmitter = new SseEmitterUTF8(30 * 60 * 1000L);

        try {
            if (StringUtils.isBlank(promptRequest.getModelCode())) {
                promptRequest.setModelCode(gsGlobalConfig.getKmsBizConfig().getFastLlmModelCode());
            }

            promptApplicationService.modelRequest(promptRequest,sseEmitter);
            return sseEmitter;
        } catch (Exception e) {
            return promptApplicationService.exceptionResponse(e);
        }
    }


    @HideFromApiTypes(ApiType.OPENAPI)
    @Operation(summary = "相似问生成", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "相似问生成", groupName = "prompt管理")
    @StatOpenApi(name = "相似问生成", groupName = "prompt管理")
    @AuditLog(businessType = "prompt管理", operType = "相似问生成", operDesc = "相似问生成", objId="null")
    @PostMapping(KmsApis.SIMILAR)
    public Result<List<String>> similar(@Validated @RequestBody SimilarRequest similarRequest) {
        return Result.success(promptApplicationService.genSimilar(similarRequest));
    }


}


