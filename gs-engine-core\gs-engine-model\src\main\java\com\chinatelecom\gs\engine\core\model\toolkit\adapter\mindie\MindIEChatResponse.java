package com.chinatelecom.gs.engine.core.model.toolkit.adapter.mindie;
import com.chinatelecom.gs.engine.core.sdk.vo.llm.BaseLLMResponse;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class MindIEChatResponse implements BaseLLMResponse {

    private String model;

    private String object;

    private Integer created;

    private List<Choice> choices;

    @Data
    public static class Choice implements Serializable{
        private Integer index;

        private String finish_reason;

        private MindIEMessage message;

        private MindIEMessage delta;
    }

    @Data
    public static class Usage implements Serializable {
        private Integer prompt_tokens;
        private Integer total_tokens;
        private Integer completion_tokens;
    }

    /**
     * 获取输出内容
     *
     * @return
     */
    @Override
    public String outputContent() {
        return null;
    }
}
