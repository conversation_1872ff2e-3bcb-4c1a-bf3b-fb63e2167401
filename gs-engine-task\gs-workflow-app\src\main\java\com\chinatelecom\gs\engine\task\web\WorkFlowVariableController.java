package com.chinatelecom.gs.engine.task.web;

import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.common.auth.KsMenuConfig;
import com.chinatelecom.gs.engine.common.auth.MenuConfig;
import com.chinatelecom.gs.engine.common.auth.PermissionTag;
import com.chinatelecom.gs.engine.common.auth.PermissionTypeEnum;
import com.chinatelecom.gs.engine.common.constants.Constants;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.common.platform.StatOpenApi;
import com.chinatelecom.gs.engine.common.utils.DataResourceAccess;
import com.chinatelecom.gs.engine.sdk.common.annotation.ApiType;
import com.chinatelecom.gs.engine.sdk.common.annotation.HideFromApiTypes;
import com.chinatelecom.gs.engine.task.sdk.TaskApis;
import com.chinatelecom.gs.engine.task.sdk.vo.variable.*;
import com.chinatelecom.gs.privilege.common.dto.GrantObjectDTO;
import com.chinatelecom.gs.privilege.common.enums.PrivilegeEnum;
import com.chinatelecom.gs.privilege.common.enums.ResourceTypeEnum;
import com.chinatelecom.gs.privilege.util.PrivilegeUtil;
import com.chinatelecom.gs.workflow.core.service.WorkFlowVariableAppService;
import com.chinatelelcom.gs.engine.sdk.common.Page;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.validation.constraints.NotBlank;


/**
 * <AUTHOR>
 * @description
 * @date 2025/02/11
 */
@Slf4j
@Tag(name = "WorkFlow变量Api")
@RestController
@PermissionTag(code = {MenuConfig.DIALOG_FLOW, MenuConfig.WORKFLOW, KsMenuConfig.WORKFLOW_FLOW})
@PermissionTag(code = MenuConfig.STRATEGY, type = PermissionTypeEnum.MENU)
@RequestMapping({TaskApis.TASK_API + Constants.WEB_PREFIX + "/variable", TaskApis.TASK_API + Constants.API_PREFIX + "/variable"})
public class WorkFlowVariableController {

    @Autowired
    private PrivilegeUtil privilegeUtil;

    @Autowired
    private DataResourceAccess dataResourceAccess;

    @Resource
    private WorkFlowVariableAppService workFlowVariableAppService;

    @Operation(summary = "全局变量分页列表", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "业务流管理")})})
    @PlatformRestApi(name = "全局变量分页列表", groupName = "WorkFlow变量Api")
    @StatOpenApi(name = "全局变量分页列表", groupName = "WorkFlow变量Api")
    @PostMapping("/page")
    @AuditLog(businessType = "WorkFlow变量Api", operType = "全局变量分页列表", operDesc = "全局变量分页列表", objId = "#pageRequest.workflowId")
    @HideFromApiTypes(ApiType.OPENAPI)
    public Result<Page<WorkFlowVariableDataDetailVO>> page(@Validated @RequestBody WorkFlowVariableQueryRequest pageRequest) {
        checkResourceAuth(pageRequest.getWorkFlowId());
        return Result.success(workFlowVariableAppService.page(pageRequest, true));
    }

    @Operation(summary = "添加全局变量", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "业务流管理")})})
    @PlatformRestApi(name = "添加全局变量", groupName = "WorkFlow变量Api")
    @StatOpenApi(name = "添加全局变量", groupName = "WorkFlow变量Api")
    @PostMapping("/add")
    @AuditLog(businessType = "WorkFlow变量Api", operType = "添加全局变量", operDesc = "添加全局变量", objId = "#addRequest.workflowId")
    @HideFromApiTypes(ApiType.OPENAPI)
    public Result<Boolean> add(@Validated @RequestBody WorkFlowVariableAddRequest addRequest) {
        checkResourceAuth(addRequest.getWorkFlowId());
        return workFlowVariableAppService.add(addRequest);
    }

    @Operation(summary = "更新全局变量", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "业务流管理")})})
    @StatOpenApi(name = "更新全局变量", groupName = "WorkFlow变量Api")
    @PlatformRestApi(name = "更新全局变量", groupName = "WorkFlow变量Api")
    @PostMapping("/update")
    @AuditLog(businessType = "WorkFlow变量Api", operType = "更新全局变量", operDesc = "更新全局变量", objId = "#editRequest.workflowId")
    @HideFromApiTypes(ApiType.OPENAPI)
    public Result<Boolean> update(@Validated @RequestBody WorkFlowVariableEditRequest editRequest) {
        checkResourceAuth(editRequest.getWorkFlowId());
        return workFlowVariableAppService.update(editRequest);
    }

    @Operation(summary = "删除全局变量", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "业务流管理")})})
    @StatOpenApi(name = "删除全局变量", groupName = "WorkFlow变量Api")
    @PlatformRestApi(name = "删除全局变量", groupName = "WorkFlow变量Api")
    @PostMapping("/delete")
    @AuditLog(businessType = "WorkFlow变量Api", operType = "删除全局变量", operDesc = "删除全局变量", objId = "#delRequest.workflowId")
    @HideFromApiTypes(ApiType.OPENAPI)
    public Result<Boolean> delete(@Validated @RequestBody WorkFlowVariableDelRequest delRequest) {
        checkResourceAuth(delRequest.getWorkFlowId());
        return workFlowVariableAppService.delete(delRequest);
    }

    @Operation(summary = "查询全局变量详情", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "业务流管理")})})
    @StatOpenApi(name = "查询全局变量详情", groupName = "WorkFlow变量Api")
    @PlatformRestApi(name = "查询全局变量详情", groupName = "WorkFlow变量Api")
    @GetMapping("/detail")
    @AuditLog(businessType = "WorkFlow变量Api", operType = "查询全局变量详情", operDesc = "查询全局变量详情", objId = "#variableCode")
    @HideFromApiTypes(ApiType.OPENAPI)
    public Result<WorkFlowVariableDataDetailVO> detail(@RequestParam("workFlowId") @NotBlank(message = "workflow编码不能为空") String workFlowId,
                                                       @RequestParam("variableCode") @NotBlank(message = "变量编码不能为空") String variableCode) {
        checkResourceAuth(workFlowId);
        return Result.success(workFlowVariableAppService.getDetail(variableCode, true));
    }

    private void checkResourceAuth(String workflowId) {
        GrantObjectDTO grantObjectDTO = privilegeUtil.hasPrivilege(workflowId, ResourceTypeEnum.WORKFLOW);
        if (grantObjectDTO == null) {
            throw new BizException("CA007", "没有权限访问该资源");
        }
        boolean checkResult = grantObjectDTO.getPrivilege() != null && !grantObjectDTO.getPrivilege().equals(PrivilegeEnum.no_permission);
        if (!checkResult) {
            throw new BizException("CA007", "没有权限访问该资源");
        }
    }
}
