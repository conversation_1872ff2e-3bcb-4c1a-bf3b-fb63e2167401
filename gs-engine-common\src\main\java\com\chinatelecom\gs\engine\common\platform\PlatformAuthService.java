package com.chinatelecom.gs.engine.common.platform;

import com.chinatelecom.cloud.platform.client.rpc.Permission;
import com.chinatelecom.cloud.platform.client.util.AuthUtils;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component
public class PlatformAuthService {
    private static final String ENGINE_RESOURCE_TYPE = "2";

    @Resource
    private GovernmentAuthClient governmentAuthClient;
    @Resource
    private KsAuthClient ksAuthClient;

    private static final Cache<String, List<String>> RESOURCE_LIST_CACHE = Caffeine.newBuilder().expireAfterWrite(60, TimeUnit.SECONDS).maximumSize(1000).build();


    /**
     * 获取资源列表
     *
     * @return List<String>
     */
    public List<String> getResourceList() {
        String key = StringUtils.join(RequestContext.getUserId(), "_", RequestContext.getTenantId(), "_",
                RequestContext.getAppSourceType() != null ? RequestContext.getAppSourceType().name() : StringUtils.EMPTY);
        return RESOURCE_LIST_CACHE.get(key, k -> {
            List<String> result = null;
            if (RequestContext.getAppSourceType() != null) {
                switch (RequestContext.getAppSourceType()) {
                    case KS:
                        result = getKsResources();
                        break;
                    case GOVERNMENT:
                        result = getGovernmentResources();
                        break;
                    default:
                        result = getEngineResources();
                        break;
                }
            } else {
                result = getEngineResources();
            }
            return result;
        });
    }

    /**
     * 获取ks资源列表
     *
     * @return List<String>
     */
    private List<String> getKsResources() {
        return Optional.ofNullable(ksAuthClient.getResourceList())
                .map(Result::getData)
                .orElseGet(Collections::emptyList)
                .stream()
                .map(Permission::getPermissionCode)
                .collect(Collectors.toList());
    }

    /**
     * 获取Government资源列表
     *
     * @return List<String>
     */
    private List<String> getGovernmentResources() {
        AppOwnerRequest request = new AppOwnerRequest();
        request.setCorpCode(RequestContext.getTenantId());
        request.setUserId(RequestContext.getUserId());

        return Optional.ofNullable(governmentAuthClient.getResourceList(request))
                .map(Result::getData)
                .orElseGet(Collections::emptyList);
    }

    /**
     * 获取Engine资源列表
     *
     * @return List<String>
     */
    private List<String> getEngineResources() {
        return Optional.ofNullable(AuthUtils.getResourceList(
                        RequestContext.getTenantId(),
                        RequestContext.getUserId(),
                        ENGINE_RESOURCE_TYPE
                ).getData())
                .orElseGet(Collections::emptyList)
                .stream()
                .map(Permission::getPermissionCode)
                .collect(Collectors.toList());
    }
}

