package com.chinatelecom.gs.engine.channel.dao.repository;
import static org.junit.jupiter.api.Assertions.assertSame;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import com.baomidou.mybatisplus.extension.service.IService;
import com.chinatelecom.gs.engine.channel.dao.po.ChannelApiSecretPO;
import com.chinatelecom.gs.engine.channel.service.dto.ChannelApiSecretDTO;
import com.chinatelecom.gs.engine.robot.sdk.config.enums.ApiSecretType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import java.util.Arrays;
import java.util.List;

public class ChannelApiSecretRepositoryTest {
    @Mock
    private ChannelApiSecretRepository channelApiSecretRepository;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testFindBySecretId() {
        // Arrange
        String secretId = "secret123";
        String appId = "app123";
        ChannelApiSecretDTO expectedDto = new ChannelApiSecretDTO();
        when(channelApiSecretRepository.findBySecretId(secretId, appId)).thenReturn(expectedDto);

        // Act
        ChannelApiSecretDTO result = channelApiSecretRepository.findBySecretId(secretId, appId);

        // Assert
        assertSame(expectedDto, result);
        verify(channelApiSecretRepository).findBySecretId(secretId, appId);
    }

    @Test
    public void testChannelApiSecretListByAppId() {
        // Arrange
        String appId = "app123";
        ApiSecretType apiSecretType = ApiSecretType.API;
        int limit = 10;
        List<ChannelApiSecretDTO> expectedList = Arrays.asList(new ChannelApiSecretDTO(), new ChannelApiSecretDTO());
        when(channelApiSecretRepository.channelApiSecretListByAppId(appId, apiSecretType, limit)).thenReturn(expectedList);

        // Act
        List<ChannelApiSecretDTO> resultList = channelApiSecretRepository.channelApiSecretListByAppId(appId, apiSecretType, limit);

        // Assert
        assertSame(expectedList, resultList);
        verify(channelApiSecretRepository).channelApiSecretListByAppId(appId, apiSecretType, limit);
    }
}