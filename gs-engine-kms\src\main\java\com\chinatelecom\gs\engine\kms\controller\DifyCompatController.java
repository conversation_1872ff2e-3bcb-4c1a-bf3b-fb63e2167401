package com.chinatelecom.gs.engine.kms.controller;

import com.chinatelecom.gs.engine.common.constants.Apis;
import com.chinatelecom.gs.engine.common.platform.StatOpenApi;
import com.chinatelecom.gs.engine.kms.sdk.constants.KmsApis;
import com.chinatelecom.gs.engine.kms.sdk.enums.EnvType;
import com.chinatelecom.gs.engine.kms.sdk.vo.dify.*;
import com.chinatelecom.gs.engine.kms.sdk.vo.search.BaseItem;
import com.chinatelecom.gs.engine.kms.sdk.vo.search.SearchParam;
import com.chinatelecom.gs.engine.kms.sdk.vo.search.SearchResp;
import com.chinatelecom.gs.engine.kms.sdk.vo.search.sdk.SdkSearchResponse;
import com.chinatelecom.gs.engine.kms.search.SearchService;
import com.chinatelecom.gs.engine.kms.utils.MetadataConditionEvaluator;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@RestController
@RequestMapping(KmsApis.OPENAPI + Apis.OPEN + "/dify")
@Tag(name = "Dify兼容接口", description = "提供与Dify外部知识库API兼容的接口")
public class DifyCompatController {

    private static final String HEADER_STRING = "Bearer ";

    @Value("${platform.super.tenant:SuperAis}")
    private String superTenant;

    @Resource
    private SearchService searchService;

    @PostMapping("/retrieval")
    @Operation(summary = "Dify兼容外部知识库检索接口", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})},
            description = "支持在Dify客户端填写外部知识库地址和API密钥(见请求head头参数)直接使用 根据查询内容从指定知识库中检索相关文档，支持元数据筛选和相似度阈值设置",
            parameters = {
                    @Parameter(
                            name = "Authorization",
                            description = "租户id@用户id拼接以后 通过base64 utf8转码为请求的key 例: ks@12345678=>a3NAMTIzNDU2Nzg=最后需加入 Bearer 例: Bearer a3NAMTIzNDU2Nzg=",
                            in = ParameterIn.HEADER,
                            required = true,
                            example = "Bearer a3NAMTIzNDU2Nzg="
                    ),
                    @Parameter(name = "x-userid", description = "此接口无需填写", in = ParameterIn.HEADER, required = false),
                    @Parameter(name = "x-tenantid", description = "此接口无需填写", in = ParameterIn.HEADER, required = false),
                    @Parameter(name = "x-source", description = "此接口无需填写", in = ParameterIn.HEADER, required = false)
            })
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "检索成功",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = DifySearchResponse.class))),
            @ApiResponse(responseCode = "400", description = "请求参数错误",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = DifyErrorResponse.class))),
            @ApiResponse(responseCode = "401", description = "认证失败",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = DifyErrorResponse.class))),
            @ApiResponse(responseCode = "404", description = "知识库不存在",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = DifyErrorResponse.class)))
    })
    @StatOpenApi(name = "检索", groupName = "Dify知识库API")
    public ResponseEntity<?> search(
            @Parameter(description = "检索请求参数", required = true)
            @RequestBody DifySearchRequest request,
            HttpServletRequest httpRequest) {
        try {

            // 2. 参数适配
            SearchParam searchParam = new SearchParam();
            searchParam.setEnv(EnvType.TEST);
            searchParam.setQuery(request.getQuery());
            SearchParam.Filter filter = new SearchParam.Filter();
            long topK = request.getRetrievalSetting().getTopK();
            double scoreThreshold = request.getRetrievalSetting().getScoreThreshold();
            filter.setTopN(topK != 0 ? topK : 10L);
            filter.setThreshold(scoreThreshold);
            List<SearchParam.KnowledgeFilter> knowledgeFilters = new ArrayList<>();
            SearchParam.KnowledgeFilter knowledgeFilter = new SearchParam.KnowledgeFilter();
            knowledgeFilter.setKnowledgeBaseCode(request.getKnowledgeId());
            knowledgeFilters.add(knowledgeFilter);
            filter.setKnowledgeFilters(knowledgeFilters);
            searchParam.setFilter(filter);

            // 3. 分片搜索调用
            SearchResp<? extends BaseItem> resp = searchService.searchChunkSync(searchParam);

            // 4. 结果适配和metadata条件筛选
            DifySearchResponse difyResp = new DifySearchResponse();
            if (resp != null && resp.getData() != null && resp.getData().getItems() != null) {
                for (SdkSearchResponse.SdkRetrieveItem<? extends BaseItem> item : resp.getData().getItems()) {
                    BaseItem base = item.getSource();

                    // 应用metadata条件筛选
                    if (request.getMetadataCondition() != null) {
                        if (!MetadataConditionEvaluator.evaluate(base, request.getMetadataCondition())) {
                            continue; // 跳过不符合条件的结果
                        }
                    }

                    DifySearchData data = new DifySearchData();
                    data.setContent(base.getContent());
                    data.setTitle(base.getTitle());
                    // 优先使用RankScore，如果为null则使用item.getScore()
                    Double rankScore = (base.getMetaData() != null) ? base.getMetaData().getRankScore() : null;
                    data.setScore(rankScore != null ? rankScore : item.getScore());

                    // 构建增强的metadata
                    Metadata meta = buildEnhancedMetadata(base);
                    data.setMetadata(meta);

                    difyResp.getRecords().add(data);
                }
            }
            return ResponseEntity.ok(difyResp);

        } catch (BizException e) {
            log.error("Dify兼容接口业务异常: {}", e.getMessage(), e);
            return handleDifyError(e);
        } catch (Exception e) {
            log.error("Dify兼容接口系统异常: {}", e.getMessage(), e);
            DifyErrorResponse errorResp = new DifyErrorResponse(500, "内部服务器错误");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResp);
        }
    }

    /**
     * 构建增强的metadata，包含更多字段信息
     */
    private Metadata buildEnhancedMetadata(BaseItem base) {
        Metadata meta = new Metadata();
        meta.setPath(base.getFileKey());
        meta.setDescription(base.getDescription());

        // 添加扩展metadata字段
        if (base.getKnowledgeType() != null) {
            meta.addMetadata("category", base.getKnowledgeType().name());
        }
        if (base.getTags() != null && !base.getTags().isEmpty()) {
            meta.addMetadata("tags", base.getTags());
        }
        if (base.getCreateTime() != null) {
            meta.addMetadata("create_time", base.getCreateTime());
        }
        if (base.getUpdateTime() != null) {
            meta.addMetadata("update_time", base.getUpdateTime());
        }
        if (base.getPublishTime() != null) {
            meta.addMetadata("publish_time", base.getPublishTime());
        }
        if (base.getKnowledgeBaseName() != null) {
            meta.addMetadata("knowledge_base", base.getKnowledgeBaseName());
        }
        if (base.getKnowledgeCode() != null) {
            meta.addMetadata("knowledge_code", base.getKnowledgeCode());
        }
        if (base.getKnowledgeBaseCode() != null) {
            meta.addMetadata("knowledge_base_code", base.getKnowledgeBaseCode());
        }
        if (base.getCreateName() != null) {
            meta.addMetadata("create_name", base.getCreateName());
        }
        return meta;
    }

    /**
     * 处理Dify错误响应
     */
    private ResponseEntity<DifyErrorResponse> handleDifyError(BizException e) {
        String code = e.getCode();
        DifyErrorResponse errorResp;
        HttpStatus httpStatus;

        // 根据业务异常码映射到Dify错误码
        switch (code) {
            case "A0025": // 认证相关错误
                errorResp = new DifyErrorResponse(1002, "无效的API密钥");
                httpStatus = HttpStatus.FORBIDDEN;
                break;
            default:
                if (e.getMessage().contains("知识库不存在")) {
                    errorResp = new DifyErrorResponse(2001, "知识库不存在");
                    httpStatus = HttpStatus.BAD_REQUEST;
                } else {
                    errorResp = new DifyErrorResponse(500, "内部服务器错误");
                    httpStatus = HttpStatus.INTERNAL_SERVER_ERROR;
                }
                break;
        }

        return ResponseEntity.status(httpStatus).body(errorResp);
    }

}