SET IDENTITY_INSERT "RECENT" ON;
SET IDENTITY_INSERT "RECENT" OFF;
SET IDENTITY_INSERT "ROBOT_AI_INTENT" ON;
SET IDENTITY_INSERT "ROBOT_AI_INTENT" OFF;
SET IDENTITY_INSERT "ROBOT_AI_MODEL" ON;
INSERT INTO "ROBOT_AI_MODEL"("ID","TENANT_ID","APP_ID","<PERSON><PERSON><PERSON>_CODE","<PERSON><PERSON><PERSON>_NAME","MODEL_TYPE","MODEL_STATE","DATA_FORMAT","DATA_DIMENSION","API_KEY","INTENT_NUMBER","DESCRIPTION","EXTERNAL_MODEL_ID","EXTERNAL_MODEL_URL","EXTERNAL_MODEL_CONFIG","GLOBAL_FLAG","MODEL_CONFIG","YN","CREATE_ID","CREATE_NAME","CREATE_TIME","UPDATE_ID","UPDATE_NAME","UPDATE_TIME","<PERSON>IS<PERSON><PERSON><PERSON><PERSON><PERSON>_MODEL_CONFIG","M<PERSON><PERSON>_SECRET","MODEL_PROVIDER","THRESHOLD","IS_DEFAULT","MODEL_CALL_NAME") VALUES(1,'0','0','model_992668744299712566','语音识别模型','ASR','VALID',null,null,null,0,'',null,'http://127.0.0.1:8080/member2/funasr/predict',null,1,null,0,'_empty_','未知用户','2024-12-27 13:51:12.000000000','_empty_','未知用户','2024-12-27 13:51:12.000000000',0,'','',null,'1','');
INSERT INTO "ROBOT_AI_MODEL"("ID","TENANT_ID","APP_ID","MODEL_CODE","MODEL_NAME","MODEL_TYPE","MODEL_STATE","DATA_FORMAT","DATA_DIMENSION","API_KEY","INTENT_NUMBER","DESCRIPTION","EXTERNAL_MODEL_ID","EXTERNAL_MODEL_URL","EXTERNAL_MODEL_CONFIG","GLOBAL_FLAG","MODEL_CONFIG","YN","CREATE_ID","CREATE_NAME","CREATE_TIME","UPDATE_ID","UPDATE_NAME","UPDATE_TIME","DISTINGUISH_MODEL_CONFIG","MODEL_SECRET","MODEL_PROVIDER","THRESHOLD","IS_DEFAULT","MODEL_CALL_NAME") VALUES(2,'0','0','model_992668744299712567','文字识别模型','OCR','VALID',null,null,null,0,'',null,'http://127.0.0.1:8080/member2/ppocr-gpu/api/ocr',null,1,null,0,'_empty_','未知用户','2024-12-27 13:51:12.000000000','_empty_','未知用户','2024-12-27 13:51:12.000000000',0,'','',null,'1','');
INSERT INTO "ROBOT_AI_MODEL"("ID","TENANT_ID","APP_ID","MODEL_CODE","MODEL_NAME","MODEL_TYPE","MODEL_STATE","DATA_FORMAT","DATA_DIMENSION","API_KEY","INTENT_NUMBER","DESCRIPTION","EXTERNAL_MODEL_ID","EXTERNAL_MODEL_URL","EXTERNAL_MODEL_CONFIG","GLOBAL_FLAG","MODEL_CONFIG","YN","CREATE_ID","CREATE_NAME","CREATE_TIME","UPDATE_ID","UPDATE_NAME","UPDATE_TIME","DISTINGUISH_MODEL_CONFIG","MODEL_SECRET","MODEL_PROVIDER","THRESHOLD","IS_DEFAULT","MODEL_CALL_NAME") VALUES(3,'0','0','model_992668744299712568','版面分析模型','LAYOUT','VALID',null,null,null,0,'',null,'http://127.0.0.1:8080/member2/layout/predict',null,1,null,0,'_empty_','未知用户','2024-12-27 13:51:12.000000000','_empty_','未知用户','2024-12-27 13:51:12.000000000',0,'','',null,'1','');
INSERT INTO "ROBOT_AI_MODEL"("ID","TENANT_ID","APP_ID","MODEL_CODE","MODEL_NAME","MODEL_TYPE","MODEL_STATE","DATA_FORMAT","DATA_DIMENSION","API_KEY","INTENT_NUMBER","DESCRIPTION","EXTERNAL_MODEL_ID","EXTERNAL_MODEL_URL","EXTERNAL_MODEL_CONFIG","GLOBAL_FLAG","MODEL_CONFIG","YN","CREATE_ID","CREATE_NAME","CREATE_TIME","UPDATE_ID","UPDATE_NAME","UPDATE_TIME","DISTINGUISH_MODEL_CONFIG","MODEL_SECRET","MODEL_PROVIDER","THRESHOLD","IS_DEFAULT","MODEL_CALL_NAME") VALUES(4,'0','0','model_992668744299712569','多模态模型','VLLM','VALID',null,null,null,0,'',null,'http://127.0.0.1:8080/member2/multimodal-llm/image_caption',null,1,null,0,'_empty_','未知用户','2024-12-27 13:51:12.000000000','_empty_','未知用户','2024-12-27 13:51:12.000000000',0,'','',null,'1','');
INSERT INTO "ROBOT_AI_MODEL"("ID","TENANT_ID","APP_ID","MODEL_CODE","MODEL_NAME","MODEL_TYPE","MODEL_STATE","DATA_FORMAT","DATA_DIMENSION","API_KEY","INTENT_NUMBER","DESCRIPTION","EXTERNAL_MODEL_ID","EXTERNAL_MODEL_URL","EXTERNAL_MODEL_CONFIG","GLOBAL_FLAG","MODEL_CONFIG","YN","CREATE_ID","CREATE_NAME","CREATE_TIME","UPDATE_ID","UPDATE_NAME","UPDATE_TIME","DISTINGUISH_MODEL_CONFIG","MODEL_SECRET","MODEL_PROVIDER","THRESHOLD","IS_DEFAULT","MODEL_CALL_NAME") VALUES(5,'0','0','model_992668744299715570','chat','LLM','VALID',null,null,null,0,'',null,'http://127.0.0.1:8080/member2/offline-llm/v1/chat/completions',null,1,null,0,'_empty_','未知用户','2024-12-27 13:51:12.000000000','_empty_','未知用户','2024-12-27 13:51:12.000000000',0,'','teleChat',null,'1','');
INSERT INTO "ROBOT_AI_MODEL"("ID","TENANT_ID","APP_ID","MODEL_CODE","MODEL_NAME","MODEL_TYPE","MODEL_STATE","DATA_FORMAT","DATA_DIMENSION","API_KEY","INTENT_NUMBER","DESCRIPTION","EXTERNAL_MODEL_ID","EXTERNAL_MODEL_URL","EXTERNAL_MODEL_CONFIG","GLOBAL_FLAG","MODEL_CONFIG","YN","CREATE_ID","CREATE_NAME","CREATE_TIME","UPDATE_ID","UPDATE_NAME","UPDATE_TIME","DISTINGUISH_MODEL_CONFIG","MODEL_SECRET","MODEL_PROVIDER","THRESHOLD","IS_DEFAULT","MODEL_CALL_NAME") VALUES(6,'0','0','model_992668744299715571','离线解析大模型','OFFLINE_LLM','VALID',null,null,null,0,'',null,'http://127.0.0.1:8080/member2/offline-llm/v1/chat/completions',null,1,null,0,'_empty_','未知用户','2024-12-27 13:51:12.000000000','_empty_','未知用户','2024-12-27 13:51:12.000000000',0,'','',null,'1','');
INSERT INTO "ROBOT_AI_MODEL"("ID","TENANT_ID","APP_ID","MODEL_CODE","MODEL_NAME","MODEL_TYPE","MODEL_STATE","DATA_FORMAT","DATA_DIMENSION","API_KEY","INTENT_NUMBER","DESCRIPTION","EXTERNAL_MODEL_ID","EXTERNAL_MODEL_URL","EXTERNAL_MODEL_CONFIG","GLOBAL_FLAG","MODEL_CONFIG","YN","CREATE_ID","CREATE_NAME","CREATE_TIME","UPDATE_ID","UPDATE_NAME","UPDATE_TIME","DISTINGUISH_MODEL_CONFIG","MODEL_SECRET","MODEL_PROVIDER","THRESHOLD","IS_DEFAULT","MODEL_CALL_NAME") VALUES(7,'0','0','model_859333379638693890','bge表征','EMBEDDING','VALID','double',1024,null,0,'',null,'http://127.0.0.1:8080/predict_query','[{"name": "bge-query", "type": "VECTOR", "appCode": "806827800746397696", "vectorType": "USERREQUEST"}, {"name": "bge-doc", "type": "VECTOR", "appCode": "806827800746397696", "vectorType": "OFFLINEDOCUMENTSMODELCONFIG"}]',1,null,0,'_empty_','未知用户','2024-01-04 18:35:53.000000000','_empty_','未知用户','2024-05-21 15:46:06.000000000',0,null,null,null,'1','');
INSERT INTO "ROBOT_AI_MODEL"("ID","TENANT_ID","APP_ID","MODEL_CODE","MODEL_NAME","MODEL_TYPE","MODEL_STATE","DATA_FORMAT","DATA_DIMENSION","API_KEY","INTENT_NUMBER","DESCRIPTION","EXTERNAL_MODEL_ID","EXTERNAL_MODEL_URL","EXTERNAL_MODEL_CONFIG","GLOBAL_FLAG","MODEL_CONFIG","YN","CREATE_ID","CREATE_NAME","CREATE_TIME","UPDATE_ID","UPDATE_NAME","UPDATE_TIME","DISTINGUISH_MODEL_CONFIG","MODEL_SECRET","MODEL_PROVIDER","THRESHOLD","IS_DEFAULT","MODEL_CALL_NAME") VALUES(8,'0','0','model_992668744299715591','通用精排','RERANK','VALID',null,null,null,0,'',null,'http://127.0.0.1:8080/member2/offline-llm/v1/chat/completions',null,1,null,0,'_empty_','未知用户','2024-12-27 13:51:12.000000000','_empty_','未知用户','2024-12-27 13:51:12.000000000',0,'','',null,'1','');

SET IDENTITY_INSERT "ROBOT_AI_MODEL" OFF;
SET IDENTITY_INSERT "SESSION_INFO" ON;
SET IDENTITY_INSERT "SESSION_INFO" OFF;
SET IDENTITY_INSERT "TAG_RELATION" ON;
SET IDENTITY_INSERT "TAG_RELATION" OFF;
SET IDENTITY_INSERT "TAG_TREE_NODE" ON;
SET IDENTITY_INSERT "TAG_TREE_NODE" OFF;
SET IDENTITY_INSERT "WORKFLOW_TASK" ON;
SET IDENTITY_INSERT "WORKFLOW_TASK" OFF;
SET IDENTITY_INSERT "WORKFLOW_TEST_LOG" ON;
SET IDENTITY_INSERT "WORKFLOW_TEST_LOG" OFF;
SET IDENTITY_INSERT "WORKFLOW_VERSION_INFO" ON;
SET IDENTITY_INSERT "WORKFLOW_VERSION_INFO" OFF;
SET IDENTITY_INSERT "AGENT_AI_MODEL" ON;
SET IDENTITY_INSERT "AGENT_AI_MODEL" OFF;
SET IDENTITY_INSERT "AGENT_BASIC_INFO" ON;
SET IDENTITY_INSERT "AGENT_BASIC_INFO" OFF;
SET IDENTITY_INSERT "AGENT_CONFIG" ON;
SET IDENTITY_INSERT "AGENT_CONFIG" OFF;
SET IDENTITY_INSERT "AGENT_DEFAULT_CONFIG" ON;
INSERT INTO "AGENT_DEFAULT_CONFIG"("ID","CONFIG_KEY","CONFIG_VALUE","TYPE","CATEGORY","YN","CREATE_ID","CREATE_NAME","UPDATE_ID","UPDATE_NAME","CREATE_TIME","UPDATE_TIME","VERSION","TENANT_ID") VALUES(38,'agentConfig.model','{"modelName":"DeepSeek-R1-Qwen32b","modelCode":"model_992668744299715570","modelMode":"PRECISION_MODE","outputType":"txt","modelRandomness":1,"modelSamplingRate":0.0,"memoryCount":1,"enableAdvConf":true,"enableSortModel":true}',1,1,0,'8468034137038716928','test','8468034137038716928','test','2024-07-11 18:42:33.592000000','2025-03-18 17:38:15.270000000',1,'zhinengti');
INSERT INTO "AGENT_DEFAULT_CONFIG"("ID","CONFIG_KEY","CONFIG_VALUE","TYPE","CATEGORY","YN","CREATE_ID","CREATE_NAME","UPDATE_ID","UPDATE_NAME","CREATE_TIME","UPDATE_TIME","VERSION","TENANT_ID") VALUES(39,'agentInfo.reply.template','#角色
你是一位专注于美食领域的资深食评家和美食内容创作者。你擅长运用生动有趣的语言和视角分析各类美食，并为读者推荐优质的美食体验。
#目标
根据你的美食专家身份,为用户生成具有吸引力的美食内容,帮助他们发现并体验优质的美食。
#背景知识
－美食领域存在大量优质内容，能够引导用户发现有价值的美食体验。
－优质的美食内容需要融合专业评判、生动描述和个人见解,才能真正吸引用户。
－美食内容创作需要关注用户痛点和需求，以满足他们对美食的探索和体验欲望。
#限制
－请严格遵守相关法律法规，避免涉及违法或不当内容。
－内容创作应以用户需求为中心,切忌过于自我或主观。
－输出的内容应符合平台规范和用户习惯，注重可读性和实用性。
#技能
1.美食评论分析能力：
－准确把握不同美食的特寺点、口味和适用场景。
－运用专业术语描述美食特征,并提供客观、中肯的评价。
2.内容创作能力：
－擅长运用生动有趣的语言，吸引用户阅读和互动。
－善于挖掘美食背后的故事和文化内涵，增加内容的深度和价值。
－熟悉 SEO 优化技巧，提高内容的曝光和转化率。
3.用户洞察能力：
－了解目标用户的美食偏好和消费习惯，满足他们的需求。
－洞察用户痛点和潜在需求，为其提供有价值的美食体验。',1,1,0,'8468034137038716928','test','8468034137038716928','test','2024-07-11 18:42:33.592000000','2024-08-01 01:14:17.562000000',1,'zhinengti');
INSERT INTO "AGENT_DEFAULT_CONFIG"("ID","CONFIG_KEY","CONFIG_VALUE","TYPE","CATEGORY","YN","CREATE_ID","CREATE_NAME","UPDATE_ID","UPDATE_NAME","CREATE_TIME","UPDATE_TIME","VERSION","TENANT_ID") VALUES(40,'agentConfig.template.category.config','[{"templateId":"1","templateName":"智能客服"},{"templateId":"2","templateName":"招聘助手"}, {"templateId":"3","templateName":"文档助手"},{"templateId":"4","templateName":"私人助理"}]',1,1,0,'8468034137038716928','mockUser','8468034137038716928','mockUser','2024-07-19 11:27:07.406000000','2024-07-19 17:26:22.059000000',1,'test');
INSERT INTO "AGENT_DEFAULT_CONFIG"("ID","CONFIG_KEY","CONFIG_VALUE","TYPE","CATEGORY","YN","CREATE_ID","CREATE_NAME","UPDATE_ID","UPDATE_NAME","CREATE_TIME","UPDATE_TIME","VERSION","TENANT_ID") VALUES(41,'agentConfig.kms.config','{"searchStrategy":"MIX","retriever":8}',1,1,0,'8468034137038716928','test','8468034137038716928','test','2024-07-11 18:42:33.592000000','2025-03-18 17:38:15.364000000',1,'zhinengti');
INSERT INTO "AGENT_DEFAULT_CONFIG"("ID","CONFIG_KEY","CONFIG_VALUE","TYPE","CATEGORY","YN","CREATE_ID","CREATE_NAME","UPDATE_ID","UPDATE_NAME","CREATE_TIME","UPDATE_TIME","VERSION","TENANT_ID") VALUES(42,'agentConfig.global.policy','{"globalVarSwitch": false,"securityFenceSwitch": false,"globalVarList": null}',1,1,0,'8468034137038716928','test','8468034137038716928','test','2024-07-11 18:42:33.592000000','2025-03-18 17:38:15.363000000',1,'zhinengti');
INSERT INTO "AGENT_DEFAULT_CONFIG"("ID","CONFIG_KEY","CONFIG_VALUE","TYPE","CATEGORY","YN","CREATE_ID","CREATE_NAME","UPDATE_ID","UPDATE_NAME","CREATE_TIME","UPDATE_TIME","VERSION","TENANT_ID") VALUES(43,'agentConfig.dialog.config','{"agentPrologue":"","noAnswerScript":"抱歉，你的问题我无法理解，辛苦换个表述重新询问吧～","agentSuggest":true,"suggestCount":3}',1,1,0,'8468034137038716928','test','8468034137038716928','test','2024-07-11 18:42:33.592000000','2024-07-31 10:42:21.622000000',1,'zhinengti');
INSERT INTO "AGENT_DEFAULT_CONFIG"("ID","CONFIG_KEY","CONFIG_VALUE","TYPE","CATEGORY","YN","CREATE_ID","CREATE_NAME","UPDATE_ID","UPDATE_NAME","CREATE_TIME","UPDATE_TIME","VERSION","TENANT_ID") VALUES(45,'agentConfig.offSiteDeployment.switch','{"isOpen":false}',1,2,0,'1','','1','','2024-07-25 10:31:08.492000000','2024-07-25 16:19:12.956000000',0,'');
INSERT INTO "AGENT_DEFAULT_CONFIG"("ID","CONFIG_KEY","CONFIG_VALUE","TYPE","CATEGORY","YN","CREATE_ID","CREATE_NAME","UPDATE_ID","UPDATE_NAME","CREATE_TIME","UPDATE_TIME","VERSION","TENANT_ID") VALUES(46,'botStrategy.llm.qaPrompt','{ "value":"请根据以下的上下文：
#{context}，回答以下用户的问题：#{question}

回答输出的时候请注意以下两点，
一.如果不能利用上下文回答问题，请直接根据模型自身知识回答。否则，输出根据提供的引用内容来回答问题
二.如果回答内容是纯文本，直接返回；" }',1,1,0,'1','','1','','2024-07-25 19:55:26.597000000','2025-03-18 17:38:15.232000000',1,'');
INSERT INTO "AGENT_DEFAULT_CONFIG"("ID","CONFIG_KEY","CONFIG_VALUE","TYPE","CATEGORY","YN","CREATE_ID","CREATE_NAME","UPDATE_ID","UPDATE_NAME","CREATE_TIME","UPDATE_TIME","VERSION","TENANT_ID") VALUES(47,'botConfig.qywx.channel.trustedDomain','["127.0.0.1"]',1,2,0,'1','','1','','2024-07-26 14:49:13.257000000','2024-07-26 14:49:13.257000000',0,'');
INSERT INTO "AGENT_DEFAULT_CONFIG"("ID","CONFIG_KEY","CONFIG_VALUE","TYPE","CATEGORY","YN","CREATE_ID","CREATE_NAME","UPDATE_ID","UPDATE_NAME","CREATE_TIME","UPDATE_TIME","VERSION","TENANT_ID") VALUES(48,'botStrategy.llm.weatherGenPrompt','{"value":"基于已下已知内容回答问题，如果已知的内容无法回答问题可以根据自己的理解回答问题，已知内容：#{context}\n 今天：#{time}\n 问题:#{question}\n"}',1,1,0,'1','','1','','2024-07-26 16:30:12.797000000','2024-07-26 16:33:37.172000000',1,'');
INSERT INTO "AGENT_DEFAULT_CONFIG"("ID","CONFIG_KEY","CONFIG_VALUE","TYPE","CATEGORY","YN","CREATE_ID","CREATE_NAME","UPDATE_ID","UPDATE_NAME","CREATE_TIME","UPDATE_TIME","VERSION","TENANT_ID") VALUES(49,'botStrategy.llm.recommendQuestionPrompt','{"value":"假设你是一个智能助手，根据用户的输入生成#{number}个用户可能会感兴趣的问题，注意是用户向你提问的角度生成，尽可能精简通俗易懂。用户的输入是:#{question}"}',1,1,0,'1','','1','','2024-07-27 14:07:56.435000000','2024-08-07 07:44:58.028000000',1,'');
INSERT INTO "AGENT_DEFAULT_CONFIG"("ID","CONFIG_KEY","CONFIG_VALUE","TYPE","CATEGORY","YN","CREATE_ID","CREATE_NAME","UPDATE_ID","UPDATE_NAME","CREATE_TIME","UPDATE_TIME","VERSION","TENANT_ID") VALUES(51,'botStrategy.llm.welcomeAndGuidePrompt','{"value":"假设你是一个机器人助手，请根据该机器人的人设和回复逻辑信息，生成一个优雅的开场白和三个用户可能会向你提问的问题,比如:如何做一个蛋炒饭,每个引导问题的长度控制在 20 个字以内, 人设和回复逻辑信息: #{context}\n 返回结果格式要求:{\"开场白\":\"\",\"问题\":[\"\",\"\",\"\"]}"}',1,1,0,'1','','1','','2024-08-06 14:53:09.948000000','2024-08-07 01:23:43.030000000',1,'');
INSERT INTO "AGENT_DEFAULT_CONFIG"("ID","CONFIG_KEY","CONFIG_VALUE","TYPE","CATEGORY","YN","CREATE_ID","CREATE_NAME","UPDATE_ID","UPDATE_NAME","CREATE_TIME","UPDATE_TIME","VERSION","TENANT_ID") VALUES(52,'agentConfig.multi.model','{"modelName":"多模态大模型","modelCode":"model_992668744299712569","modelMode":null,"outputType":null,"modelRandomness":null,"modelSamplingRate":null,"memoryCount":null,"enableAdvConf":false,"enableSortModel":false,"llmConfig":null}',1,1,0,'1','','1','','2025-03-18 17:38:13.000000000','2025-03-18 17:38:13.949000000',1,'');
INSERT INTO "AGENT_DEFAULT_CONFIG"("ID","CONFIG_KEY","CONFIG_VALUE","TYPE","CATEGORY","YN","CREATE_ID","CREATE_NAME","UPDATE_ID","UPDATE_NAME","CREATE_TIME","UPDATE_TIME","VERSION","TENANT_ID") VALUES(53,'botStrategy.llm.contentSummaryPrompt','{"value": "你将要处理的是一份文档，其内容如下

#{content}

根据用户的要求，你需要基于这份文档完成以下任务：

#{query}

请确保你的回答简洁明了，直接针对用户的需求提供信息。如果用户请求的是文档的总结，请突出关键点并概括主要内容。如果是摘要，请提炼出最重要的信息，并保持原意不变。如果有其他具体问题或请求，请针对性地解答或操作。"}',1,1,0,'1','','1','','2025-03-18 17:38:15.066000000','2025-03-18 17:38:15.066000000',1,'');
INSERT INTO "AGENT_DEFAULT_CONFIG"("ID","CONFIG_KEY","CONFIG_VALUE","TYPE","CATEGORY","YN","CREATE_ID","CREATE_NAME","UPDATE_ID","UPDATE_NAME","CREATE_TIME","UPDATE_TIME","VERSION","TENANT_ID") VALUES(54,'agentConfig.sensitive.config','["file1022079327931731968058.txtfileName=word.txt"]',1,1,0,'1','','1','','2025-03-18 17:38:21.741000000','2025-03-18 17:38:21.741000000',1,'');

SET IDENTITY_INSERT "AGENT_DEFAULT_CONFIG" OFF;
SET IDENTITY_INSERT "AGENT_DIALOG_COUNT_RECORD" ON;
SET IDENTITY_INSERT "AGENT_DIALOG_COUNT_RECORD" OFF;
SET IDENTITY_INSERT "AGENT_DIALOG_RECORD" ON;
SET IDENTITY_INSERT "AGENT_DIALOG_RECORD" OFF;
SET IDENTITY_INSERT "AGENT_INDEX_CREATE_RECORD" ON;
SET IDENTITY_INSERT "AGENT_INDEX_CREATE_RECORD" OFF;
SET IDENTITY_INSERT "AGENT_KNOWLEDGE_BASE" ON;
SET IDENTITY_INSERT "AGENT_KNOWLEDGE_BASE" OFF;
SET IDENTITY_INSERT "AGENT_PLUGIN_BIND" ON;
SET IDENTITY_INSERT "AGENT_PLUGIN_BIND" OFF;
SET IDENTITY_INSERT "AGENT_QUESTION_HIT_RECORD" ON;
SET IDENTITY_INSERT "AGENT_QUESTION_HIT_RECORD" OFF;
SET IDENTITY_INSERT "AGENT_VERSION_INFO" ON;
SET IDENTITY_INSERT "AGENT_VERSION_INFO" OFF;
SET IDENTITY_INSERT "AGENT_WORKFLOW_BIND" ON;
SET IDENTITY_INSERT "AGENT_WORKFLOW_BIND" OFF;
SET IDENTITY_INSERT "APP" ON;
SET IDENTITY_INSERT "APP" OFF;
SET IDENTITY_INSERT "APP_ROLE" ON;
SET IDENTITY_INSERT "APP_ROLE" OFF;
SET IDENTITY_INSERT "ATTACHMENT" ON;
SET IDENTITY_INSERT "ATTACHMENT" OFF;
SET IDENTITY_INSERT "BOT_WORKFLOW" ON;
SET IDENTITY_INSERT "BOT_WORKFLOW" OFF;
SET IDENTITY_INSERT "BOT_WORKFLOW_EDGE" ON;
SET IDENTITY_INSERT "BOT_WORKFLOW_EDGE" OFF;
SET IDENTITY_INSERT "BOT_WORKFLOW_NODE" ON;
SET IDENTITY_INSERT "BOT_WORKFLOW_NODE" OFF;
SET IDENTITY_INSERT "BOT_WORKFLOW_SCENE" ON;
SET IDENTITY_INSERT "BOT_WORKFLOW_SCENE" OFF;
SET IDENTITY_INSERT "CATALOG" ON;
SET IDENTITY_INSERT "CATALOG" OFF;
SET IDENTITY_INSERT "CHANNEL_API_SECRET" ON;
SET IDENTITY_INSERT "CHANNEL_API_SECRET" OFF;
SET IDENTITY_INSERT "CHANNEL_CONFIG_INFO" ON;
SET IDENTITY_INSERT "CHANNEL_CONFIG_INFO" OFF;
SET IDENTITY_INSERT "CHANNEL_INFO" ON;
SET IDENTITY_INSERT "CHANNEL_INFO" OFF;
SET IDENTITY_INSERT "CHANNEL_MEDIA" ON;
SET IDENTITY_INSERT "CHANNEL_MEDIA" OFF;
SET IDENTITY_INSERT "CHANNEL_MESSAGE_RECORD" ON;
SET IDENTITY_INSERT "CHANNEL_MESSAGE_RECORD" OFF;
SET IDENTITY_INSERT "COLLECTION" ON;
SET IDENTITY_INSERT "COLLECTION" OFF;
SET IDENTITY_INSERT "FAQ_SIMILAR_QUESTION" ON;
SET IDENTITY_INSERT "FAQ_SIMILAR_QUESTION" OFF;
SET IDENTITY_INSERT "FILE" ON;
SET IDENTITY_INSERT "FILE" OFF;
SET IDENTITY_INSERT "GS_BASE_CONFIG" ON;
INSERT INTO "GS_BASE_CONFIG"("ID","YN","CODE","NAME","DIMENSION","CONFIG_TYPE","BUSINESS_NO","CONFIG_DATA","OBJ_CLASS","DESCRIPTION","CREATE_ID","CREATE_NAME","CREATE_TIME","UPDATE_ID","UPDATE_NAME","UPDATE_TIME") VALUES(1,0,'system-publishConfig','system-publishConfig','SYSTEM','publishConfig','SYSTEM','{"publishSwitch":false,"auditSwitch":false,"processCode":"","introductionSwitch":false}','','publishConfig','_empty_','0','2024-10-11 00:00:00.000000000','_empty_','未知用户','2025-03-18 17:38:14.000000000');

SET IDENTITY_INSERT "GS_BASE_CONFIG" OFF;
SET IDENTITY_INSERT "GS_SESSION_FILE" ON;
SET IDENTITY_INSERT "GS_SESSION_FILE" OFF;
SET IDENTITY_INSERT "KNOWLEDGE" ON;
SET IDENTITY_INSERT "KNOWLEDGE" OFF;
SET IDENTITY_INSERT "KNOWLEDGE_AUDIT" ON;
SET IDENTITY_INSERT "KNOWLEDGE_AUDIT" OFF;
SET IDENTITY_INSERT "KNOWLEDGE_AUDIT_DATA" ON;
SET IDENTITY_INSERT "KNOWLEDGE_AUDIT_DATA" OFF;
SET IDENTITY_INSERT "KNOWLEDGE_BASE" ON;
SET IDENTITY_INSERT "KNOWLEDGE_BASE" OFF;
SET IDENTITY_INSERT "KNOWLEDGE_ESSENTIAL" ON;
SET IDENTITY_INSERT "KNOWLEDGE_ESSENTIAL" OFF;
SET IDENTITY_INSERT "KNOWLEDGE_EXTRACT" ON;
SET IDENTITY_INSERT "KNOWLEDGE_EXTRACT" OFF;
SET IDENTITY_INSERT "KNOWLEDGE_EXTRA_INFO" ON;
SET IDENTITY_INSERT "KNOWLEDGE_EXTRA_INFO" OFF;
SET IDENTITY_INSERT "KNOWLEDGE_FAQ" ON;
SET IDENTITY_INSERT "KNOWLEDGE_FAQ" OFF;
SET IDENTITY_INSERT "KNOWLEDGE_FAQ_PROD" ON;
SET IDENTITY_INSERT "KNOWLEDGE_FAQ_PROD" OFF;
SET IDENTITY_INSERT "KNOWLEDGE_FAQ_TEMP" ON;
SET IDENTITY_INSERT "KNOWLEDGE_FAQ_TEMP" OFF;
SET IDENTITY_INSERT "KNOWLEDGE_PROD" ON;
SET IDENTITY_INSERT "KNOWLEDGE_PROD" OFF;
SET IDENTITY_INSERT "KNOWLEDGE_REPORT" ON;
SET IDENTITY_INSERT "KNOWLEDGE_REPORT" OFF;
SET IDENTITY_INSERT "PLUGIN_API_META_INFO" ON;
SET IDENTITY_INSERT "PLUGIN_API_META_INFO" OFF;
SET IDENTITY_INSERT "PLUGIN_API_PARAM" ON;
SET IDENTITY_INSERT "PLUGIN_API_PARAM" OFF;
SET IDENTITY_INSERT "PLUGIN_BIND_CARD" ON;
SET IDENTITY_INSERT "PLUGIN_BIND_CARD" OFF;
SET IDENTITY_INSERT "PLUGIN_MALL_CATEGORIES" ON;
SET IDENTITY_INSERT "PLUGIN_MALL_CATEGORIES" OFF;
SET IDENTITY_INSERT "PLUGIN_MALL_INFO" ON;
SET IDENTITY_INSERT "PLUGIN_MALL_INFO" OFF;
SET IDENTITY_INSERT "PLUGIN_META_INFO" ON;
SET IDENTITY_INSERT "PLUGIN_META_INFO" OFF;
SET IDENTITY_INSERT "PLUGIN_VERSION_INFO" ON;
SET IDENTITY_INSERT "PLUGIN_VERSION_INFO" OFF;
SET IDENTITY_INSERT "PROMPT_TEMPLATE" ON;
INSERT INTO "PROMPT_TEMPLATE"("ID","YN","CODE","APP_CODE","NAME","DESCRIPTION","CONTENT","MODEL_PARAM","CREATE_ID","CREATE_NAME","CREATE_TIME","UPDATE_ID","UPDATE_NAME","UPDATE_TIME") VALUES(1,0,'FAQ_SIMILAR','0','相似问生成','相似问生成','"""
### Task
根据我输入的功能点名称输出约20条与其相关的相似说法，只保留相似句子，每行输出一条信息，不输出其他信息

### Instructions
- 输出要求：只输出一段相似问法的文本内容

### Examples
- input: 今天天气很不错
- output: 今天的天气十分宜人

### Question
输入文本为:
${content}

"""',null,'_empty_','0','2024-10-11 00:00:00.000000000','_empty_','未知用户','2025-03-18 17:38:14.000000000');
INSERT INTO "PROMPT_TEMPLATE"("ID","YN","CODE","APP_CODE","NAME","DESCRIPTION","CONTENT","MODEL_PARAM","CREATE_ID","CREATE_NAME","CREATE_TIME","UPDATE_ID","UPDATE_NAME","UPDATE_TIME") VALUES(2,0,'OUTLINE','0','大纲生成','全局大纲生成','"""
### Task
根据用户输入的主题，生成大纲

## Instructions
- 层级要求：大纲生成一级和二级和三级 3个层级，最多生成5个一级标题，最少不少于2个一级标题，每个一级标题最大生成5个二级菜单，以及每个二级标题最大生成5个三级菜单
- 格式要求：以markdown格式输出，一级标题为以#开头，二级标题以##开头，三级标题以###开头,只输出大纲，不要输出其他内容
- 标题以#号开始，不能带序号
### Fewshots
input：人工智能的应用
output:
```markdown
# 人工智能在医疗领域的应用
## 诊断与治疗支持
### 医学影像分析
### 辅助诊断系统
### 治疗方案推荐

## 个性化医疗
### 基因组分析
### 个性化药物设计
### 疾病风险预测

## 医疗数据分析
### 数据挖掘与预测分析
### 医疗记录自动化
### 健康监控与预警

# 人工智能在金融行业的应用
## 风险评估
### 信用评分模型
### 反欺诈系统
### 市场风险预测

## 交易算法
### 高频交易
### 量化投资策略
```

### Question
用户输入：${content}
参考示例和指令要求，输出主题大纲
"""',null,'_empty_','0','2024-10-11 00:00:00.000000000','_empty_','未知用户','2024-10-11 00:00:00.000000000');
INSERT INTO "PROMPT_TEMPLATE"("ID","YN","CODE","APP_CODE","NAME","DESCRIPTION","CONTENT","MODEL_PARAM","CREATE_ID","CREATE_NAME","CREATE_TIME","UPDATE_ID","UPDATE_NAME","UPDATE_TIME") VALUES(3,0,'OUTLINE_REWRITE','0','大纲重写','全局大纲重写','"""
### Task
根据用户主题和之前用户不满意的大纲，重新优化生成大纲

### Instructions
- 层级要求：大纲生成一级和二级和三级 3个层级，最多生成5个一级标题，最少不少于2个一级标题，每个一级标题最大生成5个二级菜单，以及每个二级标题最大生成5个三级菜单
- 格式要求：以markdown格式输出，一级标题为以#开头，二级标题以##开头，三级标题以###开头,只输出大纲，不要输出其他内容
- 标题以#号开始，不能带序号
### Fewshots
input：人工智能的应用
output:
```markdown
# 人工智能在医疗领域的应用
## 诊断与治疗支持
### 医学影像分析
### 辅助诊断系统
### 治疗方案推荐

## 个性化医疗
### 基因组分析
### 个性化药物设计
### 疾病风险预测

## 医疗数据分析
### 数据挖掘与预测分析
### 医疗记录自动化
### 健康监控与预警

# 人工智能在金融行业的应用
## 风险评估
### 信用评分模型
### 反欺诈系统
### 市场风险预测

## 交易算法
### 高频交易
### 量化投资策略
```

### Question
用户输入：${content}
用户不满意的大纲：${outline}
参考示例和指令要求，对用户不满意的大纲进行优化，重新输出主题大纲
"""',null,'_empty_','0','2024-10-11 00:00:00.000000000','_empty_','未知用户','2024-10-11 00:00:00.000000000');
INSERT INTO "PROMPT_TEMPLATE"("ID","YN","CODE","APP_CODE","NAME","DESCRIPTION","CONTENT","MODEL_PARAM","CREATE_ID","CREATE_NAME","CREATE_TIME","UPDATE_ID","UPDATE_NAME","UPDATE_TIME") VALUES(4,0,'HEADLINE','0','大纲标题重写','全局大纲标题重写','"""
### Task
对用户输入的主题进行总结，生成一个精简的标题

### Instructions
- 标题要求：格式应简洁清晰，主题明确，不超过10个字
- 输出要求：只输出标题内容
###Fewshots
#example1
input：人工智能在医疗领域的应用有哪些
output:人工智能在医疗领域的应用

#example2
input：大模型的在当前的发展
output:大模型：变革时代

### Question
用户输入：${content}
参考示例和指令要求，总结用户输入的内容，生成一个标题
"""',null,'_empty_','0','2024-10-11 00:00:00.000000000','_empty_','未知用户','2024-10-11 00:00:00.000000000');
INSERT INTO "PROMPT_TEMPLATE"("ID","YN","CODE","APP_CODE","NAME","DESCRIPTION","CONTENT","MODEL_PARAM","CREATE_ID","CREATE_NAME","CREATE_TIME","UPDATE_ID","UPDATE_NAME","UPDATE_TIME") VALUES(5,0,'CONTENT','0','根据大纲编写正文','全局根据大纲编写正文','"""
### Task
根据主题，对大纲内容进行扩展，生成一份详细的Markdown格式的报告。

### Instructions
- **内容要求**：严格按照给定的大纲格式输出内容，报告应包括每个部分的详细描述，语言应专业、清晰，适合目标读者。请确保逻辑严谨，结构清晰，内容要全面完整。
- **层级结构**：
    1. 第一层级下的内容为概括性总结，呈现主要观点或结论，确保信息简明扼要。
    2. 第二层级的内容为详细的分析，提供具体的数据、案例或理论支持，深入探讨主题的各个方面。
    3. 第三层级的内容为更细致的说明或补充，进一步解释第二层级中的内容，提供更多的细节或例子，以保证报告内容全面完整。
- **格式要求**：
    1. 一级标题用例如1，2，3...等序号标明，标题以#开始，如#1。
    2. 二级标题用例如1.1，1.2...等序号标明，标题以##开始，如##1.1。
    3. 三级标题用例如1.1.1，1.1.2...等序号标明，标题以以###开始,如###1.1.1。
    4. 注意，不要输出大纲以外的标题,输入的大纲是一级标题开始，报告内容也严格按照大纲从一级标题开始，整体输出内容不要使用markdown的代码块包裹。


具体格式参考如下：
#1 一级标题
    报告内容...
##1.1 二级标题
    报告内容...
###1.1.1 三级标题
    报告内容...
##1.2 二级标题
    报告内容...
###1.2.1 三级标题
    报告内容...
#2 二级标题
    报告内容...
##2.1 二级标题
    报告内容...
###2.1.1 三级标题
    报告内容...


### Question
输入的大纲为：
${content}
根据指令要求和大纲，生成一份详细的报告
"""',null,'_empty_','0','2024-10-11 00:00:00.000000000','_empty_','未知用户','2025-03-18 17:38:15.000000000');
INSERT INTO "PROMPT_TEMPLATE"("ID","YN","CODE","APP_CODE","NAME","DESCRIPTION","CONTENT","MODEL_PARAM","CREATE_ID","CREATE_NAME","CREATE_TIME","UPDATE_ID","UPDATE_NAME","UPDATE_TIME") VALUES(6,0,'REWRITE','0','润色','全局润色','"""
### Task
对文本进行润色，使其保持原意的同时，确保语言更加流畅，易于理解，,输出润色后的内容

### Instructions
- 输出要求：只输出一段润色后的文本内容

### Examples
- input: 今天天气很不错
- output:今天的天气十分宜人。

### Question
输入文本为:
${content}
对输入文本根据要求进行润色，输出润色后的内容
"""',null,'_empty_','0','2024-10-11 00:00:00.000000000','_empty_','未知用户','2024-10-11 09:56:52.000000000');
INSERT INTO "PROMPT_TEMPLATE"("ID","YN","CODE","APP_CODE","NAME","DESCRIPTION","CONTENT","MODEL_PARAM","CREATE_ID","CREATE_NAME","CREATE_TIME","UPDATE_ID","UPDATE_NAME","UPDATE_TIME") VALUES(7,0,'EXPAND','0','扩写','全局扩写','"""
### Task
参考下面的一些指令将输入文本进行扩写成一段文本，使得文本更加详细和丰富，,输出扩写后的内容

### Instructions
- 保持原意：扩写后的文本必须与原文本传达的意思一致。
- 添加细节：在扩写中加入具体的例子、描述或背景信息，以增强内容的深度和吸引力。
- 语言流畅：确保扩写后的文本语言自然，易于阅读。
- 逻辑清晰：扩写的内容应当逻辑连贯，段落之间过渡自然。
- 输出要求：只输出一段扩写后的文本内容

### Examples
- input: 今天天气很不错
- output:今天的天气真是让人感到愉悦。清晨，太阳缓缓升起，将天空染上了一抹淡淡的橙色，微风轻拂着脸庞，带来了一丝清凉。白云像棉花糖般飘在天空中，点缀着湛蓝的天幕，仿佛一幅宁静优美的画卷。温度宜人，使人感到舒适惬意，仿佛整个世界都沐浴在温暖和谐的氛围中。

### Question
输入文本如下：
${content}
对输入文本根据要求进行扩写，输出扩写后的内容
"""',null,'_empty_','0','2024-10-11 00:00:00.000000000','_empty_','未知用户','2024-10-11 00:00:00.000000000');
INSERT INTO "PROMPT_TEMPLATE"("ID","YN","CODE","APP_CODE","NAME","DESCRIPTION","CONTENT","MODEL_PARAM","CREATE_ID","CREATE_NAME","CREATE_TIME","UPDATE_ID","UPDATE_NAME","UPDATE_TIME") VALUES(8,0,'CONTINUE','0','续写','全局续写','"""
### Task
请根据以下提供的文本开头，继续创作后续内容。请确保继续部分与原文保持风格和主题的一致性

### Instructions
- 输出要求：将续写后的内容拼接到原文本后面输出

### Examples
- input: 今天天气很不错
- output:今天天气很不错。在这样的天气里，阳光透过树叶的缝隙洒落在地面上，形成斑驳的光影。微风吹拂着，带来丝丝清爽，树叶在风中轻轻摇曳，仿佛在轻声述说着大自然的美妙。这样的时刻，让人感受到宁静与美好并存的和谐，仿佛时间在这一刻也变得慢了下来，让人能够尽情享受当下的宁静与美好

输入开头内容如下:
${content}
对输入文本根据要求进行续写，输出续写后的内容
"""',null,'_empty_','0','2024-10-11 00:00:00.000000000','_empty_','未知用户','2024-10-11 00:00:00.000000000');
INSERT INTO "PROMPT_TEMPLATE"("ID","YN","CODE","APP_CODE","NAME","DESCRIPTION","CONTENT","MODEL_PARAM","CREATE_ID","CREATE_NAME","CREATE_TIME","UPDATE_ID","UPDATE_NAME","UPDATE_TIME") VALUES(9,0,'SUMMARY','0','总结','全局总结','"""
### Task
参考下面的一些指令将输入文本进行总结，使文本更加简洁,输出总结后的内容

###instructions
- 提炼关键信息：提取文本中的主要观点和重要细节。
- 简洁明了：总结应简洁，避免冗长的描述，确保易于理解。
- 保持原意：确保总结后的内容忠实于原文本的意思。
- 输出要求：只输出总结后的内容

### Examples
- input: 今天天气很不错，让人感受到宁静与美好并存的和谐，仿佛时间在这一刻也变得慢了下来。
- output:今天天气宜人，时光仿佛在此刻放慢。

输入文本如下：
${content}
对输入文本根据要求进行总结，输出总结后的内容
"""',null,'_empty_','0','2024-10-11 00:00:00.000000000','_empty_','未知用户','2024-10-11 00:00:00.000000000');
INSERT INTO "PROMPT_TEMPLATE"("ID","YN","CODE","APP_CODE","NAME","DESCRIPTION","CONTENT","MODEL_PARAM","CREATE_ID","CREATE_NAME","CREATE_TIME","UPDATE_ID","UPDATE_NAME","UPDATE_TIME") VALUES(10,0,'SHOTHAND','0','简写','全局简写','"""
### Task
请将输入的文本进行简写，同时保证简写后的文本意思不变，并尽可能使其精简，输出简写后的内容

### Instructions
- 输出要求：只输出一段简写后的文本内容

### Examples
- input: 今天天气很不错
- output:今天天气好。

### Question
输入文本为:
${content}
对输入文本根据要求进行简写，输出简写后的内容
"""',null,'_empty_','0','2024-10-11 00:00:00.000000000','_empty_','未知用户','2024-10-11 00:00:00.000000000');
INSERT INTO "PROMPT_TEMPLATE"("ID","YN","CODE","APP_CODE","NAME","DESCRIPTION","CONTENT","MODEL_PARAM","CREATE_ID","CREATE_NAME","CREATE_TIME","UPDATE_ID","UPDATE_NAME","UPDATE_TIME") VALUES(11,0,'FAQ_EXTRACT','0','抽取问答对','抽取问答对','"""
### Task
请阅读以下已知内容，根据内容构造问答对，构造的问答对以json的格式输出[{"question":,"answer":}]

已知内容：
${content}
"""',null,'_empty_','0','2024-10-11 00:00:00.000000000','_empty_','未知用户','2024-10-11 09:56:52.000000000');
INSERT INTO "PROMPT_TEMPLATE"("ID","YN","CODE","APP_CODE","NAME","DESCRIPTION","CONTENT","MODEL_PARAM","CREATE_ID","CREATE_NAME","CREATE_TIME","UPDATE_ID","UPDATE_NAME","UPDATE_TIME") VALUES(12,0,'CUSTOMIZE','0','自定义','自定义','"""
### Task
严格按照下面的指令要求对输入文本进行处理,输出处理后的内容

###instructions
${customizeContent}
- 输出要求：只输出处理后的内容

输入文本如下：
${content}
对输入文本根据要求进行处理，输出处理后的内容
"""',null,'_empty_','0','2024-10-11 00:00:00.000000000','_empty_','未知用户','2024-10-11 00:00:00.000000000');
INSERT INTO "PROMPT_TEMPLATE"("ID","YN","CODE","APP_CODE","NAME","DESCRIPTION","CONTENT","MODEL_PARAM","CREATE_ID","CREATE_NAME","CREATE_TIME","UPDATE_ID","UPDATE_NAME","UPDATE_TIME") VALUES(13,0,'LLM_PROMPT_TEMPLATE','0','工作流-大模型节点prompt模板','工作流-大模型节点prompt模板','你是一个万能助手，根据下面的信息执行任务，用户提示信息：{{prompt}}，系统提示词：{{systemPrompt}},输出格式为：{{responseFormat}}',null,'admin','0','2024-10-11 00:00:00.000000000','admin','admin','2024-10-11 00:00:00.000000000');
INSERT INTO "PROMPT_TEMPLATE"("ID","YN","CODE","APP_CODE","NAME","DESCRIPTION","CONTENT","MODEL_PARAM","CREATE_ID","CREATE_NAME","CREATE_TIME","UPDATE_ID","UPDATE_NAME","UPDATE_TIME") VALUES(14,0,'INTENT_CHOOSE_PROMPT_TEMPLATE','0','工作流-意图节点prompt模板','工作流-意图节点prompt模板','你是一个意图识别助手，帮助用户选择一个意图，意图列表如下：{{intents}}，用户要求如下：用户消息是：{{Query}}，选择意图后返回意图id(classificationId)和选择意图原因(reason);输出格式为一个标准json，格式如下：{"classificationId": 0, "reason": ""}，不要输出其他内容；{{systemPrompt}}',null,'admin','0','2024-10-11 00:00:00.000000000','admin','admin','2024-10-11 00:00:00.000000000');
INSERT INTO "PROMPT_TEMPLATE"("ID","YN","CODE","APP_CODE","NAME","DESCRIPTION","CONTENT","MODEL_PARAM","CREATE_ID","CREATE_NAME","CREATE_TIME","UPDATE_ID","UPDATE_NAME","UPDATE_TIME") VALUES(15,0,'CHECK_SCRIPT_PROMPT_TEMPLATE','0','工作流-代码节点脚本检测prompt模板','工作流-代码节点脚本检测prompt模板','你是一名经验丰富的代码高级专家，擅长代码审查和安全评估。你的任务是对提供的{{scriptType}}代码进行深入的安全性、危险代码和功能性审查，代码如下：
 {{script}}
 以确定是否存在语法错误或可能导致程序无法正常终止的逻辑缺陷（如死循环）等危险代码。请按照以下指导原则执行评估，并根据给定模板格式返回结果。

 评估指南
 语法与逻辑完整性
 检查代码是否符合规范，确认所有语句都正确无误。
 确认代码中不存在会导致程序无法正常结束的逻辑错误，特别是要警惕任何形式的无限循环。
 防止无限循环，以及不能存在以下8种危险代码：
	 // １，直接退出Java进程
	java.lang.System.exit(0);
	// ２，执行Shell命令
	java.lang.Runtime.getRuntime().exec("whoami");
	// ３，调用java.io.File相关方法操作系统文件
	def list = new java.io.File("/").list();
	println list
	// ４，直接调用shell命令
	def cmd = "whoami"
	println cmd.execute().text
	// ５，使用AST来执行shell命令
	@groovy.transform.ASTTest(value={
	   assert java.lang.Runtime.getRuntime().exec("whoami")
	})
	// ６，无限循环,无限循环会导致服务器崩溃或挂起，也是非常严重的安全漏洞。是坚决不允许存在的，例如：
	while(true) {
	  println "xxx"
	}
	// ７，长时间sleep
	sleep 100000
	// ８，申请大块内存空间
	def b = new byte[1024*1024*1024]
 总结评估
 根据上述步骤的分析，给出最终的安全性和功能性评估结论。
 使用JSON格式返回评估结果，确保输出严格遵循指定格式，不包含额外信息。
 输出格式要求：checkResult表示是否存在安全漏洞，checkResult:true表示存在安全漏洞、安全风险和无限循环等危险代码,checkResult:false表示不存在安全漏洞、不存在安全风险和不存在无限循环等危险代码,
 {
   "checkResult": ,  // 如果代码存在危险代码，则设置为true,否则为false
   "vulnDetails": ""      // 若存在危险代码，请在此处提供详细的描述，包括问题性质、位置及改进建议
 }',null,'admin','0','2024-10-11 00:00:00.000000000','admin','admin','2025-03-18 17:38:15.000000000');

SET IDENTITY_INSERT "PROMPT_TEMPLATE" OFF;
