package com.chinatelecom.gs.engine.kms.controller;

import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.kms.dto.KnowledgeFaqTempDTO;
import com.chinatelecom.gs.engine.kms.sdk.constants.KmsApis;
import com.chinatelecom.gs.engine.kms.sdk.vo.common.CodeName;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.faqextract.*;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.faqtemp.ExtractFaqTempCreateReq;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.faqtemp.ExtractFaqTempPageInfoQueryReq;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.faqtemp.ExtractFaqTempUpdateReq;
import com.chinatelecom.gs.engine.kms.service.KnowledgeExtractService;
import com.chinatelecom.gs.engine.kms.service.KnowledgeFaqTempService;
import com.chinatelelcom.gs.engine.sdk.common.Page;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.constraints.NotNull;


/**
 * @description:智能faq抽取
 * @author: xtxiang
 **/
@RestController
@Slf4j
@Tag(name = "智能faq抽取 Controller")
@RequestMapping(KmsApis.KMS_API + KmsApis.KNOWLEDGE + KmsApis.FAQ + KmsApis.FAQ_EXTRACTION)
public class KnowledgeFaqExtractionController {

    @Autowired
   private KnowledgeExtractService knowledgeFaqExtractService;

    @Autowired
    private KnowledgeFaqTempService knowledgeFaqAppService;


    @Operation(summary = "智能FAQ抽取", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "智能FAQ抽取", groupName = "问答对抽取")
    @AuditLog(businessType = "问答对抽取", operType = "智能FAQ抽取", operDesc = "智能FAQ抽取", objId="#param.code")
    @PostMapping(KmsApis.START)
    public Result<KnowledgeFaqExtractResp> extractInfo(@Validated @RequestBody KnowledgeFaqExtractFaqCodeReq param) {
        return Result.success(knowledgeFaqExtractService.extractInfo(param));
    }

    @Operation(summary = "智能FAQ抽取状态", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "智能FAQ抽取状态", groupName = "问答对抽取")
    @AuditLog(businessType = "问答对抽取", operType = "智能FAQ抽取状态", operDesc = "智能FAQ抽取状态", objId="#param.code")
    @PostMapping(value =KmsApis.STATUS)
    public Result<ExtractStatusResp> getStatus(@Validated @RequestBody KnowledgeFaqExtractFaqCodeReq param) {
        return Result.success(knowledgeFaqExtractService.getExtractStatusByCode(param));
    }

    @Operation(summary = "智能抽取问答对（临时）详情", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "智能抽取问答对（临时）详情", groupName = "问答对抽取")
    @AuditLog(businessType = "问答对抽取", operType = "智能抽取问答对（临时）详情", operDesc = "智能抽取问答对（临时）详情", objId="#code")
    @GetMapping(path ="/{code}")
    public Result<KnowledgeFaqTempDTO> getDetail(@PathVariable(name = "code")  String code ) {
        return Result.success(knowledgeFaqAppService.tmpGet(code));
    }


    @Operation(summary = "问答表更新", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "问答表更新", groupName = "问答对抽取")
    @AuditLog(businessType = "问答对抽取", operType = "问答表更新", operDesc = "问答表更新", objId="#param.code")
    @PostMapping(KmsApis.TMPUPDATE)
    public Result<Boolean> tmpUpdate( @Validated @NotNull @RequestBody ExtractFaqTempUpdateReq param) {
        return Result.success(knowledgeFaqAppService.tmpUpdate(param));
    }


    @Operation(summary = "智能抽取临时问答对结果入库", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "智能抽取临时问答对结果入库", groupName = "问答对抽取")
    @AuditLog(businessType = "问答对抽取", operType = "智能抽取临时问答对结果入库", operDesc = "智能抽取临时问答对结果入库", objId="#codes.knowledgeCode")
    @PostMapping(KmsApis.PUT)
    public Result<Boolean> tnoPut(@Validated @NotNull @RequestBody KnowledgeFaqExtractStoreReq codes) {
        String userTip = knowledgeFaqAppService.store(codes);
        Result<Boolean> success = Result.success();
        success.setUserTip(userTip);
        return success;
    }

    @Operation(summary = "智能抽取临时问答对创建", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "智能抽取临时问答对创建", groupName = "问答对抽取")
    @AuditLog(businessType = "问答对抽取", operType = "智能抽取临时问答对创建", operDesc = "智能抽取临时问答对创建", objId="#param.knowledgeCode")
    @PostMapping(KmsApis.TMPCREATE)
    public Result<Boolean> tmpCreate(@Validated @NotNull @RequestBody ExtractFaqTempCreateReq param) {
        return Result.success(knowledgeFaqAppService.TmpCreate(param));
    }

    @Operation(summary = "智能抽取结果删除", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "智能抽取结果删除", groupName = "问答对抽取")
    @AuditLog(businessType = "问答对抽取", operType = "智能抽取结果删除", operDesc = "智能抽取结果删除", objId="#param.code")
    @PostMapping(KmsApis.TMPDELETE)
    public Result<Boolean> tmpDelete(@Validated @NotNull @RequestBody  KnowledgeFaqExtractDeleteReq param) {
        Boolean result = knowledgeFaqAppService.tmpDelete(param);
        return Result.success(result);
    }


    @Operation(summary = "分页查询智能抽取结果", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "分页查询智能抽取结果", groupName = "问答对抽取")
    @AuditLog(businessType = "问答对抽取", operType = "分页查询智能抽取结果", operDesc = "分页查询智能抽取结果", objId="#code")
    @PostMapping(KmsApis.TMPDPAGE+"/{code}")
    public Result<Page<KnowledgeFaqTempDTO>> tmpPage(@PathVariable("code") String code, @Validated @NotNull @RequestBody ExtractFaqTempPageInfoQueryReq param) {
        param.setKnowledgeCode(code);
        return Result.success(knowledgeFaqAppService.tmpPage( param));
    }

    @Operation(summary = "润色优化", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "润色优化", groupName = "问答对抽取")
    @AuditLog(businessType = "问答对抽取", operType = "润色优化", operDesc = "润色优化", objId="null")
    @PostMapping(KmsApis.TMPREWRITE)
    public Result<KnowledgeFaqExtracRewriteResp> rewrite(@Validated @NotNull @RequestBody KnowledgeFaqExtracRewriteReq param) {
        KnowledgeFaqExtracRewriteResp result = knowledgeFaqAppService.rewrite(param);
        return Result.success(result);
    }

    @Operation(summary = "停止FAQ抽取任务", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "停止FAQ抽取任务", groupName = "问答对抽取")
    @AuditLog(businessType = "问答对抽取", operType = "停止FAQ抽取任务", operDesc = "停止FAQ抽取任务", objId="#code.code")
    @PostMapping(KmsApis.STOP)
    public Result<Boolean> stopExtractTask(@NotNull @RequestBody CodeName code) {
        return Result.success(knowledgeFaqExtractService.stopExtractTask(code.getCode()));
    }
}


