package com.chinatelecom.gs.engine.common.cache.memory;

import com.chinatelecom.gs.engine.core.sdk.vo.llm.LLMMessage;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MessageResponseCache extends LLMMessage implements Serializable {
    private String engineType;
    private String toolIntentId;
    private String toolIntentName;
    private String messageId;
    private String downMsgId;
    private String upMsgId;
    private String answer;
}
