package com.chinatelecom.gs.engine.core.manager.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.chinatelecom.cloud.platform.client.rpc.Menu;
import com.chinatelecom.cloud.platform.client.rpc.Permission;
import com.chinatelecom.gs.engine.common.config.property.GsGlobalConfig;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.core.manager.service.BaseConfigAppService;
import com.chinatelecom.gs.engine.core.manager.vo.config.PublishConfig;
import com.chinatelecom.gs.engine.core.manager.vo.config.SpeechConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

public class PermissionServiceImplTest {

    @InjectMocks
    private PermissionServiceImpl permissionService;

    @Mock
    private BaseConfigAppService configService;

    @Mock
    private GsGlobalConfig gsGlobalConfig;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    // 对公共方法的测试
    @Test
    void testFilterResource_WithValidData_ReturnsFilteredPermissions() {
        // Given
        List<Permission> permissions = new ArrayList<>();
        Permission perm1 = new Permission();
        perm1.setPermissionCode("publishFilter");
        Permission perm2 = new Permission();
        perm2.setPermissionCode("normalPermission");
        permissions.add(perm1);
        permissions.add(perm2);

        PublishConfig publishConfig = new PublishConfig();
        publishConfig.setPublishSwitch(false); // Should filter out "publishFilter"
        publishConfig.setAuditSwitch(true);

        SpeechConfig speechConfig = new SpeechConfig();
        speechConfig.setAsrSwitch(true);
        speechConfig.setTtsSwitch(true);

        Map<String, GsGlobalConfig.FilterModeConfig> filterModes = new HashMap<>();
        GsGlobalConfig.FilterModeConfig publishFilterConfig = new GsGlobalConfig.FilterModeConfig();
        publishFilterConfig.setFilteredCodes(CollUtil.newHashSet("publishFilter", "anotherPublishCode"));
        filterModes.put("publishFilter", publishFilterConfig);

        when(configService.getConfigOrDefault("publishConfig", "test-tenant")).thenReturn(publishConfig);
        when(configService.getConfigOrDefault("speechConfig", "test-tenant")).thenReturn(speechConfig);
        when(gsGlobalConfig.getFilterModes()).thenReturn(filterModes);

        try (MockedStatic<RequestContext> mockedRequestContext = mockStatic(RequestContext.class)) {
            mockedRequestContext.when(RequestContext::getTenantId).thenReturn("test-tenant");

            // When
            List<Permission> result = permissionService.filterResource(permissions);

            // Then
            assertNotNull(result);
            assertEquals(1, result.size());
            assertEquals("normalPermission", result.get(0).getPermissionCode());
        }
    }

    // 对边界条件的测试
    @Test
    void testFilterResource_WithEmptyPermissions_ReturnsEmptyList() {
        // Given
        List<Permission> permissions = new ArrayList<>();

        PublishConfig publishConfig = new PublishConfig();
        publishConfig.setPublishSwitch(true);
        publishConfig.setAuditSwitch(true);

        SpeechConfig speechConfig = new SpeechConfig();
        speechConfig.setAsrSwitch(true);
        speechConfig.setTtsSwitch(true);

        when(configService.getConfigOrDefault("publishConfig", "test-tenant")).thenReturn(publishConfig);
        when(configService.getConfigOrDefault("speechConfig", "test-tenant")).thenReturn(speechConfig);

        try (MockedStatic<RequestContext> mockedRequestContext = mockStatic(RequestContext.class)) {
            mockedRequestContext.when(RequestContext::getTenantId).thenReturn("test-tenant");

            // When
            List<Permission> result = permissionService.filterResource(permissions);

            // Then
            assertNotNull(result);
            assertTrue(result.isEmpty());
        }
    }

    // 对公共方法的测试
    @Test
    void testFilterMenu_WithValidData_ReturnsFilteredMenus() {
        // Given
        List<Menu> menus = new ArrayList<>();
        Menu menu1 = new Menu();
        menu1.setUrlAddress("publishFilter");
        Menu menu2 = new Menu();
        menu2.setUrlAddress("normalMenu");
        menus.add(menu1);
        menus.add(menu2);

        PublishConfig publishConfig = new PublishConfig();
        publishConfig.setPublishSwitch(false); // Should filter out "publishFilter"
        publishConfig.setAuditSwitch(true);

        SpeechConfig speechConfig = new SpeechConfig();
        speechConfig.setAsrSwitch(true);
        speechConfig.setTtsSwitch(true);

        Map<String, GsGlobalConfig.FilterModeConfig> filterModes = new HashMap<>();
        GsGlobalConfig.FilterModeConfig publishFilterConfig = new GsGlobalConfig.FilterModeConfig();
        publishFilterConfig.setFilteredCodes(CollUtil.newHashSet("publishFilter", "anotherPublishCode"));
        filterModes.put("publishFilter", publishFilterConfig);

        when(configService.getConfigOrDefault("publishConfig", "test-tenant")).thenReturn(publishConfig);
        when(configService.getConfigOrDefault("speechConfig", "test-tenant")).thenReturn(speechConfig);
        when(gsGlobalConfig.getFilterModes()).thenReturn(filterModes);

        try (MockedStatic<RequestContext> mockedRequestContext = mockStatic(RequestContext.class)) {
            mockedRequestContext.when(RequestContext::getTenantId).thenReturn("test-tenant");

            // When
            List<Menu> result = permissionService.filterMenu(menus);

            // Then
            assertNotNull(result);
            assertEquals(1, result.size());
            assertEquals("normalMenu", result.get(0).getUrlAddress());
        }
    }

    // 对边界条件的测试
    @Test
    void testFilterMenu_WithEmptyMenus_ReturnsEmptyList() {
        // Given
        List<Menu> menus = new ArrayList<>();

        PublishConfig publishConfig = new PublishConfig();
        publishConfig.setPublishSwitch(true);
        publishConfig.setAuditSwitch(true);

        SpeechConfig speechConfig = new SpeechConfig();
        speechConfig.setAsrSwitch(true);
        speechConfig.setTtsSwitch(true);

        when(configService.getConfigOrDefault("publishConfig", "test-tenant")).thenReturn(publishConfig);
        when(configService.getConfigOrDefault("speechConfig", "test-tenant")).thenReturn(speechConfig);

        try (MockedStatic<RequestContext> mockedRequestContext = mockStatic(RequestContext.class)) {
            mockedRequestContext.when(RequestContext::getTenantId).thenReturn("test-tenant");

            // When
            List<Menu> result = permissionService.filterMenu(menus);

            // Then
            assertNotNull(result);
            assertTrue(result.isEmpty());
        }
    }

    // 对边界条件的测试
    @Test
    void testFilterMenu_WithNestedMenuStructure_ReturnsFilteredMenus() {
        // Given
        List<Menu> menus = new ArrayList<>();
        Menu parentMenu = new Menu();
        parentMenu.setUrlAddress("parentMenu");

        List<Menu> children = new ArrayList<>();
        Menu child1 = new Menu();
        child1.setUrlAddress("publishFilter");
        Menu child2 = new Menu();
        child2.setUrlAddress("normalChild");
        children.add(child1);
        children.add(child2);
        parentMenu.setChildren(children);
        menus.add(parentMenu);

        PublishConfig publishConfig = new PublishConfig();
        publishConfig.setPublishSwitch(false); // Should filter out "publishFilter"
        publishConfig.setAuditSwitch(true);

        SpeechConfig speechConfig = new SpeechConfig();
        speechConfig.setAsrSwitch(true);
        speechConfig.setTtsSwitch(true);

        Map<String, GsGlobalConfig.FilterModeConfig> filterModes = new HashMap<>();
        GsGlobalConfig.FilterModeConfig publishFilterConfig = new GsGlobalConfig.FilterModeConfig();
        publishFilterConfig.setFilteredCodes(CollUtil.newHashSet("publishFilter"));
        filterModes.put("publishFilter", publishFilterConfig);

        when(configService.getConfigOrDefault("publishConfig", "test-tenant")).thenReturn(publishConfig);
        when(configService.getConfigOrDefault("speechConfig", "test-tenant")).thenReturn(speechConfig);
        when(gsGlobalConfig.getFilterModes()).thenReturn(filterModes);

        try (MockedStatic<RequestContext> mockedRequestContext = mockStatic(RequestContext.class)) {
            mockedRequestContext.when(RequestContext::getTenantId).thenReturn("test-tenant");

            // When
            List<Menu> result = permissionService.filterMenu(menus);

            // Then
            assertNotNull(result);
            assertEquals(1, result.size());
            assertEquals("parentMenu", result.get(0).getUrlAddress());
            assertNotNull(result.get(0).getChildren());
            assertEquals(1, result.get(0).getChildren().size());
            assertEquals("normalChild", result.get(0).getChildren().get(0).getUrlAddress());
        }
    }

    // 对异常情况的测试
    @Test
    void testFilterResource_WhenConfigServiceThrowsException_ThrowsException() {
        // Given
        List<Permission> permissions = new ArrayList<>();
        Permission perm = new Permission();
        perm.setPermissionCode("testPermission");
        permissions.add(perm);

        when(configService.getConfigOrDefault("publishConfig", "test-tenant"))
                .thenThrow(new RuntimeException("Config service error"));

        try (MockedStatic<RequestContext> mockedRequestContext = mockStatic(RequestContext.class)) {
            mockedRequestContext.when(RequestContext::getTenantId).thenReturn("test-tenant");

            // When & Then
            assertThrows(RuntimeException.class, () -> permissionService.filterResource(permissions));
        }
    }

    // 使用Mock对象测试依赖项
    @Test
    void testApplyResourceFilters_WithEmptyEnabledModes_ReturnsAllPermissions() {
        // Given
        List<Permission> permissions = new ArrayList<>();
        Permission perm1 = new Permission();
        perm1.setPermissionCode("perm1");
        Permission perm2 = new Permission();
        perm2.setPermissionCode("perm2");
        permissions.add(perm1);
        permissions.add(perm2);
        Set<String> enabledModes = new HashSet<>();

        // When
        List<Permission> result = permissionService.applyResourceFilters(permissions, enabledModes);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.contains(perm1));
        assertTrue(result.contains(perm2));
        // Verify that gsGlobalConfig.getFilterModes() was not called
        verify(gsGlobalConfig, never()).getFilterModes();
    }

    // 使用Mock对象测试依赖项
    @Test
    void testApplyMenuFilters_WithEmptyEnabledModes_ReturnsAllMenus() {
        // Given
        List<Menu> menus = new ArrayList<>();
        Menu menu1 = new Menu();
        menu1.setUrlAddress("menu1");
        Menu menu2 = new Menu();
        menu2.setUrlAddress("menu2");
        menus.add(menu1);
        menus.add(menu2);
        Set<String> enabledModes = new HashSet<>();

        // When
        List<Menu> result = permissionService.applyMenuFilters(menus, enabledModes);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.contains(menu1));
        assertTrue(result.contains(menu2));
        // Verify that gsGlobalConfig.getFilterModes() was not called
        verify(gsGlobalConfig, never()).getFilterModes();
    }

    // 验证方法返回值和副作用
    @Test
    void testGetFilteredCodes_WhenAllConfigsAreEnabled_ReturnsEmptySet() {
        // Given
        PublishConfig publishConfig = new PublishConfig();
        publishConfig.setPublishSwitch(true);
        publishConfig.setAuditSwitch(true);

        SpeechConfig speechConfig = new SpeechConfig();
        speechConfig.setAsrSwitch(true);
        speechConfig.setTtsSwitch(true);

        when(configService.getConfigOrDefault("publishConfig", "test-tenant")).thenReturn(publishConfig);
        when(configService.getConfigOrDefault("speechConfig", "test-tenant")).thenReturn(speechConfig);

        try (MockedStatic<RequestContext> mockedRequestContext = mockStatic(RequestContext.class)) {
            mockedRequestContext.when(RequestContext::getTenantId).thenReturn("test-tenant");

            // When
            // Use reflection to access private method
            Set<String> result = null;
            try {
                java.lang.reflect.Method method = PermissionServiceImpl.class.getDeclaredMethod("getFilteredCodes", String.class);
                method.setAccessible(true);
                result = (Set<String>) method.invoke(permissionService, "test-tenant");
            } catch (Exception e) {
                fail("Exception should not be thrown: " + e.getMessage());
            }

            // Then
            assertNotNull(result);
            assertTrue(result.isEmpty());
        }
    }

    // 验证方法返回值和副作用
    @Test
    void testGetFilteredCodes_WhenConfigsAreDisabled_ReturnsFilterCodes() {
        // Given
        PublishConfig publishConfig = new PublishConfig();
        publishConfig.setPublishSwitch(false); // Should add "publishFilter"
        publishConfig.setAuditSwitch(false);   // Should add "auditFilter"

        SpeechConfig speechConfig = new SpeechConfig();
        speechConfig.setAsrSwitch(false);      // Should add "asrFilter"
        speechConfig.setTtsSwitch(false);      // Should add "ttsFilter"

        when(configService.getConfigOrDefault("publishConfig", "test-tenant")).thenReturn(publishConfig);
        when(configService.getConfigOrDefault("speechConfig", "test-tenant")).thenReturn(speechConfig);

        try (MockedStatic<RequestContext> mockedRequestContext = mockStatic(RequestContext.class)) {
            mockedRequestContext.when(RequestContext::getTenantId).thenReturn("test-tenant");

            // When
            // Use reflection to access private method
            Set<String> result = null;
            try {
                java.lang.reflect.Method method = PermissionServiceImpl.class.getDeclaredMethod("getFilteredCodes", String.class);
                method.setAccessible(true);
                result = (Set<String>) method.invoke(permissionService, "test-tenant");
            } catch (Exception e) {
                fail("Exception should not be thrown: " + e.getMessage());
            }

            // Then
            assertNotNull(result);
            assertEquals(4, result.size());
            assertTrue(result.contains("publishFilter"));
            assertTrue(result.contains("auditFilter"));
            assertTrue(result.contains("asrFilter"));
            assertTrue(result.contains("ttsFilter"));
        }
    }

    // 验证方法返回值和副作用
    @Test
    void testGetAllFilteredCodes_WithValidEnabledModes_ReturnsFilteredCodes() {
        // Given
        Set<String> enabledModes = new HashSet<>(Arrays.asList("mode1", "mode2"));

        Map<String, GsGlobalConfig.FilterModeConfig> filterModes = new HashMap<>();
        GsGlobalConfig.FilterModeConfig mode1Config = new GsGlobalConfig.FilterModeConfig();
        mode1Config.setFilteredCodes(CollUtil.newHashSet("code1", "code2"));
        GsGlobalConfig.FilterModeConfig mode2Config = new GsGlobalConfig.FilterModeConfig();
        mode2Config.setFilteredCodes(CollUtil.newHashSet("code3", "code4"));
        filterModes.put("mode1", mode1Config);
        filterModes.put("mode2", mode2Config);

        when(gsGlobalConfig.getFilterModes()).thenReturn(filterModes);

        // When
        // Use reflection to access private method
        Set<String> result = null;
        try {
            java.lang.reflect.Method method = PermissionServiceImpl.class.getDeclaredMethod("getAllFilteredCodes", Set.class);
            method.setAccessible(true);
            result = (Set<String>) method.invoke(permissionService, enabledModes);
        } catch (Exception e) {
            fail("Exception should not be thrown: " + e.getMessage());
        }

        // Then
        assertNotNull(result);
        assertEquals(4, result.size());
        assertTrue(result.contains("code1"));
        assertTrue(result.contains("code2"));
        assertTrue(result.contains("code3"));
        assertTrue(result.contains("code4"));
    }
}
