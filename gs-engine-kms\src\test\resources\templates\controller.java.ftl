package ${package.Controller};


import com.chinatelelcom.gs.engine.sdk.common.Result;
import com.chinatelelcom.gs.engine.sdk.common.Result;

import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

<#if restControllerStyle>
<#else>
    import org.springframework.stereotype.Controller;
</#if>
<#if superControllerClassPackage??>
    import ${superControllerClassPackage};
</#if>


import javax.validation.Valid;
import java.util.List;

/**
* <p>
    * ${table.comment} Controller
    * </p>
*
* <AUTHOR>
* @since ${date}
*/
@Api(tags = "${table.comment} Controller")
<#if restControllerStyle>
    @RestController
<#else>
    @Controller
</#if>
@Slf4j
@RequestMapping(PATH)
<#if kotlin>
    class ${table.controllerName}<#if superControllerClass??> : ${superControllerClass}()</#if>
<#else>
    <#if superControllerClass??>
        public class ${table.controllerName} extends ${superControllerClass} {
    <#else>
        public class ${table.controllerName} {
    </#if>

    @Autowired
    private ${table.serviceName} ${table.serviceName?uncap_first};

    @ApiOperation(value = "${table.comment}分页列表", response = ${entity}.class)
    @PostMapping(KmsApis.PAGE_API)
    public  Result<Page<${entity}VO>> page(@Validated @RequestBody ${entity}QueryParam param) {

    return Result.success(${table.serviceName?uncap_first}.pageQuery(param));
    }

    @ApiOperation(value = "${table.comment}详情", response = ${entity}.class)
    @GetMapping(KmsApis.CODE_PATH)
    public  Result<${entity}VO> get(@PathVariable("code")  String code) {
    return Result.success(${table.serviceName?uncap_first}.get(code));
    }

    @ApiOperation(value = "${table.comment}新增")
    @PostMapping
    public Result<Boolean> add(@Validated @RequestBody ${entity}CreateParam createParam) {
        return Result.success(${table.serviceName?uncap_first}.create(createParam));
    }


    @ApiOperation(value = "${table.comment}更新")
    @PutMapping(KmsApis.CODE_PATH)
    public Result<Boolean> update(@PathVariable("code") String code, @Validated @RequestBody ${entity}UpdateParam param) {
        return Result.success(${table.serviceName?uncap_first}.update(code, param));
        }



    @ApiOperation(value = "${table.comment}删除")
    @PostMapping(KmsApis.DELETE_API)
    public Result<Boolean> delete(@Validated @RequestBody CodeParam codes) {
        return Result.success(${table.serviceName?uncap_first}.delete(codes));
        }
}


</#if>
