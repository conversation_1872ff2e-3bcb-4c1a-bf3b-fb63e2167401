package com.chinatelecom.gs.engine.task.handler.script;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.chinatelecom.gs.engine.common.utils.LlmUtil;
import com.chinatelecom.gs.engine.core.model.enums.ModelTypeEnum;
import com.chinatelecom.gs.engine.core.model.toolkit.StreamingChatLanguageModel;
import com.chinatelecom.gs.engine.core.model.toolkit.core.dto.LLMRequest;
import com.chinatelecom.gs.engine.core.model.toolkit.core.dto.ModelProviderEnum;
import com.chinatelecom.gs.engine.core.model.toolkit.core.dto.Response;
import com.chinatelecom.gs.engine.core.sdk.rpc.ModelServiceClient;
import com.chinatelecom.gs.engine.core.sdk.vo.ModelPageListParam;
import com.chinatelecom.gs.engine.task.handler.<PERSON>riptNodeHandler;
import com.chinatelecom.gs.engine.task.handler.script.api.ScriptExecutorApi;
import com.chinatelecom.gs.engine.task.handler.script.api.request.SandboxRunRequest;
import com.chinatelecom.gs.engine.task.handler.script.api.response.SandboxRunData;
import com.chinatelecom.gs.engine.task.handler.script.api.response.SandboxRunResponse;
import com.chinatelecom.gs.workflow.core.workflow.core.enums.ScriptTypeEnum;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @USER: pengmc1
 * @DATE: 2024/10/9 9:15
 */

@Slf4j
@Component
public class PythonJepScriptExecutor extends ScriptExecutor {

    @Resource
    private ScriptExecutorApi scriptExecutorApi;

    @Resource
    private ModelServiceClient remoteServiceClient;

    @Resource
    private StreamingChatLanguageModel streamingChatLanguageModel;

    @Override
    public ScriptTypeEnum scriptType() {
        return ScriptTypeEnum.PYTHON;
    }

    /**
     * python 脚本执行器
     *
     * @param scriptRequest 脚本请求对象，包含脚本代码和参数等信息
     * @return Object 脚本执行结果
     */
    @Override
    protected Object executeScript(ScriptRequest scriptRequest) {
        // 启用沙盒模式
        String code = LlmUtil.removeComments(scriptRequest.getScriptCode(), scriptRequest.getScriptType().getCode());
        log.debug("执行的脚本代码: {}", code);
        log.info("执行param: {}", JSON.toJSONString(new Object[]{scriptRequest.getScriptParam()}));

        // 注意这里使用的是英文的单引号，并且考虑到code可能包含特殊字符，采用三重引号包裹用户输入的代码
        SandboxRunRequest request = new SandboxRunRequest();
        request.setLanguage("python3");
        request.setCode(getRunnerScript(code, JSON.toJSONString(scriptRequest.getScriptParam())));
        request.setEnableNetwork(false);
        request.setArgs(new Object[]{scriptRequest.getScriptParam()});

        log.info("python沙箱执行请求参数{}", JSON.toJSONString(request));
        SandboxRunResponse sandboxRunResponse = scriptExecutorApi.scriptExecutor(request);
        if (0 != sandboxRunResponse.getCode()) {
            log.error("执行Python脚本失败, result: {}", JSON.toJSONString(sandboxRunResponse));
            throw new BizException("BA011", "Python脚本执行失败，请检查您的脚本代码是否正确:" + sandboxRunResponse.getMessage());
        }
        log.info("执行结果: {}", JSON.toJSONString(sandboxRunResponse.getData()));
        SandboxRunData runData = sandboxRunResponse.getData();
        if (Objects.nonNull(runData) && Objects.nonNull(runData.getStdout())) {
            try {
                if (CharSequenceUtil.isNotBlank(runData.getError()) && scriptRequest.getIsTest()) {
                    // 如果是调试，把错误信息交给大模型处理，给用户一个友好的提示，并且去除错误信息的敏感信息
                    String debugTip = checkScript(scriptRequest.getScriptType(), scriptRequest.getScriptCode(), runData.getError());
                    throw new BizException("BA012", "Python脚本执行失败，请检查您的脚本代码是否正确:" + debugTip);
                }
                String result = getResult(runData.getStdout());
                if (CharSequenceUtil.isBlank(result) && CharSequenceUtil.isBlank(runData.getError())) {
                    // 如果没有返回值且也没有错误，则认为执行成功但未获取到返回值，返回null
                    return null;
                }
                return JSON.parseObject(result);
            } catch (Exception e) {
                return runData.getStdout();
            }
        }
        log.info("执行Python脚本成功，但未获取到返回值:{}", JSON.toJSONString(runData));
        return null;
    }

    private String getRunnerScript(String code, String inputs) {
        return String.format("%s\nimport json\n" +
                        "import os\n" +
                        "def restricted_open(path, mode):\n" +
                        "    if path.startswith(\"/etc/\"):\n" +
                        "        raise PermissionError(\"Access denied to /etc/\")\n" +
                        "    return original_open(path, mode)\n" +
                        "original_open = open\n" +
                        "open = restricted_open\n" +
                        "inputs_obj = json.loads(r'%s')\n" +
                        "output_obj = main(inputs_obj)\n" +
                        "output_json = json.dumps(output_obj, indent=4)\n" +
                        "print(f'<<RESULT>>{output_json}<<RESULT>>')",
                code, inputs);
    }

    private String getResult(String stdout) {
        Pattern pattern = Pattern.compile("<<RESULT>>(.*?)<<RESULT>>", Pattern.DOTALL);
        Matcher matcher = pattern.matcher(stdout);

        if (matcher.find()) {
            // 提取内容并去除首尾空格
            return matcher.group(1).trim();
        }
        return null;
    }


    /**
     * 给出脚本修复建议
     *
     * @param scriptType ScriptTypeEnum 脚本类型
     * @param scriptCode ScriptParam
     * @return ScriptRequest
     */
    private String checkScript(ScriptTypeEnum scriptType, String scriptCode, String error) {
        if (scriptType == null) {
            throw new BizException("BA006", "脚本类型不存在");
        }
        // 校验代码时候存在安全漏洞
        ModelPageListParam modelParam = remoteServiceClient.getDefaultModel(ModelTypeEnum.LLM.getCode());


        if (Objects.isNull(modelParam)) {
            throw new BizException("BA007", "代码检测无模型可用");
        }
        ScriptNodeHandler.ScriptParam scriptParam = new ScriptNodeHandler.ScriptParam();
        scriptParam.setScriptType(scriptType.getCode());
        scriptParam.setScriptCode(scriptCode);

        return llmCheckScript(scriptParam, modelParam, error);
    }

    /**
     * 大模型检测代码是否存在的问题
     *
     * @param scriptParam ScriptParam
     * @param modelParam  ModelPageListParam
     * @return String
     */
    private String llmCheckScript(ScriptNodeHandler.ScriptParam scriptParam, ModelPageListParam modelParam, String error) {
        HashMap<String, Object> promptMap = new HashMap<>();
        promptMap.put("scriptType", scriptParam.getScriptType());
        promptMap.put("script", scriptParam.getScriptCode());
        promptMap.put("error", error);
        String checkScriptPromptTemplate = "你是一名 {scriptType} 专家，帮助我找出代码中存在的问题，并且给出修复建议；\n 代码如下：{script} \n" +
                ",错误信息如下：{error}；\n 只输出存在问题和修复建议，输出内容控制在200字以内，不要输出其他信息，并且不要输出服务器的路径，不要输出敏感信息。";
        String llmPrompt = LlmUtil.replaceVariables(checkScriptPromptTemplate, promptMap);
        LLMRequest llmRequest = buildRequest(modelParam, llmPrompt);
        Response<String> llmResponse = streamingChatLanguageModel.syncGenerate(llmRequest);
        return llmResponse.content();
    }

    /**
     * 构建大模型请求参数
     *
     * @param modelConfig    ModelPageListParam
     * @param promptTemplate String
     * @return LLMRequest
     */
    private LLMRequest buildRequest(ModelPageListParam modelConfig, String promptTemplate) {
        LLMRequest llmRequest = new LLMRequest();
        llmRequest.getLlmModelInfo().setProvider(ModelProviderEnum.from(modelConfig.getModelProvider()));
        llmRequest.getLlmModelInfo().setModelApi(modelConfig.getApiKey());
        llmRequest.getLlmModelInfo().setModelSecret(modelConfig.getModelSecret());
        llmRequest.getLlmModelInfo().setModelName(modelConfig.getModelCallName());
        llmRequest.getLlmModelInfo().setExtraConfig(modelConfig.getExternalModelConfig());
        llmRequest.getLlmModelInfo().setModelUrl(modelConfig.getExternalModelUrl());
        llmRequest.setStreaming(1);
        llmRequest.setTemperature(0.5);
        llmRequest.setText(promptTemplate);
        llmRequest.setUserId(IdUtil.fastSimpleUUID());
        if (Objects.nonNull(modelConfig.getExtraDataVO())) {
            llmRequest.getLlmModelInfo().setMaxToken(modelConfig.getExtraDataVO().getMaxInputToken());
            llmRequest.getLlmModelInfo().setTransformerType(modelConfig.getExtraDataVO().getTransformerType());
            llmRequest.getLlmModelInfo().setNativeCall(modelConfig.getExtraDataVO().getNativeCall());
            llmRequest.getLlmModelInfo().setNativeCallUrl(modelConfig.getExtraDataVO().getNativeCallUrl());
        }
        return llmRequest;
    }

}
