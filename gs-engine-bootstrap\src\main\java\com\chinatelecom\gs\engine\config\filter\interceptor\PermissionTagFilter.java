package com.chinatelecom.gs.engine.config.filter.interceptor;

import com.alibaba.fastjson.JSON;
import com.chinatelecom.cloud.platform.client.rpc.Menu;
import com.chinatelecom.cloud.platform.client.rpc.Permission;
import com.chinatelecom.cloud.platform.client.util.AuthUtils;
import com.chinatelecom.gs.engine.common.auth.PermissionTag;
import com.chinatelecom.gs.engine.common.cache.RedisService;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.context.RequestInfo;
import com.chinatelecom.gs.engine.common.platform.AppOwnerRequest;
import com.chinatelecom.gs.engine.common.platform.GovernmentAuthClient;
import com.chinatelecom.gs.engine.common.platform.KsAuthClient;
import com.chinatelecom.gs.engine.common.utils.InterceptorUtils;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RefreshScope
@Slf4j
@Component
public class PermissionTagFilter implements HandlerInterceptor {

    @Value(("${app.default.menuCheckSwitch:true}"))
    private Boolean menuCheckSwitch;

    @Value(("${app.default.menuCheckCacheSwitch:false}"))
    private Boolean menuCheckCacheSwitch;

    @Resource
    private RedisService redisService;

    @Value("${gs.system.env:}")
    private String environment;

    @Resource
    private KsAuthClient ksAuthClient;

    @Resource
    private GovernmentAuthClient governmentAuthClient;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 开关关闭时, 关闭权限检查
        if (!Boolean.TRUE.equals(menuCheckSwitch)) {
            return true;
        }

        RequestInfo userInfo = RequestContext.get();
        if (userInfo == null) {
            return true;
        }
        if (!(handler instanceof HandlerMethod)) {
            return true;
        }

        HandlerMethod handlerMethod = (HandlerMethod) handler;
        Method method = handlerMethod.getMethod();
        Class<?> clazz = handlerMethod.getBeanType();

        PermissionTag[] annotations = method.getAnnotationsByType(PermissionTag.class);
        if (annotations == null || annotations.length == 0) {
            annotations = clazz.getAnnotationsByType(PermissionTag.class);
        }

        // 无注解，放行
        if (annotations.length == 0) {
            return true;
        }

        // 缓存键
        String cacheKey = environment + ":" + "menuCheck" + ":" + userInfo.getTenantId() + ":" + userInfo.getUserId();
        List<String> allPermissions;
        if (Boolean.TRUE.equals(redisService.hasKey(cacheKey)) && Boolean.TRUE.equals(menuCheckCacheSwitch)) {
            String s = redisService.get(cacheKey);
            allPermissions = JSON.parseArray(s, String.class);
        } else {
            // 获取菜单和资源权限
            allPermissions = getPermissions(userInfo);
            if (CollectionUtils.isEmpty(allPermissions)) {
                return false;
            }
            if (CollectionUtils.isNotEmpty(allPermissions) && Boolean.TRUE.equals(menuCheckCacheSwitch)) {
                String json = JSON.toJSONString(allPermissions);
                // 写入缓存，有效期十分钟
                redisService.setEx(cacheKey, json, 10, TimeUnit.MINUTES);
            }
        }

        for (PermissionTag permissionTag : annotations) {
            String[] requiredCodes = permissionTag.code();
            if (requiredCodes == null || requiredCodes.length == 0) {
                continue;
            }
            if (CollectionUtils.containsAny(allPermissions, requiredCodes)) {
                return true;
            }
        }

        log.warn("接口请求无权限，method:{}  url:{}", request.getMethod(), request.getRequestURI());
        return InterceptorUtils.writeError(response, "CA998", "你没有该资源访问权限");
    }

    /**
     * 获取菜单和资源权限
     *
     * @param userInfo RequestInfo
     * @return List<String>
     */
    private List<String> getPermissions(RequestInfo userInfo) {
        List<String> allPermissions;
        switch (userInfo.getAppSourceType()) {
            case KS:
                allPermissions = getKsPermissions();
                break;
            case GOVERNMENT:
                allPermissions = getGovernmentPermissions(userInfo);
                break;
            default:
                allPermissions = getEnginePermissions(userInfo);
                break;
        }
        return allPermissions;
    }

    /**
     * 获取engine权限
     *
     * @param userInfo RequestInfo
     * @return List<String>
     */
    private List<String> getEnginePermissions(RequestInfo userInfo) {
        List<String> allPermissions;
        List<Menu> menus = queryMenus(userInfo);

        List<Permission> resources = Optional.ofNullable(AuthUtils.getResourceList(userInfo.getTenantId(), userInfo.getUserId(), "2").getData())
                .orElse(Collections.emptyList());

        allPermissions = menus.stream()
                .map(Menu::getUrlAddress)
                .collect(Collectors.toList());

        resources.forEach(r -> allPermissions.add(r.getPermissionCode()));
        return allPermissions;
    }

    /**
     * 兼容树形结构的菜单配置
     *
     * @param userInfo
     * @return
     */
    private List<Menu> queryMenus(RequestInfo userInfo) {
        List<Menu> menus = Optional.ofNullable(AuthUtils.getMenuList(userInfo.getTenantId(), userInfo.getUserId()).getData())
                .orElse(Collections.emptyList());
        if (CollectionUtils.isNotEmpty(menus)) {
            List<Menu> result = new ArrayList<>();
            bfs(menus, result);
            return result;
        } else {
            return menus;
        }
    }

    /**
     * 获取government权限
     *
     * @param userInfo RequestInfo
     * @return List<String>
     */
    private List<String> getGovernmentPermissions(RequestInfo userInfo) {
        List<String> allPermissions;
        log.debug("government 获取菜单和资源权限");
        AppOwnerRequest getMenusRequest = new AppOwnerRequest();
        getMenusRequest.setUserId(userInfo.getUserId());
        getMenusRequest.setCorpCode(userInfo.getTenantId());
        Result<List<String>> menuResult = governmentAuthClient.getMenuList(getMenusRequest);
        if (Boolean.FALSE.equals(menuResult.isSuccess()) || menuResult.getData() == null) {
            log.error("government 获取菜单权限失败,result:{}", menuResult);
            return Collections.emptyList();
        }
        allPermissions = menuResult.getData();
        Result<List<String>> resourceResult = governmentAuthClient.getResourceList(getMenusRequest);
        if (Boolean.FALSE.equals(resourceResult.isSuccess()) || resourceResult.getData() == null) {
            log.error("government 获取资源权限失败,result:{}", resourceResult);
            return Collections.emptyList();
        }
        allPermissions.addAll(resourceResult.getData());
        return allPermissions;
    }

    /**
     * 获取ks权限(菜单和资源)
     *
     * @return List<String>
     */
    private List<String> getKsPermissions() {
        List<String> allPermissions;
        log.debug("ks 获取菜单和资源权限");
        Result<List<Menu>> menuResult = ksAuthClient.getMenuList();
        if (Boolean.FALSE.equals(menuResult.isSuccess()) || menuResult.getData() == null) {
            log.error("ks 获取菜单权限失败,{}", menuResult);
            return Collections.emptyList();
        }
        List<Menu> menus = coverMenus(menuResult.getData());
        allPermissions = menus.stream().map(Menu::getUrlAddress).collect(Collectors.toList());
        Result<List<Permission>> resourceResult = ksAuthClient.getResourceList();
        if (Boolean.FALSE.equals(resourceResult.isSuccess()) || resourceResult.getData() == null) {
            log.error("ks 获取资源权限失败 {}", resourceResult);
            return Collections.emptyList();
        }
        List<String> permissionCode = resourceResult.getData().stream().map(Permission::getPermissionCode).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(permissionCode)) {
            allPermissions.addAll(permissionCode);
        }
        return allPermissions;
    }

    /**
     * 兼容树形结构的菜单配置
     *
     * @param menus List<Menu>
     * @return List<Menu>
     */
    private List<Menu> coverMenus(List<Menu> menus) {
        if (CollectionUtils.isNotEmpty(menus)) {
            List<Menu> result = new ArrayList<>();
            bfs(menus, result);
            return result;
        } else {
            return menus;
        }
    }

    private void bfs(List<Menu> menus, List<Menu> result) {
        if (CollectionUtils.isNotEmpty(menus)) {
            result.addAll(menus);
            for (Menu menu : menus) {
                bfs(menu.getChildren(), result);
            }
        }
    }


}
