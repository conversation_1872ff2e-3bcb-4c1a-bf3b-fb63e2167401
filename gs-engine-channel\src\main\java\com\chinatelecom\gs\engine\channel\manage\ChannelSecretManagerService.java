package com.chinatelecom.gs.engine.channel.manage;

import com.chinatelecom.gs.engine.channel.service.dto.ChannelApiSecretDTO;
import com.chinatelecom.gs.engine.robot.sdk.config.enums.ApiSecretType;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.page.Page;

/**
 * @Description: TelechatSecretManagerService
 * @Author: qingx
 * @Time: 2023/08/29
 * @Version: 1.0
 */
public interface ChannelSecretManagerService {

    /**
     * 分页查询密钥列表
     *
     * @param appId    应用id
     * @param pageNo   页码
     * @param pageSize 页大小
     * @return IPage<TelechatApiSecretDTO>
     */
    Page<ChannelApiSecretDTO> list(String appId, ApiSecretType apiSecretType, Integer pageNo, Integer pageSize);

    /**
     * 创建API密钥
     *
     * @param appId      应用id
     * @param secretName 密钥名称
     * @return true-成功, false-失败
     */
    boolean create(String appId, String channelId, String secretName, ApiSecretType apiSecretType);

    /**
     * 修改API密钥
     *
     * @param secretName 密钥名称
     * @return true-成功, false-失败
     */
    boolean modify(String appId, String secretId, String secretName);

    /**
     * 删除API密钥
     *
     * @param secretId 密钥id
     * @return true-成功, false-失败
     */
    boolean remove(String appId, String secretId);

    /**
     * 通过密钥id查询密钥
     *
     * @param secretId 密钥id
     * @return 密钥内容
     */
    ChannelApiSecretDTO getSecretWithSecretId(String secretId);

    ChannelApiSecretDTO getSecretWithSecret(String secret);

    ChannelApiSecretDTO getSecretWithChannelId(String channelId);

    boolean removeSecretWithChannelId(String channelId);

    boolean isSecretExist(String appId, ApiSecretType apiSecretType);

    String regenerate(String appId, String secretId);
}
