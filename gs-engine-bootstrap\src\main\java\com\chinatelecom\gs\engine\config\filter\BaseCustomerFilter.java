package com.chinatelecom.gs.engine.config.filter;

import com.chinatelecom.gs.engine.common.config.property.GsGlobalConfig;
import com.chinatelecom.gs.engine.common.utils.InterceptorUtils;
import com.chinatelecom.gs.engine.config.filter.interceptor.*;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024年12月31日
 */
@Component
@Slf4j
@Order(1)
public class BaseCustomerFilter implements Filter {

    private BaseInterceptorRegistry interceptorRegistry = new BaseInterceptorRegistry();

    private AntPathMatcher antPathMatcher = new AntPathMatcher();

    @Resource
    private GsGlobalConfig gsGlobalConfig;

    @Resource
    private WebContextInterceptor webContextInterceptor;

    @Resource
    private RpcContextInterceptor rpcContextInterceptor;

    @Resource
    private InitInterceptor initInterceptor;

    @Resource
    private OpenApiContextInterceptor openApiContextInterceptor;

    @Resource
    private OpenApiOpenContextInterceptor openApiOpenContextInterceptor;

    @Resource
    private ApiContextInterceptor apiContextInterceptor;

    @Resource
    private CsrfInterceptor csrfInterceptor;

    @PostConstruct
    public void init() {
        interceptorRegistry.addInterceptor(webContextInterceptor).addPathPatterns("/**/web/**").order(2);
        interceptorRegistry.addInterceptor(rpcContextInterceptor).addPathPatterns("/**/rpc/**").excludePathPatterns("/bot/asr/callback/taskInfo")
                .excludePathPatterns(gsGlobalConfig.getSystem().getRpcExcludeUrls().split(",")).order(3);
        interceptorRegistry.addInterceptor(apiContextInterceptor).addPathPatterns("/**/channel/openapi/**")
                .excludePathPatterns(gsGlobalConfig.getSystem().getApiExcludeUrls().split(",")).order(4);
        interceptorRegistry.addInterceptor(openApiContextInterceptor).addPathPatterns("/**/openapi/**")
                .excludePathPatterns(gsGlobalConfig.getSystem().getOpenApiExcludeUrls().split(",")).order(5);
        interceptorRegistry.addInterceptor(openApiOpenContextInterceptor).addPathPatterns("/**/openapi/open/**", "/**/openapi/agent/v1/chat/completions").order(6);
        interceptorRegistry.addInterceptor(initInterceptor).addPathPatterns("/**/web/**").order(6);
        interceptorRegistry.addInterceptor(csrfInterceptor).addPathPatterns("/**/web/**").order(7);
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        Exception exception = null;
        try {
            boolean preResult = preHandle(request, response);
            if (preResult) {
                chain.doFilter(request, response);
                postHandle(request, response);
            }
        } catch (Exception e) {
            if (e.getCause() instanceof BizException && "C0005".equals(((BizException) e.getCause()).getCode())) {
                log.error("当前问答触发安全围栏，请换个说法试试！感谢您的理解！", e);
                InterceptorUtils.writeError((HttpServletResponse) response, "C0005", e.getCause().getMessage());
            } else {
                log.error("自定义拦截器执行异常", e);
                exception = e;
                InterceptorUtils.writeError((HttpServletResponse) response, "A0022", "基础拦截器执行异常");
            }
        } finally {
            try {
                afterCompletion(request, response, exception);
            } catch (Exception e) {
                log.error("后置处理异常", e);
            }
        }
    }


    private boolean preHandle(ServletRequest request, ServletResponse response) throws Exception {
        List<BaseInterceptorRegistration> registrations = interceptorRegistry.getRegistrations();
        for (int i = 0; i < registrations.size(); i++) {
            BaseInterceptorRegistration registration = registrations.get(i);
            boolean isFilter = matchingPath(request, registration);
            if (isFilter) {
                boolean interceptor = registration.getInterceptor().preHandle(request, response);
                if (!interceptor) {
                    log.warn("自定义拦截不通过：{}", registration.getInterceptor().getClass().getName());
                    return false;
                }
            }
        }
        return true;
    }

    private boolean matchingPath(ServletRequest request, BaseInterceptorRegistration registration) {
        String servletPath = ((HttpServletRequest) request).getServletPath();
        List<String> excludeUrls = registration.getExcludePatterns();
        if (CollectionUtils.isNotEmpty(excludeUrls)){
            for (String excludeUrl : excludeUrls) {
                if (antPathMatcher.match(excludeUrl, servletPath)){
                    return false;
                }
            }
        }

        List<String> includePatterns = registration.getIncludePatterns();
        if (CollectionUtils.isNotEmpty(includePatterns)){
            for (String includePattern : includePatterns) {
                if (antPathMatcher.match(includePattern, servletPath)){
                    return true;
                }
            }
        }
        return false;
    }

    private void afterCompletion(ServletRequest request, ServletResponse response, Exception exception) throws Exception {
        List<BaseInterceptorRegistration> registrations = interceptorRegistry.getRegistrations();
        for (int i = 0; i < registrations.size(); i++) {
            BaseInterceptorRegistration registration = registrations.get(i);
            boolean isFilter = matchingPath(request, registration);
            if (isFilter) {
                registration.getInterceptor().afterCompletion(request, response, exception);
            }
        }
    }

    private void postHandle(ServletRequest request, ServletResponse response) throws Exception {
        List<BaseInterceptorRegistration> registrations = interceptorRegistry.getRegistrations();
        for (int i = 0; i < registrations.size(); i++) {
            BaseInterceptorRegistration registration = registrations.get(i);
            boolean isFilter = matchingPath(request, registration);
            if (isFilter) {
                registration.getInterceptor().postHandle(request, response);
            }
        }
    }

}
