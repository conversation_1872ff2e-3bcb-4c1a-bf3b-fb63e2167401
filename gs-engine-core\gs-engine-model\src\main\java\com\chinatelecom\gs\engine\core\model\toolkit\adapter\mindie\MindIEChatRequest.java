package com.chinatelecom.gs.engine.core.model.toolkit.adapter.mindie;
import com.chinatelecom.gs.engine.core.sdk.vo.llm.BaseLLMRequest;
import com.chinatelecom.gs.engine.core.sdk.vo.llm.Tool;
import lombok.Data;

import java.util.List;

@Data
public class MindIEChatRequest implements BaseLLMRequest {

    private String model;

    private List<MindIEMessage> messages;

    private boolean stream = true;

    /**
     * qwen 2.5 top_p
     * 1.5  private Integer topk = 5;
     */
    private Float top_p = 0.5f;

    private double temperature = 0.3;

    private double repetition_penalty = 1.03;

    private List<Tool> tools;

    /**
     * 获取输入内容
     *
     * @return
     */
    @Override
    public String inputContent() {
        return null;
    }
}
