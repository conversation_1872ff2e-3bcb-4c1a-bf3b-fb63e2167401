package com.chinatelecom.gs.engine.core.model.toolkit.adapter.azure;

import com.azure.ai.openai.models.*;
import com.chinatelecom.gs.engine.core.sdk.vo.llm.BaseLLMRequest;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

@Data
public class GptAzureRequest implements BaseLLMRequest {
    private double temperature =0.7;

    private double top_p =1;

    private boolean stream;

    private String model;

    private List<ChatRequestMessage> messages;

    /**
     * 获取输入内容
     *
     * @return
     */
    @Override
    public String inputContent() {
        StringBuilder inputBuilder = new StringBuilder();
        if(CollectionUtils.isNotEmpty(messages)){
            for(ChatRequestMessage message : messages){
                if(message instanceof ChatRequestSystemMessage systemMessage){
                    inputBuilder.append(systemMessage.getContent());
                }else if(message instanceof ChatRequestUserMessage){
                    //todo 获取不到内容
                }else if(message instanceof ChatRequestAssistantMessage assistantMessage){
                    inputBuilder.append(assistantMessage.getContent());
                }else if(message instanceof ChatRequestToolMessage toolMessage){
                    inputBuilder.append(toolMessage.getContent());
                }else if(message instanceof ChatRequestFunctionMessage functionMessage){
                    inputBuilder.append(functionMessage.getContent());
                }
            }
        }
        return inputBuilder.toString();
    }
}
