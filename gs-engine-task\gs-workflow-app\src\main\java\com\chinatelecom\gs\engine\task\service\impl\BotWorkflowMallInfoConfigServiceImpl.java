package com.chinatelecom.gs.engine.task.service.impl;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.chinatelecom.cloud.platform.client.rpc.PlatformUser;
import com.chinatelecom.cloud.platform.client.util.SsoUtil;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.context.RequestInfo;
import com.chinatelecom.gs.engine.common.utils.IPageUtils;
import com.chinatelecom.gs.engine.kms.dto.TagDTO;
import com.chinatelecom.gs.engine.kms.repository.TagAppRepository;
import com.chinatelecom.gs.engine.kms.repository.TagRelationRepository;
import com.chinatelecom.gs.engine.kms.sdk.enums.TargetType;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.entity.TagEntity;
import com.chinatelecom.gs.engine.task.sdk.enums.WorkflowTypeEnum;
import com.chinatelecom.gs.privilege.common.dto.GrantObjectDTO;
import com.chinatelecom.gs.privilege.common.enums.PrivilegeEnum;
import com.chinatelecom.gs.privilege.common.enums.ResourceTypeEnum;
import com.chinatelecom.gs.privilege.util.PrivilegeUtil;
import com.chinatelecom.gs.workflow.core.dao.service.IBotWorkflowMallService;
import com.chinatelecom.gs.workflow.core.dao.service.IBotWorkflowNodeService;
import com.chinatelecom.gs.workflow.core.dao.service.IBotWorkflowService;
import com.chinatelecom.gs.workflow.core.dao.service.IWorkflowVersionService;
import com.chinatelecom.gs.workflow.core.domain.param.BotWorkflowDetailRsp;
import com.chinatelecom.gs.workflow.core.domain.param.BotWorkflowMallRsp;
import com.chinatelecom.gs.workflow.core.domain.param.WorkflowMallPageRequest;
import com.chinatelecom.gs.workflow.core.domain.param.WorkflowMallShelveRequest;
import com.chinatelecom.gs.workflow.core.domain.po.BotWorkflowMallInfoPO;
import com.chinatelecom.gs.workflow.core.domain.po.BotWorkflowPO;
import com.chinatelecom.gs.workflow.core.service.BotWorkflowConfigService;
import com.chinatelecom.gs.workflow.core.service.BotWorkflowMallInfoConfigService;
import com.chinatelelcom.gs.engine.sdk.common.Page;
import com.chinatelelcom.gs.engine.sdk.common.PageImpl;
import com.chinatelelcom.gs.engine.sdk.common.enums.AppSourceType;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RefreshScope
public class BotWorkflowMallInfoConfigServiceImpl implements BotWorkflowMallInfoConfigService {

    @Autowired
    private IBotWorkflowMallService botWorkflowMallService;

    @Autowired
    private IBotWorkflowService iBotWorkflowService;

    @Autowired
    private IWorkflowVersionService workflowVersionService;

    @Autowired
    private TagAppRepository tagAppRepository;

    @Autowired
    private TagRelationRepository tagRelationRepository;

    @Autowired
    private BotWorkflowConfigService botWorkflowConfigService;

    @Autowired
    private IBotWorkflowNodeService botWorkflowNodeService;

    @Autowired
    private IBotWorkflowService IBotWorkflowService;

    @Autowired
    private IWorkflowVersionService IWorkflowVersionService;

    @Autowired
    private PrivilegeUtil privilegeUtil;

    @Value("${platform.super.tenant:SuperAis}")
    private String superTenant;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean shelve(WorkflowMallShelveRequest request) {
        // 检查是否为管理员
        PlatformUser user = SsoUtil.get();
        if (!Boolean.TRUE.equals(user.getIsAdmin())) {
            throw new BizException("A0087", "非管理员不能上架");
        }

        RequestInfo requestInfo = RequestContext.get();
        checkResourceAuth(request.getWorkflowId());

        // 查询业务流是否存在, 上架的是已经发布的业务流
        Long publishVersion = workflowVersionService.getPublishVersion(request.getWorkflowId());
        if (publishVersion == null) {
            throw new BizException("AC007", "业务流不存在");
        }
        BotWorkflowPO botWorkflowPO = iBotWorkflowService.queryByIdAndVersion(request.getWorkflowId(), publishVersion, requestInfo.getAppCode(), requestInfo.getAppSourceType().name());
        if (botWorkflowPO == null) {
            throw new BizException("AC007", "业务流不存在");
        }
        // 查询是否已经上架过, 已经上架的不需重复上架
        BotWorkflowMallInfoPO botWorkflowMallInfoPO = botWorkflowMallService.queryByWorkflowId(request.getWorkflowId());
        if (botWorkflowMallInfoPO != null) {
            throw new BizException("AC053", "业务流已经上架");
        }
        if (WorkflowTypeEnum.GUIDE_FLOW.getCode().equals(botWorkflowPO.getWorkflowType()) && AppSourceType.KS.equals(RequestContext.getAppSourceType())) {
            throw new BizException("AC061", "引导流程不能上架");
        }
        BotWorkflowMallInfoPO botWorkflowMallPO = new BotWorkflowMallInfoPO();
        botWorkflowMallPO.setWorkflowId(botWorkflowPO.getWorkflowId());
        botWorkflowMallPO.setWorkflowCnName(botWorkflowPO.getWorkflowCnName());
        botWorkflowMallPO.setWorkflowName(botWorkflowPO.getWorkflowName());
        botWorkflowMallPO.setCreateId(requestInfo.getUserId());
        botWorkflowMallPO.setCreateName(requestInfo.getUserName());
        botWorkflowMallPO.setUpdateId(requestInfo.getUserId());
        botWorkflowMallPO.setUpdateName(requestInfo.getUserName());
        botWorkflowMallPO.setCreateTime(LocalDateTime.now());
        botWorkflowMallPO.setUpdateTime(LocalDateTime.now());
        botWorkflowMallPO.setTenantId(requestInfo.getTenantId());
        botWorkflowMallPO.setAppCode(requestInfo.getAppCode());
        botWorkflowMallPO.setSource(superTenant.equals(RequestContext.getAppCode()) ? 1 : 0);
        botWorkflowMallPO.setSourceSystem(requestInfo.getAppSourceType());
        botWorkflowMallService.save(botWorkflowMallPO);

        // 处理标签 先查询出该插件所有的标签, 然后再绑定到插件商店的标签中
        Set<String> tagCodeList = tagRelationRepository.selectTagCodeByTarget(TargetType.WORKFLOW, botWorkflowPO.getWorkflowId());
        handleTag(botWorkflowPO.getWorkflowId(), new ArrayList<>(tagCodeList));

        return Boolean.TRUE;
    }

    private void handleTag(String workflowId, List<String> tagCodeList) {
        if (CollectionUtils.isNotEmpty(tagCodeList)) {
            tagCodeList.forEach(tagCode -> {
                TagDTO tagDTO = tagAppRepository.selectByCode(tagCode);
                if (tagDTO == null) {
                    throw new BizException("AA083", "标签不存在");
                }
            });
        }
        tagRelationRepository.saveTagRelation(TargetType.WORKFLOW_MALL, workflowId, tagCodeList);
    }

    @Override
    public Boolean unshelve(WorkflowMallShelveRequest request) {
        // 检查是否为管理员
        PlatformUser user = SsoUtil.get();
        if (!Boolean.TRUE.equals(user.getIsAdmin())) {
            throw new BizException("A0087", "非管理员不能下架");
        }

        RequestInfo requestInfo = RequestContext.get();
        checkResourceAuth(request.getWorkflowId());
        // 查询是否已经下架, 只能本人下架
        BotWorkflowMallInfoPO botWorkflowMallInfoPO = botWorkflowMallService.queryByWorkflowIdAndUserInfo(request.getWorkflowId(), requestInfo.getUserId(), requestInfo.getAppSourceType().name());
        if (botWorkflowMallInfoPO == null) {
            throw new BizException("AC054", "业务流不存在或者已经下架");
        }

        botWorkflowMallService.remove(botWorkflowMallInfoPO);

        handleTag(request.getWorkflowId(), Lists.newArrayList());

        return Boolean.TRUE;
    }

    @Override
    public Page<BotWorkflowMallRsp> page(WorkflowMallPageRequest request) {
        if (AppSourceType.KS.equals(RequestContext.getAppSourceType())) {
            request.setWorkflowTypeList(WorkflowTypeEnum.WORKFLOW.getCode());
        }
        List<String> workflowIdList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(request.getTagCodeList())) {
            Set<String> allSubTagCodes = tagAppRepository.fetchAllSubTag(request.getTagCodeList());
            allSubTagCodes.addAll(request.getTagCodeList());
            workflowIdList = tagRelationRepository.findByTagCodes(TargetType.WORKFLOW_MALL, allSubTagCodes);
            if (CollectionUtils.isEmpty(workflowIdList)) {
                return new PageImpl<>(request.getPageNo(), request.getPageSize(), 0, 0, Collections.emptyList());
            }
        }
        request.setWorkflowIdList(workflowIdList);
        Page<BotWorkflowMallInfoPO> botWorkflowMallInfoPOList = botWorkflowMallService.queryByPage(request);
        return IPageUtils.convert(botWorkflowMallInfoPOList, this::convert);
    }

    @Override
    public BotWorkflowDetailRsp detail(String workflowId) {
        BotWorkflowMallInfoPO botWorkflowMallInfoPO = botWorkflowMallService.queryByWorkflowId(workflowId);
        if (botWorkflowMallInfoPO == null) {
            return null;
        }

        return botWorkflowConfigService.botWorkflowDetail(botWorkflowMallInfoPO.getWorkflowId(), false);
    }

    private BotWorkflowMallRsp convert(BotWorkflowMallInfoPO botWorkflowMallInfoPO) {
        BotWorkflowMallRsp botWorkflowRsp = new BotWorkflowMallRsp();
        //查询业务流信息
        Long publishVersion = IWorkflowVersionService.getPublishVersion(botWorkflowMallInfoPO.getWorkflowId());
        if (publishVersion == null) {
            return botWorkflowRsp;
        }
        BotWorkflowPO workflow = IBotWorkflowService.queryByIdAndVersion(botWorkflowMallInfoPO.getWorkflowId(), publishVersion, null, null);
        if (workflow == null) {
            return botWorkflowRsp;
        }
        BeanUtils.copyProperties(workflow, botWorkflowRsp);
        botWorkflowRsp.setTagList(getTag(botWorkflowRsp.getWorkflowId()));

        botWorkflowConfigService.fillParams(botWorkflowRsp);
        botWorkflowRsp.setSource(botWorkflowMallInfoPO.getSource());
        return botWorkflowRsp;
    }

    public List<TagEntity> getTag(String workflowId) {
        // 获取标签信息
        List<TagEntity> tagEntityList = Lists.newArrayList();
        Set<String> tagCodeList = tagRelationRepository.selectTagCodeByTarget(TargetType.WORKFLOW_MALL, workflowId);
        if (CollectionUtils.isEmpty(tagCodeList)) {
            return tagEntityList;
        }
        Map<String, String> codeNameMap = tagAppRepository.queryParentNameByCodes(tagCodeList);
        tagCodeList.forEach(tagCode -> {
            TagDTO tagDTO = tagAppRepository.selectByCode(tagCode);
            if (tagDTO == null) {
                return;
            }
            TagEntity tagEntity = new TagEntity();
            tagEntity.setTagCode(tagDTO.getCode());
            tagEntity.setTagName(tagDTO.getName());
            if (StringUtils.isNotEmpty(tagDTO.getPath())) {
                String path = tagDTO.getPath();
                String[] tagPaths = path.split("/");
                tagEntity.setPath(Arrays.stream(tagPaths).filter(StringUtils::isNotBlank).
                        map(codeNameMap::get).collect(Collectors.joining("/")));
            }
            tagEntityList.add(tagEntity);
        });
        return tagEntityList;
    }

    private void checkResourceAuth(String workflowId) {
        GrantObjectDTO grantObjectDTO = privilegeUtil.hasPrivilege(workflowId, ResourceTypeEnum.WORKFLOW);
        if (grantObjectDTO == null) {
            throw new BizException("CA007", "没有权限访问该资源");
        }
        boolean checkResult = grantObjectDTO.getPrivilege() != null && !grantObjectDTO.getPrivilege().equals(PrivilegeEnum.no_permission);
        if (!checkResult) {
            throw new BizException("CA007", "没有权限访问该资源");
        }
    }
}
