package com.chinatelecom.gs.engine.channel.openai.service;

import com.chinatelecom.gs.engine.channel.openai.dto.ChatCompletionRequest;
import com.chinatelecom.gs.engine.channel.openai.dto.ChatCompletionResponse;
import com.chinatelecom.gs.engine.channel.openai.dto.ChatCompletionStreamResponse;
import com.chinatelecom.gs.engine.channel.openai.dto.ChatMessage;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.FinalMessageResponse;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.MessageRequest;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.SseMessageResponse;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.BotAnswer;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.reason.Reason;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.enums.SendMessageTypeEnum;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;

class OpenAICompatServiceTest {

    private OpenAICompatService service;

    @BeforeEach
    void setUp() {
        service = new OpenAICompatService();
    }

    @Test
    void testConvertToMessageRequest_withUserMessage() {
        ChatCompletionRequest request = new ChatCompletionRequest();
        request.setModel("gpt-3.5");
        request.setUser("testUser");
        request.setSessionId("session123");
        request.setTest(1);

        ChatMessage userMessage = new ChatMessage();
        userMessage.setRole("user");
        userMessage.setContent("Hello World");

        request.setMessages(Collections.singletonList(userMessage));

        MessageRequest result = service.convertToMessageRequest(request);

        assertNotNull(result);
        assertEquals("gpt-3.5", result.getAgentCode());
        assertEquals("session123", result.getSessionId());
        assertNotNull(result.getMessageId());
        assertEquals("testUser", result.getUserId());
        assertTrue(result.getTest().equals(1));
        assertEquals("Hello World", result.getContent());
    }

    @Test
    void testConvertToOpenAIResponse_withAnswer() {
        FinalMessageResponse response = new FinalMessageResponse();
        BotAnswer answer = new BotAnswer();
        answer.setContent("This is the answer.");
        Reason reasoningContent = new Reason();
        reasoningContent.setReasoningContent("thinking...");
        reasoningContent.setCost("100ms");
        answer.setReasoning(reasoningContent);
        response.setAnswers(Collections.singletonList(answer));

        ChatCompletionRequest request = new ChatCompletionRequest();
        request.setModel("gpt-3.5");

        ChatCompletionResponse result = service.convertToOpenAIResponse(response, request);

        assertNotNull(result);
        assertNotNull(result.getId());
        assertTrue(result.getId().startsWith("chatcmpl-"));
        assertEquals("gpt-3.5", result.getModel());
        assertEquals(1, result.getChoices().size());
        assertEquals("assistant", result.getChoices().get(0).getMessage().getRole());
        assertEquals("This is the answer.", result.getChoices().get(0).getMessage().getContent());
        assertEquals("thinking...", result.getChoices().get(0).getMessage().getReasoning_content());
    }

    @Test
    void testConvertToOpenAIStreamResponse_withAddEvent() {
        SseMessageResponse sseResponse = new SseMessageResponse();
        BotAnswer answer = new BotAnswer();
        answer.setContent("Streaming...");
        Reason reasoningContent = new Reason();
        reasoningContent.setReasoningContent("stream thinking");
        reasoningContent.setCost("100ms");
        answer.setReasoning(reasoningContent);
        sseResponse.setAnswer(answer);
        sseResponse.setEventType(SendMessageTypeEnum.ADD);

        ChatCompletionRequest request = new ChatCompletionRequest();
        request.setModel("gpt-3.5");

        ChatCompletionStreamResponse result = service.convertToOpenAIStreamResponse(sseResponse, request);

        assertNotNull(result);
        assertNotNull(result.getId());
        assertTrue(result.getId().startsWith("chatcmpl-"));
        assertEquals("gpt-3.5", result.getModel());
        assertEquals(1, result.getChoices().size());
        assertEquals("assistant", result.getChoices().get(0).getDelta().getRole());
        assertEquals("Streaming...", result.getChoices().get(0).getDelta().getContent());
        assertEquals("stream thinking", result.getChoices().get(0).getDelta().getReasoning_content());
    }
}
