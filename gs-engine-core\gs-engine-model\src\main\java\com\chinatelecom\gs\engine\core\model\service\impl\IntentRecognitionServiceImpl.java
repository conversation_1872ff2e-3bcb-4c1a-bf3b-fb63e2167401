package com.chinatelecom.gs.engine.core.model.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.utils.IdGenerator;
import com.chinatelecom.gs.engine.common.utils.JsonUtils;
import com.chinatelecom.gs.engine.common.utils.LlmUtil;
import com.chinatelecom.gs.engine.core.model.entity.param.ModelInfo;
import com.chinatelecom.gs.engine.core.model.entity.param.ModelRecognitionParam;
import com.chinatelecom.gs.engine.core.model.entity.param.RecognitionRequest;
import com.chinatelecom.gs.engine.core.model.entity.vo.ModelRecognitionResponse;
import com.chinatelecom.gs.engine.core.model.service.AiModelAppService;
import com.chinatelecom.gs.engine.core.model.service.IntentRecognitionService;
import com.chinatelecom.gs.engine.core.model.service.ModelRecognitionService;
import com.chinatelecom.gs.engine.core.model.toolkit.StreamingChatLanguageModel;
import com.chinatelecom.gs.engine.core.model.toolkit.core.dto.*;
import com.chinatelecom.gs.engine.core.model.toolkit.core.service.CommonRankModel;
import com.chinatelecom.gs.engine.core.sdk.enums.IntentRecallTypeEnum;
import com.chinatelecom.gs.engine.core.sdk.request.CodeRequest;
import com.chinatelecom.gs.engine.core.sdk.request.IntentQueryRequest;
import com.chinatelecom.gs.engine.core.sdk.rpc.IntentManagerRpcApi;
import com.chinatelecom.gs.engine.core.sdk.vo.*;
import com.chinatelecom.gs.engine.kms.sdk.api.PromptAppServiceApi;
import com.chinatelecom.gs.engine.kms.sdk.vo.prompt.PromptTemplateVO;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 算法模型意图表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-05-09
 */
@Service
@Slf4j
public class IntentRecognitionServiceImpl implements IntentRecognitionService {
    @Resource
    private IntentManagerRpcApi intentManagerRpcApi;

    @Resource
    private ModelRecognitionService modelRecognitionService;

    @Resource
    private StreamingChatLanguageModel streamingChatLanguageModel;

    @Resource
    private PromptAppServiceApi promptAppServiceApi;

    @Resource
    private AiModelAppService aiModelAppService;

    @Resource
    private CommonRankModel commonRankModel;

    @Value("${platform.super.tenant:SuperAis}")
    private String superTenant;

    private static final String INTENT_RECOGNITION_PROMPT_TEMPLATE = "INTENT_RECOGNITION_PROMPT_TEMPLATE";

    private static final Map<String, Pattern> PATTERN_CACHE = new ConcurrentHashMap<>();

    /**
     * 意图识别
     *
     * @param param IntentionRecognitionParam
     * @return IntentionRecognitionResponse
     */
    @Override
    public List<IntentionRecognitionResponse> intentRecognitionList(IntentionRecognitionParam param) {
        log.info("开始意图识别，参数：{}", JSON.toJSONString(param));
        List<IntentionRecognitionResponse> intentionRecognitionResponses = Collections.synchronizedList(new ArrayList<>());
        try {
            CodeRequest request = buildQueryIntentListParam(param);
            Result<List<AgentIntentResponse>> listResult = intentManagerRpcApi.queryList(request);

            if (Boolean.TRUE.equals(listResult.isSuccess()) && CollUtil.isNotEmpty(listResult.getData())) {
                llmRecall(param, listResult, intentionRecognitionResponses);
            }

            Result<AgentIntentConfigResponse> intentConfigResponseResult = intentManagerRpcApi.getIntentConfig(param.getIsSystemAgent() ? superTenant : RequestContext.getAppCode());
            if (Boolean.FALSE.equals(intentConfigResponseResult.isSuccess()) || Objects.isNull(intentConfigResponseResult.getData())) {
                log.warn("意图高级设置配置查询失败");
                return intentionRecognitionResponses;
            }
            AgentIntentConfigResponse intentConfig = intentConfigResponseResult.getData();

            if (Boolean.TRUE.equals(listResult.isSuccess()) && CollUtil.isNotEmpty(listResult.getData())) {
                List<AgentIntentResponse> intents = listResult.getData().stream().filter(AgentIntentResponse::getOn).collect(Collectors.toList());
                regularRecall(param, intents, intentionRecognitionResponses);
                classifierRecall(param, intents, intentionRecognitionResponses);
            }
            exampleRecall(param, intentConfig, listResult, intentionRecognitionResponses);
        } catch (Exception e) {
            log.error("意图识别过程中发生异常", e);
        }
        return intentionRecognitionResponses;
    }

    /**
     * 按优先级召回一个意图
     *
     * @param param IntentionRecognitionParam
     * @return IntentionRecognitionResponse
     */
    @Override
    public IntentionRecognitionResponse intentRecognition(IntentionRecognitionParam param) {
        List<IntentionRecognitionResponse> intentionRecognitionResponses = intentRecognitionList(param);
        return chooseRefIntent(intentionRecognitionResponses);
    }


    /**
     * 按优先级选择一个意图
     * 规则>分类模型>相似问>大模型
     *
     * @param intentionRecognitionResponses List<IntentionRecognitionResponse>
     * @return IntentionRecognitionResponse
     */
    private IntentionRecognitionResponse chooseRefIntent(List<IntentionRecognitionResponse> intentionRecognitionResponses) {
        if (CollUtil.isEmpty(intentionRecognitionResponses)) {
            return null;
        }
        List<IntentionRecognitionResponse> regularIntentList = intentionRecognitionResponses.stream().filter(f -> f.getRecallType().equals(IntentRecallTypeEnum.REGULAR_MATCHING)).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(regularIntentList)) {
            return regularIntentList.get(0);
        }
        regularIntentList = intentionRecognitionResponses.stream().filter(f -> f.getRecallType().equals(IntentRecallTypeEnum.CLASSIFIER_RECOGNITION)).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(regularIntentList)) {
            return regularIntentList.get(0);
        }
        regularIntentList = intentionRecognitionResponses.stream().filter(f -> f.getRecallType().equals(IntentRecallTypeEnum.EXAMPLE_SENTENCE_RECALL)).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(regularIntentList)) {
            return regularIntentList.get(0);
        }
        regularIntentList = intentionRecognitionResponses.stream().filter(f -> f.getRecallType().equals(IntentRecallTypeEnum.LLM_RECOGNITION)).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(regularIntentList)) {
            return regularIntentList.get(0);
        }
        return null;
    }

    private List<AgentIntentResponse> getOnIntentList(Result<List<AgentIntentResponse>> listResult, IntentionRecognitionParam param) {
        List<AgentIntentResponse> intents = listResult.getData().stream().filter(o -> o.getOn() && o.getEnableLlmRecall()).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(param.getClassificatIntents())) {
            List<AgentIntentResponse> inputIntents = param.getClassificatIntents().stream().map(o -> {
                AgentIntentResponse agentIntentResponse = new AgentIntentResponse();
                agentIntentResponse.setCode(o.getClassificationId().toString());
                agentIntentResponse.setName(o.getName());
                agentIntentResponse.setDescription(o.getName());
                return agentIntentResponse;
            }).collect(Collectors.toList());
            if (CollUtil.isEmpty(intents)) {
                intents = Lists.newArrayList();
            }
            if (CollUtil.isNotEmpty(inputIntents)) {
                intents.addAll(inputIntents);
            }
        }
        log.info("开启大模型召回的意图：{}", JSON.toJSONString(intents));
        return intents;
    }

    private List<QueryRankRequest.CandidateQuery> getQueryIntentList(IntentSearchVO<IntentIndexData> queryResponse) {

        return queryResponse.getItems().stream().map(o -> {
            QueryRankRequest.CandidateQuery intent = new QueryRankRequest.CandidateQuery();
            IntentIndexData source = o.getSource();
            intent.setId(source.getIntentCode());
            intent.setScore(o.getScore());
            intent.setSimilarContent(source.getContent());
            return intent;
        }).collect(Collectors.toList());
    }

    /**
     * build query intent list request param
     *
     * @param param IntentionRecognitionParam
     * @return CodeRequest
     */
    private CodeRequest buildQueryIntentListParam(IntentionRecognitionParam param) {
        CodeRequest request = new CodeRequest();
        request.setCodes(param.getIntentList());
        return request;
    }

    /**
     * 分类模型召回
     *
     * @param param                         IntentionRecognitionParam
     * @param onIntentList                  List<AgentIntentResponse>
     * @param intentionRecognitionResponses List<IntentionRecognitionResponse>
     */
    private void classifierRecall(IntentionRecognitionParam param, List<AgentIntentResponse> onIntentList, List<IntentionRecognitionResponse> intentionRecognitionResponses) {
        HashMap<String, List<AgentIntentResponse>> intentMap = new HashMap<>();
        for (AgentIntentResponse agentIntentResponse : onIntentList) {
            if (CharSequenceUtil.isBlank(agentIntentResponse.getModelCode())) {
                continue;
            }
            intentMap.computeIfAbsent(agentIntentResponse.getModelCode(), k -> new ArrayList<>()).add(agentIntentResponse);
        }
        if (CollUtil.isEmpty(intentMap)) {
            return;
        }
        intentMap.forEach((k, v) -> {
            try {
                ModelPageListParam modelPageListParam = aiModelAppService.get(k, false);
                if (Objects.nonNull(modelPageListParam)) {
                    ModelRecognitionParam modelRecognitionParam = getModelRecognitionParam(param, k, modelPageListParam);
                    log.info("分类模型召回参数：{}", JSON.toJSONString(modelPageListParam));
                    ModelRecognitionResponse predit = modelRecognitionService.predit(modelRecognitionParam);
                    log.info("分类模型召回结果：{}", JSON.toJSONString(predit));
                    if (Objects.nonNull(predit)) {
                        predit.getData().forEach(item -> {
                            Map<String, AgentIntentResponse> intentIdToResponseMap = v.stream()
                                    .collect(Collectors.toMap(AgentIntentResponse::getCode, Function.identity()));
                            if (intentIdToResponseMap.containsKey(item.getIntentId()) && item.getFinalScore() >= modelPageListParam.getThreshold()) {
                                AgentIntentResponse agentIntentResponse = intentIdToResponseMap.get(item.getIntentId());
                                IntentionRecognitionResponse response = new IntentionRecognitionResponse();
                                BeanUtil.copyProperties(agentIntentResponse, response);
                                response.setRecallType(IntentRecallTypeEnum.CLASSIFIER_RECOGNITION);
                                intentionRecognitionResponses.add(response);
                            }
                        });
                    }
                }
            } catch (Exception e) {
                log.error("分类模型召回过程中发生异常", e);
            }
        });
    }

    /**
     * 构建分类模型召回请求参数
     *
     * @param param              IntentionRecognitionParam
     * @param modelCode          String
     * @param modelPageListParam ModelPageListParam
     * @return
     */
    private ModelRecognitionParam getModelRecognitionParam(IntentionRecognitionParam param, String modelCode, ModelPageListParam modelPageListParam) {
        ModelRecognitionParam modelRecognitionParam = new ModelRecognitionParam();
        modelRecognitionParam.setUid(IdGenerator.id());
        modelRecognitionParam.setTimestamp(System.currentTimeMillis());
        modelRecognitionParam.setSeqid(IdGenerator.id());
        modelRecognitionParam.setInputText(param.getQuery());

        ModelInfo modelInfo = new ModelInfo();
        modelInfo.setUrl(modelPageListParam.getExternalModelUrl());
        modelInfo.setModelCode(modelCode);
        modelInfo.setModelName(modelPageListParam.getModelName());
        modelRecognitionParam.setModelInfo(modelInfo);
        return modelRecognitionParam;
    }

    /**
     * 正则召回
     *
     * @param param                         IntentionRecognitionParam
     * @param onIntentList                  List<AgentIntentResponse>
     * @param intentionRecognitionResponses List<IntentionRecognitionResponse>
     */
    private void regularRecall(IntentionRecognitionParam param, List<AgentIntentResponse> onIntentList, List<IntentionRecognitionResponse> intentionRecognitionResponses) {
        log.info("开始正则召回");
        onIntentList.forEach(item -> {
            boolean isMatched = false;
            if (CollUtil.isEmpty(item.getRules())) {
                log.info("正则匹配失败，意图{}没有规则", item);
                return;
            }
            for (String rule : item.getRules()) {
                if (CharSequenceUtil.isBlank(rule)) {
                    continue;
                }
                Pattern pattern = PATTERN_CACHE.computeIfAbsent(rule, Pattern::compile);
                Matcher matcher = pattern.matcher(param.getQuery());
                if (matcher.find()) {
                    isMatched = true;
                    break;
                }
            }
            if (isMatched) {
                IntentionRecognitionResponse response = new IntentionRecognitionResponse();
                BeanUtil.copyProperties(item, response);
                response.setRecallType(IntentRecallTypeEnum.REGULAR_MATCHING);
                intentionRecognitionResponses.add(response);
                log.info("正则匹配成功{}", item);
            }
        });
    }

    private void llmRecall(IntentionRecognitionParam param, Result<List<AgentIntentResponse>> listResult, List<IntentionRecognitionResponse> intentionRecognitionResponses) {
        if (CharSequenceUtil.isNotBlank(param.getModelCode())) {
            try {
                // 查询大模型配置
                ModelPageListParam llmModeInfo = aiModelAppService.get(param.getModelCode(), false);
                // 查询提示词
                Result<PromptTemplateVO> promptTemplateVOResult = promptAppServiceApi.get(INTENT_RECOGNITION_PROMPT_TEMPLATE);
                PromptTemplateVO templateVO = promptTemplateVOResult.getData();
                HashMap<String, Object> paramMap = buildPromptParam(param, listResult);
                if (CollUtil.isEmpty(paramMap)) {
                    return;
                }
                Object intentList = paramMap.get("intentList");
                if (Objects.isNull(intentList) || JSON.parseArray(intentList.toString()).size() <= 1 && intentList.toString().contains("\"其它\"") || JSON.parseArray(intentList.toString()).isEmpty()) {
                    return;
                }
                Map<String, ClassificatIntent> inputIntentMap = new HashMap<>();
                // 更简洁的写法（无冲突处理）
                if (CollUtil.isNotEmpty(param.getClassificatIntents())) {
                    inputIntentMap = param.getClassificatIntents().stream()
                            .collect(Collectors.toMap(
                                    intent -> String.valueOf(intent.getClassificationId()),
                                    Function.identity()
                            ));
                }


                LLMRequest llmRequest = buildRequest(llmModeInfo, LlmUtil.replaceVariables(templateVO.getContent(), paramMap));
                // 调用大模型召回意图
                log.info("大模型召回意图请求参数：{}", llmRequest.simpleLog());
                Response<String> llmResponse = streamingChatLanguageModel.syncGenerate(llmRequest);
                log.info("大模型召回意图返回信息: {}", llmResponse.content());
                String content = LlmUtil.toJson(llmResponse.content());
                IntentResult intentResult = JSON.parseObject(content, IntentResult.class);
                Map<String, ClassificatIntent> finalInputIntentMap = inputIntentMap;
                listResult.getData().forEach(intent -> {
                    if (intentResult.getClassificationId().equals(intent.getCode())) {
                        IntentionRecognitionResponse response = new IntentionRecognitionResponse();
                        BeanUtil.copyProperties(intent, response);
                        response.setRecallType(IntentRecallTypeEnum.LLM_RECOGNITION);
                        intentionRecognitionResponses.add(response);
                    } else if (CollUtil.isNotEmpty(finalInputIntentMap) && finalInputIntentMap.containsKey(intentResult.getClassificationId())) {
                        ClassificatIntent classificatIntent = finalInputIntentMap.get(intentResult.getClassificationId());
                        IntentionRecognitionResponse response = new IntentionRecognitionResponse();
                        response.setCode(intentResult.getClassificationId());
                        response.setName(classificatIntent.getName());
                        response.setDescription(classificatIntent.getDesc());
                        response.setIntentType(classificatIntent.getInputType());
                        response.setRecallType(IntentRecallTypeEnum.LLM_RECOGNITION);
                        intentionRecognitionResponses.add(response);
                    }
                });
            } catch (Exception e) {
                log.error("大模型识别意图过程中发生异常", e);
            }
        }
    }

    /**
     * 构建prompt替换参数
     *
     * @param param      IntentionRecognitionParam
     * @param listResult Result<List<AgentIntentResponse>>
     * @return HashMap<String, Object>
     */
    private HashMap<String, Object> buildPromptParam(IntentionRecognitionParam param, Result<List<AgentIntentResponse>> listResult) {
        HashMap<String, Object> paramMap = new HashMap<>();
        paramMap.put("query", param.getQuery());
        paramMap.put("intentList", JSON.toJSONString(getOnIntentList(listResult, param).stream().map(o -> {
            JSONObject intent = new JSONObject();
            intent.put("name", o.getName());
            intent.put("intentCode", o.getCode());
            intent.put("description", o.getDescription());
            return intent;
        }).collect(Collectors.toList())));
        return paramMap;
    }

    /**
     * 例句召回
     *
     * @param param                         IntentionRecognitionParam
     * @param intentConfig                  AgentIntentConfigResponse
     * @param queryIntentResult             Result<List<AgentIntentResponse>>
     * @param intentionRecognitionResponses List<IntentionRecognitionResponse>
     */
    private void exampleRecall(IntentionRecognitionParam param, AgentIntentConfigResponse intentConfig, Result<List<AgentIntentResponse>> queryIntentResult, List<IntentionRecognitionResponse> intentionRecognitionResponses) {
        log.info("例句召回开始");
        IntentQueryRequest intentQueryRequest = new IntentQueryRequest();
        intentQueryRequest.setCodes(param.getIntentList());
        intentQueryRequest.setQuery(param.getQuery());
        IntentSearchVO<IntentIndexData> intentQueryResponse = intentManagerRpcApi.queryIntent(intentQueryRequest, param.getIsSystemAgent() ? superTenant : RequestContext.getAppCode());
        log.info("例句es召回结果：{}", JSON.toJSONString(intentQueryResponse));
        Boolean useRerank = intentConfig.getUseRerank();
        if (Boolean.TRUE.equals(useRerank) && CharSequenceUtil.isNotBlank(intentConfig.getRerankModelCode()) && Objects.nonNull(intentQueryResponse)
                && CollUtil.isNotEmpty(intentQueryResponse.getItems())) {
            // 开启精排模型
            try {
                // 查询精排模型
                ModelPageListParam rerankModelConfig = aiModelAppService.get(intentConfig.getRerankModelCode(), false);
                RecognitionRequest recognitionRequest = new RecognitionRequest();
                recognitionRequest.setProcessContent(param.getQuery());
                recognitionRequest.setUserId(CharSequenceUtil.isNotBlank(param.getSessionId()) ? param.getSessionId() : IdUtil.fastSimpleUUID());
                List<QueryRankRequest.CandidateQuery> intentList = getQueryIntentList(intentQueryResponse);
                // 精排模型重新排序
                ExternalModelInfo externalModelInfo = buildExternalModelInfo(rerankModelConfig);
                QueryRankRequest queryRankRequest = new QueryRankRequest();
                queryRankRequest.setModelInfo(externalModelInfo);
                queryRankRequest.setRequestId(IdUtil.fastSimpleUUID());
                queryRankRequest.setQuery(param.getQuery());
                queryRankRequest.setTopN(1);
                queryRankRequest.setCandidateQueryList(intentList);
                log.info("例句召回调用精排模型开始,请求参数:{}", JsonUtils.toJsonString(queryRankRequest));
                List<QueryRankRequest.CandidateQuery> rankResult = commonRankModel.rank(queryRankRequest);
                log.info("例句召回调用精排模型结束,返回结果:{}", JsonUtils.toJsonString(rankResult));
                Map<String, AgentIntentResponse> intentMap = queryIntentResult.getData().stream().collect(Collectors.toMap(AgentIntentResponse::getCode, Function.identity()));
                intentionRecognitionResponses.addAll(rankResult.stream()
                        .distinct()
                        .filter(item -> item.getScore() >= intentConfig.getThreshold())
                        .map(o -> {
                            IntentionRecognitionResponse intentionRecognitionResponse = new IntentionRecognitionResponse();
                            if (intentMap.containsKey(o.getId())) {
                                BeanUtil.copyProperties(intentMap.get(o.getId()), intentionRecognitionResponse);
                                intentionRecognitionResponse.setScore(o.getScore());
                                intentionRecognitionResponse.setReRankScore(o.getScore());
                                intentionRecognitionResponse.setRecallType(IntentRecallTypeEnum.EXAMPLE_SENTENCE_RECALL);
                                return intentionRecognitionResponse;
                            }
                            return null;
                        })
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList()));
            } catch (Exception e) {
                log.error("精排模型调用过程中发生异常", e);
            }
        } else {
            // 未开启精排，使用检索召回
            if (Objects.nonNull(intentQueryResponse) && intentQueryResponse.getTotal() > 0) {
                List<IntentionRecognitionResponse> exampleRecallList = intentQueryResponse.getItems().stream().filter(item -> item.getScore() >= intentConfig.getThreshold()).map(item -> {
                    IntentionRecognitionResponse response = new IntentionRecognitionResponse();
                    IntentIndexData source = item.getSource();
                    response.setCode(source.getIntentCode());
                    response.setName(source.getTitle());
                    response.setDescription(source.getDescription());
                    response.setExamples(source.getContent());
                    response.setModelName(source.getModelName());
                    response.setModelCode(source.getModelCode());
                    response.setRecallType(IntentRecallTypeEnum.EXAMPLE_SENTENCE_RECALL);
                    response.setScore(item.getScore());
                    response.setReRankScore(item.getScore());
                    return response;
                }).collect(Collectors.toList());
                intentionRecognitionResponses.addAll(exampleRecallList);
            }
        }
    }

    private ExternalModelInfo buildExternalModelInfo(ModelPageListParam faqRerankModelParam) {
        ExternalModelInfo externalModelInfo = new ExternalModelInfo();
        externalModelInfo.setModelUrl(faqRerankModelParam.getExternalModelUrl());
        externalModelInfo.setModelName(faqRerankModelParam.getModelName());
        externalModelInfo.setModelSecret(faqRerankModelParam.getModelSecret());
        externalModelInfo.setModelApi(faqRerankModelParam.getApiKey());
        externalModelInfo.setProvider(ModelProviderEnum.from(faqRerankModelParam.getModelProvider()));
        return externalModelInfo;
    }


    /**
     * build llm request
     *
     * @param modelConfig    ModelPageConvertParam
     * @param promptTemplate String
     * @return LLMRequest
     */
    private LLMRequest buildRequest(ModelPageListParam modelConfig, String promptTemplate) {
        LLMRequest llmRequest = new LLMRequest();
        llmRequest.getLlmModelInfo().setProvider(ModelProviderEnum.from(modelConfig.getModelProvider()));
        llmRequest.getLlmModelInfo().setModelApi(modelConfig.getApiKey());
        llmRequest.getLlmModelInfo().setModelSecret(modelConfig.getModelSecret());
        llmRequest.getLlmModelInfo().setModelName(modelConfig.getModelCallName());
        llmRequest.getLlmModelInfo().setModelUrl(modelConfig.getExternalModelUrl());
        llmRequest.setStreaming(1);
        llmRequest.setText(promptTemplate);
        llmRequest.setUserId(IdUtil.fastSimpleUUID());
        if (Objects.nonNull(modelConfig.getExtraDataVO())) {
            llmRequest.getLlmModelInfo().setMaxToken(modelConfig.getExtraDataVO().getMaxInputToken());
            llmRequest.getLlmModelInfo().setTransformerType(modelConfig.getExtraDataVO().getTransformerType());
            llmRequest.getLlmModelInfo().setNativeCall(modelConfig.getExtraDataVO().getNativeCall());
            llmRequest.getLlmModelInfo().setNativeCallUrl(modelConfig.getExtraDataVO().getNativeCallUrl());
        }
        return llmRequest;
    }

    @Getter
    @Setter
    public static class IntentResult {
        /**
         * 意图ID和portId一样
         */
        private String classificationId;

        /**
         * 意图描述
         */
        private String reason;
    }

}
