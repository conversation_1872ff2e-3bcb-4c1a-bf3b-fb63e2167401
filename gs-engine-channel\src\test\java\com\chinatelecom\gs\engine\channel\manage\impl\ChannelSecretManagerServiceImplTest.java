package com.chinatelecom.gs.engine.channel.manage.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.chinatelecom.gs.engine.channel.api.converter.VOBeanConvertor;
import com.chinatelecom.gs.engine.channel.common.enums.ChannelTypeEnum;
import com.chinatelecom.gs.engine.channel.dao.po.ChannelApiSecretPO;
import com.chinatelecom.gs.engine.channel.dao.po.YN;
import com.chinatelecom.gs.engine.channel.dao.repository.ChannelApiSecretRepository;
import com.chinatelecom.gs.engine.channel.dao.repository.ChannelInfoRepository;
import com.chinatelecom.gs.engine.channel.manage.ChannelConfigService;
import com.chinatelecom.gs.engine.channel.service.dto.ChannelApiSecretDTO;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.context.RequestInfo;
import com.chinatelecom.gs.engine.robot.sdk.config.enums.ApiSecretType;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.page.Page;
import com.chinatelelcom.gs.engine.sdk.common.enums.AppSourceType;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

public class ChannelSecretManagerServiceImplTest {

    @InjectMocks
    private ChannelSecretManagerServiceImpl service;

    @Mock
    private ChannelApiSecretRepository channelApiSecretRepository;

    @Mock
    private ChannelInfoRepository channelInfoRepository;

    @Mock
    private ChannelConfigService channelConfigService;

    @Mock
    private VOBeanConvertor voBeanConvertor;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        // 设置 RequestContext 的静态模拟
        RequestInfo requestInfo = new RequestInfo();
        requestInfo.setUserId("testUser");
        requestInfo.setUserName("testUser");
        requestInfo.setTenantId("testTenant");
        requestInfo.setAppSourceType(AppSourceType.GOVERNMENT);
        requestInfo.setAppCode("APP001");
        RequestContext.set(requestInfo);
    }

    @AfterEach
    public void tearDown() {
        RequestContext.remove();
    }

    @Test
    public void testList_Normal() {
        String appId = "app1";
        ApiSecretType type = ApiSecretType.API;
        int pageNo = 1, pageSize = 10;

        ChannelApiSecretPO po = new ChannelApiSecretPO();
        po.setId(1L);
        po.setName("secret1");

        List<ChannelApiSecretPO> records = Collections.singletonList(po);

        IPage<ChannelApiSecretPO> page = mock(IPage.class);
        when(page.getRecords()).thenReturn(records);
        when(page.getTotal()).thenReturn(1L);
        when(page.getCurrent()).thenReturn(1L);
        when(page.getSize()).thenReturn(10L);
        when(page.getPages()).thenReturn(1L);

        LambdaQueryWrapper<ChannelApiSecretPO> condition = Wrappers.lambdaQuery();
        condition.eq(ChannelApiSecretPO::getAppId, appId);
        condition.eq(ChannelApiSecretPO::getSecretType, ApiSecretType.API);
        condition.eq(ChannelApiSecretPO::getYn, YN.YES.getValue());
        condition.orderByDesc(ChannelApiSecretPO::getCreateTime);

        // ✅ 使用 IPage 而不是 Page
        when(channelApiSecretRepository.page(any(IPage.class), any(LambdaQueryWrapper.class))).thenReturn(page);

        // 调用服务方法
        Page<ChannelApiSecretDTO> result = service.list(appId, type, pageNo, pageSize);

        assertNotNull(result);
        assertEquals(1, result.getRecords().size());

        // ✅ 使用宽松匹配
        verify(channelApiSecretRepository).page(
                any(com.baomidou.mybatisplus.core.metadata.IPage.class),
                any(LambdaQueryWrapper.class)
        );
    }


    @Test
    public void testCreate_Success() {
        String appId = "app1";
        String channelId = "ch1";
        String secretName = "secret1";

        // ✅ 模拟 addChannelInfo 方法
        when(channelInfoRepository.addChannelInfo(anyString(), any(ChannelTypeEnum.class))).thenReturn("newChId");

        // ✅ 模拟 IdGenerator.id()
//        when(IdGenerator.id()).thenReturn("newSecretId");

        // ✅ 模拟 save 方法
        when(channelApiSecretRepository.save(any(ChannelApiSecretPO.class))).thenReturn(true);

        // ✅ 调用被测方法
        boolean result = service.create(appId, channelId, secretName, ApiSecretType.API);

        // ✅ 断言结果
        assertTrue(result);
    }

    @Test
    public void testRemove_LastSecret_NotAllowed() {
        String appId = "app1";
        String secretId = "s1";

        List<ChannelApiSecretDTO> list = Collections.singletonList(new ChannelApiSecretDTO());
        when(channelApiSecretRepository.channelApiSecretListByAppId(appId, ApiSecretType.API, 2)).thenReturn(list);

        assertThrows(BizException.class, () -> service.remove(appId, secretId));
    }

    @Test
    public void testIsSecretExist_NoSecret() {
        String appId = "app1";
        when(channelApiSecretRepository.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        boolean exists = service.isSecretExist(appId, ApiSecretType.API);
        assertFalse(exists);
    }

    @Test
    public void testCheckNameRepeat_Duplicate() {
        String appId = "app1";
        String name = "dup";

        ChannelApiSecretPO po = new ChannelApiSecretPO();
        when(channelApiSecretRepository.list(any(LambdaQueryWrapper.class))).thenReturn(Collections.singletonList(po));

        assertThrows(BizException.class, () -> service.checkNameRepeat(appId, name));
    }
}
