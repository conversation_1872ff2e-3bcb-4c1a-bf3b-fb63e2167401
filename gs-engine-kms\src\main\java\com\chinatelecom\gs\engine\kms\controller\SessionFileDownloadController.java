package com.chinatelecom.gs.engine.kms.controller;

import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.common.constants.Apis;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.common.platform.StatOpenApi;
import com.chinatelecom.gs.engine.kms.service.SessionFileService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;



/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024年12月26日
 */


@Slf4j
@Tag(name = "会话文件")
@Controller
@RequestMapping({"/bot/session/" + Apis.RPC_PREFIX, "/bot/session/" + Apis.OPENAPI})
public class SessionFileDownloadController {

    @Resource
    private SessionFileService sessionFileService;

    @PlatformRestApi(name = "会话文件下载", groupName = "对话框挂载文件")
    @AuditLog(businessType = "对话框挂载文件", operType = "会话文件下载", operDesc = "会话文件下载", objId="#fileCode")
    @GetMapping("/file/download/{fileCode}")
    @StatOpenApi(groupName = "对话框挂载文件", name = "会话文件下载")
    public void filedownload(@PathVariable("fileCode") String fileCode, HttpServletResponse response) {
        sessionFileService.download(fileCode, response);
    }
}
