package com.chinatelecom.gs.engine.kms.controller;


import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.kms.sdk.constants.KmsApis;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.prod.FaqComparePageParam;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.prod.FaqCompareParam;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.prod.FaqCompareVO;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.prod.FaqPublishParam;
import com.chinatelecom.gs.engine.kms.service.KnowledgeFaqProdAppService;
import com.chinatelecom.gs.engine.sdk.common.annotation.ApiType;
import com.chinatelecom.gs.engine.sdk.common.annotation.HideFromApiTypes;
import com.chinatelelcom.gs.engine.sdk.common.Page;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * <p>
 * 问答线上表 Controller
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-16
 */
@RestController
@Slf4j
@Tag(name = "问答发布管理")
@RequestMapping(KmsApis.KMS_API + KmsApis.KNOWLEDGE + KmsApis.FAQ + KmsApis.PROD)
public class KnowledgeFaqProdController {

    @Autowired
    private KnowledgeFaqProdAppService knowledgeFaqProdAppService;


    @Operation(summary = "发布问答知识文档", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "发布问答知识文档", groupName = "问答发布管理")
    @AuditLog(businessType = "问答发布管理", operType = "发布问答知识文档", operDesc = "发布问答知识文档", objId="#param.knowledgeCode")
    @PostMapping(KmsApis.PUBLISH)
    public Result<Boolean> publish(@Validated @RequestBody FaqPublishParam param) {
        knowledgeFaqProdAppService.triggerPublish(param);
        return Result.success(true);
    }

    @HideFromApiTypes(ApiType.OPENAPI)
    @Operation(summary = "重置问答文档", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "重置问答文档", groupName = "问答发布管理")
    @AuditLog(businessType = "问答发布管理", operType = "重置问答文档", operDesc = "重置问答文档", objId="#param.knowledgeBaseCode")
    @PostMapping(KmsApis.RESET)
    public Result<Boolean> reset(@Validated @RequestBody FaqPublishParam param) {
        knowledgeFaqProdAppService.reset(param);
        return Result.success(true);
    }

    @HideFromApiTypes(ApiType.OPENAPI)
    @Operation(summary = "问答对列表对比", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "问答对列表对比", groupName = "问答发布管理")
    @AuditLog(businessType = "问答发布管理", operType = "问答对列表对比", operDesc = "问答对列表对比", objId="#param.knowledgeCode")
    @PostMapping(KmsApis.COMPARE + KmsApis.PAGE_API)
    public Result<Page<FaqCompareVO>> comparePage(@Validated @RequestBody FaqComparePageParam param) {
        Page<FaqCompareVO> result = knowledgeFaqProdAppService.comparePage(param);
        return Result.success(result);
    }


    @HideFromApiTypes(ApiType.OPENAPI)
    @Operation(summary = "问答对内容对比", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "问答对内容对比", groupName = "问答发布管理")
    @AuditLog(businessType = "问答发布管理", operType = "问答对内容对比", operDesc = "问答对内容对比", objId="#param.faqCode")
    @PostMapping(KmsApis.COMPARE)
    public Result<FaqCompareVO> compare(@Validated @RequestBody FaqCompareParam param) {
        FaqCompareVO result = knowledgeFaqProdAppService.compare(param);
        return Result.success(result);
    }

//
//    @ApiOperation(value = "问答线上表分页列表")
//    @PlatformRestApi(name = "问答线上表分页列表", groupName = "问答发布管理")
//    @AuditLog(businessType = "问答发布管理", operType = "问答线上表分页列表", operDesc = "问答线上表分页列表", objId="#param")
//    @PostMapping(KmsApis.PAGE_API)
//    public Result<Page<KnowledgeFaqProdVO>> page(@Validated @RequestBody KnowledgeFaqProdQueryParam param) {
//
//        return Result.success(knowledgeFaqProdAppService.pageQuery(param));
//    }
//
//    @ApiOperation(value = "问答线上表详情")
//    @PlatformRestApi(name = "问答线上表详情", groupName = "问答发布管理")
//    @AuditLog(businessType = "问答发布管理", operType = "问答线上表详情", operDesc = "问答线上表详情", objId="#param")
//    @GetMapping(KmsApis.CODE_PATH)
//    public Result<KnowledgeFaqProdVO> get(@PathVariable("code") String code) {
//        return Result.success(knowledgeFaqProdAppService.get(code));
//    }
//
//    @ApiOperation(value = "问答线上表新增")
//    @PlatformRestApi(name = "问答线上表新增", groupName = "问答发布管理")
//    @AuditLog(businessType = "问答发布管理", operType = "问答线上表新增", operDesc = "问答线上表新增", objId="#param")
//    @PostMapping
//    public Result<KnowledgeFaqProdVO> add(@Validated @RequestBody KnowledgeFaqProdCreateParam createParam) {
//        return Result.success(knowledgeFaqProdAppService.create(createParam));
//    }
//
//
//    @ApiOperation(value = "问答线上表更新")
//    @PlatformRestApi(name = "问答线上表更新", groupName = "问答发布管理")
//    @AuditLog(businessType = "问答发布管理", operType = "问答线上表更新", operDesc = "问答线上表更新", objId="#param")
//    @PutMapping(KmsApis.CODE_PATH)
//    public Result<Boolean> update(@PathVariable("code") String code, @Validated @RequestBody KnowledgeFaqProdUpdateParam param) {
//        return Result.success(knowledgeFaqProdAppService.update(code, param));
//    }
//
//
//    @ApiOperation(value = "问答线上表删除")
//    @PlatformRestApi(name = "问答线上表删除", groupName = "问答发布管理")
//    @AuditLog(businessType = "问答发布管理", operType = "问答线上表删除", operDesc = "问答线上表删除", objId="#param")
//    @PostMapping(KmsApis.DELETE_API)
//    public Result<Boolean> delete(@Validated @RequestBody CodeParam codes) {
//        return Result.success(knowledgeFaqProdAppService.delete(codes));
//    }
}


