package com.chinatelecom.gs.engine.kms.controller;


import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.kms.model.vo.KnowledgeAuditDataQueryParam;
import com.chinatelecom.gs.engine.kms.model.vo.KnowledgeAuditDataVO;
import com.chinatelecom.gs.engine.kms.sdk.constants.KmsApis;
import com.chinatelecom.gs.engine.kms.service.KnowledgeAuditDataAppService;
import com.chinatelelcom.gs.engine.sdk.common.Page;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * <p>
 * 知识审核内容表 Controller
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-03
 */
@Slf4j
@Tag(name = "知识审核内容表")
@RestController
@RequestMapping({KmsApis.KMS_API + KmsApis.AUDIT_DATA})
public class KnowledgeAuditDataController {

    @Autowired
    private KnowledgeAuditDataAppService knowledgeAuditDataAppService;

    @Operation(summary = "知识审核内容表分页列表", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "知识审核内容表分页查询", groupName = "知识审核")
    @AuditLog(businessType = "知识审核", operType = "知识审核内容表分页查询", operDesc = "知识审核内容表分页查询", objId="#param.auditCode")
    @PostMapping(KmsApis.PAGE_API)
    public Result<Page<KnowledgeAuditDataVO>> page(@Validated @RequestBody KnowledgeAuditDataQueryParam param) {
        return Result.success(knowledgeAuditDataAppService.pageQuery(param));
    }
//
//    @ApiOperation(value = "知识审核内容表详情")
//    @GetMapping(KmsApis.CODE_PATH)
//    public Result<KnowledgeAuditDataVO> get(@PathVariable("code") String code) {
//        return Result.success(knowledgeAuditDataAppService.get(code));
//    }
//
//    @ApiOperation(value = "知识审核内容表新增")
//    @PostMapping
//    public Result<KnowledgeAuditDataVO> add(@Validated @RequestBody KnowledgeAuditDataCreateParam createParam) {
//        return Result.success(knowledgeAuditDataAppService.create(createParam));
//    }
//
//
//    @ApiOperation(value = "知识审核内容表更新")
//    @PutMapping(KmsApis.CODE_PATH)
//    public Result<Boolean> update(@PathVariable("code") String code, @Validated @RequestBody KnowledgeAuditDataUpdateParam param) {
//        return Result.success(knowledgeAuditDataAppService.update(code, param));
//    }
//
//
//    @ApiOperation(value = "知识审核内容表删除")
//    @PostMapping(KmsApis.DELETE_API)
//    public Result<Boolean> delete(@Validated @RequestBody CodeParam codes) {
//        return Result.success(knowledgeAuditDataAppService.delete(codes));
//    }
}


