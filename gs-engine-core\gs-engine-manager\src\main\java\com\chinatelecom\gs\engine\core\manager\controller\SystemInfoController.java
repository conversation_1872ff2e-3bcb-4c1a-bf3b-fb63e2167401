package com.chinatelecom.gs.engine.core.manager.controller;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.chinatelecom.cloud.common.pojo.BaseResult;
import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.cloud.platform.client.rpc.CorpAppResponse;
import com.chinatelecom.cloud.platform.client.util.AuthUtils;
import com.chinatelecom.gs.engine.common.constants.Apis;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.context.RequestInfo;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.core.manager.vo.SystemInfoVO;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.regex.Matcher;
import java.util.regex.Pattern;


/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024年11月28日
 */
@RestController
@Slf4j
@Tag(name = "应用信息接口")
@RequestMapping({Apis.BASE_PREFIX + Apis.WEB_API + Apis.SYSTEM})
public class SystemInfoController {

    // 定义正则表达式，匹配 x.x.x 格式的版本号
    private static final Pattern VERSION_PATTERN = Pattern.compile("\\d+\\.\\d+\\.\\d+");

    @Value("${SRD_TIMESTAMP:}")
    private String timestamp;

    @Value("${SRD_COMMIT_SHA:}")
    private String commitSha;

    @Value("${SRD_GIT_BRANCH:}")
    private String gitBranch;

    @Value("${SRD_COMMIT_TAG:}")
    private String gitTag;

    /**
     * 手动指定产品版本
     */
    @Value("${gs.system.version:}")
    private String productVersion;

    @Operation(summary = "系统版本信息查询", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @GetMapping(Apis.INFO)
    @PlatformRestApi(name = "系统版本信息查询", groupName = "系统信息查询")
    @AuditLog(businessType = "应用信息接口", operType = "系统版本信息查询", operDesc = "系统版本信息查询", objId="null")
    public Result<SystemInfoVO> systemInfo() {
        RequestInfo req = RequestContext.getAndCheck();
        SystemInfoVO vo = new SystemInfoVO();
        vo.setUserId(req.getUserId());
        vo.setUserName(req.getUserName());
        vo.setTenantId(req.getTenantId());
        vo.setTimestamp(timestamp);
        vo.setCommitSha(commitSha);
        vo.setGitBranch(gitBranch);
        vo.setGitTag(gitTag);

        CorpAppResponse corpAppResponse = queryCorpApp();
        if (corpAppResponse != null) {
            vo.setLicenseStartDate(corpAppResponse.getStartDate());
            vo.setLicenseExpireDate(corpAppResponse.getExpireDate());
            if (corpAppResponse.getStartDate() != null && corpAppResponse.getExpireDate() != null) {
                vo.setLicenseDays(DateUtil.between(corpAppResponse.getStartDate(), corpAppResponse.getExpireDate(), DateUnit.DAY));
            }
        }
        vo.setVersion(getVersion());
        return Result.success(vo);
    }

    private String getVersion() {
        String version = productVersion;
        if (StringUtils.isBlank(version)) {
            version = extractVersion(gitTag);
        }
        if (StringUtils.isBlank(version)) {
            version = extractVersion(gitBranch);
        }
        return version;
    }

    public static String extractVersion(String input) {
        if (StringUtils.isBlank(input)) {
            return null;
        }

        Matcher matcher = VERSION_PATTERN.matcher(input);
        if (matcher.find()) {
            return matcher.group();
        }
        return null;
    }

    private CorpAppResponse queryCorpApp() {
        try {
            BaseResult<CorpAppResponse> baseResult = AuthUtils.getCurrentCorpApp(RequestContext.getTenantId());
            if (baseResult.getCode() != BaseResult.SUCCESS) {
                throw new BizException("CA010", "查询应用信息失败：{}", baseResult.getMsg());
            }
            return baseResult.getData();
        } catch (Exception e) {
            log.warn("查询云平台logo配置失败", e);
        }
        return null;
    }


}
