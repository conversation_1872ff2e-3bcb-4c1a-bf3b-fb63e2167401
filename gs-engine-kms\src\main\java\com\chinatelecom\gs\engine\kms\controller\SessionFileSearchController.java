package com.chinatelecom.gs.engine.kms.controller;


import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.kms.sdk.api.SessionFileSearchApi;
import com.chinatelecom.gs.engine.kms.sdk.constants.KmsApis;
import com.chinatelecom.gs.engine.kms.sdk.vo.file.SessionFileQueryParam;
import com.chinatelecom.gs.engine.kms.sdk.vo.file.SessionFileVO;
import com.chinatelecom.gs.engine.kms.service.SessionFileService;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;


@RestController
@Slf4j
@RequestMapping(KmsApis.RPC + KmsApis.SESSION)
public class SessionFileSearchController implements SessionFileSearchApi {

    @Resource
    private SessionFileService sessionFileService;

    @Override
    @Operation(summary = "会话文件删除", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "会话文件删除", groupName = "对话框挂载文件")
    @AuditLog(businessType = "对话框挂载文件", operType = "会话文件删除", operDesc = "会话文件删除", objId="#param.fileCodes")
    @PostMapping("/files")
    public Result<List<SessionFileVO>> files(@Valid @RequestBody SessionFileQueryParam param) {
        return Result.success(sessionFileService.queryFile(param));
    }

    @Override
    @Operation(summary = "会话文件删除", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PlatformRestApi(name = "会话文件删除", groupName = "对话框挂载文件")
    @AuditLog(businessType = "对话框挂载文件", operType = "会话文件删除", operDesc = "会话文件删除", objId="#sessionIds")
    @PostMapping("/list")
    public Result<List<SessionFileVO>> list(@RequestBody List<String> sessionIds) {
        return Result.success(sessionFileService.listFile(sessionIds));
    }
}
