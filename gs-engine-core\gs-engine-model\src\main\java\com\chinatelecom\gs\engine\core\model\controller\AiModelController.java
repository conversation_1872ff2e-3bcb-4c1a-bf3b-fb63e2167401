package com.chinatelecom.gs.engine.core.model.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.common.auth.KsMenuConfig;
import com.chinatelecom.gs.engine.common.auth.MenuConfig;
import com.chinatelecom.gs.engine.common.auth.PermissionTag;
import com.chinatelecom.gs.engine.common.config.aspect.annotation.DebugLog;
import com.chinatelecom.gs.engine.common.constants.Constants;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.common.utils.HttpFileUtils;
import com.chinatelecom.gs.engine.core.model.entity.param.*;
import com.chinatelecom.gs.engine.core.model.entity.vo.QueryByModelTypeVO;
import com.chinatelecom.gs.engine.core.model.entity.vo.UploadResultVO;
import com.chinatelecom.gs.engine.core.model.service.AiModelAppService;
import com.chinatelecom.gs.engine.core.model.utils.FileUtil;
import com.chinatelecom.gs.engine.core.sdk.vo.ModelIntentParam;
import com.chinatelecom.gs.engine.core.sdk.vo.ModelPageListParam;
import com.chinatelelcom.gs.engine.sdk.common.PageImpl;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import com.google.common.collect.Lists;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ResourceLoader;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 模型管理控制器
 *
 * <AUTHOR>
 */
@Slf4j
@Tag(name = "模型管理")
@Validated
@RestController
@RequestMapping(Constants.BASE_PREFIX + Constants.WEB_PREFIX + "/model")
public class AiModelController {

    @Autowired
    private AiModelAppService aiModelAppService;

    @jakarta.annotation.Resource
    private ResourceLoader resourceLoader;

    @Value("${platform.super.tenant:SuperAis}")
    private String superTenant;


    @Operation(summary = "获取模型列表", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "系统配置")})})
    @PlatformRestApi(name = "获取模型列表", groupName = "意图管理")
    @PostMapping("/list")
    @ResponseBody
    @AuditLog(businessType = "模型管理", operType = "获取模型列表", operDesc = "获取模型列表", objId="null")
    public Result<PageImpl<ModelPageListParam>> aiModelList(@Valid @NotNull @RequestBody ModelPageParam param) {
        PageImpl<ModelPageListParam> modelPageListParamPage = aiModelAppService.aiModelList(param);
        List<ModelPageListParam> records = modelPageListParamPage.getRecords();

        List<ModelPageListParam> pageResult = Lists.newArrayList();
        if (CollUtil.isNotEmpty(records)) {
            pageResult = records.stream().map(o -> {
                ModelPageListParam modelPageListParam = new ModelPageListParam();
                if (Boolean.TRUE.equals(o.getIsGlobal())) {
                    modelPageListParam.setModelCode(o.getModelCode());
                    modelPageListParam.setModelType(o.getModelType());
                    modelPageListParam.setModelName(o.getModelName());
                    modelPageListParam.setIsDefault(o.getIsDefault());
                    modelPageListParam.setIsGlobal(true);
                } else {
                    BeanUtil.copyProperties(o, modelPageListParam);
                    modelPageListParam.setIsGlobal(false);
                }
                return modelPageListParam;
            }).collect(Collectors.toList());
        }
        modelPageListParamPage.setRecords(pageResult);
        return Result.success(modelPageListParamPage);
    }

    @PermissionTag(code = {MenuConfig.ROBOT, KsMenuConfig.BOT, MenuConfig.MODEL_MANAGE, KsMenuConfig.MODEL_MANAGE})
    @Operation(summary = "创建/修改", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "系统配置")})})
    @PlatformRestApi(name = "创建/修改", groupName = "意图管理")
    @PostMapping("/save")
    @ResponseBody
    @AuditLog(businessType = "模型管理", operType = "创建/修改", operDesc = "创建/修改", objId="#param.appId")
    public Result<Boolean> edit(@Valid @NotNull @RequestBody ModelEditParam param) {
        return Result.success(aiModelAppService.edit(param));
    }

    @Operation(summary = "查询大模型提供方", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "系统配置")})})
    @PlatformRestApi(name = "查询大模型提供方", groupName = "意图管理")
    @PostMapping("/provider")
    @ResponseBody
    @AuditLog(businessType = "模型管理", operType = "查询大模型提供方", operDesc = "查询大模型提供方", objId="null")
    public Result<List<NameAndCodeParam>> getModelProvider() {
        return Result.success(aiModelAppService.getModelProvider());
    }

    @PermissionTag(code = {MenuConfig.ROBOT, KsMenuConfig.BOT, MenuConfig.MODEL_MANAGE, KsMenuConfig.MODEL_MANAGE})
    @Operation(summary = "删除", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "系统配置")})})
    @PlatformRestApi(name = "删除", groupName = "意图管理")
    @GetMapping("/remove")
    @ResponseBody
    @AuditLog(businessType = "模型管理", operType = "删除", operDesc = "删除", objId="#modelCode")
    public Result<Boolean> remove(@RequestParam("modelCode") @NotBlank(message = "模型编码不能为空") String modelCode) {
        return Result.success(aiModelAppService.remove(modelCode));
    }

    @Operation(summary = "查询模型详情", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "系统配置")})})
    @PlatformRestApi(name = "查询模型详情", groupName = "意图管理")
    @GetMapping("/details")
    @ResponseBody
    @AuditLog(businessType = "模型管理", operType = "查询模型详情", operDesc = "查询模型详情", objId="#modelCode")
    public Result<ModelPageListParam> get(@RequestParam("modelCode") @NotBlank(message = "模型编码不能为空") String modelCode) {
        ModelPageListParam modelPageListParam = aiModelAppService.get(modelCode, false);
        if (modelPageListParam != null) {
            if (!Boolean.TRUE.equals(modelPageListParam.getIsGlobal()) || RequestContext.getAppCode().equals(superTenant)) {
                modelPageListParam.setIsGlobal(!superTenant.equals(RequestContext.getAppCode()));
                return Result.success(modelPageListParam);
            }
            ModelPageListParam mode = new ModelPageListParam();
            mode.setModelCode(modelPageListParam.getModelCode());
            mode.setModelType(modelPageListParam.getModelType());
            mode.setModelName(modelPageListParam.getModelName());
            mode.setIsGlobal(!superTenant.equals(RequestContext.getAppCode()));
            modelPageListParam = mode;
        }
        return Result.success(modelPageListParam);
    }

    @Operation(summary = "查询模型类型", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "系统配置")})})
    @PlatformRestApi(name = "查询模型类型", groupName = "意图管理")
    @GetMapping("/modelType")
    @ResponseBody
    @AuditLog(businessType = "模型管理", operType = "查询模型类型", operDesc = "查询模型类型", objId="null")
    public Result<List<ModelTypeParam>> getModelType() {
        return Result.success(aiModelAppService.getModelType());
    }

    @Operation(summary = "查询模型数据格式类型", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "系统配置")})})
    @PlatformRestApi(name = "查询模型数据格式类型", groupName = "意图管理")
    @GetMapping("/dataType")
    @ResponseBody
    @AuditLog(businessType = "模型管理", operType = "查询模型数据格式类型", operDesc = "查询模型数据格式类型", objId="null")
    public Result<List<ModelTypeParam>> getModelDataType() {
        return Result.success(aiModelAppService.getModelDataType());
    }


    @Operation(summary = "查询模型意图", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "系统配置")})})
    @PlatformRestApi(name = "查询模型意图", groupName = "意图管理")
    @PostMapping("/intent")
    @ResponseBody
    @AuditLog(businessType = "模型管理", operType = "查询模型意图", operDesc = "查询模型意图", objId="#param.modelCode")
    public Result<PageImpl<ModelIntentParam>> queryIntentPage(@Valid @NotNull @RequestBody ModelIntentPageParam param) {
        return Result.success(aiModelAppService.queryIntentPage(param));
    }

    @PermissionTag(code = {MenuConfig.ROBOT, KsMenuConfig.BOT, MenuConfig.MODEL_MANAGE, KsMenuConfig.MODEL_MANAGE})
    @Operation(summary = "创建意图", method = "POST", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "系统配置")})})
    @PlatformRestApi(name = "创建意图", groupName = "意图管理")
    @PostMapping("/createIntent")
    @AuditLog(businessType = "模型管理", operType = "创建意图", operDesc = "创建意图", objId="#modelCode")
    public Result<Boolean> createIntent(@RequestParam(name = "modelCode") String modelCode,
                                        @RequestParam(name = "key") String key,
                                        @RequestParam(name = "userId") String userId,
                                        @RequestParam(name = "intentJson") String intentJson) {
        return Result.success(aiModelAppService.createIntent(modelCode, key, intentJson, userId));
    }

    @Operation(summary = "模型导入模板下载", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "系统配置")})})
    @PlatformRestApi(name = "模型导入模板下载", groupName = "意图管理")
    @PostMapping("/tplDownload")
    @AuditLog(businessType = "模型管理", operType = "模型导入模板下载", operDesc = "模型导入模板下载", objId="null")
    public void tplDownload(HttpServletResponse response) throws Exception {
        try {
            String fileName = "模型导入模板.xlsx";
            String filePath = "classpath:/file/" + fileName;
            final var resource = resourceLoader.getResource(filePath);
            InputStream inputStream = resource.getInputStream();
            HttpFileUtils.download(inputStream, fileName, true, response);
        } catch (IOException e) {
            log.error("下载文件异常", e);
            throw new BizException(e, "AA017", "下载文件异常");
        }
    }

    @PermissionTag(code = {MenuConfig.ROBOT, KsMenuConfig.BOT, MenuConfig.MODEL_MANAGE, KsMenuConfig.MODEL_MANAGE})
    @Operation(summary = "模型导入", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "系统配置")})})
    @PlatformRestApi(name = "模型导入", groupName = "意图管理")
    @PostMapping("/upload")
    @DebugLog(operation = "模型导入")
    @ResponseBody
    @AuditLog(businessType = "模型管理", operType = "模型导入", operDesc = "模型导入", objId="null")
    public Result<UploadResultVO> upload(@RequestParam("file") MultipartFile file) throws Exception {
        FileUtil.checkFileType(file, Lists.newArrayList(".xlsx"));
        UploadResultVO result = aiModelAppService.upload(file);
        return result.isSuccess() ? Result.success(new UploadResultVO(true, "成功!")) : Result.failed(Result.INVALID_PARAM, result.getMsg());
    }

    @PermissionTag(code = {MenuConfig.ROBOT, KsMenuConfig.BOT, MenuConfig.MODEL_MANAGE, KsMenuConfig.MODEL_MANAGE})
    @Operation(summary = "模型导出", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "系统配置")})})
    @PlatformRestApi(name = "模型导出", groupName = "意图管理")
    @PostMapping("/download")
    @DebugLog(operation = "模型导出")
    @AuditLog(businessType = "模型管理", operType = "模型导出", operDesc = "模型导出", objId="#downParam.appId")
    public void download(HttpServletResponse response,
                         @Valid @NotNull @RequestBody AiModelDownParam downParam) throws Exception {
        aiModelAppService.download(downParam, response);
    }

    @Operation(summary = "根据模型类型获取模型信息", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "系统配置")})})
    @PlatformRestApi(name = "根据模型类型获取模型信息", groupName = "意图管理")
    @GetMapping("/queryByModelType")
    @AuditLog(businessType = "模型管理", operType = "根据模型类型获取模型信息", operDesc = "根据模型类型获取模型信息", objId="#modelType")
    public Result<List<QueryByModelTypeVO>> queryByModelType(@RequestParam("modelType") String modelType) {
        return Result.success(aiModelAppService.queryByModelType(modelType));
    }
}
