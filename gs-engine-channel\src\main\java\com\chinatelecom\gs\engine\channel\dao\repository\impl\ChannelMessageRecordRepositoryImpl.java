package com.chinatelecom.gs.engine.channel.dao.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.chinatelecom.gs.engine.channel.dao.mapper.ChannelMessageRecordMapper;
import com.chinatelecom.gs.engine.channel.dao.po.ChannelMessageRecordPO;
import com.chinatelecom.gs.engine.channel.dao.repository.ChannelMessageRecordRepository;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2023/12/22 13:49
 * @description
 */
@Service
public class ChannelMessageRecordRepositoryImpl
        extends ServiceImpl<ChannelMessageRecordMapper, ChannelMessageRecordPO>
        implements ChannelMessageRecordRepository {
}
