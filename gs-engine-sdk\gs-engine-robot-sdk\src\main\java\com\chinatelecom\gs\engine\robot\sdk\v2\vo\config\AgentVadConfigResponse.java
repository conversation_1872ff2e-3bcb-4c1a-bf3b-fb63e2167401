package com.chinatelecom.gs.engine.robot.sdk.v2.vo.config;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 静音检测配置参数
 *
 * @author: Wei
 * @date: 2025-02-06 09:32
 */
@Data
@Schema(description = "静音检测配置参数")
public class AgentVadConfigResponse {

    /**
     * 机器人编码
     */
    @Schema(description = "机器人编码")
    private String agentCode;

    /**
     * 自定义检测时间，毫秒值ms
     */
    @Schema(description = "自定义检测时间，毫秒值ms")
    private Long detectionTime;

    /**
     * 回复设置，
     *  0：用户静音全局兜底回复
     *  1：重复当前话术
     */
    @Schema(description = """
            回复设置，
             0：用户静音全局兜底回复
             1：重复当前话术""")
    private String reply;

    /**
     * 兜底回复话术
     */
    @Schema(description = "兜底回复话术")
    private String replyScript;

    /**
     * 挂断设置静音挂断次数设置
     */
    @Schema(description = "挂断设置静音挂断次数设置")
    private Long hangUpCount;

    /**
     * 挂断话术
     */
    @Schema(description = "挂断话术")
    private String hangUpScript;

}
