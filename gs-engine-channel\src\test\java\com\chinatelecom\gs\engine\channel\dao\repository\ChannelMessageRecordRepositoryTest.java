package com.chinatelecom.gs.engine.channel.dao.repository;
import static org.junit.jupiter.api.Assertions.assertSame;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import com.baomidou.mybatisplus.extension.service.IService; import com.chinatelecom.gs.engine.channel.dao.po.ChannelMessageRecordPO; import org.junit.jupiter.api.BeforeEach; import org.junit.jupiter.api.Test; import org.mockito.Mock; import org.mockito.MockitoAnnotations;
public class ChannelMessageRecordRepositoryTest {
    @Mock
    private ChannelMessageRecordRepository channelMessageRecordRepository;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testSelectById() {
        // Arrange
        Long id = 1L;
        ChannelMessageRecordPO expectedPo = new ChannelMessageRecordPO();
        when(channelMessageRecordRepository.getById(id)).thenReturn(expectedPo);

        // Act
        ChannelMessageRecordPO result = channelMessageRecordRepository.getById(id);

        // Assert
        assertSame(expectedPo, result);
        verify(channelMessageRecordRepository).getById(id);
    }

    @Test
    public void testInsert() {
        // Arrange
        ChannelMessageRecordPO po = new ChannelMessageRecordPO();
        boolean expectedResult = true;
        when(channelMessageRecordRepository.save(po)).thenReturn(expectedResult);

        // Act
        boolean result = channelMessageRecordRepository.save(po);

        // Assert
        assertTrue(result);
        verify(channelMessageRecordRepository).save(po);
    }

    @Test
    public void testUpdateById() {
        // Arrange
        ChannelMessageRecordPO po = new ChannelMessageRecordPO();
        boolean expectedResult = true;
        when(channelMessageRecordRepository.updateById(po)).thenReturn(expectedResult);

        // Act
        boolean result = channelMessageRecordRepository.updateById(po);

        // Assert
        assertTrue(result);
        verify(channelMessageRecordRepository).updateById(po);
    }

    @Test
    public void testDeleteById() {
        // Arrange
        Long id = 1L;
        boolean expectedResult = true;
        when(channelMessageRecordRepository.removeById(id)).thenReturn(expectedResult);

        // Act
        boolean result = channelMessageRecordRepository.removeById(id);

        // Assert
        assertTrue(result);
        verify(channelMessageRecordRepository).removeById(id);
    }
}
