package com.chinatelecom.gs.privilege.service;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;



@Slf4j
@Service
public class AuthInfoSyncScheduler {

    @Resource
    private TeamService teamService;

    @Resource
    private UserService userService;


    public static final long taskPeriod = 300000;

    @Scheduled(fixedRate = taskPeriod, initialDelay = 10000) // 启动后延迟10s开始执行，每5分钟执行一次
    public void schedule() {
        teamService.construct();
        userService.construct();
    }
}
