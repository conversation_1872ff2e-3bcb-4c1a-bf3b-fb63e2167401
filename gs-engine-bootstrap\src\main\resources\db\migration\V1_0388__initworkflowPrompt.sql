INSERT INTO prompt_template( yn, code, app_code, name, description, content, model_param, create_id, create_name, create_time, update_id, update_name, update_time)
VALUES( 0, 'LLM_PROMPT_TEMPLATE', '0', '工作流-大模型节点prompt模板', '工作流-大模型节点prompt模板', '你是一个万能助手，根据下面的信息执行任务，用户提示信息：{{prompt}}，系统提示词：{{systemPrompt}},输出格式为：{{responseFormat}}', NULL, 'admin', '0', '2024-10-11 00:00:00', 'admin', 'admin', '2024-10-11 00:00:00');
INSERT INTO prompt_template( yn, code, app_code, name, description, content, model_param, create_id, create_name, create_time, update_id, update_name, update_time)
VALUES( 0, 'INTENT_CHOOSE_PROMPT_TEMPLATE', '0', '工作流-意图节点prompt模板', '工作流-意图节点prompt模板', '你是一个意图识别助手，帮助用户选择一个意图，意图列表如下：{{intents}}，用户要求如下：用户消息是：{{Query}}，选择意图后返回意图id(classificationId)和选择意图原因(reason);输出格式为一个标准json，格式如下：{"classificationId": 0, "reason": ""}，不要输出其他内容；{{systemPrompt}}', NULL, 'admin', '0', '2024-10-11 00:00:00', 'admin', 'admin', '2024-10-11 00:00:00');
INSERT INTO prompt_template( yn, code, app_code, name, description, content, model_param, create_id, create_name, create_time, update_id, update_name, update_time)
VALUES( 0, 'CHECK_SCRIPT_PROMPT_TEMPLATE', '0', '工作流-代码节点脚本检测prompt模板', '工作流-代码节点脚本检测prompt模板', '你是一名经验丰富的安全高级专家，擅长代码审查和安全评估。你的任务是对提供的{{scriptType}}代码进行深入的安全性和功能性审查，代码如下：\n {{script}} \n 以确定是否存在语法错误或可能导致程序无法正常终止的逻辑缺陷（如死循环）。请按照以下指导原则执行评估，并根据给定模板格式返回结果。\n \n 评估指南\n 语法与逻辑完整性\n 检查代码是否符合规范，确认所有语句都正确无误。\n 确认代码中不存在会导致程序无法正常结束的逻辑错误，特别是要警惕任何形式的无限循环。\n 防止无限循环\n 特别关注可能导致无限循环的代码段，确保所有循环都有适当的退出条件。\n 如果发现有无限循环的风险，详细说明原因及其影响。\n 总结评估\n 根据上述步骤的分析，给出最终的安全性和功能性评估结论。\n 使用JSON格式返回评估结果，确保输出严格遵循指定格式，不包含额外信息。\n 输出格式要求：\n {\n   \"checkResult\": false,  // 如果代码有导致程序无法正常结束的逻辑错误、安全问题或者语法错误，则设置为false,否则为true\n   \"vulnDetails\": \"\"      // 若存在任何问题，请在此处提供详细的描述，包括问题性质、位置及改进建议\n }', NULL, 'admin', '0', '2024-10-11 00:00:00', 'admin', 'admin', '2024-10-11 00:00:00');
