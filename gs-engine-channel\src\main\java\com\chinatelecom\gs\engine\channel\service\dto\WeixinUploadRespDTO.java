package com.chinatelecom.gs.engine.channel.service.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/1/17 18:10
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WeixinUploadRespDTO {

    private Integer errcode;

    private String errmsg;

    private String type;

    @JSONField(name = "media_id")
    private String mediaId;
}
