package com.chinatelecom.gs.engine.kms.common.report;

import com.chinatelecom.gs.engine.core.model.toolkit.core.dto.Response;
import com.chinatelecom.gs.engine.core.sdk.vo.llm.WrapLLMMessage;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import okhttp3.ResponseBody;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import retrofit2.Call;

import java.util.concurrent.CountDownLatch;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2025年05月21日
 */
@Slf4j
public class MultipleReportStreamHandler extends SseBaseStreamHandler {

    private final MultipleSseStream multipleSseStream;

    @Setter
    private CountDownLatch latch;

    public MultipleReportStreamHandler(MultipleSseStream multipleSseStream) {
        this.multipleSseStream = multipleSseStream;
    }

    @Override
    public void onNext(Call<ResponseBody> call, WrapLLMMessage token) {
        if (!safeCheckMsg(token)) {
            multipleSseStream.onNext(call, token);
        }
    }

    @Override
    public void onComplete(Response<WrapLLMMessage> response) {
        // 暂不处理
        latch.countDown();
    }

    @Override
    public void onError(Throwable error, Response<WrapLLMMessage> response) {
        log.error("大模型请求异常", error);
        latch.countDown();
        if (!safeCheckMsg(error)) {
            sendMessage(ReportMessage.ofError("大模型请求异常").toString());
            multipleSseStream.finallyComplete();
            multipleSseStream.abort(error);
        }
    }

    @Override
    protected SseEmitter getSseEmitter() {
        return multipleSseStream.getEmitter();
    }

    @Override
    protected void abort(Throwable e) {
        multipleSseStream.abort(e);
    }
}
