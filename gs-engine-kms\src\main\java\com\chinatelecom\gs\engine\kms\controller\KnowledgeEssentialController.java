package com.chinatelecom.gs.engine.kms.controller;


import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.kms.sdk.constants.KmsApis;
import com.chinatelecom.gs.engine.kms.sdk.vo.essential.KnowledgeIntroductionVO;
import com.chinatelecom.gs.engine.kms.service.KnowledgeEssentialAppService;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;


/**
 * <p>
 * 知识要素表 Controller
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-15
 */
@RestController
@Slf4j
@Tag(name = "知识要素表 Controller")
@RequestMapping(KmsApis.KMS_API + KmsApis.ESSENTIAL_API)
public class KnowledgeEssentialController {

    @Resource
    private KnowledgeEssentialAppService knowledgeEssentialAppService;

//    @ApiOperation(value = "知识要素表分页列表", response = KnowledgeEssentialPO.class)
//    @PostMapping(KmsApis.PAGE_API)
//    public Result<Page<KnowledgeEssentialVO>> page(@Validated @RequestBody KnowledgeEssentialQueryParam param) {
//
//        return Result.success(knowledgeEssentialAppService.pageQuery(param));
//    }
//
//    @ApiOperation(value = "知识要素表详情", response = KnowledgeEssentialPO.class)
//    @GetMapping(KmsApis.CODE_PATH)
//    public Result<KnowledgeEssentialVO> get(@PathVariable("code") String code) {
//        return Result.success(knowledgeEssentialAppService.get(code));
//    }
//
//    @ApiOperation(value = "知识要素表新增")
//    @PostMapping
//    public Result<Boolean> add(@Validated @RequestBody KnowledgeEssentialCreateParam createParam) {
//        return Result.success(knowledgeEssentialAppService.create(createParam));
//    }
//
//
//    @ApiOperation(value = "知识要素表更新")
//    @PutMapping(KmsApis.CODE_PATH)
//    public Result<Boolean> update(@PathVariable("code") String code, @Validated @RequestBody KnowledgeEssentialUpdateParam param) {
//        return Result.success(knowledgeEssentialAppService.update(code, param));
//    }
//
//
//    @ApiOperation(value = "知识要素表删除")
//    @PostMapping(KmsApis.DELETE_API)
//    public Result<Boolean> delete(@Validated @RequestBody CodeParam codes) {
//        return Result.success(knowledgeEssentialAppService.delete(codes));
//    }
//    @Operation( summary = "从路径导入知识", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})} )
//    @DebugLog(operation = "从路径导入知识")
//    @PostMapping(value = KmsApis.IMPORT_BY_KEY_API)

    @Operation(summary = "知识导读", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @GetMapping(KmsApis.KNOWLEDGE_CODE_PATH)
    @PlatformRestApi(name = "知识导读", groupName = "知识管理")
    @AuditLog(businessType = "知识管理", operType = "知识导读", operDesc = "知识导读", objId="#knowledgeCode")
    public Result<KnowledgeIntroductionVO> queryIntroduction(@PathVariable("knowledgeCode") String knowledgeCode) {
        return Result.success(knowledgeEssentialAppService.queryIntroduction(knowledgeCode));
    }

}


