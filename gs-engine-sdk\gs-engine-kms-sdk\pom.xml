<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.chinatelecom</groupId>
        <artifactId>gs-engine-sdk</artifactId>
        <version>2.0.0-SNAPSHOT</version>
    </parent>

    <groupId>com.chinatelecom</groupId>
    <artifactId>gs-engine-kms-sdk</artifactId>
    <version>2.0.0-SNAPSHOT</version>

    <dependencies>
        <dependency>
            <groupId>com.chinatelecom</groupId>
            <artifactId>gs-engine-sdk-common</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.security</groupId>
                    <artifactId>spring-security-crypto</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-crypto</artifactId>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>jakarta.servlet</groupId>
            <artifactId>jakarta.servlet-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <!--        <dependency>-->
<!--            <groupId>com.chinatelecom</groupId>-->
<!--            <artifactId>telecom-ai-search-open-sdk</artifactId>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.google.guava</groupId>-->
<!--            <artifactId>guava</artifactId>-->
<!--        </dependency>-->
    </dependencies>

</project>
