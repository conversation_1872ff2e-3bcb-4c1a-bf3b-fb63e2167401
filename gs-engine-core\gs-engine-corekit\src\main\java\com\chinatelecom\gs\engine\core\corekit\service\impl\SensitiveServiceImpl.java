package com.chinatelecom.gs.engine.core.corekit.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.afterturn.easypoi.exception.excel.ExcelImportException;
import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.chinatelecom.gs.engine.common.cache.RedisService;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.context.RequestInfo;
import com.chinatelecom.gs.engine.common.enums.DimensionEnum;
import com.chinatelecom.gs.engine.common.infra.base.impl.BaseExtendServiceImpl;
import com.chinatelecom.gs.engine.core.corekit.domain.po.SensitivePO;
import com.chinatelecom.gs.engine.core.corekit.domain.request.SensitiveExcelImportRequest;
import com.chinatelecom.gs.engine.core.corekit.domain.request.SensitivePageRequest;
import com.chinatelecom.gs.engine.core.corekit.domain.request.SensitiveRequest;
import com.chinatelecom.gs.engine.core.corekit.domain.response.SensitiveResponse;
import com.chinatelecom.gs.engine.core.corekit.mapper.SensitiveMapper;
import com.chinatelecom.gs.engine.core.corekit.ner.ACTrie;
import com.chinatelecom.gs.engine.core.corekit.ner.Emit;
import com.chinatelecom.gs.engine.core.corekit.service.SensitiveService;
import com.chinatelecom.gs.engine.core.manager.convert.ConfigConvert;
import com.chinatelecom.gs.engine.core.manager.service.BaseConfigAppService;
import com.chinatelecom.gs.engine.core.manager.vo.config.SensitiveConfig;
import com.chinatelecom.gs.engine.core.manager.vo.config.base.BaseConfigCreateParam;
import com.chinatelelcom.gs.engine.sdk.common.Page;
import com.chinatelelcom.gs.engine.sdk.common.PageImpl;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: Wei
 * @date: 2025-02-05 17:54
 */
@Slf4j
@Service
public class SensitiveServiceImpl  extends BaseExtendServiceImpl<SensitiveMapper, SensitivePO> implements SensitiveService {

    private final Map<String, ACTrie> sensitiveTrieMap = new ConcurrentHashMap<>();
    private static final String FILENAME = "/file/word.txt";
    private static final String SENSITIVE_SWITCH_PREFIX = "sensitive_switch:%s";

    @Resource
    private BaseConfigAppService configAppService;
    @Resource
    private RedisService redisService;

    /**
     * 敏感词前缀树初始化
     */
    @PostConstruct
    void init(){
        log.info("初始化敏感词前缀树");
        try{
            //设置请求上下文，绕过租户检查
            RequestInfo requestInfo = new RequestInfo();
            requestInfo.setTenant(false);
            RequestContext.set(requestInfo);
            syncSensitiveData();
        }finally {
            RequestContext.remove();
        }
    }

    @Override
    public Boolean saveSensitive(SensitiveRequest request) {
        saveCheck(request);
        SensitivePO sensitivePO = BeanUtil.copyProperties(request, SensitivePO.class);
        return this.baseMapper.insertOrUpdate(sensitivePO);
    }

    @Override
    public Boolean delete(List<String> ids) {
        return this.removeByIds(ids);
    }

    @Override
    public Page<SensitiveResponse> pageQuery(SensitivePageRequest request) {
        IPage<SensitivePO> page = new PageDTO<>(request.getPageNum(), request.getPageSize());
        IPage<SensitivePO> pageRecord = this.page(page, Wrappers.<SensitivePO>lambdaQuery()
                .eq(StringUtils.isNotEmpty(request.getSensitiveBaseCode()), SensitivePO::getSensitiveBaseCode, request.getSensitiveBaseCode())
                .like(StringUtils.isNotEmpty(request.getKeyword()), SensitivePO::getStandardWords, request.getKeyword())
                .or()
                .like(StringUtils.isNotEmpty(request.getKeyword()), SensitivePO::getStandardWords, request.getKeyword())
                .orderByDesc(SensitivePO::getUpdateTime)
                .orderByAsc(SensitivePO::getId)
        );

        PageImpl pageVo = new PageImpl();
        pageVo.setPages(pageRecord.getPages());
        pageVo.setCurrent(pageRecord.getCurrent());
        pageVo.setSize(pageRecord.getSize());
        pageVo.setTotal(pageRecord.getTotal());
        List<SensitiveResponse> records = pageRecord.getRecords().stream().map(record -> BeanUtil.copyProperties(record, SensitiveResponse.class)).collect(Collectors.toList());
        pageVo.setRecords(records);
        return pageVo;
    }

    @Override
    public SensitiveResponse detail(Long id) {
        SensitivePO sensitivePO = this.getById(id);
        SensitiveResponse sensitiveResponse = BeanUtil.copyProperties(sensitivePO, SensitiveResponse.class);
        return sensitiveResponse;
    }

    private SensitiveConfig getSensitiveConfig(){
        String sensitiveConfigStr = redisService.get(SENSITIVE_SWITCH_PREFIX.formatted(RequestContext.getTenantId()));
        SensitiveConfig sensitiveConfig;
        if(StringUtils.isBlank(sensitiveConfigStr)){
            sensitiveConfig = configAppService.getConfigOrDefault(ConfigConvert.SENSITIVE_CONFIG, RequestContext.getTenantId());
            //存入缓存 过期时间30分钟 敏感词匹配时获取
            redisService.setEx(SENSITIVE_SWITCH_PREFIX.formatted(RequestContext.getTenantId()), JSON.toJSONString(sensitiveConfig), 30L, TimeUnit.MINUTES);
        }else {
            sensitiveConfig = JSON.parseObject(sensitiveConfigStr, SensitiveConfig.class);
        }
        return sensitiveConfig;
    }

    @Override
    public List<String> matchSensitiveValue(String content) {
        SensitiveConfig sensitiveConfig = getSensitiveConfig();
        if(!sensitiveConfig.getEnable()){
            return Collections.emptyList();
        }
        RequestInfo requestInfo = RequestContext.get();
        if(!sensitiveTrieMap.isEmpty() && sensitiveTrieMap.containsKey(requestInfo.getAppCode())){
            ACTrie acTrie = sensitiveTrieMap.get(requestInfo.getAppCode());
            Collection<Emit> emits = acTrie.parseText(content);
            if (CollectionUtils.isNotEmpty(emits)){
                List<String> matchValues = emits.stream().map(Emit::getKeyword).collect(Collectors.toList());
                log.info("【敏感词】匹配命中:{}", matchValues);
                return matchValues;
            }
        }
        return Collections.emptyList();
    }

    @Override
    public List<String> matchSensitiveValueWithoutSwitch(String content) {
        RequestInfo requestInfo = RequestContext.get();
        if(!sensitiveTrieMap.isEmpty() && sensitiveTrieMap.containsKey(requestInfo.getAppCode())){
            ACTrie acTrie = sensitiveTrieMap.get(requestInfo.getAppCode());
            Collection<Emit> emits = acTrie.parseText(content);
            if (CollectionUtils.isNotEmpty(emits)){
                List<String> matchValues = emits.stream().map(Emit::getKeyword).collect(Collectors.toList());
                log.info("【敏感词】匹配命中:{}", matchValues);
                return matchValues;
            }
        }
        return Collections.emptyList();
    }

    @Override
    public void downloadTemplate(HttpServletResponse response) throws IOException {
        OutputStream outputStream = null;
        try{
            // 创建导出参数
            ExportParams params = new ExportParams("敏感词导入模版", "sheet1", ExcelType.XSSF);
            // 导出Excel文件
            Workbook workbook = ExcelExportUtil.exportExcel(params, SensitiveExcelImportRequest.class, Lists.newArrayList());
            String fileName = URLEncoder.encode("敏感词导入模版.xlsx", "UTF-8");
            // 输出文件流
            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
            outputStream = response.getOutputStream();
            workbook.write(outputStream);
            outputStream.flush();
            workbook.close();
        }finally{
            if(outputStream != null){
                outputStream.close();
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean upload(MultipartFile file) throws Exception {
        ImportParams params = new ImportParams();
        params.setTitleRows(1); // 设置标题行数量，如果你的数据从第二行开始，这里设置为1（可选）
        params.setHeadRows(1);  // 设置头信息行数，如果你的列名在第一行，这里设置为1（可选）
        params.setImportFields(new String[]{"标准词*", "同义词（使用,分割）*"});
        ExcelImportResult<SensitiveExcelImportRequest> importResult = null;
        try {
            importResult = ExcelImportUtil.importExcelMore(file.getInputStream(), SensitiveExcelImportRequest.class, params);
        } catch (ExcelImportException exception){
            if(exception.getMessage().contains("模板")){
                throw new BizException("A0001", "模版错误，请使用系统下载的模版");
            }else {
                throw exception;
            }
        }
        List<SensitiveExcelImportRequest> list = importResult.getList();
        //数据校验
        checkImportData(list);
        //过滤空数据
        List<SensitivePO> saveData = list.stream()
                .filter(data -> StringUtils.isNotBlank(data.getStandardWords()) || StringUtils.isNotBlank(data.getSynonymousWords()))
                .map(data -> {
                    SensitivePO sensitivePO = buildImportData(data);
                    return sensitivePO;
                }).collect(Collectors.toList());
        return saveOrUpdateBatch(saveData);
    }

    @Override
    public void syncSensitiveData() {
        sensitiveTrieMap.clear();
        List<SensitivePO> list = this.list(Wrappers.emptyWrapper());
        Map<String, List<SensitivePO>> listMap = list.stream().collect(Collectors.groupingBy(SensitivePO::getAppCode));
        listMap.forEach((tenantId, datas) -> {
            ACTrie acTrie = new ACTrie();
            Set<String> allSensitiveKeywords = new HashSet<>(1000);
            datas.forEach(keywords ->{
                allSensitiveKeywords.add(keywords.getStandardWords());
                if(StringUtils.isNotBlank(keywords.getSynonymousWords())){
                    ArrayList<String> synonymousWords = Lists.newArrayList(keywords.getSynonymousWords().split(","));
                    allSensitiveKeywords.addAll(synonymousWords);
                }
            });
            allSensitiveKeywords.forEach(acTrie::addKeyword);
            sensitiveTrieMap.put(tenantId, acTrie);
        });
    }

    @Override
    public Boolean enableSensitive(Boolean enable) {
        SensitiveConfig sensitiveConfig = configAppService.getConfigOrDefault(ConfigConvert.SENSITIVE_CONFIG, RequestContext.getTenantId());
        sensitiveConfig.setEnable(enable);
        BaseConfigCreateParam param = new BaseConfigCreateParam();
        param.setBusinessNo(RequestContext.getTenantId());
        param.setName("敏感词开关配置");
        param.setDimension(DimensionEnum.TENANT);
        param.setConfigType(ConfigConvert.SENSITIVE_CONFIG);
        param.setConfigData(JSON.toJSONString(sensitiveConfig));
        configAppService.createOrUpdate(param);
        //存入缓存 过期时间30分钟 敏感词匹配时获取
        redisService.setEx(SENSITIVE_SWITCH_PREFIX.formatted(RequestContext.getTenantId()), param.getConfigData(), 30L, TimeUnit.MINUTES);
        return Boolean.TRUE;
    }

    @Override
    @Transactional
    public void initSensitiveData() {
        try {
            log.info("开始初始化租户：{}，敏感词", RequestContext.getTenantId());
            ClassPathResource resource = new ClassPathResource(FILENAME);
            String text = IOUtils.toString(resource.getInputStream(), StandardCharsets.UTF_8).replaceAll("[\\r\\n]+", "");
            String[] split = text.split(";");
            List<SensitivePO> list = this.list(Wrappers.emptyWrapper());
            Map<String, SensitivePO> sensitivePOMap = list.stream().collect(Collectors.toMap(SensitivePO::getStandardWords, Function.identity(), (v1, v2) -> v1));
            List<SensitivePO> sensitivePOList = Arrays.stream(split)
                    .filter(word -> StringUtils.isNotBlank(word) && !sensitivePOMap.containsKey(word))
                    .map(word -> {
                            SensitivePO sensitivePO = new SensitivePO();
                            sensitivePO.setStandardWords(word);
                            return sensitivePO;
            }).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(sensitivePOList)){
                this.saveBatch(sensitivePOList);
            }
            log.info("初始化租户：{}，敏感词结束", RequestContext.getTenantId());
        } catch (IOException e) {
            log.error("敏感词获取异常！", e);
        }
    }

    /**
     * 导入如数校验
     * @param list
     */
    private void checkImportData(List<SensitiveExcelImportRequest> list) {
        list.forEach(data -> {
            if(StringUtils.isEmpty(data.getStandardWords())){
                throw new BizException("A0065", "标准词不能为空");
            }
        });
    }

    private SensitivePO buildImportData(SensitiveExcelImportRequest request) {
        SensitivePO sensitivePO = this.getOne(Wrappers.<SensitivePO>lambdaQuery().eq(SensitivePO::getStandardWords, request.getStandardWords()).last("limit 1"));
        if(Objects.isNull(sensitivePO)){
            sensitivePO = new SensitivePO();
        }
        sensitivePO.setStandardWords(request.getStandardWords());
        sensitivePO.setSynonymousWords(request.getSynonymousWords());
        return sensitivePO;
    }

    private void saveCheck(SensitiveRequest request){
        Long count = baseMapper.selectCount(Wrappers.<SensitivePO>lambdaQuery()
                .eq(SensitivePO::getStandardWords, request.getStandardWords())
                .ne(Objects.nonNull(request.getId()), SensitivePO::getId, request.getId()));
        if (count > 0) {
            throw new BizException("A0066","标准词已存在");
        }
    }
}
