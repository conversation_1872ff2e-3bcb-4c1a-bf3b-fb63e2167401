package com.chinatelecom.gs.engine.core.model.toolkit.adapter.multi;

import com.chinatelecom.gs.engine.core.sdk.vo.llm.BaseLLMRequest;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

@Data
public class MultiLLMRequest implements BaseLLMRequest {


    private double temperature = 0;

    private List<String> images;

    private List<String> queries;

    /**
     * 获取输入内容
     *
     * @return
     */
    @Override
    public String inputContent() {
        StringBuilder inputBuilder = new StringBuilder();
        if(CollectionUtils.isNotEmpty(queries)){
            for(String query: queries){
                inputBuilder.append(query);
            }
        }
        return inputBuilder.toString();
    }
}
