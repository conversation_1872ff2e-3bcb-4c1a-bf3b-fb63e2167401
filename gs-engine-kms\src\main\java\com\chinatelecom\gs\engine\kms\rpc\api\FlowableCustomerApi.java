package com.chinatelecom.gs.engine.kms.rpc.api;

import com.chinatelecom.tele.flowable.api.model.pager.PagerResult;
import com.chinatelecom.tele.flowable.api.model.vo.BaseResponse;
import com.chinatelecom.tele.flowable.api.request.FlowStartRequest;
import com.chinatelecom.tele.flowable.api.response.ModelInfoQueryResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2025年01月10日
 */

@FeignClient(name = "tele-flowable", contextId = "flowableCustomerApi", url = "${feign.service.url.tele-flowable:#{null}}")
public interface FlowableCustomerApi {


    @Operation(summary = "发起流程", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "审批管理")})})
    @PostMapping({"/flowable/api/startFlow"})
    BaseResponse<String> startFlow(@RequestHeader("appCode") String appCode,
                                   @RequestHeader("appSecret") String appSecret,
                                   @Validated @RequestBody FlowStartRequest request);


    @GetMapping({"/flowable/api/getPageModelInfo"})
    BaseResponse<PagerResult<ModelInfoQueryResponse>> getPageModelInfo(@RequestHeader("appCode") String appCode,
                                                                       @RequestHeader("appSecret") String appSecret,
                                                                       @RequestParam("status") Integer status,
                                                                       @RequestParam("appSn") String appSn,
                                                                       @RequestParam("tenantId") String tenantId,
                                                                       @RequestParam("page") Integer page,
                                                                       @RequestParam("pageSize") Integer pageSize);


}
