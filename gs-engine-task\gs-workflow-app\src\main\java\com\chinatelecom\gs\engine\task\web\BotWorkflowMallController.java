package com.chinatelecom.gs.engine.task.web;

import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.common.auth.KsMenuConfig;
import com.chinatelecom.gs.engine.common.auth.MenuConfig;
import com.chinatelecom.gs.engine.common.auth.PermissionTag;
import com.chinatelecom.gs.engine.common.constants.Constants;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.task.sdk.TaskApis;
import com.chinatelecom.gs.workflow.core.domain.param.BotWorkflowDetailRsp;
import com.chinatelecom.gs.workflow.core.domain.param.BotWorkflowMallRsp;
import com.chinatelecom.gs.workflow.core.domain.param.WorkflowMallPageRequest;
import com.chinatelecom.gs.workflow.core.domain.param.WorkflowMallShelveRequest;
import com.chinatelecom.gs.workflow.core.service.BotWorkflowMallInfoConfigService;
import com.chinatelelcom.gs.engine.sdk.common.Page;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


/**
 * <AUTHOR>
 */
@Slf4j
@Tag(name = "业务流市场")
@RestController
@Validated
@PermissionTag(code = {MenuConfig.FLOW_MARKET, KsMenuConfig.FLOW_MARKET})
@RequestMapping(TaskApis.TASK_API + Constants.WEB_PREFIX + "/workflowMall")
public class BotWorkflowMallController {

    @Autowired
    private BotWorkflowMallInfoConfigService botWorkflowMallInfoConfigService;

    @Operation(summary = "上架业务流", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "业务流管理")})})
    @PlatformRestApi(name = "上架业务流", groupName = "业务流商店")
    @PostMapping("/shelve")
    @AuditLog(businessType = "业务流市场", operType = "上架业务流", operDesc = "上架业务流", objId = "#request.workflowId")
    public Result<Boolean> shelve(@RequestBody WorkflowMallShelveRequest request) {
        return Result.success(botWorkflowMallInfoConfigService.shelve(request));
    }

    @Operation(summary = "下架业务流", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "业务流管理")})})
    @PlatformRestApi(name = "下架业务流", groupName = "业务流商店")
    @PostMapping("/unshelve")
    @AuditLog(businessType = "业务流市场", operType = "下架业务流", operDesc = "下架业务流", objId = "#request.workflowId")
    public Result<Boolean> unshelve(@RequestBody WorkflowMallShelveRequest request) {
        return Result.success(botWorkflowMallInfoConfigService.unshelve(request));
    }

    @Operation(summary = "查询已上架业务流", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "业务流管理")})})
    @PlatformRestApi(name = "查询已上架业务流", groupName = "业务流商店")
    @PostMapping("/page")
    @AuditLog(businessType = "业务流市场", operType = "查询已上架业务流", operDesc = "查询已上架业务流", objId = "null")
    public Result<Page<BotWorkflowMallRsp>> page(@Validated @RequestBody WorkflowMallPageRequest request) {
        return Result.success(botWorkflowMallInfoConfigService.page(request));
    }

    @Operation(summary = "查询已上架业务流详情", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "业务流管理")})})
    @PlatformRestApi(name = "查询已上架业务流详情", groupName = "业务流商店")
    @GetMapping("/detail")
    @AuditLog(businessType = "业务流市场", operType = "查询已上架业务流详情", operDesc = "查询已上架业务流详情", objId = "#workflowId")
    public Result<BotWorkflowDetailRsp> detail(@RequestParam String workflowId) {
        return Result.success(botWorkflowMallInfoConfigService.detail(workflowId));
    }
}
