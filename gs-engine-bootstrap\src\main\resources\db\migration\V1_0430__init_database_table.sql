CREATE TABLE IF NOT EXISTS `prompt` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增 id',
    `prompt_code` varchar(255) NOT NULL COMMENT '提示词code',
    `prompt_name` varchar(255) NOT NULL COMMENT '提示词名称',
    `prompt_type` int NOT NULL COMMENT '提示词类型',
    `prompt_content` varchar(1000) NOT NULL COMMENT '提示词名称',
    `yn` int NOT NULL DEFAULT '0',
    `tenant_id` varchar(255)  DEFAULT NULL ,
    `create_id`    varchar(100)    NOT NULL DEFAULT '1' COMMENT '创建用户ID',
    `create_name`  varchar(256)    NOT NULL DEFAULT '' COMMENT '记录创建人名称',
    `update_id`    varchar(100)    NOT NULL DEFAULT '1' COMMENT '最后修改用户ID',
    `update_name`  varchar(256)    NOT NULL DEFAULT '' COMMENT '最后一次记录操作人',
    `create_time`  datetime(3)     NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '记录创建时间',
    `update_time`  datetime(3)     NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '记录最后一次更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_prompt_code` (`prompt_code`) COMMENT '提示词code索引',
    KEY `idx_prompt_name` (`prompt_name`) COMMENT '提示词名称索引',
    KEY `idx_create_time` (`create_time`) COMMENT '创建时间索引'
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


CREATE TABLE IF NOT EXISTS `bot_database` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增 id',
    `db_code` varchar(255) NOT NULL COMMENT '数据库code',
    `name` varchar(255) NOT NULL COMMENT '名称',
    `type` varchar(255) NOT NULL COMMENT '类型',
    `description` varchar(1000) NULL COMMENT '描述',
    `config` varchar(1000) NULL COMMENT '数据库配置',
    `yn` int NOT NULL DEFAULT '0',
    `tenant_id` varchar(255)  DEFAULT NULL ,
    `create_id`    varchar(100)    NOT NULL DEFAULT '1' COMMENT '创建用户ID',
    `create_name`  varchar(256)    NOT NULL DEFAULT '' COMMENT '记录创建人名称',
    `update_id`    varchar(100)    NOT NULL DEFAULT '1' COMMENT '最后修改用户ID',
    `update_name`  varchar(256)    NOT NULL DEFAULT '' COMMENT '最后一次记录操作人',
    `create_time`  datetime(3)     NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '记录创建时间',
    `update_time`  datetime(3)     NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '记录最后一次更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_db_code` (`db_code`) COMMENT '数据库code索引',
    KEY `idx_name` (`name`) COMMENT '名称索引',
    KEY `idx_create_time` (`create_time`) COMMENT '创建时间索引'
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


CREATE TABLE IF NOT EXISTS `bot_database_table` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增 id',
    `db_code` varchar(255) NOT NULL COMMENT '数据库code',
    `table_code` varchar(255) NOT NULL COMMENT '表code',
    `name` varchar(255) NOT NULL COMMENT '名称',
    `name_for_llm` varchar(255) NULL COMMENT '中文名称',
    `description` varchar(1000) NULL COMMENT '表描述',
    `row_num` int NOT NULL COMMENT '行数量',
    `column_num` int NOT NULL COMMENT '列数量',
    `limit_column_num` int NULL COMMENT '行数量限制',
    `original_file_name` varchar(100) NULL COMMENT '原始文件名称',
    `file_key` varchar(100) NULL COMMENT '上传文件key',
    `state` varchar(100) NULL COMMENT '校验状态',
    `yn` int NOT NULL DEFAULT '0',
    `tenant_id` varchar(255)  DEFAULT NULL ,
    `create_id`    varchar(100)    NOT NULL DEFAULT '1' COMMENT '创建用户ID',
    `create_name`  varchar(256)    NOT NULL DEFAULT '' COMMENT '记录创建人名称',
    `update_id`    varchar(100)    NOT NULL DEFAULT '1' COMMENT '最后修改用户ID',
    `update_name`  varchar(256)    NOT NULL DEFAULT '' COMMENT '最后一次记录操作人',
    `create_time`  datetime(3)     NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '记录创建时间',
    `update_time`  datetime(3)     NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '记录最后一次更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_table_code` (`table_code`) COMMENT '数据库code索引',
    KEY `idx_name` (`name`) COMMENT '名称索引',
    KEY `idx_create_time` (`create_time`) COMMENT '创建时间索引'
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE IF NOT EXISTS `bot_table_column` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增 id',
    `db_code` varchar(255) NOT NULL COMMENT '数据库code',
    `table_code` varchar(255) NOT NULL COMMENT '表code',
    `column_code` varchar(255) NOT NULL COMMENT '字段code',
    `column_name` varchar(255) NOT NULL COMMENT '字段名称',
    `name_for_llm` varchar(255) NULL COMMENT '字段中文名称',
    `description` varchar(1000) NULL COMMENT '字段描述',
    `column_type` varchar(128) NOT NULL COMMENT '字段类型',
    `column_index` int NOT NULL COMMENT '列排序',
    `column_example` varchar(2000) NULL COMMENT '行示例',
    `column_llm_input` varchar(1000) NULL COMMENT '输入',
    `llm_visible` int NULL COMMENT 'llm可见的',
    `synonyms` varchar(1000) NULL COMMENT '同义词',
    `unit` varchar(128) NULL COMMENT '单位',
    `yn` int NOT NULL DEFAULT '0',
    `tenant_id` varchar(255)  DEFAULT NULL ,
    `create_id`    varchar(100)    NOT NULL DEFAULT '1' COMMENT '创建用户ID',
    `create_name`  varchar(256)    NOT NULL DEFAULT '' COMMENT '记录创建人名称',
    `update_id`    varchar(100)    NOT NULL DEFAULT '1' COMMENT '最后修改用户ID',
    `update_name`  varchar(256)    NOT NULL DEFAULT '' COMMENT '最后一次记录操作人',
    `create_time`  datetime(3)     NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '记录创建时间',
    `update_time`  datetime(3)     NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '记录最后一次更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_db_code` (`db_code`) COMMENT 'dbCode索引',
    KEY `idx_table_code` (`table_code`) COMMENT 'tableCode索引',
    KEY `idx_column_code` (`column_code`) COMMENT '字段code索引',
    KEY `idx_column_name` (`column_name`) COMMENT '名称索引',
    KEY `idx_create_time` (`create_time`) COMMENT '创建时间索引'
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


CREATE TABLE IF NOT EXISTS `bot_database_upload` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增 id',
    `db_code` varchar(255) NOT NULL COMMENT '数据库code',
    `table_code` varchar(255) NOT NULL COMMENT '表code',
    `file_code` varchar(255) NOT NULL COMMENT '文件code',
    `name` varchar(255) NOT NULL COMMENT '名称',
    `size` long NOT NULL COMMENT '文件大小',
    `extension` varchar(255) NULL COMMENT '后缀',
    `mimeTyp` varchar(1000) NULL COMMENT '文件类型',
    `file_key` varchar(1000) NULL COMMENT '文件key',
    `yn` int NOT NULL DEFAULT '0',
    `tenant_id` varchar(255)  DEFAULT NULL ,
    `create_id`    varchar(100)    NOT NULL DEFAULT '1' COMMENT '创建用户ID',
    `create_name`  varchar(256)    NOT NULL DEFAULT '' COMMENT '记录创建人名称',
    `update_id`    varchar(100)    NOT NULL DEFAULT '1' COMMENT '最后修改用户ID',
    `update_name`  varchar(256)    NOT NULL DEFAULT '' COMMENT '最后一次记录操作人',
    `create_time`  datetime(3)     NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '记录创建时间',
    `update_time`  datetime(3)     NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '记录最后一次更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_db_code` (`db_code`) COMMENT '数据库code索引',
    KEY `idx_file_code` (`file_code`) COMMENT '数据库code索引',
    KEY `idx_name` (`name`) COMMENT '名称索引',
    KEY `idx_create_time` (`create_time`) COMMENT '创建时间索引'
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


CREATE TABLE IF NOT EXISTS `agent_database_bind` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增 id',
    `agent_code` varchar(255) NOT NULL COMMENT 'agentCode',
    `db_code` varchar(255) NOT NULL COMMENT '数据库code',
    `name` varchar(255) NOT NULL COMMENT '数据库名称',
    `agent_version` int NOT NULL comment '版本',
    `yn` int NOT NULL DEFAULT '0',
    `app_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '空间id',
    `tenant_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
    `create_id` varchar(255) NOT NULL,
    `create_name` varchar(255) NOT NULL,
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_id` varchar(255) NOT NULL,
    `update_name` varchar(255) NOT NULL,
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_db_code` (`db_code`) COMMENT '工作流名称索引',
    KEY `idx_agent_code` (`agent_code`) COMMENT '工作流名称索引',
    KEY `idx_app_code` (`app_code`) COMMENT '空间id索引',
    KEY `idx_create_time` (`create_time`) COMMENT '创建时间索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
