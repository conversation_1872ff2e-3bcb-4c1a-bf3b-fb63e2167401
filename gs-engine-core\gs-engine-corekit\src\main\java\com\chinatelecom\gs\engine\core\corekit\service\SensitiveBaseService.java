package com.chinatelecom.gs.engine.core.corekit.service;

import com.chinatelecom.gs.engine.common.infra.base.BaseExtendService;
import com.chinatelecom.gs.engine.core.corekit.domain.po.SensitiveBasePO;
import com.chinatelecom.gs.engine.core.corekit.domain.request.SensitiveBasePageRequest;
import com.chinatelecom.gs.engine.core.corekit.domain.request.SensitiveBaseRequest;
import com.chinatelecom.gs.engine.core.corekit.domain.response.SensitiveBaseResponse;
import com.chinatelecom.gs.engine.core.corekit.domain.response.SensitiveResponse;
import com.chinatelelcom.gs.engine.sdk.common.Page;

import javax.validation.Valid;
import java.util.List;

public interface SensitiveBaseService extends BaseExtendService<SensitiveBasePO> {
    Boolean saveSensitiveBase(@Valid SensitiveBaseRequest request);

    Boolean delete(List<String> codes);

    Page<SensitiveBaseResponse> pageQuery(SensitiveBasePageRequest request);
}
