package com.chinatelecom.gs.engine.task.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinatelecom.gs.engine.common.constants.Constants;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.context.RequestInfo;
import com.chinatelecom.gs.engine.common.mq.impl.KafkaMQService;
import com.chinatelecom.gs.engine.common.utils.BizAssert;
import com.chinatelecom.gs.engine.common.utils.CheckImgUtils;
import com.chinatelecom.gs.engine.common.utils.DataResourceAccess;
import com.chinatelecom.gs.engine.common.utils.JsonUtils;
import com.chinatelecom.gs.engine.kms.dto.TagDTO;
import com.chinatelecom.gs.engine.kms.repository.TagAppRepository;
import com.chinatelecom.gs.engine.kms.repository.TagRelationRepository;
import com.chinatelecom.gs.engine.kms.sdk.api.KmsKnowledgeBaseApi;
import com.chinatelecom.gs.engine.kms.sdk.enums.KmsRelType;
import com.chinatelecom.gs.engine.kms.sdk.enums.TargetType;
import com.chinatelecom.gs.engine.kms.sdk.vo.common.CodeBatchParam;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.base.KBPublishStatisticsVO;
import com.chinatelecom.gs.engine.robot.manage.info.dao.service.AgentWorkflowBindService;
import com.chinatelecom.gs.engine.robot.manage.info.domain.po.AgentWorkflowBindPO;
import com.chinatelecom.gs.engine.robot.manage.info.service.AgentBasicConfigService;
import com.chinatelecom.gs.engine.robot.sdk.dto.Result;
import com.chinatelecom.gs.engine.robot.sdk.v2.spi.agent.client.AgentInfoRpcApi;
import com.chinatelecom.gs.engine.robot.sdk.v2.spi.kms.AgentKmsApi;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.entity.TagEntity;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.kms.request.DeleteKnowledgeBaseRequest;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.kms.request.SubscribeKnowledgeRequest;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.model.UpdateStrategyInfo;
import com.chinatelecom.gs.engine.task.sdk.entity.Condition;
import com.chinatelecom.gs.engine.task.sdk.entity.Param;
import com.chinatelecom.gs.engine.task.sdk.enums.WorkStatusEnum;
import com.chinatelecom.gs.engine.task.sdk.enums.WorkflowTypeEnum;
import com.chinatelecom.gs.engine.task.sdk.param.QueryWorkflowParam;
import com.chinatelecom.gs.engine.task.sdk.x6.Edge;
import com.chinatelecom.gs.engine.task.sdk.x6.Node;
import com.chinatelecom.gs.privilege.common.dto.GrantObjectDTO;
import com.chinatelecom.gs.privilege.common.dto.ResourceDTO;
import com.chinatelecom.gs.privilege.common.enums.PrivilegeEnum;
import com.chinatelecom.gs.privilege.common.enums.ResourceTypeEnum;
import com.chinatelecom.gs.privilege.util.PrivilegeUtil;
import com.chinatelecom.gs.privilege.util.dto.PrivilegeResources;
import com.chinatelecom.gs.workflow.core.dao.service.*;
import com.chinatelecom.gs.workflow.core.domain.param.*;
import com.chinatelecom.gs.workflow.core.domain.po.*;
import com.chinatelecom.gs.workflow.core.service.BotWorkflowConfigService;
import com.chinatelecom.gs.workflow.core.service.FileService;
import com.chinatelecom.gs.workflow.core.service.WorkFlowVariableAppService;
import com.chinatelecom.gs.workflow.core.workflow.core.DagParser;
import com.chinatelecom.gs.workflow.core.workflow.core.constant.TopicConstants;
import com.chinatelecom.gs.workflow.core.workflow.core.enums.*;
import com.chinatelecom.gs.workflow.core.workflow.core.model.biz.param.KnowledgeNodeBizParam;
import com.chinatelecom.gs.workflow.core.workflow.core.model.dag.Dag;
import com.chinatelecom.gs.workflow.core.workflow.core.model.dag.DagEvent;
import com.chinatelecom.gs.workflow.core.workflow.core.model.node.DagNode;
import com.chinatelelcom.gs.engine.sdk.common.enums.AppSourceType;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import com.chinatelelcom.gs.engine.sdk.common.utils.SpringContextUtils;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.hc.core5.net.URIBuilder;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.net.URISyntaxException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class BotWorkflowConfigServiceImpl implements BotWorkflowConfigService {

    private static final Logger log = LoggerFactory.getLogger(BotWorkflowConfigServiceImpl.class);

    @Autowired
    private DagParser dagParser;

    @Autowired
    private IBotWorkflowService IBotWorkflowService;

    @Autowired
    private IBotWorkflowNodeService IBotWorkflowNodeService;

    @Autowired
    private IBotWorkflowEdgeService IBotWorkflowEdgeService;

    @Autowired
    private IWorkflowVersionService IWorkflowVersionService;

    @Autowired
    private BotWorkflowConfigService botWorkflowConfigService;

    @Autowired
    private IBotWorkflowSceneService IBotWorkflowSceneService;

    @Autowired
    private KafkaMQService kafkaMQService;

    @Resource
    private AgentInfoRpcApi agentInfoRpcApi;

    @Autowired
    private DataResourceAccess dataResourceAccess;

    @Resource
    private AgentKmsApi agentKmsApi;

    @Autowired
    private IBotWorkflowIntentRelationService IBotWorkflowIntentRelationService;

    @Resource
    private WorkFlowVariableAppService workFlowVariableAppService;

    @Autowired
    private TagAppRepository tagAppRepository;

    @Autowired
    private TagRelationRepository tagRelationRepository;

    @Autowired
    private FileService fileService;

    @Autowired
    private IBotWorkflowMallService botWorkflowMallService;

    @Autowired
    private AgentWorkflowBindService agentWorkflowBindService;

    @Autowired
    private AgentBasicConfigService agentBasicInfoService;

    @Autowired
    private PrivilegeUtil privilegeUtil;

    @Autowired
    private KmsKnowledgeBaseApi kmsKnowledgeBaseApi;

    private String param = "[{\"name\":\"answerType\",\"paramType\":1},{\"name\":\"content\",\"paramType\":5},{\"name\":\"instructions\",\"paramType\":10,\"subParameters\":[{\"name\":\"code\",\"paramType\":1},{\"name\":\"values\",\"paramType\":5}]},{\"name\":\"reasoning\",\"paramType\":5,\"subParameters\":[{\"name\":\"reasoningContent\",\"paramType\":1},{\"name\":\"cost\",\"paramType\":1}]},{\"name\":\"extraData\",\"paramType\":5},{\"name\":\"messageId\",\"paramType\":1},{\"name\":\"responseType\",\"paramType\":1},{\"name\":\"outputs\",\"paramType\":5}]";

    private List<Param> parameters;

    @Value("${platform.super.tenant:SuperAis}")
    private String superTenant;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveBotWorkflow(BotWorkflowParam botWorkflowParam) {
        String workflowId = botWorkflowParam.getWorkflowId();
        RequestInfo userInfo = RequestContext.get();
        // 获取插件编辑版本
        Long editVersion = IWorkflowVersionService.getEditVersion(workflowId);
        // 先查询是否为默认策略
        BotWorkflowPO botWorkflowPO = IBotWorkflowService.queryByIdAndVersion(workflowId, editVersion,
                null, null);
        if (botWorkflowPO == null) {
            throw new BizException("AC007", "业务流不存在");
        }
        if ("1".equals(botWorkflowPO.getIsDefault())) {
            throw new BizException("AC052", "默认策略不允许编辑");
        }

        // 检查权限
        checkAuth(botWorkflowPO, userInfo);

        updateStrategyInfo(botWorkflowParam, botWorkflowPO);

        // 保存节点和边
        if (!CollectionUtils.isEmpty(botWorkflowParam.getNodes())) {
            // 删除旧的节点数据
            IBotWorkflowNodeService.deleteByWorkflowId(botWorkflowPO.getWorkflowId(), editVersion, userInfo.getAppCode());
            // 保存节点
            List<Node> nodes = botWorkflowParam.getNodes();
            List<BotWorkFlowNodePO> workFlowNodePOList = nodes.stream().map(node -> {
                checkKnowledge(node, workflowId);  //订阅知识库保存
                BotWorkFlowNodePO workFlowNodePO = new BotWorkFlowNodePO();
                workFlowNodePO.setNodeType(NodeTypeEnum.getByCode(node.getNodeType()));
                workFlowNodePO.setWorkflowId(workflowId);
                workFlowNodePO.setData(JSON.toJSONString(node.getData()));
                workFlowNodePO.setNodeId(node.getNodeId());
                workFlowNodePO.setCreateTime(LocalDateTime.now());
                workFlowNodePO.setCreateId(userInfo.getUserId());
                workFlowNodePO.setUpdateTime(LocalDateTime.now());
                workFlowNodePO.setUpdateId(userInfo.getUserId());
                workFlowNodePO.setVersion(editVersion);
                workFlowNodePO.setCreateName(userInfo.getUserName());
                workFlowNodePO.setUpdateName(userInfo.getUserName());
                workFlowNodePO.setAppCode(userInfo.getAppCode());
                return workFlowNodePO;
            }).collect(Collectors.toList());
            IBotWorkflowNodeService.saveBatch(workFlowNodePOList);
        }
        if (!CollectionUtils.isEmpty(botWorkflowParam.getEdges())) {
            // 删除旧的边数据
            IBotWorkflowEdgeService.deleteByWorkflowId(botWorkflowPO.getWorkflowId(), editVersion, userInfo.getAppCode());
            //保存边
            List<Edge> edges = botWorkflowParam.getEdges();
            List<BotWorkflowEdgePO> workflowEdgePOList = edges.stream().map(edge -> {
                BotWorkflowEdgePO workflowEdgePO = new BotWorkflowEdgePO();
                workflowEdgePO.setWorkflowId(workflowId);
                workflowEdgePO.setSourceNodeId(edge.getSourceNodeId() == null ? "" : edge.getSourceNodeId());
                workflowEdgePO.setTargetNodeId(edge.getTargetNodeId() == null ? "" : edge.getTargetNodeId());
                workflowEdgePO.setSourcePortId(edge.getSourcePortId());
                workflowEdgePO.setTargetPortId(edge.getTargetPortId());
                workflowEdgePO.setCreateTime(LocalDateTime.now());
                workflowEdgePO.setCreateId(userInfo.getUserId());
                workflowEdgePO.setUpdateTime(LocalDateTime.now());
                workflowEdgePO.setUpdateId(userInfo.getUserId());
                workflowEdgePO.setVersion(editVersion);
                workflowEdgePO.setCreateName(userInfo.getUserName());
                workflowEdgePO.setUpdateName(userInfo.getUserName());
                workflowEdgePO.setAppCode(userInfo.getAppCode());
                return workflowEdgePO;
            }).collect(Collectors.toList());
            IBotWorkflowEdgeService.saveBatch(workflowEdgePOList);
        }
        // 更新画布信息, 画布如果太长了, 则将画布数据存到对象存储服务中, 并删除旧数据
        if (botWorkflowPO.getCanvas() != null && botWorkflowPO.getCanvas().startsWith(Constants.FILE_TYPE)) {
            fileService.remove(botWorkflowPO.getCanvas());
        }
        String canvas = botWorkflowParam.getCanvas();
        // 判断画布是否产生变化
        String existedCanvas = botWorkflowPO.getCanvas();
        if (existedCanvas != null && existedCanvas.startsWith(Constants.FILE_TYPE)) {
            try {
                InputStream download = fileService.download(existedCanvas);
                existedCanvas = IOUtils.toString(download, "UTF-8");
                // 删除画布数据
            } catch (Exception e) {
                log.error("下载文件失败，fileKey:{}", existedCanvas, e);
            }
        }
        boolean updated = !Objects.equals(existedCanvas, botWorkflowParam.getCanvas());
        if (canvas != null && canvas.length() >= Constants.CANVAS_MAX_LENGTH) {
            try {
                String fileSerialNo = fileService.uploadCanvas(new ByteArrayInputStream(canvas.getBytes()), new ByteArrayInputStream(canvas.getBytes()));
                botWorkflowPO.setCanvas(fileSerialNo);
            } catch (Exception e) {
                throw new BizException("AC024", "画布数据保存失败");
            }
        } else {
            botWorkflowPO.setCanvas(canvas);
        }
        // debugStatus状态需要判断画布是否产生了变化
        if (updated) {
            botWorkflowPO.setDebugStatus(DebugStateEnum.UNEXECUTED.getCode());
        }
        // 更新状态, 如果是已经发布的工作流，则更新为修改待发布
        Integer status = Objects.equals(botWorkflowPO.getStatus(), PublishStateEnum.PUBLISHED.getCode()) ? PublishStateEnum.UPDATE_UNPUB.getCode() : botWorkflowPO.getStatus();
        botWorkflowPO.setStatus(status);
        botWorkflowPO.setUpdateTime(LocalDateTime.now());
        botWorkflowPO.setUpdateId(userInfo.getUserId());
        botWorkflowPO.setUpdateName(userInfo.getUserName());
        IBotWorkflowService.updateById(botWorkflowPO);
        //发送编辑mq
        sendEditMsg(userInfo, botWorkflowParam.getWorkflowId(), EventTypeEnum.UPDATE);
        return Boolean.TRUE;
    }

    private void updateStrategyInfo(BotWorkflowParam botWorkflowParam, BotWorkflowPO botWorkflowPO) {
        // 应答策略和机器人同步模型配置
        if (!botWorkflowPO.getWorkflowType().equals(WorkflowTypeEnum.USER_STRATEGY.getCode())) {
            return;
        }
        if (!botWorkflowParam.isNeedUpdateAgent()) {
            return;
        }
        if (!CollectionUtils.isNotEmpty(botWorkflowParam.getNodes())) {
            return;
        }
        // 过滤出策略节点
        List<Node> strategyNodes = botWorkflowParam.getNodes().stream().filter(node -> node.getNodeType().equals(NodeTypeEnum.DIALOG_STRATEGY.getCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(strategyNodes)) {
            return;
        }
        UpdateStrategyInfo updateStrategyInfo = new UpdateStrategyInfo();
        updateStrategyInfo.setWorkflowId(botWorkflowParam.getWorkflowId());
        updateStrategyInfo.setNodeList(JSON.toJSONString(strategyNodes));
        agentInfoRpcApi.updateStrategyInfo(updateStrategyInfo);
    }

    /**
     * 检查添加工作流知识库
     *
     * @param node       节点
     * @param workflowId 工作流id
     */
    private void checkKnowledge(Node node, String workflowId) {
        if (NodeTypeEnum.KNOWLEDGE.getCode().equals(node.getNodeType())) {
            KnowledgeNodeBizParam bizParam = JSON.parseObject(
                    JSON.toJSONString(node.getData().getInputs().getBizParam()), KnowledgeNodeBizParam.class);
            DeleteKnowledgeBaseRequest delRequest = new DeleteKnowledgeBaseRequest();
            delRequest.setAgentCode(workflowId);
            agentKmsApi.deleteByCode(delRequest);  //先清空旧数据
            if (Objects.nonNull(bizParam) && CollectionUtils.isNotEmpty(bizParam.getKmsList())) {
                for (KnowledgeNodeBizParam.KmsItem kmsItem : bizParam.getKmsList()) {
                    SubscribeKnowledgeRequest request = new SubscribeKnowledgeRequest();
                    request.setRelType(KmsRelType.FLOW.name());
                    request.setAgentCode(workflowId);
                    request.setKnowledgeBaseCode(kmsItem.getKnowledgeId());
                    request.setType(kmsItem.getKnowledgeType());
                    Result<Boolean> subRes = agentKmsApi.subscribe(request);
                    if (subRes == null || !subRes.isSuccess()) {
                        log.error("工作流订阅知识库失败, request:{}", JsonUtils.toJsonString(request));
                        BizAssert.throwBizException("AA031", "工作流订阅知识库失败");
                    }
                }
            }
        }
    }

    @Override
    public Node queryNodeByWorkIdNodeId(String workflowId, String nodeId, String appCode, boolean test) {
        Long version = null;
        if (test) {
            version = IWorkflowVersionService.getEditVersion(workflowId);
        } else {
            version = IWorkflowVersionService.getPublishVersion(workflowId);
        }

        BotWorkFlowNodePO workflowPO = IBotWorkflowNodeService.queryNodeByWorkIdNodeId(workflowId, nodeId, version, appCode);
        Node node = new Node();
        node.setNodeId(workflowPO.getNodeId());
        node.setNodeType(workflowPO.getNodeType().getCode());
        node.setData(JSON.parseObject(workflowPO.getData(), Node.NodeData.class));

        return node;
    }

    private boolean checkWorkflowIcon(String icon) {
        // 前端默认图标
        if ("/ais/common/f/images/defaultAvatar.png".equals(icon) || "/ais/ks/f/images/defaultAvatar.png".equals(icon)) {
            return false;
        }

        if (icon.startsWith("http") || icon.startsWith("https")) {
            String url = icon.replace(" ", "");
            try {
                URIBuilder uriBuilder = new URIBuilder(url);
                String path = uriBuilder.getPath();
                if (path.startsWith("/ais/workflow/web/file/download") && CheckImgUtils.isImageLink(icon)) {
                    return false;
                }
            } catch (URISyntaxException e) {
                return true;
            }
            return true;
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createBotWorkflow(BotWorkflowCreateParam botWorkflowParam) {
        if (StringUtils.isEmpty(botWorkflowParam.getWorkflowType())) {
            throw new BizException("AC022", "业务流类型不能为空");
        }
        List<PrivilegeResources> privilegeResources = privilegeUtil.checkResourcePrivileges(null, ResourceTypeEnum.WORKFLOW, null);
        Set<String> resourceCodes = privilegeResources.stream().map(PrivilegeResources::getResourceId).collect(Collectors.toSet());
        RequestInfo userInfo = RequestContext.get();

        if (checkWorkflowIcon(botWorkflowParam.getIconUrl())) {
            throw new BizException("AC049", "图标存在非法输入");
        }

        //1. 先检查名称是否重复
        checkName(botWorkflowParam, userInfo, null, resourceCodes);
        // 2. 先生成工作流id
        String workflowId = UUID.randomUUID().toString().replace("-", "");
        //3. 生成版本
        initConfig(workflowId);

        BotWorkflowPO botWorkflowPO = new BotWorkflowPO();
        BeanUtils.copyProperties(botWorkflowParam, botWorkflowPO);
        botWorkflowPO.setWorkflowId(workflowId);
        botWorkflowPO.setStatus(PublishStateEnum.UNPUBLISHED.getCode());
        botWorkflowPO.setBotRefCount(0);
        botWorkflowPO.setVersion(1L);
        botWorkflowPO.setDebugStatus(DebugStateEnum.UNEXECUTED.getCode());
        botWorkflowPO.setTenantId(userInfo.getTenantId());
        botWorkflowPO.setCreateTime(LocalDateTime.now());
        botWorkflowPO.setUpdateTime(LocalDateTime.now());
        botWorkflowPO.setCreateId(userInfo.getUserId());
        botWorkflowPO.setCreateName(userInfo.getUserName());
        botWorkflowPO.setUpdateId(userInfo.getUserId());
        botWorkflowPO.setUpdateName(userInfo.getUserName());
        botWorkflowPO.setAppCode(userInfo.getAppCode());
        if (!WorkflowTypeEnum.STRATEGY_TEMPLATE.getCode().equals(botWorkflowParam.getWorkflowType())) {
            botWorkflowPO.setSourceSystem(userInfo.getAppSourceType());
        }
        IBotWorkflowService.save(botWorkflowPO);
        // 处理标签
        handleTag(botWorkflowPO.getWorkflowId(), botWorkflowParam.getTagCodeList());

        saveOrUpdateAuth(workflowId);
        return workflowId;
    }

    private void handleTag(String promptCode, List<String> tagCodeList) {
        if (CollectionUtils.isNotEmpty(tagCodeList)) {
            tagCodeList.forEach(tagCode -> {
                TagDTO tagDTO = tagAppRepository.selectByCode(tagCode);
                if (tagDTO == null) {
                    throw new BizException("AA083", "标签不存在");
                }
            });
        }
        tagRelationRepository.saveTagRelation(TargetType.WORKFLOW, promptCode, tagCodeList);
    }

    /**
     * 保存或者修改资源权限
     *
     * @param workflowId   String
     */
    private void saveOrUpdateAuth(String workflowId) {
        ResourceDTO resourceDTO = new ResourceDTO();
        resourceDTO.setResourceId(workflowId);
        resourceDTO.setResourceType(ResourceTypeEnum.ROBOT);
        resourceDTO.setIsPublic(false);
        privilegeUtil.addResource(resourceDTO);
    }

    private void initConfig(String workflowId) {
        IWorkflowVersionService.add(workflowId, 1L, WorkStatusEnum.DEVELOP.getVersionStatus(), RuntimeStatusEnum.OFF_LINE.getValue());
    }

    private void checkName(BotWorkflowCreateParam botWorkflowParam, RequestInfo userInfo, Long version, Set<String> resourceCodes) {
        BotWorkflowUpdateParam botWorkflowUpdateParam = new BotWorkflowUpdateParam();
        if (botWorkflowParam instanceof BotWorkflowUpdateParam updateParam) {
            botWorkflowUpdateParam = updateParam;
        }
        String workflowCnName = botWorkflowParam.getWorkflowCnName();
        if (StringUtils.isEmpty(workflowCnName)) {
            throw new BizException("AC008", "名称不能为空");
        }
        if (workflowCnName.length() > 50) {
            throw new BizException("AC009", "名称最长不能超过50个字符");
        }
        List<BotWorkflowPO> workflowPOList;
        if (!WorkflowTypeEnum.STRATEGY_TEMPLATE.getCode().equals(botWorkflowParam.getWorkflowType())) {
            workflowPOList = IBotWorkflowService.queryWorkflowByName(workflowCnName, version, userInfo.getAppCode(), resourceCodes, userInfo.getAppSourceType().name());
        } else {
            workflowPOList = IBotWorkflowService.queryWorkflowByName(workflowCnName, version, userInfo.getAppCode(), resourceCodes, null);
        }

        if (CollectionUtils.isEmpty(workflowPOList)) {
            return;
        }
        if (version == null) {
            List<BotWorkflowPO> sameNameList = workflowPOList.stream().filter(botWorkflowPO -> botWorkflowPO.getCreateId().equals(userInfo.getUserId())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(sameNameList)) {
                throw new BizException("AC010", "该名称已存在");
            }
        } else {
            // 查询出业务流名称与更新的相同，则说明业务流名称没有变更，进行过滤
            BotWorkflowPO botWorkflowPO = IBotWorkflowService.queryByWorkflowIdAndVersion(botWorkflowUpdateParam.getWorkflowId(), version);
            if (!botWorkflowPO.getWorkflowCnName().equals(botWorkflowUpdateParam.getWorkflowCnName())) {
                // 业务流名称已经变更， 则判断是否与自己创建的业务流名称重复， 如果是则抛出异常
                List<BotWorkflowPO> sameNameList = workflowPOList.stream().filter(workflowPO -> workflowPO.getCreateId().equals(userInfo.getUserId())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(sameNameList)) {
                    throw new BizException("AC010", "该名称已存在");
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateBotWorkflow(BotWorkflowUpdateParam botWorkflowUpdateParam) {
        // 判断是否有工作流权限
        RequestInfo userInfo = RequestContext.get();
        if (checkWorkflowIcon(botWorkflowUpdateParam.getIconUrl())) {
            throw new BizException("AC049", "图标存在非法输入");
        }

        // 获取编辑版本
        Long editVersion = IWorkflowVersionService.getEditVersion(botWorkflowUpdateParam.getWorkflowId());
        // 先查询是否为默认策略
        BotWorkflowPO defaultWorkflowPO = IBotWorkflowService.queryByIdAndVersion(botWorkflowUpdateParam.getWorkflowId(), editVersion,
                null, null);
        if (defaultWorkflowPO != null && "1".equals(defaultWorkflowPO.getIsDefault())) {
            throw new BizException("AC052", "默认策略不允许编辑");
        }

        // 检查工作流是否存在
        BotWorkflowPO botWorkflowPO = IBotWorkflowService.queryByIdAndVersion(botWorkflowUpdateParam.getWorkflowId(), editVersion,
                userInfo.getAppCode(), null);
        if (botWorkflowPO == null) {
            throw new BizException("AC007", "业务流不存在");
        }

        if (CheckImgUtils.isNotEndWithImg(botWorkflowUpdateParam.getIconUrl())) {
            throw new BizException("AC049", "图标存在非法输入");
        }

        // 检查权限
        checkAuth(botWorkflowPO, userInfo);
        Set<String> resourceCodes = new HashSet<>();
        if (!WorkflowTypeEnum.STRATEGY_TEMPLATE.getCode().equals(botWorkflowPO.getWorkflowType()) && !Objects.equals(userInfo.getAppSourceType(), botWorkflowPO.getSourceSystem())) {
            throw new BizException("AC051", "数据来源不一致");
        }

        // 检查工作流名称
        checkName(botWorkflowUpdateParam, userInfo, editVersion, resourceCodes);

        botWorkflowPO.setDescription(botWorkflowUpdateParam.getDescription());
        botWorkflowPO.setIconUrl(botWorkflowUpdateParam.getIconUrl());
        botWorkflowPO.setWorkflowName(botWorkflowUpdateParam.getWorkflowName());
        botWorkflowPO.setWorkflowCnName(botWorkflowUpdateParam.getWorkflowCnName());
        botWorkflowPO.setWorkflowType(botWorkflowUpdateParam.getWorkflowType());
        botWorkflowPO.setBotType(botWorkflowUpdateParam.getBotType());
        botWorkflowPO.setUpdateId(userInfo.getUserId());
        botWorkflowPO.setUpdateName(userInfo.getUserName());
        botWorkflowPO.setStatus(Objects.equals(botWorkflowPO.getStatus(), PublishStateEnum.PUBLISHED.getCode()) ? PublishStateEnum.UPDATE_UNPUB.getCode() : botWorkflowPO.getStatus());
        botWorkflowPO.setVersion(editVersion);
        botWorkflowPO.setUpdateTime(LocalDateTime.now());

        boolean updateResult = IBotWorkflowService.updateById(botWorkflowPO);
        // 处理标签
        handleTag(botWorkflowPO.getWorkflowId(), botWorkflowUpdateParam.getTagCodeList());

        return updateResult;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean releaseWorkflow(String workflowId) {
        RequestInfo userInfo = RequestContext.get();
        // 获取编辑版本
        Long version = IWorkflowVersionService.getEditVersion(workflowId);
        // 检查工作流是否存在
        BotWorkflowPO botWorkflowPO = IBotWorkflowService.queryByIdAndVersion(workflowId, version, userInfo.getAppCode(), null);
        if (botWorkflowPO == null) {
            throw new BizException("AC007", "业务流不存在");
        }
        // 检查调试状态
        if (!DebugStateEnum.SUCCESS.getCode().equals(botWorkflowPO.getDebugStatus())) {
            throw new BizException("AC015", "该业务流未调试通过, 不允许发布");
        }
        if (Objects.equals(botWorkflowPO.getStatus(), PublishStateEnum.PUBLISHED.getCode())) {
            throw new BizException("AC014", "该业务流已经发布, 请勿重复发布");
        }
        // 用户应答策略不用发布, 跟随机器人一起发布
        if (WorkflowTypeEnum.USER_STRATEGY.getCode().equals(botWorkflowPO.getWorkflowType())) {
            return Boolean.TRUE;
        }
        // 检查权限
        checkAuth(botWorkflowPO, userInfo);

        // 检查业务流名称是否发生变更
        checkWorkflowName(botWorkflowPO, userInfo);

        AppSourceType appSourceType = WorkflowTypeEnum.STRATEGY_TEMPLATE.getCode().equals(botWorkflowPO.getWorkflowType()) ? null : userInfo.getAppSourceType();

        // 不是初始版本
        if (version > 1) {
            // 将上一个版本改为离线状态
            Long oldRunningVersion = IWorkflowVersionService.updateVersionByStatus(workflowId,
                    WorkStatusEnum.PUBLISHED,
                    RuntimeStatusEnum.RUNNING_PROD,
                    WorkStatusEnum.PUBLISHED.getWorkflowStatus(),
                    RuntimeStatusEnum.OFF_LINE.getValue(),
                    null);
            if (oldRunningVersion == null) {
                throw new BizException("AC011",
                        "publish error, change oldRunningVersion status failed");
            }
        }
        // 将状态更新为PUBLISHED, 上线状态改为上线
        version = IWorkflowVersionService.updateVersion(workflowId,
                version,
                WorkStatusEnum.PUBLISHED.getWorkflowStatus(),
                RuntimeStatusEnum.RUNNING_PROD.getValue(),
                null);
        if (version == null) {
            throw new BizException("AC012",
                    "publish error, change newRunningVersion status failed");
        }
        // 新增一个版本, 编辑中, 离线
        Long editVersion = version + 1;
        Boolean addVersion = IWorkflowVersionService.add(workflowId, editVersion,
                WorkStatusEnum.DEVELOP.getVersionStatus(),
                RuntimeStatusEnum.OFF_LINE.getValue());
        if (Boolean.FALSE.equals(addVersion)) {
            throw new BizException("AC013",
                    "publish error, generate new version failed");
        }
        // 将状态改为已发布
        botWorkflowPO.setStatus(PublishStateEnum.PUBLISHED.getCode());
        botWorkflowPO.setUpdateId(userInfo.getUserId());
        botWorkflowPO.setUpdateName(userInfo.getUserName());
        botWorkflowPO.setUpdateTime(LocalDateTime.now());
        IBotWorkflowService.updateById(botWorkflowPO);
        // 复制新版本数据
        botWorkflowConfigService.createNewVersionWorkflow(workflowId, version, appSourceType);
        // 复制挂载知识库数据
        agentKmsApi.publish(workflowId, version);
        // WorkFlow变量的发布
        workFlowVariableAppService.publish(workflowId, version);

        // 处理标签, 检查该业务流是否已经上架, 如果已经上架则需要更新上架标签
        BotWorkflowMallInfoPO botWorkflowMallInfoPO = botWorkflowMallService.queryByWorkflowId(botWorkflowPO.getWorkflowId());
        if (botWorkflowMallInfoPO != null) {
            // 处理标签 先查询出该插件所有的标签, 然后再绑定到插件商店的标签中
            Set<String> tagCodeList = tagRelationRepository.selectTagCodeByTarget(TargetType.WORKFLOW, botWorkflowPO.getWorkflowId());
            if (CollectionUtils.isNotEmpty(tagCodeList)) {
                tagCodeList.forEach(tagCode -> {
                    TagDTO tagDTO = tagAppRepository.selectByCode(tagCode);
                    if (tagDTO == null) {
                        throw new BizException("AA083", "标签不存在");
                    }
                });
            }
            tagRelationRepository.saveTagRelation(TargetType.WORKFLOW_MALL, workflowId, tagCodeList);
        }
        Boolean copyIntentResult = copyIntent(workflowId, version);
        if (!copyIntentResult) {
            throw new BizException("AA107", "复制意图失败");
        }
        //发送发布mq
        sendEditMsg(userInfo, workflowId, EventTypeEnum.PUBLISH);
        return Boolean.TRUE;
    }

    private void checkWorkflowName(BotWorkflowPO botWorkflowPO, RequestInfo userInfo) {
        // 获取已经发布的版本号
        Long version = IWorkflowVersionService.getPublishVersion(botWorkflowPO.getWorkflowId());
        if (version == null) {
            return;
        }
        // 检查工作流是否存在
        BotWorkflowPO publishedWorkflowPO = IBotWorkflowService.queryByIdAndVersion(botWorkflowPO.getWorkflowId(), version, userInfo.getAppCode(), null);
        if (publishedWorkflowPO == null) {
            return;
        }
        if (Objects.equals(publishedWorkflowPO.getWorkflowCnName(), botWorkflowPO.getWorkflowCnName())) {
            log.info("业务流名称未发生变更");
            return;
        }
        // 更新绑定表业务流名称
        List<AgentWorkflowBindPO> agentWorkflowBindPOS = agentWorkflowBindService.queryBindByWorkflowId(botWorkflowPO.getWorkflowId());
        if (CollectionUtils.isEmpty(agentWorkflowBindPOS)) {
            return;
        }
        agentWorkflowBindPOS.forEach(agentWorkflowBindPO -> agentWorkflowBindPO.setWorkflowCnName(botWorkflowPO.getWorkflowCnName()));
        agentWorkflowBindService.saveOrUpdateBatch(agentWorkflowBindPOS);
    }

    @Override
    public void createNewVersionWorkflow(String workflowId, Long version, AppSourceType appSourceType) {
        RequestInfo userInfo = RequestContext.get();
        Long newVersion = version + 1;
        // 复制工作流主表
        BotWorkflowPO botWorkflowPO = IBotWorkflowService.queryByIdAndVersion(workflowId, version, userInfo.getAppCode(), appSourceType == null ? null : appSourceType.name());
        if (botWorkflowPO == null) {
            throw new BizException("AC007", "业务流不存在");
        }
        botWorkflowPO.setId(null);
        botWorkflowPO.setVersion(newVersion);
        String canvas = copyCanvas(botWorkflowPO.getCanvas());
        botWorkflowPO.setCanvas(canvas);
        IBotWorkflowService.save(botWorkflowPO);
        // 复制工作流节点表
        List<BotWorkFlowNodePO> botWorkFlowNodePOS = IBotWorkflowNodeService.queryByWorkflowId(workflowId, userInfo.getAppCode(), version);
        if (!CollectionUtils.isEmpty(botWorkFlowNodePOS)) {
            botWorkFlowNodePOS.forEach(botWorkFlowNodePO -> {
                botWorkFlowNodePO.setId(null);
                botWorkFlowNodePO.setVersion(newVersion);
            });
            IBotWorkflowNodeService.saveBatch(botWorkFlowNodePOS);
        }
        // 复制工作流边表
        List<BotWorkflowEdgePO> botWorkflowEdgePOS = IBotWorkflowEdgeService.queryByWorkflowId(workflowId, userInfo.getAppCode(), version);
        if (!CollectionUtils.isEmpty(botWorkflowEdgePOS)) {
            botWorkflowEdgePOS.forEach(botWorkflowEdgePO -> {
                botWorkflowEdgePO.setId(null);
                botWorkflowEdgePO.setVersion(newVersion);
            });
            IBotWorkflowEdgeService.saveBatch(botWorkflowEdgePOS);
        }
    }

    @Override
    public Long copyTemplateData(String workflowId, Long version, String bindWorkflowId) {
        RequestInfo userInfo = RequestContext.get();
        // 复制版本表
        BotWorkflowVersionPO maxEditVersion = IWorkflowVersionService.getMaxEditVersion(bindWorkflowId);
        if (maxEditVersion == null) {
            throw new BizException("AC016", "未找到对应的版本");
        }
        Long newVersion = maxEditVersion.getVersion() + 1;
        maxEditVersion.setId(null);
        maxEditVersion.setVersion(newVersion);
        maxEditVersion.setStatus(WorkStatusEnum.DEVELOP.getVersionStatus());
        maxEditVersion.setRuntimeStatus(RuntimeStatusEnum.OFF_LINE.getValue());
        IWorkflowVersionService.save(maxEditVersion);
        // 复制工作流主表
        BotWorkflowPO botWorkflowPO = IBotWorkflowService.queryByIdAndVersion(workflowId, version, null, null);
        if (botWorkflowPO == null) {
            throw new BizException("AC007", "业务流不存在");
        }

        // 复制画布
        String canvas = copyCanvas(botWorkflowPO.getCanvas());
        botWorkflowPO.setCanvas(canvas);
        botWorkflowPO.setWorkflowId(bindWorkflowId);
        botWorkflowPO.setWorkflowType(WorkflowTypeEnum.USER_STRATEGY.getCode());
        botWorkflowPO.setId(null);
        botWorkflowPO.setVersion(newVersion);
        botWorkflowPO.setCreateId(userInfo.getUserId());
        botWorkflowPO.setCreateName(userInfo.getUserName());
        botWorkflowPO.setUpdateId(userInfo.getUserId());
        botWorkflowPO.setUpdateName(userInfo.getUserName());
        botWorkflowPO.setCreateTime(LocalDateTime.now());
        botWorkflowPO.setUpdateTime(LocalDateTime.now());
        botWorkflowPO.setAppCode(userInfo.getAppCode());
        botWorkflowPO.setSourceSystem(userInfo.getAppSourceType());
        botWorkflowPO.setIsDefault("0");
        IBotWorkflowService.save(botWorkflowPO);
        // 复制工作流节点表
        List<BotWorkFlowNodePO> botWorkFlowNodePOS = IBotWorkflowNodeService.queryByWorkflowId(workflowId, null, version);
        if (!CollectionUtils.isEmpty(botWorkFlowNodePOS)) {
            botWorkFlowNodePOS.forEach(botWorkFlowNodePO -> {
                botWorkFlowNodePO.setWorkflowId(bindWorkflowId);
                botWorkFlowNodePO.setId(null);
                botWorkFlowNodePO.setVersion(newVersion);
                botWorkFlowNodePO.setCreateId(userInfo.getUserId());
                botWorkFlowNodePO.setCreateName(userInfo.getUserName());
                botWorkFlowNodePO.setUpdateId(userInfo.getUserId());
                botWorkFlowNodePO.setUpdateName(userInfo.getUserName());
                botWorkFlowNodePO.setCreateTime(LocalDateTime.now());
                botWorkFlowNodePO.setAppCode(userInfo.getAppCode());
                botWorkFlowNodePO.setUpdateTime(LocalDateTime.now());
            });
            IBotWorkflowNodeService.saveBatch(botWorkFlowNodePOS);
        }
        // 复制工作流边表
        List<BotWorkflowEdgePO> botWorkflowEdgePOS = IBotWorkflowEdgeService.queryByWorkflowId(workflowId, null, version);
        if (!CollectionUtils.isEmpty(botWorkflowEdgePOS)) {
            botWorkflowEdgePOS.forEach(botWorkflowEdgePO -> {
                botWorkflowEdgePO.setWorkflowId(bindWorkflowId);
                botWorkflowEdgePO.setId(null);
                botWorkflowEdgePO.setVersion(newVersion);
                botWorkflowEdgePO.setCreateId(userInfo.getUserId());
                botWorkflowEdgePO.setCreateName(userInfo.getUserName());
                botWorkflowEdgePO.setUpdateId(userInfo.getUserId());
                botWorkflowEdgePO.setUpdateName(userInfo.getUserName());
                botWorkflowEdgePO.setCreateTime(LocalDateTime.now());
                botWorkflowEdgePO.setAppCode(userInfo.getAppCode());
                botWorkflowEdgePO.setUpdateTime(LocalDateTime.now());
            });
            IBotWorkflowEdgeService.saveBatch(botWorkflowEdgePOS);
        }
        return newVersion;
    }

    @Override
    public Boolean saveIntent(BotWorkflowIntentParam botWorkflowIntentParam) {
        RequestInfo userInfo = RequestContext.get();
        checkResourceAuth(botWorkflowIntentParam.getWorkflowId());

        // 获取编辑版本
        Long editVersion = IWorkflowVersionService.getEditVersion(botWorkflowIntentParam.getWorkflowId());
        // 检查工作流是否存在
        BotWorkflowPO botWorkflowPO = IBotWorkflowService.queryByIdAndVersion(botWorkflowIntentParam.getWorkflowId(), editVersion, userInfo.getAppCode(), null);

        if (botWorkflowPO == null) {
            throw new BizException("AC007", "业务流不存在");
        }
        // 删除旧的意图数据
        IBotWorkflowIntentRelationService.deleteOldData(botWorkflowPO.getWorkflowId(), editVersion);
        // 保存新的意图数据
        if (!CollectionUtils.isEmpty(botWorkflowIntentParam.getIntentList())) {
            List<BotWorkflowIntentRelationPO> intentRelationPOS = botWorkflowIntentParam.getIntentList().stream().map(intentEntity -> {
                BotWorkflowIntentRelationPO botWorkflowIntentRelationPO = new BotWorkflowIntentRelationPO();
                botWorkflowIntentRelationPO.setWorkflowId(botWorkflowPO.getWorkflowId());
                botWorkflowIntentRelationPO.setWorkflowVersion(editVersion);
                botWorkflowIntentRelationPO.setIntentCode(intentEntity.getIntentCode());
                botWorkflowIntentRelationPO.setIntentName(intentEntity.getIntentName());
                botWorkflowIntentRelationPO.setCreateId(userInfo.getUserId());
                botWorkflowIntentRelationPO.setCreateName(userInfo.getUserName());
                botWorkflowIntentRelationPO.setUpdateId(userInfo.getUserId());
                botWorkflowIntentRelationPO.setUpdateName(userInfo.getUserName());
                botWorkflowIntentRelationPO.setCreateTime(LocalDateTime.now());
                botWorkflowIntentRelationPO.setUpdateTime(LocalDateTime.now());
                botWorkflowIntentRelationPO.setTenantId(userInfo.getTenantId());
                botWorkflowIntentRelationPO.setAppCode(userInfo.getAppCode());
                return botWorkflowIntentRelationPO;
            }).collect(Collectors.toList());
            IBotWorkflowIntentRelationService.saveBatch(intentRelationPOS);
        }

        return Boolean.TRUE;
    }

    /**
     * 发布工作流，复制一个版本
     *
     * @param workflowId
     * @param oldVersion
     * @return
     */
    public Boolean copyIntent(String workflowId, Long oldVersion) {
        List<BotWorkflowIntentRelationPO> botWorkflowIntentRelationPOS = IBotWorkflowIntentRelationService.queryByWorkflowId(workflowId, oldVersion);
        if (CollectionUtils.isEmpty(botWorkflowIntentRelationPOS)) {
            return Boolean.TRUE;
        }
        long newVersion = oldVersion + 1;
        List<BotWorkflowIntentRelationPO> copyIntentList = botWorkflowIntentRelationPOS.stream().map(botWorkflowIntentRelationPO -> {
            BotWorkflowIntentRelationPO newIntentRelationPO = new BotWorkflowIntentRelationPO();
            BeanUtils.copyProperties(botWorkflowIntentRelationPO, newIntentRelationPO);
            newIntentRelationPO.setId(null);
            newIntentRelationPO.setWorkflowVersion(newVersion);
            return newIntentRelationPO;
        }).collect(Collectors.toList());
        return IBotWorkflowIntentRelationService.saveBatch(copyIntentList);
    }

    @Override
    public List<IntentEntity> queryRelatedIntent(String workflowId) {
        RequestInfo userInfo = RequestContext.get();
        checkResourceAuth(workflowId);

        // 获取编辑版本
        Long editVersion = IWorkflowVersionService.getEditVersion(workflowId);
        // 检查工作流是否存在
        BotWorkflowPO botWorkflowPO = IBotWorkflowService.queryByIdAndVersion(workflowId, editVersion, userInfo.getAppCode(), null);
        if (botWorkflowPO == null) {
            throw new BizException("AC007", "业务流不存在");
        }

        List<BotWorkflowIntentRelationPO> botWorkflowIntentRelationPOS = IBotWorkflowIntentRelationService.queryByWorkflowId(workflowId, editVersion);

        if (CollectionUtils.isEmpty(botWorkflowIntentRelationPOS)) {
            return Collections.emptyList();
        }
        return botWorkflowIntentRelationPOS.stream().map(botWorkflowIntentRelationPO -> {
            IntentEntity intentEntity = new IntentEntity();
            intentEntity.setIntentCode(botWorkflowIntentRelationPO.getIntentCode());
            intentEntity.setIntentName(botWorkflowIntentRelationPO.getIntentName());
            return intentEntity;
        }).collect(Collectors.toList());
    }

    @Override
    public List<BotWorkflowDetailRsp> querySubWorkflowList(QuerySubWorkflowParam querySubWorkflowParam) {
        RequestInfo requestInfo = RequestContext.get();
        checkResourceAuth(querySubWorkflowParam.getMainWorkflowId());

        // 查询子流程
        List<String> workflowIds = IBotWorkflowService.querySubByIdAndVersion(querySubWorkflowParam.getMainWorkflowId(), requestInfo.getAppCode(), requestInfo.getAppSourceType().name());

        if (CollectionUtils.isEmpty(workflowIds)) {
            return Collections.emptyList();
        }
        // 查询子流程详情
        List<BotWorkflowDetailRsp> workflowDetailRspList = Lists.newArrayList();
        for (String workflowId : workflowIds) {
            BotWorkflowDetailRsp botWorkflowDetailRsp = new BotWorkflowDetailRsp();
            // 先查询子流程版本
            Long subVersion = IWorkflowVersionService.getEditVersion(workflowId);

            BotWorkflowPO botWorkflowPO = IBotWorkflowService.queryByIdAndVersion(workflowId, subVersion, requestInfo.getAppCode(), RequestContext.getAppSourceType().name());
            if (botWorkflowPO == null) {
                continue;
            }
            BeanUtils.copyProperties(botWorkflowPO, botWorkflowDetailRsp);
            if (!StringUtils.isEmpty(botWorkflowPO.getScene())) {
                botWorkflowDetailRsp.setScene(JSON.parseArray(botWorkflowPO.getScene(), String.class));
            }

            List<BotWorkFlowNodePO> nodeList = IBotWorkflowNodeService.queryByWorkflowId(botWorkflowPO.getWorkflowId(), requestInfo.getAppCode(), subVersion);
            if (!CollectionUtils.isEmpty(nodeList)) {
                botWorkflowDetailRsp.setNodes(nodeList.stream().map(nodePO -> {
                    Node node = new Node();
                    node.setNodeId(nodePO.getNodeId());
                    node.setNodeType(nodePO.getNodeType().getCode());
                    node.setData(JSON.parseObject(nodePO.getData(), Node.NodeData.class));
                    return node;
                }).collect(Collectors.toList()));
                for (BotWorkFlowNodePO node : nodeList) {
                    Node.NodeData nodeData = JSON.parseObject(node.getData(), Node.NodeData.class);
                    if (NodeTypeEnum.START.equals(node.getNodeType())) {
                        botWorkflowDetailRsp.setInputParams(nodeData.getInputs().getRequestParams());
                    }
                    if (NodeTypeEnum.END.equals(node.getNodeType())) {
                        botWorkflowDetailRsp.setOutputParams(nodeData.getOutputs());
                    }
                }
            }
            List<BotWorkflowEdgePO> edgeList = IBotWorkflowEdgeService.queryByWorkflowId(botWorkflowPO.getWorkflowId(), requestInfo.getAppCode(), subVersion);
            if (!CollectionUtils.isEmpty(edgeList)) {
                botWorkflowDetailRsp.setEdges(edgeList.stream().map(edgePO -> {
                    Edge edge = new Edge();
                    BeanUtils.copyProperties(edgePO, edge);
                    return edge;
                }).collect(Collectors.toList()));
            }
            workflowDetailRspList.add(botWorkflowDetailRsp);
        }
        // 根据创建时间进行排序
        workflowDetailRspList = workflowDetailRspList.stream().sorted(Comparator.comparing(BotWorkflowDetailRsp::getCreateTime)).collect(Collectors.toList());

        return workflowDetailRspList;
    }

    @Override
    public Page<BotWorkflowRsp> queryDefaultTemplate(QueryWorkflowParam pageQuery) {
        Page<BotWorkflowRsp> workflowPOPage = new Page<>(pageQuery.getPageIndex(), pageQuery.getPageSize());
        Result<String> stringResult = agentInfoRpcApi.queryDefaultTemplateId();
        if (StringUtils.isEmpty(stringResult.getData())) {
            return workflowPOPage;
        }
        String workflowId = stringResult.getData();
        Long version = IWorkflowVersionService.getPublishVersion(workflowId);
        if (version == null) {
            version = IWorkflowVersionService.getEditVersion(workflowId);
        }
        log.info("查询agent详情,workflowId,{}, 版本：{}", workflowId, version, null);
        BotWorkflowPO botWorkflowPO = IBotWorkflowService.queryByIdAndVersion(workflowId, version, null, null);
        BotWorkflowRsp botWorkflowRsp = new BotWorkflowRsp();
        BeanUtils.copyProperties(botWorkflowPO, botWorkflowRsp);
        workflowPOPage.setRecords(Collections.singletonList(botWorkflowRsp));
        workflowPOPage.setTotal(1);
        return workflowPOPage;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String copyWorkflow(String workflowId, boolean isTemplate) {
        RequestInfo userInfo = RequestContext.get();
        Long version;
        // 复制机器人时需要复制编辑态的数据
        if (isTemplate) {
            version = IWorkflowVersionService.getPublishVersion(workflowId);
        } else {
            version = IWorkflowVersionService.getEditVersion(workflowId);
        }
        if (version == null) {
            version = IWorkflowVersionService.getEditVersion(workflowId);
        }

        // 复制工作流主表
        BotWorkflowPO botWorkflowPO = IBotWorkflowService.queryByIdAndVersion(workflowId, version, null, null);
        if (botWorkflowPO == null) {
            throw new BizException("AC007", "业务流不存在");
        }
        // 手动复制才进行鉴权
        if (!isTemplate && (!WorkflowTypeEnum.STRATEGY_TEMPLATE.getCode().equals(botWorkflowPO.getWorkflowType()) || !WorkflowTypeEnum.USER_STRATEGY.getCode().equals(botWorkflowPO.getWorkflowType()))) {
            checkResourceAuth(workflowId);
        }

        // 生成新的workflowId
        String newWorkflowId = UUID.randomUUID().toString().replace("-", "");
        String workflowName;
        // 生成新的工作流名称
        if (isTemplate) {
            // 设置工作流类型为用户策略
            botWorkflowPO.setWorkflowType(WorkflowTypeEnum.USER_STRATEGY.getCode());
            botWorkflowPO.setSourceSystem(userInfo.getAppSourceType());
            workflowName = botWorkflowPO.getWorkflowCnName();
        } else {
            workflowName = generaCopyWorkflowName(botWorkflowPO.getWorkflowCnName(), userInfo);
            botWorkflowPO.setSourceSystem(botWorkflowPO.getSourceSystem());
        }
        // 生成新的版本号
        initConfig(newWorkflowId);
        // 复制画布
        String canvas = copyCanvas(botWorkflowPO.getCanvas());
        botWorkflowPO.setCanvas(canvas);
        botWorkflowPO.setId(null);
        botWorkflowPO.setWorkflowId(newWorkflowId);
        botWorkflowPO.setWorkflowCnName(workflowName);
        botWorkflowPO.setVersion(1L);
        botWorkflowPO.setCreateId(userInfo.getUserId());
        botWorkflowPO.setCreateName(userInfo.getUserName());
        botWorkflowPO.setUpdateId(userInfo.getUserId());
        botWorkflowPO.setUpdateName(userInfo.getUserName());
        botWorkflowPO.setStatus(PublishStateEnum.UNPUBLISHED.getCode());
        botWorkflowPO.setDebugStatus(DebugStateEnum.UNEXECUTED.getCode());
        botWorkflowPO.setAppCode(userInfo.getAppCode());
        botWorkflowPO.setCreateTime(LocalDateTime.now());
        botWorkflowPO.setUpdateTime(LocalDateTime.now());
        botWorkflowPO.setIsDefault("0");
        IBotWorkflowService.save(botWorkflowPO);

        // 复制工作流节点表
        List<BotWorkFlowNodePO> botWorkFlowNodePOS = IBotWorkflowNodeService.queryByWorkflowId(workflowId, null, version);
        if (!CollectionUtils.isEmpty(botWorkFlowNodePOS)) {
            botWorkFlowNodePOS.forEach(botWorkFlowNodePO -> {
                botWorkFlowNodePO.setId(null);
                botWorkFlowNodePO.setWorkflowId(newWorkflowId);
                botWorkFlowNodePO.setVersion(1L);
                botWorkFlowNodePO.setCreateId(userInfo.getUserId());
                botWorkFlowNodePO.setCreateName(userInfo.getUserName());
                botWorkFlowNodePO.setUpdateId(userInfo.getUserId());
                botWorkFlowNodePO.setUpdateName(userInfo.getUserName());
                botWorkFlowNodePO.setUpdateTime(LocalDateTime.now());
                botWorkFlowNodePO.setCreateTime(LocalDateTime.now());
                botWorkFlowNodePO.setAppCode(userInfo.getAppCode());
            });
            IBotWorkflowNodeService.saveBatch(botWorkFlowNodePOS);
        }
        // 复制工作流边表
        List<BotWorkflowEdgePO> botWorkflowEdgePOS = IBotWorkflowEdgeService.queryByWorkflowId(workflowId, null, version);
        if (!CollectionUtils.isEmpty(botWorkflowEdgePOS)) {
            botWorkflowEdgePOS.forEach(botWorkflowEdgePO -> {
                botWorkflowEdgePO.setId(null);
                botWorkflowEdgePO.setWorkflowId(newWorkflowId);
                botWorkflowEdgePO.setVersion(1L);
                botWorkflowEdgePO.setCreateId(userInfo.getUserId());
                botWorkflowEdgePO.setCreateName(userInfo.getUserName());
                botWorkflowEdgePO.setUpdateId(userInfo.getUserId());
                botWorkflowEdgePO.setUpdateName(userInfo.getUserName());
                botWorkflowEdgePO.setUpdateTime(LocalDateTime.now());
                botWorkflowEdgePO.setCreateTime(LocalDateTime.now());
                botWorkflowEdgePO.setAppCode(userInfo.getAppCode());
            });
            IBotWorkflowEdgeService.saveBatch(botWorkflowEdgePOS);
        }

        // 复制标签关联关系
        Set<String> tagCodeList = tagRelationRepository.selectTagCodeByTarget(TargetType.WORKFLOW, workflowId);
        if (CollectionUtils.isNotEmpty(tagCodeList)) {
            tagRelationRepository.saveTagRelation(TargetType.WORKFLOW, newWorkflowId, tagCodeList);
        }

        // 复制workflow变量
        com.chinatelelcom.gs.engine.sdk.common.Result<Boolean> copyVarResult = workFlowVariableAppService.copy(workflowId, newWorkflowId);

        saveOrUpdateAuth(newWorkflowId);
        return newWorkflowId;
    }

    private String copyCanvas(String canvas) {
        if (StringUtils.isNotEmpty(canvas) && canvas.startsWith(Constants.FILE_TYPE)) {
            // 先下载画布, 然后再上传一个相同的
            try {
                InputStream download = fileService.download(canvas);
                String newCanvas = IOUtils.toString(download, "UTF-8");
                return fileService.uploadCanvas(new ByteArrayInputStream(newCanvas.getBytes()), new ByteArrayInputStream(newCanvas.getBytes()));
            } catch (Exception e) {
                throw new BizException("AC024", "画布数据保存失败");
            }
        } else {
            return canvas;
        }
    }

    @Override
    public WorkflowSceneRsp querySceneList(String name) {
        RequestInfo userInfo = RequestContext.get();
        WorkflowSceneRsp sceneRsp = new WorkflowSceneRsp();

        List<BotWorkflowScenePO> querySceneList = IBotWorkflowSceneService.querySceneList(name, userInfo.getAppCode(), userInfo.getAppSourceType().name());
        if (querySceneList != null) {
            sceneRsp.setSceneList(querySceneList.stream().map(BotWorkflowScenePO::getScene).distinct().collect(Collectors.toList()));
        }
        return sceneRsp;
    }

    @Override
    public List<NodeVariableRsp> getVariables(String workflowId, String nodeId) {
        BotWorkflowDetailRsp botWorkflowDetailRsp = botWorkflowDetail(workflowId, true);
        Dag dag = dagParser.parser(botWorkflowDetailRsp, botWorkflowDetailRsp.getNodes(), botWorkflowDetailRsp.getEdges());
        Map<String, DagNode> nodeMap = dag.getNodeMap();
        List<NodeVariableRsp> nodeVariables = new ArrayList<>();
        HashSet<String> nodeIds = new HashSet<>();
        addDependOutputs(nodeId, nodeMap, nodeVariables, nodeIds);
        return nodeVariables;
    }

    @Override
    public RefGlobalVariableRsp getGlobalVariable(String workflowId, Boolean test) {
        BotWorkflowDetailRsp botWorkflowDetailRsp = botWorkflowDetail(workflowId, test == null || Boolean.TRUE.equals(test));
        Dag dag = dagParser.parser(botWorkflowDetailRsp, botWorkflowDetailRsp.getNodes(), botWorkflowDetailRsp.getEdges());
        RefGlobalVariableRsp globalVariables = new RefGlobalVariableRsp();

        List<Param.VariableRefParam> paramList = Lists.newArrayList();
        Set<String> addedParams = new HashSet<>();

        // 遍历所有节点并收集全局变量引用

        dag.getNodeMap().values().forEach(node -> {
            // 从输入中获取参数
            collectGlobalVariables(node.getInputParams(), paramList, addedParams);

            // 从输出中获取参数
            collectGlobalVariables(node.getOutputParams(), paramList, addedParams);

            // 如果当前节点是选择器节点， 从条件分支中获取参数
            collectConditionGlobalVariables(node, paramList, addedParams);
        });

        globalVariables.setGlobalVariables(paramList);
        return globalVariables;
    }

    private void collectConditionGlobalVariables(DagNode node, List<Param.VariableRefParam> paramList, Set<String> addedParams) {
        if (node.getNodeType() != NodeTypeEnum.CONDITION_CHOOSE) {
            return;
        }
        List<Node.Branch> branches = node.getBranches();
        if (CollectionUtils.isEmpty(branches)) {
            return;
        }
        branches.forEach(branch -> {
            Node.BranchParam branchParam = branch.getBranchParam();
            if (branchParam == null) {
                return;
            }
            List<Condition> conditions = branchParam.getConditions();
            if (CollectionUtils.isEmpty(conditions)) {
                return;
            }
            conditions.forEach(condition -> {
                // 从条件中获取参数
                collectGlobalVariables(Lists.newArrayList(condition.getLeft()), paramList, addedParams);

                collectGlobalVariables(Lists.newArrayList(condition.getRight()), paramList, addedParams);
            });
        });
    }

    /**
     * 递归收集全局变量引用，并进行去重
     *
     * @param params      参数列表
     * @param paramList   存储全局变量引用的列表
     * @param addedParams 已添加的参数名称集合，用于去重
     */
    private void collectGlobalVariables(List<Param> params, List<Param.VariableRefParam> paramList, Set<String> addedParams) {
        if (params == null || params.isEmpty()) {
            return;
        }

        for (Param item : params) {
            if (item.isGlobalVariableRef()) {
                String variableCode = item.getRefVariable().getVariableCode();
                if (!addedParams.contains(variableCode)) {
                    paramList.add(item.getRefVariable());
                    addedParams.add(variableCode);
                }
            }

            // 递归处理子参数
            if (item.getSubParameters() != null && !item.getSubParameters().isEmpty()) {
                collectGlobalVariables(item.getSubParameters(), paramList, addedParams);
            }
        }
    }

    /**
     * 更新调试状态
     *
     * @param workflowId     工作流id
     * @param debugStateEnum 调试状态
     * @param appCode        空间id
     * @return
     */
    @Override
    public Boolean updateDebugStatus(String workflowId, DebugStateEnum debugStateEnum, String appCode) {
        Long version = IWorkflowVersionService.getEditVersion(workflowId);
        BotWorkflowPO botWorkflowPO = IBotWorkflowService.queryByIdAndVersion(workflowId, version, appCode, null);
        if (botWorkflowPO == null) {
            throw new BizException("AC007", "业务流不存在");
        }
        // 校验来源
        if (!WorkflowTypeEnum.STRATEGY_TEMPLATE.getCode().equals(botWorkflowPO.getWorkflowType())) {
            if (!Objects.equals(RequestContext.getAppSourceType(), botWorkflowPO.getSourceSystem())) {
                throw new BizException("AC051", "数据来源不一致");
            }
        }

        botWorkflowPO.setDebugStatus(debugStateEnum.getCode());
        botWorkflowPO.setUpdateTime(LocalDateTime.now());
        boolean result = IBotWorkflowService.updateById(botWorkflowPO);
        if (!result) {
            throw new BizException("AC016", "更新调试状态失败");
        }
        return true;
    }

    /**
     * 递归添加依赖节点输出变量
     *
     * @param nodeId        节点id
     * @param nodeMap       节点map
     * @param nodeVariables 变量列表
     */
    private void addDependOutputs(String nodeId, Map<String, DagNode> nodeMap, List<NodeVariableRsp> nodeVariables, HashSet<String> nodeIds) {
        if (!nodeMap.containsKey(nodeId)) {
            return;
        }
        Set<DagNode> dependNodes = nodeMap.get(nodeId).getDependNodes();
        if (CollectionUtils.isEmpty(dependNodes)) {
            return;
        }
        for (DagNode dependNode : dependNodes) {
            if (Objects.isNull(dependNode) || nodeIds.contains(dependNode.getNodeId())) {
                continue;
            }
            NodeVariableRsp variableRsp = buildVariableRsp(dependNode);
            if (variableRsp != null) {
                nodeVariables.add(variableRsp);
            }
            nodeIds.add(dependNode.getNodeId());
            addDependOutputs(dependNode.getNodeId(), nodeMap, nodeVariables, nodeIds);
        }
    }

    private static NodeVariableRsp buildVariableRsp(DagNode dependNode) {
        NodeVariableRsp variableRsp = new NodeVariableRsp();
        variableRsp.setNodeName(dependNode.getNodeTitle());
        variableRsp.setNodeType(dependNode.getNodeType().getCode());
        variableRsp.setNodeId(dependNode.getNodeId());
        variableRsp.setIconUrl(dependNode.getIconUrl());
        if (NodeTypeEnum.START.equals(dependNode.getNodeType())) {
            //设置variableRsp的variable列表中的元素的valueRefSource属性为inputParams
            if (CollectionUtils.isEmpty(dependNode.getInputParams())) {
                return null;
            }
            buildVariableSource(dependNode.getInputParams(), RefSourceEnum.INPUT);
            variableRsp.setVariable(dependNode.getInputParams());
        } else {
            if (CollectionUtils.isEmpty(dependNode.getOutputParams())) {
                return null;
            }
            buildVariableSource(dependNode.getOutputParams(), RefSourceEnum.OUTPUT);
            variableRsp.setVariable(dependNode.getOutputParams());
        }
        return variableRsp;
    }

    private static void buildVariableSource(List<Param> params, RefSourceEnum sourceEnum) {
        params.forEach(variable -> {
            variable.setValueRefSource(sourceEnum.getCode());
            if (!CollectionUtils.isEmpty(variable.getSubParameters())) {
                buildVariableSource(variable.getSubParameters(), sourceEnum);
            }
        });
    }

    private String generaCopyWorkflowName(String workflowName, RequestInfo userInfo) {
        String copyName = "%s的副本".formatted(workflowName);
        String regex = "^%s[0-9]+$".formatted(copyName);
        BotWorkflowPO botWorkflowPO = IBotWorkflowService.queryDefaultName(copyName, regex, userInfo.getAppCode());
        if (botWorkflowPO != null) {
            int number = Integer.parseInt(botWorkflowPO.getWorkflowCnName().replace(copyName, ""));
            copyName = copyName + (number + 1);
        } else {
            copyName = copyName + "1";
        }
        return copyName;

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWorkflow(String workflowId) {
        // 判断是否有工作流权限
        RequestInfo userInfo = RequestContext.get();
        List<BotWorkflowPO> workflowPOList = IBotWorkflowService.queryWorkflowList(workflowId);
        if (CollectionUtils.isEmpty(workflowPOList)) {
            throw new BizException("AC007", "业务流不存在");
        }
        if ("1".equals(workflowPOList.get(0).getIsDefault())) {
            throw new BizException("AC008", "默认业务流不能删除");
        }

        if (!WorkflowTypeEnum.STRATEGY_TEMPLATE.getCode().equals(workflowPOList.get(0).getWorkflowType()) && !WorkflowTypeEnum.USER_STRATEGY.getCode().equals(workflowPOList.get(0).getWorkflowType())) {
            checkResourceAuth(workflowId);
            // 检查来源
            if (!Objects.equals(userInfo.getAppSourceType(), workflowPOList.get(0).getSourceSystem())) {
                throw new BizException("AC051", "数据来源不一致");
            }
        }

        // 删除版本数据
        // 查询线上的是否有引用
        Long publishVersion = IWorkflowVersionService.getPublishVersion(workflowId);
        if (publishVersion != null) {
            BotWorkflowPO botWorkflowPO = IBotWorkflowService.queryByIdAndVersion(workflowId, publishVersion, userInfo.getAppCode(), null);
            if (botWorkflowPO != null && botWorkflowPO.getBotRefCount() > 0) {
                throw new BizException("AC017", "该业务流有引用，无法删除");
            }
        }
        // 删除工作流引用
        agentInfoRpcApi.deleteWorkflowBind(workflowId);

        // 检查是否有子流程需要删除
        List<String> workflowIdList = IBotWorkflowService.querySubByIdAndVersion(workflowId, userInfo.getAppCode(), userInfo.getAppSourceType().name());
        // 主流程被删除了, 子流程需同步删除
        if (CollectionUtils.isNotEmpty(workflowIdList)) {
            for (String botWorkflowId : workflowIdList) {
                SpringContextUtils.getBean(BotWorkflowConfigService.class).deleteWorkflow(botWorkflowId);
            }
        }

        boolean result = IWorkflowVersionService.deleteByWorkflowId(workflowId, userInfo.getAppCode());
        // 删除工作流版本
        if (!result) {
            throw new BizException("AC018", "删除版本数据失败");
        }
        // 删除节点
        IBotWorkflowNodeService.deleteByWorkflowId(workflowId, null, userInfo.getAppCode());
        IBotWorkflowEdgeService.deleteByWorkflowId(workflowId, null, userInfo.getAppCode());

        // 删除画布数据
        for (BotWorkflowPO botWorkflowPO : workflowPOList) {
            if (botWorkflowPO.getCanvas() != null && botWorkflowPO.getCanvas().startsWith(Constants.FILE_TYPE)) {
                fileService.remove(botWorkflowPO.getCanvas());
            }
        }

        result = IBotWorkflowService.deleteByWorkflowId(workflowId, userInfo.getAppCode());
        if (!result) {
            throw new BizException("AC019", "删除业务流数据失败");
        }

        // 删除标签关联
        tagRelationRepository.removeByTarget(TargetType.WORKFLOW, Lists.newArrayList(workflowId));

        // 检查是否上架, 已经上架需要下架
        BotWorkflowMallInfoPO botWorkflowMallInfoPO = botWorkflowMallService.queryByWorkflowId(workflowId);
        if (botWorkflowMallInfoPO != null) {
            botWorkflowMallService.remove(botWorkflowMallInfoPO);
        }

        //发送删除mq
        ResourceDTO resourceDTO = new ResourceDTO();
        resourceDTO.setResourceId(workflowId);
        resourceDTO.setResourceType(ResourceTypeEnum.WORKFLOW);
        resourceDTO.setIsPublic(false);
        privilegeUtil.deleteResource(resourceDTO);
        sendEditMsg(userInfo, workflowId, EventTypeEnum.DELETE);
        return result;
    }

    @Override
    public boolean checkWorkflowAuth(String workflowId) {
        RequestInfo userInfo = RequestContext.get();
        boolean isOwn = false;
        boolean isShelved = false;
        // 检查是否是自己的业务流
        List<PrivilegeResources> privilegeResources = privilegeUtil.checkResourcePrivileges(null, ResourceTypeEnum.WORKFLOW, null);
        Set<String> resourceSet = privilegeResources.stream().map(PrivilegeResources::getResourceId).collect(Collectors.toSet());
        if (resourceSet.contains(workflowId)) {
            isOwn = true;
        }
        // 检查是否为上架业务流
        if (Boolean.TRUE.equals(botWorkflowMallService.queryCommonWorkflow(workflowId))) {
            isShelved = true;
        }
        return isOwn || isShelved;
    }

    /**
     * 内部使用，用作不校验权限、来源、不做数据隔离，如需校验，请使用其他接口
     *
     * @param workflowId String
     * @param test       boolean
     * @return BotWorkflowPO
     */
    public BotWorkflowPO queryWorkflowDetailByLastVersion(String workflowId, boolean test) {
        RequestInfo userInfo = RequestContext.get();
        BotWorkflowDetailRsp rsp = new BotWorkflowDetailRsp();
        // 获取编辑版本
        Long version;
        if (test) {
            version = IWorkflowVersionService.getEditVersion(workflowId);
        } else {
            version = IWorkflowVersionService.getPublishVersion(workflowId);
        }
        if (version == null) {
            throw new BizException("AC007", "业务流不存在");
        }
        log.info("查询agent详情,workflowId,{}, 版本：{}, userInfo:{}", workflowId, version, userInfo);
        BotWorkflowPO botWorkflowPO = IBotWorkflowService.queryByIdAndVersion(workflowId, version, null, null);
        if (botWorkflowPO == null) {
            throw new BizException("AC007", "业务流不存在");
        }
        return botWorkflowPO;
    }


    @Override
    public BotWorkflowDetailRsp botWorkflowDetail(String workflowId, boolean test) {
        // 判断是否有工作流权限
        RequestInfo userInfo = RequestContext.get();
        BotWorkflowDetailRsp rsp = new BotWorkflowDetailRsp();
        // 获取编辑版本
        Long version;
        if (test) {
            version = IWorkflowVersionService.getEditVersion(workflowId);
        } else {
            version = IWorkflowVersionService.getPublishVersion(workflowId);
        }
        if (version == null) {
            throw new BizException("AC007", "业务流不存在");
        }
        log.info("查询agent详情,workflowId,{}, 版本：{}, userInfo:{}", workflowId, version, userInfo);
        // im聊天窗调用, 所以不传sourceType
        BotWorkflowPO botWorkflowPO = IBotWorkflowService.queryByIdAndVersion(workflowId, version, null, null);
        if (botWorkflowPO == null) {
            throw new BizException("AC007", "业务流不存在");
        }
        //im聊天窗时，把source改为当前workflow的source
        if (Objects.nonNull(RequestContext.getAppSourceType()) && RequestContext.getAppSourceType().equals(AppSourceType.IM)) {
            RequestContext.getAndCheck().setAppSourceType(botWorkflowPO.getSourceSystem());
        }


        // 检查权限
        checkAuth(botWorkflowPO, userInfo);

        BeanUtils.copyProperties(botWorkflowPO, rsp);
        if (!StringUtils.isEmpty(botWorkflowPO.getScene())) {
            rsp.setScene(JSON.parseArray(botWorkflowPO.getScene(), String.class));
        }
        String canvas = botWorkflowPO.getCanvas();
        if (canvas != null && canvas.startsWith(Constants.FILE_TYPE)) {
            try {
                InputStream inputStream = SpringContextUtils.getBean(FileService.class).download(canvas);
                ByteArrayOutputStream result = new ByteArrayOutputStream();
                byte[] buffer = new byte[1024];
                int length;
                while ((length = inputStream.read(buffer)) != -1) {
                    result.write(buffer, 0, length);
                }
                canvas = result.toString(StandardCharsets.UTF_8.name());
            } catch (Exception e) {
                throw new BizException("AC025", "画布数据读取失败");
            }
        }
        rsp.setCanvas(canvas);

        List<BotWorkFlowNodePO> nodeList = IBotWorkflowNodeService.queryByWorkflowId(workflowId, null, version);
        if (!CollectionUtils.isEmpty(nodeList)) {
            rsp.setNodes(nodeList.stream().map(nodePO -> {
                Node node = new Node();
                node.setNodeId(nodePO.getNodeId());
                node.setNodeType(nodePO.getNodeType().getCode());
                node.setData(JSON.parseObject(nodePO.getData(), Node.NodeData.class));
                return node;
            }).collect(Collectors.toList()));
            for (BotWorkFlowNodePO node : nodeList) {
                Node.NodeData nodeData = JSON.parseObject(node.getData(), Node.NodeData.class);
                if (NodeTypeEnum.START.equals(node.getNodeType())) {
                    rsp.setInputParams(nodeData.getInputs().getRequestParams());
                }
                if (NodeTypeEnum.END.equals(node.getNodeType())) {
                    rsp.setOutputParams(nodeData.getOutputs());
                }
            }
        } else {
            rsp.setNodes(Lists.newArrayList());
        }
        List<BotWorkflowEdgePO> edgeList = IBotWorkflowEdgeService.queryByWorkflowId(workflowId, null, version);
        if (!CollectionUtils.isEmpty(edgeList)) {
            rsp.setEdges(edgeList.stream().map(edgePO -> {
                Edge edge = new Edge();
                BeanUtils.copyProperties(edgePO, edge);
                return edge;
            }).collect(Collectors.toList()));
        } else {
            rsp.setEdges(Lists.newArrayList());
        }
        if (!test) {
            LocalDateTime publishTime = IWorkflowVersionService.queryPublishTime(workflowId, version);
            rsp.setPublishTime(publishTime);
        }

        // 关联标签
        rsp.setTagList(getTag(rsp.getWorkflowId()));
        setTagForUpdate(rsp);
        // 查询意图
        List<BotWorkflowIntentRelationPO> botWorkflowIntentRelationPOS = IBotWorkflowIntentRelationService.queryByWorkflowId(workflowId, botWorkflowPO.getVersion());
        if (!CollectionUtils.isEmpty(botWorkflowIntentRelationPOS)) {
            rsp.setIntents(botWorkflowIntentRelationPOS.stream().map(workflow -> {
                IntentEntity intentEntity = new IntentEntity();
                intentEntity.setIntentCode(workflow.getIntentCode());
                intentEntity.setIntentName(workflow.getIntentName());
                return intentEntity;
            }).collect(Collectors.toList()));
        }
        return rsp;
    }

    private void checkAuth(BotWorkflowPO botWorkflowPO, RequestInfo userInfo) {
        // 策略模版不查询权限
        String workflowType = botWorkflowPO.getWorkflowType();
        AppSourceType sourceSystem = botWorkflowPO.getSourceSystem();
        String workflowId = botWorkflowPO.getWorkflowId();
        Long version = botWorkflowPO.getVersion();
        checkAuth(userInfo, workflowType, workflowId, version, sourceSystem);
    }

    public void checkAuth(RequestInfo userInfo, String workflowType, String workflowId, Long version, AppSourceType sourceSystem) {
        if (WorkflowTypeEnum.STRATEGY_TEMPLATE.getCode().equals(workflowType)) {
            return;
        }
        // 检查权限步骤: 1.用户策略检查关联机器人权限, 2.工作流对话流检查是否为上架业务流,是否为自己业务流
        if (WorkflowTypeEnum.USER_STRATEGY.getCode().equals(workflowType)) {
            AgentWorkflowBindPO agentWorkflowBindPO = agentWorkflowBindService.queryByWorkflowIdAndType(workflowId, version, WorkflowTypeEnum.USER_STRATEGY.getCode());
            if (agentWorkflowBindPO != null) {
                agentBasicInfoService.checkAgentAuth(agentWorkflowBindPO.getAgentCode(), true);
            }
            return;
        }
        if (!checkWorkflowAuth(workflowId)) {
            throw new BizException("A0013", "权限验证失败");
        }

        if (userInfo.getAppSourceType() == null) {
            return;
        }
        BotWorkflowMallInfoPO botWorkflowMallInfoPO = botWorkflowMallService.queryByWorkflowId(workflowId);
        // 检查来源
        if (Objects.isNull(botWorkflowMallInfoPO) && !Objects.equals(userInfo.getAppSourceType(), sourceSystem)) {
            throw new BizException("AC051", "数据来源不一致");
        }
    }

    private void setTagForUpdate(BotWorkflowDetailRsp botWorkflowDetailRsp) {
        // 获取标签信息
        Set<String> tagCodeList = tagRelationRepository.selectTagCodeByTarget(TargetType.WORKFLOW, botWorkflowDetailRsp.getWorkflowId());
        if (CollectionUtils.isEmpty(tagCodeList)) {
            return;
        }

        botWorkflowDetailRsp.setTagCodeList(new ArrayList<>(tagCodeList));
    }

    @Override
    public BotWorkflowDetailRsp queryBotWorkflowDetail(String workflowId, boolean test) {
        // 判断是否有工作流权限
        RequestInfo userInfo = RequestContext.get();
        BotWorkflowDetailRsp rsp = new BotWorkflowDetailRsp();
        // 获取编辑版本
        Long version;
        if (test) {
            version = IWorkflowVersionService.getEditVersion(workflowId);
        } else {
            version = IWorkflowVersionService.getPublishVersion(workflowId);
        }
        if (version == null) {
            throw new BizException("AC007", "AC007");
        }
        log.info("查询agent详情,workflowId,{}, 版本：{}, userInfo:{}", workflowId, version, userInfo);
        // im聊天窗调用, 所以不传sourceType
        BotWorkflowPO botWorkflowPO = IBotWorkflowService.queryByIdAndVersion(workflowId, version, null, null);
        if (botWorkflowPO == null) {
            throw new BizException("AC007", "业务流不存在");
        }

        BeanUtils.copyProperties(botWorkflowPO, rsp);
        if (!StringUtils.isEmpty(botWorkflowPO.getScene())) {
            rsp.setScene(JSON.parseArray(botWorkflowPO.getScene(), String.class));
        }
        String canvas = botWorkflowPO.getCanvas();
        if (canvas != null && canvas.startsWith(Constants.FILE_TYPE)) {
            try {
                InputStream inputStream = SpringContextUtils.getBean(FileService.class).download(canvas);
                ByteArrayOutputStream result = new ByteArrayOutputStream();
                byte[] buffer = new byte[1024];
                int length;
                while ((length = inputStream.read(buffer)) != -1) {
                    result.write(buffer, 0, length);
                }
                canvas = result.toString(StandardCharsets.UTF_8.name());
            } catch (Exception e) {
                throw new BizException("AC025", "画布数据读取失败");
            }
        }
        rsp.setCanvas(canvas);

        List<BotWorkFlowNodePO> nodeList = IBotWorkflowNodeService.queryByWorkflowId(workflowId, null, version);
        if (!CollectionUtils.isEmpty(nodeList)) {
            rsp.setNodes(nodeList.stream().map(nodePO -> {
                Node node = new Node();
                node.setNodeId(nodePO.getNodeId());
                node.setNodeType(nodePO.getNodeType().getCode());
                node.setData(JSON.parseObject(nodePO.getData(), Node.NodeData.class));
                return node;
            }).collect(Collectors.toList()));
            for (BotWorkFlowNodePO node : nodeList) {
                Node.NodeData nodeData = JSON.parseObject(node.getData(), Node.NodeData.class);
                if (NodeTypeEnum.START.equals(node.getNodeType())) {
                    rsp.setInputParams(nodeData.getInputs().getRequestParams());
                }
                if (NodeTypeEnum.END.equals(node.getNodeType())) {
                    rsp.setOutputParams(nodeData.getOutputs());
                }
            }
        } else {
            rsp.setNodes(Lists.newArrayList());
        }
        List<BotWorkflowEdgePO> edgeList = IBotWorkflowEdgeService.queryByWorkflowId(workflowId, null, version);
        if (!CollectionUtils.isEmpty(edgeList)) {
            rsp.setEdges(edgeList.stream().map(edgePO -> {
                Edge edge = new Edge();
                BeanUtils.copyProperties(edgePO, edge);
                return edge;
            }).collect(Collectors.toList()));
        } else {
            rsp.setEdges(Lists.newArrayList());
        }
        if (!test) {
            LocalDateTime publishTime = IWorkflowVersionService.queryPublishTime(workflowId, version);
            rsp.setPublishTime(publishTime);
        }

        // 关联标签
        rsp.setTagList(getTag(rsp.getWorkflowId()));
        // 查询意图
        List<BotWorkflowIntentRelationPO> botWorkflowIntentRelationPOS = IBotWorkflowIntentRelationService.queryByWorkflowId(workflowId, botWorkflowPO.getVersion());
        if (!CollectionUtils.isEmpty(botWorkflowIntentRelationPOS)) {
            rsp.setIntents(botWorkflowIntentRelationPOS.stream().map(workflow -> {
                IntentEntity intentEntity = new IntentEntity();
                intentEntity.setIntentCode(workflow.getIntentCode());
                intentEntity.setIntentName(workflow.getIntentName());
                return intentEntity;
            }).collect(Collectors.toList()));
        }
        return rsp;
    }

    @Override
    public BotWorkflowDetailRsp queryDetailWithoutAuth(String workflowId, boolean test) {
        BotWorkflowDetailRsp rsp = new BotWorkflowDetailRsp();
        // 获取插件编辑版本
        Long version;
        if (test) {
            version = IWorkflowVersionService.getEditVersion(workflowId);
        } else {
            version = IWorkflowVersionService.getPublishVersion(workflowId);
        }
        if (version == null) {
            throw new BizException("AC007", "业务流不存在");
        }

        // im聊天窗调用, 所以不传sourceType
        BotWorkflowDetail botWorkflowPO = IBotWorkflowService.queryWorkflowWithoutCanvas(workflowId, version, null, null);
        if (botWorkflowPO == null) {
            throw new BizException("AC007", "业务流不存在");
        }

        BeanUtils.copyProperties(botWorkflowPO, rsp);

        List<BotWorkFlowNodePO> nodeList = IBotWorkflowNodeService.queryByWorkflowId(workflowId, null, version);
        if (!CollectionUtils.isEmpty(nodeList)) {
            rsp.setNodes(nodeList.stream().map(nodePO -> {
                Node node = new Node();
                node.setNodeId(nodePO.getNodeId());
                node.setNodeType(nodePO.getNodeType().getCode());
                node.setData(JSON.parseObject(nodePO.getData(), Node.NodeData.class));
                return node;
            }).collect(Collectors.toList()));
            for (BotWorkFlowNodePO node : nodeList) {
                Node.NodeData nodeData = JSON.parseObject(node.getData(), Node.NodeData.class);
                if (NodeTypeEnum.START.equals(node.getNodeType())) {
                    rsp.setInputParams(nodeData.getInputs().getRequestParams());
                }
                if (NodeTypeEnum.END.equals(node.getNodeType())) {
                    rsp.setOutputParams(nodeData.getOutputs());
                }
            }
        } else {
            rsp.setNodes(Lists.newArrayList());
        }

        List<BotWorkflowEdgePO> edgeList = IBotWorkflowEdgeService.queryByWorkflowId(workflowId, null, version);
        if (!CollectionUtils.isEmpty(edgeList)) {
            rsp.setEdges(edgeList.stream().map(edgePO -> {
                Edge edge = new Edge();
                BeanUtils.copyProperties(edgePO, edge);
                return edge;
            }).collect(Collectors.toList()));
        } else {
            rsp.setEdges(Lists.newArrayList());
        }

        // 查询意图
        List<BotWorkflowIntentRelationPO> botWorkflowIntentRelationPOS = IBotWorkflowIntentRelationService.queryByWorkflowId(workflowId, botWorkflowPO.getVersion());
        if (!CollectionUtils.isEmpty(botWorkflowIntentRelationPOS)) {
            rsp.setIntents(botWorkflowIntentRelationPOS.stream().map(workflow -> {
                IntentEntity intentEntity = new IntentEntity();
                intentEntity.setIntentCode(workflow.getIntentCode());
                intentEntity.setIntentName(workflow.getIntentName());
                return intentEntity;
            }).collect(Collectors.toList()));
        }

        return rsp;
    }

    /**
     * @param isSystem 是否是公开市场的（超级租户发布的）
     * @return BotWorkflowDetailRsp
     */
    @Override
    public BotWorkflowDetailRsp botWorkflowDetailByVersion(String workflowId, Long workflowVersion, boolean isAuth, boolean isSystem) {
        // 判断是否有工作流权限
        RequestInfo userInfo = RequestContext.get();
        BotWorkflowDetailRsp rsp = new BotWorkflowDetailRsp();
        String appCode = isSystem ? superTenant : userInfo.getAppCode();
        log.info("查询agent详情,workflowId,{}, 版本：{}, userInfo:{}", workflowId, workflowVersion, userInfo);
        BotWorkflowDetail botWorkflowPO = IBotWorkflowService.queryWorkflowWithoutCanvas(workflowId, workflowVersion, appCode, RequestContext.getAppSourceType().name());
        if (botWorkflowPO == null) {
            throw new BizException("AC007", "业务流不存在");
        }

        if (isAuth && !isSystem) {
            checkAuth(userInfo, botWorkflowPO.getWorkflowType(), workflowId, botWorkflowPO.getVersion(), botWorkflowPO.getSourceSystem());
        }

        BeanUtils.copyProperties(botWorkflowPO, rsp);
        if (!StringUtils.isEmpty(botWorkflowPO.getScene())) {
            rsp.setScene(JSON.parseArray(botWorkflowPO.getScene(), String.class));
        }
        List<BotWorkFlowNodePO> nodeList = IBotWorkflowNodeService.queryByWorkflowId(workflowId, userInfo.getAppCode(), workflowVersion);
        if (!CollectionUtils.isEmpty(nodeList)) {
            rsp.setNodes(nodeList.stream().map(nodePO -> {
                Node node = new Node();
                node.setNodeId(nodePO.getNodeId());
                node.setNodeType(nodePO.getNodeType().getCode());
                node.setData(JSON.parseObject(nodePO.getData(), Node.NodeData.class));
                return node;
            }).collect(Collectors.toList()));
            for (BotWorkFlowNodePO node : nodeList) {
                Node.NodeData nodeData = JSON.parseObject(node.getData(), Node.NodeData.class);
                if (NodeTypeEnum.START.equals(node.getNodeType())) {
                    rsp.setInputParams(nodeData.getInputs().getRequestParams());
                }
                if (NodeTypeEnum.END.equals(node.getNodeType())) {
                    rsp.setOutputParams(nodeData.getOutputs());
                }
            }
        }
        List<BotWorkflowEdgePO> edgeList = IBotWorkflowEdgeService.queryByWorkflowId(workflowId, userInfo.getAppCode(), workflowVersion);
        if (!CollectionUtils.isEmpty(edgeList)) {
            rsp.setEdges(edgeList.stream().map(edgePO -> {
                Edge edge = new Edge();
                BeanUtils.copyProperties(edgePO, edge);
                return edge;
            }).collect(Collectors.toList()));
        }
        LocalDateTime publishTime = IWorkflowVersionService.queryPublishTime(workflowId, workflowVersion);
        rsp.setPublishTime(publishTime);
        return rsp;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createBotSubWorkflow(BotWorkflowCreateSubParam botWorkflowCreateSubParam) {
        List<PrivilegeResources> privilegeResources = privilegeUtil.checkResourcePrivileges(null, ResourceTypeEnum.WORKFLOW, null);
        Set<String> resourceCodes = privilegeResources.stream().map(PrivilegeResources::getResourceId).collect(Collectors.toSet());
        RequestInfo userInfo = RequestContext.get();
        // 查询主工作流
        Long version = IWorkflowVersionService.getEditVersion(botWorkflowCreateSubParam.getMainWorkflowId());
        BotWorkflowPO botWorkflowPO = IBotWorkflowService.queryByIdAndVersion(botWorkflowCreateSubParam.getMainWorkflowId(), version, userInfo.getAppCode(), RequestContext.getAppSourceType().name());
        if (botWorkflowPO == null) {
            throw new BizException("AC007", "主工作流不存在");
        }
        if (!botWorkflowPO.getWorkflowType().equals(WorkflowTypeEnum.DIALOGFLOW.getCode())) {
            throw new BizException("AC021", "主工作流不是对话流");
        }
        //1. 先检查名称是否重复
        checkName(botWorkflowCreateSubParam.getWorkflowCnName(), userInfo, resourceCodes);

        if (CheckImgUtils.isNotEndWithImg(botWorkflowCreateSubParam.getIconUrl())) {
            throw new BizException("AC049", "对话流图标存在非法输入");
        }

        // 创建子流程
        String subWorkflowId = UUID.randomUUID().toString().replace("-", "");
        initConfig(subWorkflowId);
        BotWorkflowPO subWorkflow = new BotWorkflowPO();
        BeanUtils.copyProperties(botWorkflowPO, subWorkflow);
        subWorkflow.setId(null);
        subWorkflow.setWorkflowId(subWorkflowId);
        subWorkflow.setStatus(PublishStateEnum.UNPUBLISHED.getCode());
        subWorkflow.setWorkflowName(botWorkflowCreateSubParam.getWorkflowName());
        subWorkflow.setWorkflowCnName(botWorkflowCreateSubParam.getWorkflowCnName());
        subWorkflow.setWorkflowType(WorkflowTypeEnum.DIALOGFLOW.getCode());
        subWorkflow.setCanvas(null);
        subWorkflow.setDescription(botWorkflowCreateSubParam.getDescription());
        subWorkflow.setIconUrl(botWorkflowCreateSubParam.getIconUrl());

        subWorkflow.setTenantId(userInfo.getTenantId());
        subWorkflow.setVersion(1L);
        subWorkflow.setCreateId(userInfo.getUserId());
        subWorkflow.setUpdateId(userInfo.getUserId());
        subWorkflow.setCreateName(userInfo.getUserName());
        subWorkflow.setUpdateName(userInfo.getUserName());
        subWorkflow.setCreateTime(LocalDateTime.now());
        subWorkflow.setUpdateTime(LocalDateTime.now());
        subWorkflow.setAppCode(userInfo.getAppCode());
        subWorkflow.setMainWorkflowId(botWorkflowPO.getWorkflowId());
        subWorkflow.setSourceSystem(userInfo.getAppSourceType());
        IBotWorkflowService.save(subWorkflow);

        handleTag(subWorkflow.getWorkflowId(), botWorkflowCreateSubParam.getTagCodeList());

        // 注册资源
        saveOrUpdateAuth(subWorkflowId);

        return subWorkflowId;
    }

    private void checkName(String workflowCnName, RequestInfo userInfo, Set<String> resourceCodes) {
        if (StringUtils.isEmpty(workflowCnName)) {
            throw new BizException("AC008", "名称不能为空");
        }
        if (workflowCnName.length() > 50) {
            throw new BizException("AC009", "名称最长不能超过50个字符");
        }
        List<BotWorkflowPO> workflowPOList = IBotWorkflowService.queryWorkflowByName(workflowCnName, 1L, userInfo.getAppCode(), resourceCodes, userInfo.getAppSourceType().name());
        if (!workflowPOList.isEmpty()) {
            throw new BizException("AC010", "该名称已存在");
        }
    }

    @Override
    public Page<BotWorkflowRsp> queryWorkflowList(QueryWorkflowParam pageQuery) {
        // 查询有资源权限的工作流
        Page<BotWorkflowRsp> workflowPOPage = new Page<>(pageQuery.getPageIndex(), pageQuery.getPageSize());
        // 策略模板需要查询租户级的数据, 即不查询统一认证平台
        Set<String> resourceCodes;
        AppSourceType appSourceType = RequestContext.getAppSourceType();
        if (StringUtils.isNotEmpty(pageQuery.getWorkflowTypeList()) && pageQuery.getWorkflowTypeList().contains(WorkflowTypeEnum.STRATEGY_TEMPLATE.getCode())) {
            resourceCodes = new HashSet<>();
            appSourceType = null;
        } else {
            List<PrivilegeResources> privilegeResources = privilegeUtil.checkResourcePrivileges(null, ResourceTypeEnum.WORKFLOW, null);
            resourceCodes = privilegeResources.stream().map(PrivilegeResources::getResourceId).collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(resourceCodes)) {
                return workflowPOPage;
            }
        }
        if (CollectionUtils.isNotEmpty(pageQuery.getTagCodeList())) {
            // 根据标签查询出所有的子级
            Set<String> allSubTagCodes = tagAppRepository.fetchAllSubTag(pageQuery.getTagCodeList());
            allSubTagCodes.addAll(pageQuery.getTagCodeList());
            List<String> workFlowIdList = tagRelationRepository.findByTagCodes(TargetType.PROMPT, allSubTagCodes);
            if (CollectionUtils.isEmpty(workFlowIdList)) {
                return workflowPOPage;
            }
            resourceCodes = new HashSet<>(workFlowIdList);
        }

        Page<BotWorkflowPO> botWorkflowPOPage = IBotWorkflowService.queryWorkflowList(pageQuery, resourceCodes, appSourceType);
        // 查询工作流入参， 需要查询线上版本数据
        BeanUtils.copyProperties(botWorkflowPOPage, workflowPOPage);
        List<BotWorkflowPO> records = botWorkflowPOPage.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return workflowPOPage;
        }
        List<BotWorkflowRsp> botWorkflowRspList = records.stream().map(workflow -> {
            BotWorkflowRsp botWorkflowRsp = new BotWorkflowRsp();
            BeanUtils.copyProperties(workflow, botWorkflowRsp);
            // 查询bot引用次数
            Result<Integer> integerResult = agentInfoRpcApi.queryBotRefNum(workflow.getWorkflowId());
            Integer botRefCount = integerResult.getData();
            botWorkflowRsp.setBotRefCount(botRefCount);
            botWorkflowRsp.setTagList(getTag(botWorkflowRsp.getWorkflowId()));
            return botWorkflowRsp;
        }).collect(Collectors.toList());

        if (Boolean.TRUE.equals(pageQuery.getNeedInputParams())) {
            botWorkflowRspList.forEach(this::fillParams);
        }
        workflowPOPage.setRecords(botWorkflowRspList);
        workflowPOPage.setTotal(botWorkflowPOPage.getTotal());
        workflowPOPage.setCurrent(botWorkflowPOPage.getCurrent());
        workflowPOPage.setSize(botWorkflowPOPage.getSize());
        return workflowPOPage;
    }

    @Override
    public List<TagEntity> getTag(String workflowId) {
        // 获取标签信息
        List<TagEntity> tagEntityList = Lists.newArrayList();
        Set<String> tagCodeList = tagRelationRepository.selectTagCodeByTarget(TargetType.WORKFLOW, workflowId);
        if (CollectionUtils.isEmpty(tagCodeList)) {
            return tagEntityList;
        }
        Map<String, String> codeNameMap = tagAppRepository.queryParentNameByCodes(tagCodeList);
        tagCodeList.forEach(tagCode -> {
            TagDTO tagDTO = tagAppRepository.selectByCode(tagCode);
            if (tagDTO == null) {
                return;
            }
            TagEntity tagEntity = new TagEntity();
            tagEntity.setTagCode(tagDTO.getCode());
            tagEntity.setTagName(tagDTO.getName());
            if (StringUtils.isNotEmpty(tagDTO.getPath())) {
                String path = tagDTO.getPath();
                String[] tagPaths = path.split("/");
                tagEntity.setPath(Arrays.stream(tagPaths).filter(StringUtils::isNotBlank).
                        map(codeNameMap::get).collect(Collectors.joining("/")));
            }
            tagEntityList.add(tagEntity);
        });
        return tagEntityList;
    }

    @Override
    public Page<BotWorkflowRsp> queryCanvasWorkflowList(QueryWorkflowParam pageQuery) {
        // 查询有资源权限的工作流
        Page<BotWorkflowRsp> workflowPOPage = new Page<>(pageQuery.getPageIndex(), pageQuery.getPageSize());
        List<PrivilegeResources> privilegeResources = privilegeUtil.checkResourcePrivileges(null, ResourceTypeEnum.WORKFLOW, null);
        Set<String> resourceCodes = privilegeResources.stream().map(PrivilegeResources::getResourceId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(resourceCodes)) {
            return workflowPOPage;
        }
        Page<BotWorkflowPO> botWorkflowPOPage = IBotWorkflowService.queryMainWorkflowList(pageQuery, resourceCodes);
        // 查询工作流入参， 需要查询线上版本数据
        BeanUtils.copyProperties(botWorkflowPOPage, workflowPOPage);
        List<BotWorkflowPO> records = botWorkflowPOPage.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return workflowPOPage;
        }
        List<BotWorkflowRsp> botWorkflowRspList = getBotWorkflowRsps(records);

        if (Boolean.TRUE.equals(pageQuery.getNeedInputParams())) {
            botWorkflowRspList.forEach(workflow -> {
                fillParams(workflow);
                // 查询子流程
                List<BotWorkflowRsp> subWorkflowList = querySubWorkflowList(workflow.getWorkflowId());
                workflow.setSubWorkflowList(subWorkflowList);
            });
        }
        workflowPOPage.setRecords(botWorkflowRspList);
        workflowPOPage.setTotal(botWorkflowPOPage.getTotal());
        workflowPOPage.setCurrent(botWorkflowPOPage.getCurrent());
        workflowPOPage.setSize(botWorkflowPOPage.getSize());
        return workflowPOPage;
    }

    @Override
    public void fillParams(BotWorkflowRsp workflow) {
        Long version = IWorkflowVersionService.getPublishVersion(workflow.getWorkflowId());
        // 线上版本不存在，则使用编辑版本
        if (version == null) {
            version = workflow.getVersion();
        } else {
            LocalDateTime publishTime = IWorkflowVersionService.queryPublishTime(workflow.getWorkflowId(), version);
            workflow.setPublishTime(publishTime);
        }

        if (workflow.getVersion() == null || workflow.getStatus() == null) {
            BotWorkflowPO botWorkflowPO = IBotWorkflowService.queryByIdAndVersion(workflow.getWorkflowId(), version, RequestContext.getAppCode(), RequestContext.getAppSourceType().name());
            if (botWorkflowPO == null) {
                return;
            }
            workflow.setVersion(botWorkflowPO.getVersion());
            workflow.setStatus(botWorkflowPO.getStatus());
        }
        // 查询输入参数
        List<BotWorkFlowNodePO> botWorkFlowNodePOS = IBotWorkflowNodeService.queryNodeByType(workflow.getWorkflowId(), version,
                workflow.getAppCode(), Arrays.asList(NodeTypeEnum.START.getCode(), NodeTypeEnum.END.getCode()));
        if (CollectionUtils.isEmpty(botWorkFlowNodePOS)) {
            return;
        }
        for (BotWorkFlowNodePO botWorkFlowNodePO : botWorkFlowNodePOS) {
            Node.NodeData nodeData = JSON.parseObject(botWorkFlowNodePO.getData(), Node.NodeData.class);
            if (NodeTypeEnum.START.equals(botWorkFlowNodePO.getNodeType())) {
                workflow.setInputParams(nodeData.getInputs().getRequestParams());
            }
            if (NodeTypeEnum.END.equals(botWorkFlowNodePO.getNodeType())) {
                workflow.setOutputParams(nodeData.getOutputs());
            }
        }
    }

    @Override
    public List<KnowledgeResponse> checkKms(String workflowId) {
        List<KnowledgeResponse> knowledgeResponseList = Lists.newArrayList();
        // 查询业务流详情
        BotWorkflowDetailRsp workflowDetail = queryBotWorkflowDetail(workflowId, true);

        if (workflowDetail == null) {
            return knowledgeResponseList;
        }

        // 检查是否存在知识库节点
        List<Node> nodes = workflowDetail.getNodes().stream().filter(node -> NodeTypeEnum.KNOWLEDGE.getCode().equals(node.getNodeType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(nodes)) {
            return knowledgeResponseList;
        }
        try {
            nodes.stream().map(node -> node.getData().getInputs().getBizParam()).map(bizParam -> JSON.parseObject(JSON.toJSONString(bizParam), KnowledgeNodeBizParam.class)).forEach(knowledgeNodeBizParam -> {
                List<String> knowledegIdList = knowledgeNodeBizParam.getKmsList().stream().map(KnowledgeNodeBizParam.KmsItem::getKnowledgeId).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(knowledegIdList)) {
                    return;
                }
                Map<String, String> typeMap = knowledgeNodeBizParam.getKmsList().stream().collect(Collectors.toMap(KnowledgeNodeBizParam.KmsItem::getKnowledgeId, KnowledgeNodeBizParam.KmsItem::getKnowledgeType));
                CodeBatchParam codeBatchParam = new CodeBatchParam();
                codeBatchParam.setCodes(knowledegIdList);
                com.chinatelelcom.gs.engine.sdk.common.Result<List<KBPublishStatisticsVO>> publishStatisticsResult = kmsKnowledgeBaseApi.statisticsPublish(codeBatchParam);
                if (publishStatisticsResult == null || !publishStatisticsResult.isSuccess() || publishStatisticsResult.getData() == null) {
                    // 可选：添加日志记录失败原因
                    log.error("查询知识库是否存在未发布文档失败, 知识库:{}", knowledegIdList);
                    return;
                }
                List<KBPublishStatisticsVO> data = publishStatisticsResult.getData();
                for (KBPublishStatisticsVO vo : data) {
                    if (vo.getUnpublishedCount() > 0) {
                        KnowledgeResponse response = new KnowledgeResponse();
                        response.setName(vo.getName());
                        response.setType(typeMap.get(vo.getKnowledgeBaseCode()));
                        knowledgeResponseList.add(response);
                    }
                }
            });
        } catch (Exception e) {
            log.error("checkKms error", e);
        }
        return knowledgeResponseList;
    }

    private @NotNull List<BotWorkflowRsp> getBotWorkflowRsps(List<BotWorkflowPO> records) {
        return records.stream().map(workflow -> {
            BotWorkflowRsp botWorkflowRsp = new BotWorkflowRsp();
            BeanUtils.copyProperties(workflow, botWorkflowRsp);
            if (WorkflowTypeEnum.DIALOGFLOW.getCode().equals(workflow.getWorkflowType())) {
                try {
                    if (Objects.isNull(this.parameters)) {
                        this.parameters = JSON.parseArray(param, Param.class);
                    }
                    botWorkflowRsp.setOutputParams(parameters);
                } catch (Exception e) {
                    log.error("获取参数失败", e);
                }
            }
            // 查询bot引用次数
            Result<Integer> integerResult = agentInfoRpcApi.queryBotRefNum(workflow.getWorkflowId());
            Integer botRefCount = integerResult.getData();
            botWorkflowRsp.setBotRefCount(botRefCount);
            return botWorkflowRsp;
        }).collect(Collectors.toList());
    }

    private List<BotWorkflowRsp> querySubWorkflowList(String workflowId) {
        // 查询子流程
        List<BotWorkflowPO> subWorkflowList = IBotWorkflowService.querySubWorkflowList(workflowId);
        if (CollectionUtils.isEmpty(subWorkflowList)) {
            return Collections.emptyList();
        }
        List<BotWorkflowRsp> botWorkflowRsps = getBotWorkflowRsps(subWorkflowList);
        botWorkflowRsps.forEach(this::fillParams);
        return botWorkflowRsps;
    }

    public void sendEditMsg(RequestInfo userInfo, String workflowId, EventTypeEnum typeEnum) {
        try {
            DagEvent dagEvent = new DagEvent();
            dagEvent.setTenantId(userInfo.getTenantId());
            dagEvent.setOperateType(typeEnum.toString());
            dagEvent.setUserInfo(JSON.parseObject(JSON.toJSONString(userInfo)));
            dagEvent.setWorkflowId(workflowId);
            kafkaMQService.sendMessage(TopicConstants.GS_WORKFLOW_CHANGE_TOPIC, JSON.toJSONString(dagEvent));
        } catch (Exception e) {
            log.error("发送编辑业务流mq消息失败", e);
        }
    }


    private void checkResourceAuth(String workflowId) {
        GrantObjectDTO grantObjectDTO = privilegeUtil.hasPrivilege(workflowId, ResourceTypeEnum.WORKFLOW);
        if (grantObjectDTO == null) {
            throw new BizException("CA007", "没有权限访问该资源");
        }
        boolean checkResult = grantObjectDTO.getPrivilege() != null && !grantObjectDTO.getPrivilege().equals(PrivilegeEnum.no_permission);
        if (!checkResult) {
            throw new BizException("CA007", "没有权限访问该资源");
        }
    }

}
