package com.chinatelecom.gs.engine.core.manager.worker;

import com.chinatelecom.gs.engine.common.config.property.GsGlobalConfig;
import com.chinatelecom.gs.engine.common.log.track.mapper.LogMapper;
import com.chinatelecom.gs.engine.core.manager.service.LogEsService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class LogCleanTaskTest {

    @InjectMocks
    private LogCleanTask logCleanTask;

    @Mock
    private LogMapper logMapper;

    @Mock
    private LogEsService logEsService;

    @Mock
    private GsGlobalConfig gsGlobalConfig;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        // 设置dbName字段
        ReflectionTestUtils.setField(logCleanTask, "dbName", "test_db");

        // 设置formatter字段
        ReflectionTestUtils.setField(logCleanTask, "formatter", DateTimeFormatter.ofPattern("yyyyMM"));
    }

    @Test
    void testClearLog_WhenTableExists_DeletesTableAndEsLogs() {
        // Given
        GsGlobalConfig.TraceLogProperty traceLogProperty = mock(GsGlobalConfig.TraceLogProperty.class);
        when(gsGlobalConfig.getTraceLogProperty()).thenReturn(traceLogProperty);
        when(traceLogProperty.getDbMaxMonth()).thenReturn(3);
        when(traceLogProperty.getEsMaxMonth()).thenReturn(6);

        String expectedTableName = "gs_log_" + YearMonth.now().minusMonths(3).format(DateTimeFormatter.ofPattern("yyyyMM"));
        when(logMapper.existTable("test_db", expectedTableName)).thenReturn(1);

        when(logEsService.deleteLogsBySendTimeRange(eq(LocalDateTime.MIN), any())).thenReturn(true);

        // When
        assertDoesNotThrow(() -> logCleanTask.clearLog());

        // Then
        verify(logMapper).existTable("test_db", expectedTableName);
        verify(logMapper).deleteTable(expectedTableName);
        verify(logEsService).deleteLogsBySendTimeRange(eq(LocalDateTime.MIN), any());
    }


    @Test
    void testClearLog_WhenTableDoesNotExist_OnlyDeletesEsLogs() {
        // Given
        GsGlobalConfig.TraceLogProperty traceLogProperty = mock(GsGlobalConfig.TraceLogProperty.class);
        when(gsGlobalConfig.getTraceLogProperty()).thenReturn(traceLogProperty);
        when(traceLogProperty.getDbMaxMonth()).thenReturn(3);
        when(traceLogProperty.getEsMaxMonth()).thenReturn(6);

        String expectedTableName = "gs_log_" + YearMonth.now().minusMonths(3).format(DateTimeFormatter.ofPattern("yyyyMM"));
        when(logMapper.existTable("test_db", expectedTableName)).thenReturn(0);

        when(logEsService.deleteLogsBySendTimeRange(eq(LocalDateTime.MIN), any())).thenReturn(true);

        // When
        assertDoesNotThrow(() -> logCleanTask.clearLog());

        // Then
        verify(logMapper).existTable("test_db", expectedTableName);
        verify(logMapper, never()).deleteTable(anyString());
        verify(logEsService).deleteLogsBySendTimeRange(eq(LocalDateTime.MIN), any());
    }


    @Test
    void testClearLog_WhenLogEsServiceThrowsException_LogsError() {
        // Given
        GsGlobalConfig.TraceLogProperty traceLogProperty = mock(GsGlobalConfig.TraceLogProperty.class);
        when(gsGlobalConfig.getTraceLogProperty()).thenReturn(traceLogProperty);
        when(traceLogProperty.getDbMaxMonth()).thenReturn(3);
        when(traceLogProperty.getEsMaxMonth()).thenReturn(6);

        String expectedTableName = "gs_log_" + YearMonth.now().minusMonths(3).format(DateTimeFormatter.ofPattern("yyyyMM"));
        when(logMapper.existTable("test_db", expectedTableName)).thenReturn(0);

        LocalDateTime expectedTime = LocalDateTime.now().minusMonths(6);
        doThrow(new RuntimeException("ES error")).when(logEsService).deleteLogsBySendTimeRange(eq(LocalDateTime.MIN), any());

        // When
        assertDoesNotThrow(() -> logCleanTask.clearLog());

        // Then
        verify(logMapper).existTable("test_db", expectedTableName);
        verify(logMapper, never()).deleteTable(anyString());
        verify(logEsService).deleteLogsBySendTimeRange(eq(LocalDateTime.MIN), any());
    }


    @Test
    void testGetPreNTableName_ReturnsCorrectTableName() {
        // When
        // Use reflection to access private method
        String result = null;
        try {
            java.lang.reflect.Method method = LogCleanTask.class.getDeclaredMethod("getPreNTableName", Integer.class);
            method.setAccessible(true);
            result = (String) method.invoke(logCleanTask, 3);
        } catch (Exception e) {
            fail("Exception should not be thrown: " + e.getMessage());
        }

        // Then
        assertNotNull(result);
        assertTrue(result.startsWith("gs_log_"));
        // Format should be "gs_log_" + yyyyMM, which is 13 characters total
        assertEquals(13, result.length()); // "gs_log_" (7) + yyyyMM (6) = 13 characters
    }


    @Test
    void testGetPreNMonthTime_ReturnsCorrectTime() {
        // When
        // Use reflection to access private method
        LocalDateTime result = null;
        try {
            java.lang.reflect.Method method = LogCleanTask.class.getDeclaredMethod("getPreNMonthTime", Integer.class);
            method.setAccessible(true);
            result = (LocalDateTime) method.invoke(logCleanTask, 3);
        } catch (Exception e) {
            fail("Exception should not be thrown: " + e.getMessage());
        }

        // Then
        assertNotNull(result);
        // Check that the time is approximately 3 months ago
        LocalDateTime expectedTime = LocalDateTime.now().minusMonths(3);
        // Allow for a small time difference due to test execution time
        assertTrue(result.isBefore(expectedTime.plusMinutes(1)));
        assertTrue(result.isAfter(expectedTime.minusMinutes(1)));
    }

    @Test
    void testClearLog_WhenTraceLogPropertyIsNull_HandlesGracefully() {
        // Given
        when(gsGlobalConfig.getTraceLogProperty()).thenReturn(null);

        // When
        assertDoesNotThrow(() -> logCleanTask.clearLog());

        // Then
        verify(logMapper, never()).existTable(anyString(), anyString());
        verify(logMapper, never()).deleteTable(anyString());
        verify(logEsService, never()).deleteLogsBySendTimeRange(any(), any());
    }

    @Test
    void testClearLog_WhenDbMaxMonthIsNull_HandlesGracefully() {
        // Given
        GsGlobalConfig.TraceLogProperty traceLogProperty = mock(GsGlobalConfig.TraceLogProperty.class);
        when(gsGlobalConfig.getTraceLogProperty()).thenReturn(traceLogProperty);
        when(traceLogProperty.getDbMaxMonth()).thenReturn(null);
        when(traceLogProperty.getEsMaxMonth()).thenReturn(6);

        // When
        assertDoesNotThrow(() -> logCleanTask.clearLog());

        // Then
        verify(logMapper, never()).existTable(anyString(), anyString());
        verify(logMapper, never()).deleteTable(anyString());
        verify(logEsService, never()).deleteLogsBySendTimeRange(any(), any());
    }


    @Test
    void testClearLog_WhenEsMaxMonthIsNull_HandlesGracefully() {
        // Given
        GsGlobalConfig.TraceLogProperty traceLogProperty = mock(GsGlobalConfig.TraceLogProperty.class);
        when(gsGlobalConfig.getTraceLogProperty()).thenReturn(traceLogProperty);
        when(traceLogProperty.getDbMaxMonth()).thenReturn(3);
        when(traceLogProperty.getEsMaxMonth()).thenReturn(null);

        String expectedTableName = "gs_log_" + YearMonth.now().minusMonths(3).format(DateTimeFormatter.ofPattern("yyyyMM"));
        when(logMapper.existTable("test_db", expectedTableName)).thenReturn(0);

        // When
        assertDoesNotThrow(() -> logCleanTask.clearLog());

        // Then
        verify(logMapper).existTable("test_db", expectedTableName);
        verify(logMapper, never()).deleteTable(anyString());
        verify(logEsService, never()).deleteLogsBySendTimeRange(any(), any());
    }

}
