UPDATE `gs_base_config` SET `config_data`='{"publishSwitch":true,"auditSwitch":false,"processCode":"","introductionSwitch":false,"safeFenceSwitch":false}'
where `code`='system-publishConfig' and `dimension`='SYSTEM' and `config_type`='publishConfig' and `business_no`='SYSTEM' limit 1;


UPDATE bot_workflow SET canvas = '{"nodes":[{"position":{"x":1190,"y":70},"size":{"width":360,"height":128},"view":"vue-shape-view","shape":"end","ports":{"groups":{"right":{"attrs":{"circle":{"r":6,"magnet":true,"strokeWidth":2,"stroke":"#fff","fill":"#9197F1"}},"position":"right"},"left":{"attrs":{"circle":{"r":6,"magnet":true,"strokeWidth":2,"stroke":"#fff","fill":"#9197F1"}},"position":"left"},"pos":{"attrs":{"circle":{"r":6,"magnet":true,"strokeWidth":2,"stroke":"#fff","fill":"#9197F1"}},"position":{"name":"absolute"}}},"items":[{"id":"561e13f7-a0e7-4762-83ec-3626e36705df","group":"left","options":{},"attrs":{"circle":{"fill":"#4D53E8"}}}]},"id":"c28fe6e5-7d17-4ddb-b837-186cf859a1dd","data":{"shape":"end","title":"结束","hideDel":false,"initNode":false,"description":"工作流的最终节点，用于返回工作流运行后的结果信息。","initPos":{"x":1200,"y":100},"inputs":{"inputParameters":[],"settingOnError":{},"bizParam":{}},"branches":[{"position":"left","branchParam":{},"portId":"561e13f7-a0e7-4762-83ec-3626e36705df"}],"outputs":[{"name":"output","paramType":1,"valueRefName":"$.output","valueRefNodeId":"dfbffd79-3fdd-40f4-8523-3be3f645fc7f","valueRefNodeName":"融合策略","valueRefSource":"output","subParameters":[],"value":"","valueRefType":"ref","refVariable":null}],"bizParam":{"answerType":"returnByVariable","answerContent":{"type":"string","value":""}}},"zIndex":1},{"position":{"x":70,"y":88},"size":{"width":360,"height":92},"view":"vue-shape-view","shape":"start","ports":{"groups":{"right":{"attrs":{"circle":{"r":6,"magnet":true,"strokeWidth":2,"stroke":"#fff","fill":"#9197F1"}},"position":"right"},"left":{"attrs":{"circle":{"r":6,"magnet":true,"strokeWidth":2,"stroke":"#fff","fill":"#9197F1"}},"position":"left"},"pos":{"attrs":{"circle":{"r":6,"magnet":true,"strokeWidth":2,"stroke":"#fff","fill":"#9197F1"}},"position":{"name":"absolute"}}},"items":[{"id":"4d44a576-d2df-4da5-9d2f-bba127707339","group":"right","options":{},"attrs":{"circle":{"fill":"#4D53E8"}}}]},"id":"c31eb8dd-3246-4780-b73d-853d1eedda4e","data":{"shape":"start","hideDel":true,"title":"开始","description":"工作流的起始节点，用于设定启动工作流需要的信息。","initNode":true,"requestParams":[{"name":"BOT_USER_INPUT","paramDesc":"用户本轮对话输入内容","paramType":1,"location":1,"required":true,"defaultValue":"","enabled":true}],"settingOnError":{},"bizParam":{},"outputs":[],"branches":[{"position":"right","branchParam":{},"portId":"4d44a576-d2df-4da5-9d2f-bba127707339"}],"initPos":{"x":100,"y":100}},"zIndex":2},{"position":{"x":660,"y":70},"size":{"width":360,"height":140},"view":"vue-shape-view","shape":"dialogStrategy","ports":{"groups":{"right":{"attrs":{"circle":{"r":6,"magnet":true,"strokeWidth":2,"stroke":"#fff","fill":"#9197F1"}},"position":"right"},"left":{"attrs":{"circle":{"r":6,"magnet":true,"strokeWidth":2,"stroke":"#fff","fill":"#9197F1"}},"position":"left"},"pos":{"attrs":{"circle":{"r":6,"magnet":true,"strokeWidth":2,"stroke":"#fff","fill":"#9197F1"}},"position":{"name":"absolute"}}},"items":[{"id":"23f9d4da-4025-4c52-907a-298c3dee2292","group":"left","options":{},"attrs":{"circle":{"fill":"#4D53E8"}}},{"id":"28a9dd7e-ae96-4a52-b32d-e7eff2ea1b83","group":"right","options":{"output":true},"attrs":{"circle":{"fill":"#4D53E8"}}}]},"id":"dfbffd79-3fdd-40f4-8523-3be3f645fc7f","name":"融合策略","nodeType":"dialogStrategy","component":{"__v_isVNode":true,"__v_skip":true,"type":{"__name":"MenuNode","props":{"icon":{},"name":{}},"emits":["customEvent"],"__cssModules":{"Css":{"container":"_container_4aag0_1","info":"_info_4aag0_19","infoItem":"_infoItem_4aag0_26","icon":"_icon_4aag0_31","name":"_name_4aag0_39","addicon":"_addicon_4aag0_42"}}},"props":{"name":"融合策略"},"key":null,"ref":null,"scopeId":null,"slotScopeIds":null,"children":null,"component":null,"suspense":null,"ssContent":null,"ssFallback":null,"dirs":null,"transition":null,"el":null,"anchor":null,"target":null,"targetAnchor":null,"staticCount":0,"shapeFlag":4,"patchFlag":0,"dynamicProps":null,"dynamicChildren":null,"appContext":null,"ctx":null},"data":{"shape":"dialogStrategy","title":"融合策略","groupName":"大模型","description":"用户输入一个命题，从互联网上检索信息，判断用户的命题是否正确。","requestParams":[{"name":"query","paramType":1,"location":1,"required":false,"defaultValue":"","enabled":true,"valueRefName":"$.BOT_USER_INPUT","valueRefNodeId":"c31eb8dd-3246-4780-b73d-853d1eedda4e","valueRefNodeName":"开始","valueRefSource":"input","subParameters":[],"value":"","valueRefType":"ref","refVariable":null}],"settingOnError":{},"bizParam":{"skillParam":{"enableBotSkills":true,"skillList":[]},"kmsParam":{"retriever":3,"searchStrategy":"MIX","faq":{"enableSortModel":true,"sortCode":"model_992668744299715591","enableLLMModel":true,"llmAccept":0.5,"topn":3,"faqAccept":0.85},"doc":{"enableSortModel":false,"sortCode":"model_992668744299715591","docAccept":0.3,"topn":5}},"prompt":"{{query}}","systemPrompt":"","modelCode":"model_901039658069659653","modelName":"TeleChat","temperature":0.1,"topP":0.7,"maxTokens":1024},"outputs":[{"name":"output","paramDesc":"","paramType":1}],"branches":[{"position":"left","input":true,"branchParam":{},"portId":"23f9d4da-4025-4c52-907a-298c3dee2292"},{"output":true,"position":"right","branchParam":{},"portId":"28a9dd7e-ae96-4a52-b32d-e7eff2ea1b83"}]},"zIndex":3}],"edges":[{"shape":"edge","attrs":{"line":{"stroke":"#4D53E8"}},"id":"190bf85e-51b6-4bdf-9a5a-1c023e6d4e66","source":{"cell":"c31eb8dd-3246-4780-b73d-853d1eedda4e","port":"4d44a576-d2df-4da5-9d2f-bba127707339"},"target":{"cell":"dfbffd79-3fdd-40f4-8523-3be3f645fc7f","port":"23f9d4da-4025-4c52-907a-298c3dee2292"},"zIndex":4,"tools":{"items":[],"name":null}},{"shape":"edge","attrs":{"line":{"stroke":"#4D53E8"}},"id":"77559207-4f9a-4919-a42d-7aa1436f1934","source":{"cell":"dfbffd79-3fdd-40f4-8523-3be3f645fc7f","port":"28a9dd7e-ae96-4a52-b32d-e7eff2ea1b83"},"target":{"cell":"c28fe6e5-7d17-4ddb-b837-186cf859a1dd","port":"561e13f7-a0e7-4762-83ec-3626e36705df"},"zIndex":5,"tools":{"items":[],"name":null}}]}' WHERE workflow_id = 'ab1070c5-d83e-4a0d-aaa7-d7c0deb99883';


UPDATE agent_default_config SET config_value = '{"doc": {"docAccept": 0.3, "enableSortModel": false, "sortCode": "model_992668744299715591", "topn": 5}, "faq": {"enableLLMModel": true,"enableSortModel": true,"faqAccept": 0.85,"llmAccept": 0.5,"sortCode": "model_992668744299715591","topn": 3 },"searchStrategy": "MIX"}' WHERE config_key = 'agentConfig.kms.config';