//package com.chinatelecom.gs.engine.kms.util.safe;
//
//import org.junit.Assert;
//import org.junit.Test;
//
//import java.io.InputStream;
//
//public class ExcelDDECheckUtilsTest {
//
//    @Test
//    public void checkXlsx() {
//        InputStream inputStream = getClass().getClassLoader().getResourceAsStream("file/FAQ_NOT_SAFE.xlsx");
//        boolean result = ExcelDDECheckUtils.hasDDEInjectionRisk(inputStream, true);
//        Assert.assertTrue(result);
//    }
//
//    @Test
//    public void checkXls() {
//        InputStream inputStream = getClass().getClassLoader().getResourceAsStream("file/FAQ_NOT_SAFE_xls.xls");
//        boolean result = ExcelDDECheckUtils.hasDDEInjectionRisk(inputStream, false);
//        Assert.assertTrue(result);
//    }
//}
