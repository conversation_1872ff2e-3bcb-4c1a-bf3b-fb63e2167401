package com.chinatelecom.gs.engine.channel.dao.mapper;

import com.chinatelecom.gs.engine.channel.dao.po.ChannelMediaPO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

class ChannelMediaMapperTest {
    @Mock
    private ChannelMediaMapper channelMediaMapper;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testSelectById() {
        // Arrange
        Long id = 1L;
        ChannelMediaPO expectedPo = new ChannelMediaPO();
        when(channelMediaMapper.selectById(id)).thenReturn(expectedPo);

        // Act
        ChannelMediaPO result = channelMediaMapper.selectById(id);

        // Assert
        assertSame(expectedPo, result);
        verify(channelMediaMapper).selectById(id);
    }

    @Test
    public void testInsert() {
        // Arrange
        ChannelMediaPO po = new ChannelMediaPO();
        int expectedRowsAffected = 1;
        when(channelMediaMapper.insert(po)).thenReturn(expectedRowsAffected);

        // Act
        int rowsAffected = channelMediaMapper.insert(po);

        // Assert
        assertEquals(expectedRowsAffected, rowsAffected);
        verify(channelMediaMapper).insert(po);
    }

    @Test
    public void testUpdateById() {
        // Arrange
        ChannelMediaPO po = new ChannelMediaPO();
        int expectedRowsAffected = 1;
        when(channelMediaMapper.updateById(po)).thenReturn(expectedRowsAffected);

        // Act
        int rowsAffected = channelMediaMapper.updateById(po);

        // Assert
        assertEquals(expectedRowsAffected, rowsAffected);
        verify(channelMediaMapper).updateById(po);
    }

    @Test
    public void testDeleteById() {
        // Arrange
        Long id = 1L;
        int expectedRowsAffected = 1;
        when(channelMediaMapper.deleteById(id)).thenReturn(expectedRowsAffected);

        // Act
        int rowsAffected = channelMediaMapper.deleteById(id);

        // Assert
        assertEquals(expectedRowsAffected, rowsAffected);
        verify(channelMediaMapper).deleteById(id);
    }

}