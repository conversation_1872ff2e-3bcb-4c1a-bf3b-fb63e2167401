package com.chinatelecom.gs.engine.core.model.toolkit.core.dto;


import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.chinatelecom.gs.engine.core.sdk.vo.llm.BaseLLMRequest;
import com.chinatelecom.gs.engine.core.sdk.vo.llm.LLMMessage;
import com.chinatelecom.gs.engine.core.sdk.vo.llm.Tool;
import com.chinatelecom.gs.engine.core.sdk.vo.llm.WrapLLMMessage;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.agent.config.SafeFenceConfig;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @since 2023/7/12 16:49
 */
@Data
public class LLMRequest<R> implements BaseLLMRequest {
    /** 请求id */
    private String requestId;
    /**
     * 用户id
     */
    private String userId;
    /**
     * 输入文本, query，仅{@link  LLMProcessType}为direct时，直接使用
     */
    @Deprecated
    private String text;


    private WrapLLMMessage requestMessage;

    /**
     * 模型可以使用的工具
     */
    private List<Tool> tools;

    /**
     * 大模型系统设置
     */
    private String systemSettings;

    /**
     * 大模型处理类型
     */
    private LLMProcessType llmProcessType;


    /** 历史会话 */
    private List<WrapLLMMessage> histories;

    /** 流式输出标识 0-非流式, 1-流式 */
    private int streaming = 1;

    /**
     * 随机性
     */
    private double temperature = 1d;


    private int maxTokens = 3000;

    private Integer topk = 3;

    private Float topp = 0.5f;

    private int maxInputTokens = 12000;

    /**
     * 大模型配置
     */
    private ExternalModelInfo llmModelInfo;

    private boolean enableThinking = true;

    private CustomRequest<R> customRequest;

    /**
     * 默认值为{"type": "text"} 返回内容的格式。可选值：{"type": "text"}或{"type": "json_object"}。设置为{"type": "json_object"}时会输出标准格式的JSON字符串。如果指定该参数为{"type": "json_object"}，您需要在System Message或User Message中指引模型输出JSON格式，如：“请按照json格式输出。”
     */
    private String responseFormat = "text";


    /**
     * 是否直接请求completion接口，不请求chat接口，默认为false
     */
    private boolean directCompletion = false;

    /**
     * 安全围栏配置
     */
    private SafeFenceConfig safeFenceConfig;

    /**
     * 安全围栏开关
     */
    private Boolean safeFenceSwitch;

    /**
     * 安全围栏话术
     */
    private String fenceScript;

    /**
     * 命中安全围栏，是否发送invokeMQ消息，默认true
     */
    private Boolean sendInvokeMQ = true;

    /**
     * 指定 messageId
     */
    private String messageId;

    public LLMRequest(){
        this.llmModelInfo = new ExternalModelInfo();
        this.llmProcessType = LLMProcessType.DIRECT;
    }

    @JsonIgnore
    public String simpleLog() {
        return "{" +
                "text='" + text + '\'' +
                ", requestMessage=" + requestMessage +
                ", tools=" + tools +
                ", histories=" + histories +
                ", systemSettings='" + systemSettings + '\'' +
                '}';
    }

    public <T> LLMRequest<T> basicCopy(LLMRequest<T> target){
        target.setUserId(userId);
        target.setLlmModelInfo(llmModelInfo);
        target.setHistories(histories);
        return target;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class CustomRequest<R> implements Serializable{
        /**
         * 没有渲染的prompt模版
         */
        private Map<String, String> templates;
        /**
         * 渲染prompt的参数
         */
        private List<Map<String, String>> params;

        /**
         * 如果存在map->reduce,需要提供具体的映射函数
         */
        private transient Function<String, R> mapFunction;

        /**
         * 如果存在map->reduce,需要提供具体的映射函数
         */
        private transient Function<List<R>, Map<String, String>> reduceFunction;
    }

    /**
     * 获取输入内容
     *
     * @return
     */
    @Override
    public String inputContent() {
        StringBuilder inputBuilder = new StringBuilder();
        if (StringUtils.isNotBlank(this.systemSettings)) {
            inputBuilder.append(this.systemSettings);
        }
        if (CollectionUtils.isNotEmpty(this.tools)){
            for (Tool tool : this.tools) {
                inputBuilder.append("\n").append(JSON.toJSONString(tool));
            }
        }
        if (CollectionUtils.isNotEmpty(this.histories)){
            for (LLMMessage message : this.histories) {
                inputBuilder.append(message.getContent());
            }
        }
        if (Objects.nonNull(this.requestMessage)) {
            inputBuilder.append(this.requestMessage.getContent());
        }
        return inputBuilder.toString();
    }
}
