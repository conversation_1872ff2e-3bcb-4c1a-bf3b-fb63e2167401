package com.chinatelecom.gs.engine.common.cache.memory;

import com.chinatelecom.gs.engine.core.sdk.vo.llm.LLMMessage;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MessageRequestCache extends LLMMessage implements Serializable {
    private String messageId;
    private String originQuery;
    private boolean summary;
    private boolean tool;
    private String toolName;
}
