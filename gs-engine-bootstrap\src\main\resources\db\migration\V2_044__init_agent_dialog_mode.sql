-- 应答模式表
create table if not exists agent_dialog_mode
(
    id           bigint unsigned auto_increment comment '自增id'  primary key,
	agent_code    varchar(256)    default ''                  not null comment '机器人code',
    type         varchar(256)                                 not null comment '应答类型,有三种:react次数,flow,custom',
    react        bigint                                       null comment 'react次数,type=react时有值',
    url          varchar(256)                                 null comment '接口地址,type=custom时有值',
	version      bigint          default 1                    not null comment '配置版本',
    yn           bigint unsigned default '0'                  not null comment '记录删除标识',
    tenant_id    varchar(128)    default ''                   not null comment '租户id',
    create_id    varchar(100)    default '1'                  not null comment '创建用户ID',
    create_name  varchar(256)    default ''                   not null comment '记录创建人名称',
    update_id    varchar(100)    default '1'                  not null comment '最后修改用户ID',
    update_name  varchar(256)    default ''                   not null comment '最后一次记录操作人',
    create_time  datetime(3)     default CURRENT_TIMESTAMP(3) not null comment '记录创建时间',
    update_time  datetime(3)     default CURRENT_TIMESTAMP(3) not null on update CURRENT_TIMESTAMP(3) comment '记录最后一次更新时间',
   KEY idx_agent_code (agent_code) COMMENT 'agent_code索引'
) comment '应答模式表';
