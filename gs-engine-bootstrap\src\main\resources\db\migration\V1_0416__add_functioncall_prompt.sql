INSERT INTO prompt_template
( yn, code, app_code, name, description, content, model_param, create_id, create_name, create_time, update_id, update_name, update_time)
VALUES( 0, 'REPLY_RECOGNIZE_FUNCTION_CALL_PROMPT_TEMPLATE', '0', '参数识别FunctionCall提示词模板', '参数识别FunctionCall提示词模板', '根据提供的工具，如果工具可以被调用，则返回调用信息，以json格式返回。
如果不能被调用，请生成引导用户获取调用工具必要信息的说法，以json输出引导说法和已经获取的部分调用参数信息如：
{"guide": "用户引导说法","params": {"param1": "value1", "param2": "value2"}}
用户query：{{query}}', NULL, 'admin', '0', '2025-03-13 10:05:48', '0', 'admin', '2025-03-13 10:06:32');