package com.chinatelecom.gs.engine.core.corekit.controller.web;

import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.common.auth.KsMenuConfig;
import com.chinatelecom.gs.engine.common.auth.MenuConfig;
import com.chinatelecom.gs.engine.common.auth.PermissionTag;
import com.chinatelecom.gs.engine.common.constants.Apis;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.common.utils.FileUtil;
import com.chinatelecom.gs.engine.core.corekit.domain.request.SensitivePageRequest;
import com.chinatelecom.gs.engine.core.corekit.domain.request.SensitiveRequest;
import com.chinatelecom.gs.engine.core.corekit.domain.response.SensitiveResponse;
import com.chinatelecom.gs.engine.core.corekit.service.SensitiveService;
import com.chinatelelcom.gs.engine.sdk.common.Page;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import com.google.common.collect.Lists;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import java.io.IOException;
import java.util.List;


/**
 * @author: Wei
 * @date: 2025-02-05 09:24
 */
@Slf4j
@Tag(name = "敏感词管理")
@RestController
@RequestMapping(Apis.BASE_PREFIX + Apis.WEB_API + "/sensitive")
public class SensitiveController {

    @Resource
    private SensitiveService sensitiveService;

    @PermissionTag(code = {MenuConfig.SENSITIVE, KsMenuConfig.SENSITIVE})
    @PostMapping("/save")
    @Operation(summary = "敏感词新增/编辑", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "安全设置")})})
    @PlatformRestApi(name = "敏感词新增/编辑", groupName = "敏感词管理")
    @AuditLog(businessType = "敏感词管理", operType = "敏感词新增/编辑", operDesc = "敏感词新增/编辑", objId="#request.standardWords")
    public Result<Boolean> save(@Valid @RequestBody SensitiveRequest request){
        return Result.success(sensitiveService.saveSensitive(request));
    }

    @PermissionTag(code = {MenuConfig.SENSITIVE, KsMenuConfig.SENSITIVE})
    @GetMapping("/detail")
    @Operation(summary = "敏感词详情", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "安全设置")})})
    @PlatformRestApi(name = "敏感词详情", groupName = "敏感词管理")
    @AuditLog(businessType = "敏感词管理", operType = "敏感词详情", operDesc = "敏感词详情", objId="#id")
    public Result<SensitiveResponse> detail(@RequestParam Long id){
        return Result.success(sensitiveService.detail(id));
    }

    @PermissionTag(code = {MenuConfig.SENSITIVE, KsMenuConfig.SENSITIVE})
    @PostMapping("/delete")
    @Operation(summary = "敏感词删除", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "安全设置")})})
    @PlatformRestApi(name = "敏感词删除", groupName = "敏感词管理")
    @AuditLog(businessType = "敏感词管理", operType = "敏感词删除", operDesc = "敏感词删除", objId="null")
    public Result<Boolean> delete(@Valid @RequestBody @NotEmpty(message = "Id不能为空") List<String> ids){
        return Result.success(sensitiveService.delete(ids));
    }

    @PermissionTag(code = {MenuConfig.SENSITIVE, KsMenuConfig.SENSITIVE})
    @GetMapping("/page")
    @Operation(summary = "敏感词分页查询", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "安全设置")})})
    @PlatformRestApi(name = "敏感词分页查询", groupName = "敏感词管理")
    @AuditLog(businessType = "敏感词管理", operType = "敏感词分页查询", operDesc = "敏感词分页查询", objId="null")
    public Result<Page<SensitiveResponse>> page(SensitivePageRequest request){
        return Result.success(sensitiveService.pageQuery(request));
    }

    @PermissionTag(code = {MenuConfig.SENSITIVE, KsMenuConfig.SENSITIVE})
    @PostMapping("/downloadTemplate")
    @Operation(summary = "敏感词导入模版下载", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "安全设置")})})
    @PlatformRestApi(name = "敏感词导入模版下载", groupName = "敏感词管理")
    @AuditLog(businessType = "敏感词管理", operType = "敏感词导入模版下载", operDesc = "敏感词导入模版下载", objId="null")
    public void downloadTemplate(HttpServletResponse response) throws IOException {
        sensitiveService.downloadTemplate(response);
    }

    @PermissionTag(code = {MenuConfig.SENSITIVE, KsMenuConfig.SENSITIVE})
    @PostMapping("/upload")
    @Operation(summary = "敏感词导入", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "安全设置")})})
    @PlatformRestApi(name = "敏感词导入", groupName = "敏感词管理")
    @AuditLog(businessType = "敏感词管理", operType = "敏感词导入", operDesc = "敏感词导入", objId="null")
    public Result<Boolean> upload(@RequestParam("file") MultipartFile file) throws Exception {
        FileUtil.checkFileType(file, Lists.newArrayList(".xlsx",".xls"));
        return Result.success(sensitiveService.upload(file));
    }

    @PermissionTag(code = {MenuConfig.SENSITIVE, KsMenuConfig.SENSITIVE})
    @GetMapping("/enableSensitive")
    @Operation(summary = "敏感词开关", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "安全设置")})})
    @PlatformRestApi(name = "敏感词开关", groupName = "敏感词管理")
    @AuditLog(businessType = "敏感词管理", operType = "敏感词开关", operDesc = "敏感词开关", objId="null")
    public Result<Boolean> enableSensitive(@RequestParam("enable") Boolean enable){
        return Result.success(sensitiveService.enableSensitive(enable));
    }

    @GetMapping("/initSensitiveData")
    @Operation(summary = "初始化租户敏感词", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "安全设置")})})
    @PlatformRestApi(name = "初始化租户敏感词", groupName = "敏感词管理")
    @AuditLog(businessType = "敏感词管理", operType = "初始化租户敏感词", operDesc = "初始化租户敏感词", objId="null")
    public Result<Boolean> initSensitiveData(){
        sensitiveService.initSensitiveData();
        return Result.success();
    }

}
