//package com.chinatelecom.csbotplatform.channel.common.cache.redis;
//
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.StringUtils;
//import org.redisson.Redisson;
//import org.redisson.api.RedissonClient;
//import org.redisson.config.ClusterServersConfig;
//import org.redisson.config.Config;
//import org.redisson.config.SingleServerConfig;
//import org.redisson.spring.starter.RedissonAutoConfiguration;
//import org.springframework.boot.autoconfigure.AutoConfigureBefore;
//import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
//import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
//import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
//import org.springframework.boot.context.properties.EnableConfigurationProperties;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.core.annotation.Order;
//
//import jakarta.annotation.Resource;
//import java.util.ArrayList;
//import java.util.Arrays;
//import java.util.List;
//
///**
// * <AUTHOR>
// * @date 2023/12/25 13:57
// * @description
// */
//@Slf4j
//@Configuration
//@Order(1)
//@ConditionalOnClass(Redisson.class)
//@AutoConfigureBefore(RedissonAutoConfiguration.class)
//@ConditionalOnExpression("'${spring.redis.mode}'=='single' or '${spring.redis.mode}'=='cluster' or '${spring.redis.mode}'=='sentinel'")
//@EnableConfigurationProperties(RedisProperties.class)
//public class RedisConfiguration {
//
//    @Resource
//    private RedisProperties redisProperties;
//
//    @Order(1)
//    @Bean(destroyMethod = "shutdown")
//    @ConditionalOnProperty(name = "spring.redis.mode", havingValue = "single")
//    public RedissonClient redissonSingle() {
//        Config config = new Config();
//        String node = redisProperties.getSingle().getAddress();
//        node = node.startsWith("redis://") ? node : "redis://" + node;
//        SingleServerConfig serverConfig = config.useSingleServer()
//                .setAddress(node)
//                .setTimeout(redisProperties.getPool().getConnTimeout())
//                .setConnectionPoolSize(redisProperties.getPool().getSize())
//                .setConnectionMinimumIdleSize(redisProperties.getPool().getMinIdle());
//        log.info("redisson single username:{}, password:{}", redisProperties.getUsername(), maskPwd(redisProperties.getPassword()));
//        if(!StringUtils.isEmpty(redisProperties.getPassword())) {
//            serverConfig.setPassword(redisProperties.getPassword());
//        }
//        return Redisson.create(config);
//    }
//
//    @Order(1)
//    @Bean(destroyMethod = "shutdown")
//    @ConditionalOnProperty(name = "spring.redis.mode", havingValue = "cluster")
//    public RedissonClient redissonCluster() {
//        System.out.println("cluster redisProperties:" + redisProperties.getCluster());
//
//        Config config = new Config();
//        String[] nodes = redisProperties.getCluster().getNodes().split(",");
//        List<String> newNodes = new ArrayList<>(nodes.length);
//        Arrays.stream(nodes).forEach((index) -> newNodes.add(
//                index.startsWith("redis://") ? index : "redis://" + index)
//        );
//
//        ClusterServersConfig serverConfig = config.useClusterServers()
//                .addNodeAddress(newNodes.toArray(new String[0]))
//                .setScanInterval(redisProperties.getCluster().getScanInterval())
//                .setIdleConnectionTimeout(redisProperties.getPool().getSoTimeout())
//                .setConnectTimeout(redisProperties.getPool().getConnTimeout())
//                .setRetryAttempts(redisProperties.getCluster().getRetryAttempts())
//                .setRetryInterval(redisProperties.getCluster().getRetryInterval())
//                .setMasterConnectionPoolSize(redisProperties.getCluster().getMasterConnectionPoolSize())
//                .setSlaveConnectionPoolSize(redisProperties.getCluster().getSlaveConnectionPoolSize())
//                .setTimeout(redisProperties.getTimeout());
//
//        log.info("redisson cluster username: {}, password: {}, hosts:{}", redisProperties.getUsername(),
//                maskPwd(redisProperties.getPassword()), redisProperties.getCluster().getNodes());
//        if (StringUtils.isNotBlank(redisProperties.getUsername())) {
//            serverConfig.setUsername(redisProperties.getUsername());
//        }
//
//        if (StringUtils.isNotBlank(redisProperties.getPassword())) {
//            serverConfig.setPassword(redisProperties.getPassword());
//        }
//        return Redisson.create(config);
//    }
//
//    private String maskPwd(String pwd) {
//        if (StringUtils.isBlank(pwd)) {
//            return StringUtils.EMPTY;
//        }
//
//        StringBuilder sb = new StringBuilder().append(pwd.charAt(0))
//                .append("****")
//                .append(pwd.charAt(pwd.length() - 1));
//        return sb.toString();
//    }
//}
