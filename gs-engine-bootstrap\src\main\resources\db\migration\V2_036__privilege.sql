CREATE TABLE  IF NOT EXISTS `access_control_resource` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `resource_id` varchar(64)  NOT NULL COMMENT '资源id',
  `resource_type` varchar(64) NOT NULL COMMENT '应用对应的编码',
  `owner_id` varchar(100) NOT NULL,
  `owner_name` varchar(100) DEFAULT NULL,
  `is_public` tinyint NOT NULL DEFAULT '0' COMMENT '是否公开',
  `tenant_id` varchar(100) NOT NULL COMMENT '租户id',
  `yn` bigint unsigned NOT NULL DEFAULT '0' COMMENT '删除标记,0:未删除,其他表示已删除',
  `create_id` varchar(255) NOT NULL DEFAULT '0' COMMENT '创建用户ID',
  `create_name` varchar(255) NOT NULL DEFAULT '未知用户' COMMENT '创建用户名',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_id` varchar(255) NOT NULL DEFAULT '0' COMMENT '最后修改用户ID',
  `update_name` varchar(255) NOT NULL DEFAULT '未知用户' COMMENT '最后修改用户名',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_resource_unique` (`resource_id`, `resource_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='对象资源表';


CREATE TABLE  IF NOT EXISTS `access_control_resource_privilege` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `object_id` varchar(128) NOT NULL COMMENT '授予权限的对象id',
  `object_name` varchar(100) NOT NULL,
  `object_type` varchar(32) NOT NULL COMMENT '对象类型：USER-用户、TEAM-团队、DEPART-部门',
  `resource_id` varchar(64) NOT NULL COMMENT '资源的id',
  `grant_type` varchar(32) NOT NULL COMMENT '授予权限的类型',
  `resource_type` varchar(64) NOT NULL COMMENT '资源所属应用类型，机器人、知识库等',
  `tenant_id` varchar(100) NOT NULL COMMENT '租户id',
  `yn` bigint unsigned NOT NULL DEFAULT '0' COMMENT '删除标记,0:未删除,其他表示已删除',
  `create_id` varchar(255) NOT NULL DEFAULT '0' COMMENT '创建用户ID',
  `create_name` varchar(255) NOT NULL DEFAULT '未知用户' COMMENT '创建用户名',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_id` varchar(255) NOT NULL DEFAULT '0' COMMENT '最后修改用户ID',
  `update_name` varchar(255) NOT NULL DEFAULT '未知用户' COMMENT '最后修改用户名',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_access_control_resource_privilege_unique` (`resource_id`,`resource_type`,`object_id`,`object_type`,`yn`),
  KEY `idx_object` (`object_id`,`object_type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='对象资源权限关系表';


INSERT INTO access_control_resource(`resource_id`,`resource_type`,`owner_id`,`owner_name`,`is_public`,`tenant_id`,`yn`,
`create_id`,`create_name`,`create_time`,`update_id`,`update_name`,`update_time`)
SELECT `code`,'KNOWLEDGE',`create_id`,`create_name`,
 CASE `role_type`
    WHEN 'PRI' THEN '0'
    WHEN 'PUB' THEN '1'
 END AS is_public,
`app_code` as tenant_id,'0' as yn,
`create_id`,`create_name`,`create_time`,`update_id`,`update_name`,`update_time` FROM `knowledge_base`
WHERE yn = 0;


INSERT INTO access_control_resource_privilege(`object_id`,`object_name`,`object_type`,`resource_id`,`grant_type`,`resource_type`,`tenant_id`,`yn`,
`create_id`,`create_name`,`create_time`,`update_id`,`update_name`,`update_time`)
SELECT `owner_id`,`owner_name`,'USER',`resource_id`,'manage','KNOWLEDGE',`tenant_id`,`yn`,
`create_id`,`create_name`,`create_time`,`update_id`,`update_name`,`update_time` FROM `access_control_resource`
WHERE resource_type = 'KNOWLEDGE' and yn=0;

INSERT INTO access_control_resource(`resource_id`,`resource_type`,`owner_id`,`owner_name`,`is_public`,`tenant_id`,`yn`,
`create_id`,`create_name`,`create_time`,`update_id`,`update_name`,`update_time`)
SELECT `code`,'REPORT',`create_id`,`create_name`,'0',
`app_code` as tenant_id,'0' as yn,
`create_id`,`create_name`,`create_time`,`update_id`,`update_name`,`update_time` FROM `knowledge_report`
WHERE yn = 0;


INSERT INTO access_control_resource_privilege(`object_id`,`object_name`,`object_type`,`resource_id`,`grant_type`,`resource_type`,`tenant_id`,`yn`,
`create_id`,`create_name`,`create_time`,`update_id`,`update_name`,`update_time`)
SELECT `owner_id`,`owner_name`,'USER',`resource_id`,'manage','REPORT',`tenant_id`,`yn`,
`create_id`,`create_name`,`create_time`,`update_id`,`update_name`,`update_time` FROM `access_control_resource`
WHERE resource_type = 'REPORT' and yn=0;
