package com.chinatelecom.gs.engine.core.model.toolkit;

import com.chinatelecom.gs.engine.core.sdk.vo.llm.BaseLLMResponse;
import com.chinatelecom.gs.engine.core.sdk.vo.llm.LLMMessage;
import lombok.Data;

import java.util.Objects;

/**
 * @USER: pengmc1
 * @DATE: 2025/5/22 11:20
 */

@Data
public class ContentLLMResponse implements BaseLLMResponse {
    /**
     * 内容
     */
    private String content;
    /**
     * 输入token数
     */
    private Integer promptTokens;
    /**
     * 输出token数
     */
    private Integer completionTokens;

    public ContentLLMResponse(String content){
        this.content = content;
    }

    public ContentLLMResponse(String content, Integer promptTokens, Integer completionTokens) {
        this.content = content;
        this.promptTokens = promptTokens;
        this.completionTokens = completionTokens;
    }

    public ContentLLMResponse(LLMMessage message) {
        StringBuilder contentBuilder = new StringBuilder();
        if(Objects.nonNull(message)){
            contentBuilder.append(message.getReasoning_content()).append(message.getContent());
        }
        this.content = contentBuilder.toString();
    }

    /**
     * 获取输出内容
     *
     * @return
     */
    @Override
    public String outputContent() {
        return this.content;
    }

    /**
     * 获取输入token数
     *
     * @return
     */
    @Override
    public Integer promptTokens() {
        return this.promptTokens;
    }

    /**
     * 获取输出token数
     *
     * @return
     */
    @Override
    public Integer completionTokens() {
        return this.completionTokens;
    }
}
