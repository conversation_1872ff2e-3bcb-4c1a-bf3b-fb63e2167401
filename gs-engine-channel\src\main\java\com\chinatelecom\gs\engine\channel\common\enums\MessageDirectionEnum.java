package com.chinatelecom.gs.engine.channel.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * <AUTHOR>
 * @date 2023/12/26 11:30
 * @description
 */
public enum MessageDirectionEnum {
    USER("user", "用户消息"),
    BOT("bot", "机器人回复"),
    ;

    @EnumValue
    private String code;

    private String desc;

    MessageDirectionEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
