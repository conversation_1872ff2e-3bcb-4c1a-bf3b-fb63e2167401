package com.chinatelecom.gs.engine.kms.controller;

import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.kms.sdk.constants.KmsApis;
import com.chinatelecom.gs.engine.kms.sdk.enums.AppRoleType;
import com.chinatelecom.gs.engine.kms.sdk.vo.app.AppCreateParam;
import com.chinatelecom.gs.engine.kms.sdk.vo.app.AppQueryParam;
import com.chinatelecom.gs.engine.kms.sdk.vo.app.AppVO;
import com.chinatelecom.gs.engine.kms.service.AppService;
import com.chinatelelcom.gs.engine.sdk.common.Page;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.extensions.Extension;
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;


@RestController
@Tag(name = "空间管理")
@RequestMapping({KmsApis.KMS_API + KmsApis.APP})
public class AppController {

    @Resource
    private AppService appService;

    @Operation(summary = "查询空间列表", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PostMapping(KmsApis.PAGE_API)
    @PlatformRestApi(name = "查询空间列表", groupName = "空间管理")
    @AuditLog(businessType = "空间管理", operType = "查询空间列表", operDesc = "查询空间列表", objId="#appQueryParam")
    public Result<Page<AppVO>> page(@Validated @RequestBody AppQueryParam appQueryParam) {
        return Result.success(appService.pageQuery(appQueryParam));
    }

    @Operation(summary = "创建空间", extensions = {@Extension(name = "api", properties = {@ExtensionProperty(name = "group", value = "知识管理")})})
    @PostMapping
    @PlatformRestApi(name = "创建空间", groupName = "空间管理")
    @AuditLog(businessType = "空间管理", operType = "创建空间", operDesc = "创建空间", objId="#_RESULT_.data.code")
    public Result<AppVO> createIfNotExist(@Validated @RequestBody AppCreateParam createParam) {
        List<AppCreateParam.AppRole> appRoles = new ArrayList<>();
        createParam.setAppRoles(appRoles);
        if (createParam.getPersonal()) {
            AppCreateParam.AppRole role = AppCreateParam.AppRole.builder().roleType(AppRoleType.PRI).code(RequestContext.getUserId()).build();
            appRoles.add(role);
        } else {
            AppCreateParam.AppRole role = AppCreateParam.AppRole.builder().roleType(AppRoleType.PUB).code(RequestContext.getCheckedTenantId()).build();
            appRoles.add(role);
        }
        createParam.setCode(null);
        createParam.setTenantId(RequestContext.getCheckedTenantId());
        return Result.success(appService.createIfNotExist(createParam));
    }
}
