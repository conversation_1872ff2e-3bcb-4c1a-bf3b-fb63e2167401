package com.chinatelecom.gs.engine.core.model.toolkit;

import com.alibaba.fastjson.JSON;
import com.alibaba.ttl.threadpool.TtlExecutors;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.enums.BizTypeEnum;
import com.chinatelecom.gs.engine.common.enums.LogStatusEnum;
import com.chinatelecom.gs.engine.common.enums.LogTypeEnum;
import com.chinatelecom.gs.engine.common.log.track.InvokeProcessMessage;
import com.chinatelecom.gs.engine.common.log.track.LogMessage;
import com.chinatelecom.gs.engine.common.log.track.LogMqProducer;
import com.chinatelecom.gs.engine.core.model.rpc.TokenService;
import com.chinatelecom.gs.engine.core.model.toolkit.core.dto.Model;
import com.chinatelecom.gs.engine.core.model.toolkit.core.internal.Utils;
import com.chinatelecom.gs.engine.core.sdk.vo.llm.BaseLLMRequest;
import com.chinatelecom.gs.engine.core.sdk.vo.llm.BaseLLMResponse;
import com.chinatelecom.gs.workflow.core.workflow.core.utils.LogUtils;
import com.chinatelelcom.gs.engine.sdk.common.utils.SpringContextUtils;
import lombok.extern.slf4j.Slf4j;
import okhttp3.ConnectionPool;
import okhttp3.Dispatcher;
import okhttp3.OkHttpClient;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * @USER: pengmc1
 * @DATE: 2025/5/8 10:17
 */

@Slf4j
public abstract class AbstractLLMClient implements LLMClient{

    protected Model.ModelConfig modelConfig;

    /**
     * 发生跟踪消息
     * @param inputData
     * @param outputData
     * @param logStatusEnum
     * @param startTime
     * @param endTime
     * @param firstTokenTime
     */
    protected void sendTraceMessage(BaseLLMRequest inputData, BaseLLMResponse outputData, LogStatusEnum logStatusEnum,
                                    LocalDateTime startTime, LocalDateTime endTime, LocalDateTime firstTokenTime){
        //获取线程池
        ExecutorService commonExecutorService = (ExecutorService)SpringContextUtils.getBean("commonExecutorService");
        TokenService tokenService = SpringContextUtils.getBean(TokenService.class);
        try{
            //step1 构建消息
            String logId = UUID.randomUUID().toString();
            LogMessage logMessage = buildLogMessage(logId, inputData, outputData, logStatusEnum, startTime, endTime, firstTokenTime);
            InvokeProcessMessage invokeProcessMessage = buildInvokeProcessMessage(logId, inputData, outputData, startTime, endTime, firstTokenTime, BizTypeEnum.LLM);
            //异步推送消息
            commonExecutorService.submit(()-> {
                //step1 计算token
                Integer promptTokens = outputData.promptTokens();
                Integer completionTokens = outputData.completionTokens();
                try{
                    if(Objects.isNull(promptTokens)){
                        promptTokens = tokenService.getTokenNum(inputData.inputContent());
                    }
                    if(Objects.isNull(completionTokens)){
                        completionTokens = tokenService.getTokenNum(outputData.outputContent());
                    }
                }catch (Exception e){
                    log.error("计算token发生异常！",e);
                }
                logMessage.setPromptTokens(promptTokens);
                logMessage.setCompletionTokens(completionTokens);
                invokeProcessMessage.setInputToken(promptTokens);
                invokeProcessMessage.setOutputToken(completionTokens);
                try{
                    LogMqProducer logMqProducer = SpringContextUtils.getBean(LogMqProducer.class);
                    logMqProducer.sendLogMessage(logMessage);
                    logMqProducer.sendInvokeProcessMessage(invokeProcessMessage);
                }catch (Exception e){
                    log.error("发送大模型链路跟踪日志或过程日志消息发生异常！", e);
                }
            });
        }catch (Exception e){
            log.error("发送大模型链路跟踪日志或过程日志消息发生异常！", e);
        }
    }
    /**
     * 构建链路消息
     * @param logId
     * @param inputData
     * @param outputData
     * @param logStatusEnum
     * @param startTime
     * @param endTime
     * @param firstTokenTime
     * @return
     */
    protected LogMessage buildLogMessage(String logId, Object inputData, Object outputData,LogStatusEnum logStatusEnum,
                                         LocalDateTime startTime, LocalDateTime endTime, LocalDateTime firstTokenTime){
        LogMessage logMessage = LogUtils.buildCommonLog(LogTypeEnum.LLM_INVOKE,inputData,outputData,logStatusEnum,startTime,endTime,"");
        logMessage.setLogId(logId);
        logMessage.setName(modelConfig.getName());
        logMessage.setUrl(modelConfig.getBaseUrl());
        logMessage.setConfig(JSON.toJSONString(modelConfig));
        logMessage.setFirstTokenTime(firstTokenTime);
        return logMessage;
    }

    /**
     * 构建过程消息
     * @param logId
     * @param inputData
     * @param outputData
     * @param startTime
     * @param endTime
     * @param firstTokenTime
     * @param bizTypeEnum
     * @return
     */
    protected InvokeProcessMessage buildInvokeProcessMessage(String logId, Object inputData, Object outputData,LocalDateTime startTime, LocalDateTime endTime, LocalDateTime firstTokenTime, BizTypeEnum bizTypeEnum){
        String inputDataStr = inputData instanceof String s ? s : JSON.toJSONString(inputData);
        String outputDataStr =  outputData instanceof String s ? s : JSON.toJSONString(outputData);
        InvokeProcessMessage invokeProcessMessage = LogUtils.buildInvokeProcessMessage(BizTypeEnum.LLM, startTime, endTime);
        invokeProcessMessage.setLogId(logId);
        invokeProcessMessage.setUserId(RequestContext.getUserId());
        invokeProcessMessage.setTenantId(RequestContext.getTenantId());
        invokeProcessMessage.setModelCode(modelConfig.getName());
        invokeProcessMessage.setModelName(modelConfig.getName());
        invokeProcessMessage.setInputData(inputDataStr);
        invokeProcessMessage.setOutputData(outputDataStr);
        invokeProcessMessage.setBizName(bizTypeEnum.getDesc());
        invokeProcessMessage.setBizType(bizTypeEnum.getCode());
        invokeProcessMessage.setStartTime(startTime);
        invokeProcessMessage.setEndTime(endTime);
        invokeProcessMessage.setFirstTokenTime(firstTokenTime);
        invokeProcessMessage.setSendTime(LocalDateTime.now());
        invokeProcessMessage.setCostTime(LogUtils.calcCostTime(startTime, endTime));
        invokeProcessMessage.setFirstTokenCostTime(LogUtils.calcCostTime(startTime, firstTokenTime));
        return invokeProcessMessage;
    }

    protected void sendTraceMessage(Object inputData, Object outputData, LogStatusEnum logStatusEnum,
                                    LocalDateTime startTime, LocalDateTime endTime, LocalDateTime firstTokenTime,
                                    Integer promptTokens, Integer completionTokens, BizTypeEnum bizTypeEnum) {
        try{
            String logId = UUID.randomUUID().toString();
            LogMessage logMessage = buildLogMessage(logId, inputData, outputData, logStatusEnum, startTime, endTime, firstTokenTime);
            InvokeProcessMessage invokeProcessMessage = buildInvokeProcessMessage(logId, inputData, outputData, startTime, endTime, firstTokenTime, BizTypeEnum.LLM);
            logMessage.setPromptTokens(promptTokens);
            logMessage.setCompletionTokens(completionTokens);
            invokeProcessMessage.setInputToken(promptTokens);
            invokeProcessMessage.setOutputToken(completionTokens);
            LogMqProducer logMqProducer = SpringContextUtils.getBean(LogMqProducer.class);
            logMqProducer.asyncSendLogMessage(logMessage);
            logMqProducer.asyncSendInvokeProcessMessage(invokeProcessMessage);
        }catch (Exception e){
            log.error("发送大模型链路跟踪日志或过程日志消息发生异常！", e);
        }
    }

    protected OkHttpClient buildOkHttpClient(Model.ModelConfig modelConfig) {
        Duration timeout = Duration.ofSeconds(Utils.getOrDefault(modelConfig.getTimeout(), 60));
        //构造OkHttpClient默认的线程池，通过TtlExecutors包装一次，保证线程池中的线程都是TTL的
        ExecutorService executorService = TtlExecutors.getTtlExecutorService(new ThreadPoolExecutor(0, Integer.MAX_VALUE, 60, TimeUnit.SECONDS,new SynchronousQueue<Runnable>()));
        Dispatcher dispatcher = new Dispatcher(executorService);
        dispatcher.setMaxRequests(200);          // 全局并发上限
        dispatcher.setMaxRequestsPerHost(100);   // 单主机并发上限

        OkHttpClient okHttpClient = new OkHttpClient.Builder()
                .callTimeout(timeout)
                .connectTimeout(timeout)
                .readTimeout(timeout)
                .writeTimeout(timeout)
                .connectionPool(new ConnectionPool(200, 5, TimeUnit.MINUTES))
                .addInterceptor(new OkhttpHeaderInterceptor(modelConfig))
                .dispatcher(dispatcher)
                .build();

        return okHttpClient;
    }
}
