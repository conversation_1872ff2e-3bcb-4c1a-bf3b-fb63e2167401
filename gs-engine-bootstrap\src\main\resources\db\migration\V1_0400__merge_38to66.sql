-- V1_038__entity_init.sql
CREATE TABLE gs_entity
(
    id                   BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '自动生成',
    tenant_id            VARCHAR(128) NOT NULL COMMENT '租户ID',
    entity_code          VARCHAR(128) NOT NULL COMMENT '实体编码',
    entity_name          VARCHAR(128) NOT NULL COMMENT '实体名称',
    entity_desc          VARCHAR(512) COMMENT '实体描述',
    entity_type          VARCHAR(128) COMMENT '实体类型',
    context_turns        INTEGER COMMENT '上文轮次',
    ability              VARCHAR(64) COMMENT '识别能力',
    ability_config       TEXT COMMENT 'json字符串',
    validator_switch     TINYINT COMMENT '校验开关',
    validator_type       VARCHAR(64) COMMENT '校验类型',
    validator_regexp     VARCHAR(1024) COMMENT '校验正则表达式',
    validator_model_code VARCHAR(128) COMMENT '校验大模型编码',
    validator_prompt     VARCHAR(1024) COMMENT '校验大模型提示词',
    `yn`                 int unsigned NOT NULL COMMENT '删除状态:0-未删除，1-已删除',
    `create_id`          bigint unsigned NOT NULL COMMENT '创建用户ID',
    `create_name`        varchar(255) NOT NULL COMMENT '创建用户名称',
    `create_time`        datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_id`          bigint unsigned NOT NULL COMMENT '最后修改用户ID',
    `update_name`        varchar(255) NOT NULL COMMENT '最后修改用户名称',
    `update_time`        datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY `uk_entity_code` (`entity_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT '实体数据表';

CREATE TABLE gs_entity_data
(
    id             BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '自动生成',
    tenant_id      VARCHAR(128) NOT NULL COMMENT '租户ID',
    entity_code    VARCHAR(128) NOT NULL COMMENT '实体编码',
    entity_value   VARCHAR(128) NOT NULL COMMENT '实体值',
    entity_synonym TEXT COMMENT '同义词',
    extra_data     TEXT COMMENT '扩展配置',
    `yn`           int unsigned NOT NULL COMMENT '删除状态:0-未删除，1-已删除',
    `create_id`    bigint unsigned NOT NULL COMMENT '创建用户ID',
    `create_name`  varchar(255) NOT NULL COMMENT '创建用户名称',
    `create_time`  datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_id`    bigint unsigned NOT NULL COMMENT '最后修改用户ID',
    `update_name`  varchar(255) NOT NULL COMMENT '最后修改用户名称',
    `update_time`  datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY `uk_entity_code` (`entity_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT '实体数据表';

-- V1_039__alter_knowledge_base.sql
alter table knowledge_base add column source_system varchar(255) NOT NULL default 'KS' COMMENT '知识库创建来源';

alter table agent_basic_info add column source_system varchar(255) NOT NULL default 'KS' COMMENT '机器人创建来源系统';

-- V1_040__alter_agent_and_workflow.sql
alter table agent_basic_info add column bot_type varchar(100) NOT NULL default '1' COMMENT '机器人类型';

-- V1_041__add_instruct.sql
CREATE TABLE gs_instruct(
                            `id` BIGINT NOT NULL AUTO_INCREMENT  COMMENT '主键 ID' ,
                            `instruct_code` VARCHAR(255)    COMMENT '指令标识' ,
                            `instruct_desc` VARCHAR(500)    COMMENT '指令描述' ,
                            `instruct_param` VARCHAR(1000)    COMMENT '指令参数（Json数组字符串）' ,
                            `tenant_id` VARCHAR(128)    COMMENT '租户 ID' ,
                            `create_id` VARCHAR(100)    COMMENT '创建用户 ID' ,
                            `create_name` VARCHAR(225)    COMMENT '创建用户名称' ,
                            `create_time` DATETIME    COMMENT '创建用户时间' ,
                            `update_id` VARCHAR(255)    COMMENT '最后修改用户 ID' ,
                            `update_name` VARCHAR(255)    COMMENT '最后修改用户名称' ,
                            `update_time` DATETIME    COMMENT '更新时间' ,
                            `yn` int  DEFAULT '0'  COMMENT '删除标记，0-未删除，其他表示已删除' ,
                            PRIMARY KEY (id)
)  ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '指令';

-- V1_042__add_intent_manager.sql
CREATE TABLE `agent_intent_manager`
(
    `id`              bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `yn`              bigint unsigned NOT NULL DEFAULT '0' COMMENT '删除标记,0:未删除,其他表示已删除',
    `code`            varchar(64)     NOT NULL COMMENT 'Code',
    `app_code`        varchar(64)     NOT NULL COMMENT '应用Code',
    `name`            varchar(255)    NOT NULL COMMENT '名称',
    `description`     varchar(2048)            DEFAULT NULL COMMENT '描述',
    `examples`        text COMMENT '例句',
    `model_name`       varchar(255)    NULL COMMENT '关联意图模型名称',
    `model_code`       varchar(255)    NULL COMMENT '关联意图code',
    `model_intent_name` varchar(255)    NULL COMMENT '关联模型意图名称',
    `model_intent_code` varchar(255)    NULL COMMENT '关联模型意图code',
    `on` bit(1) NOT NULL DEFAULT b'1' COMMENT '开关',
    `create_id`       varchar(255)    NOT NULL DEFAULT '0' COMMENT '创建用户ID',
    `create_name`     varchar(255)    NOT NULL DEFAULT '未知用户' COMMENT '创建用户名',
    `create_time`     datetime        NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_id`       varchar(255)    NOT NULL DEFAULT '0' COMMENT '最后修改用户ID',
    `update_name`     varchar(255)    NOT NULL DEFAULT '未知用户' COMMENT '最后修改用户名',
    `update_time`     datetime        NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_agent_intent_manager_name` (`app_code`, `name`),
    KEY `idx_agent_intent_manager_code` (`app_code`, `code`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT ='意图管理表';

-- V1_043__add_sensitive.sql
CREATE TABLE gs_sensitive(
                             `id` BIGINT NOT NULL AUTO_INCREMENT  COMMENT '主键 ID' ,
                             `standard_words` VARCHAR(255)    COMMENT '标准词' ,
                             `synonymous_words` VARCHAR(500)    COMMENT '同义词（使用,分割）' ,
                             `tenant_id` VARCHAR(128)    COMMENT '租户 ID' ,
                             `create_id` VARCHAR(100)    COMMENT '创建用户 ID' ,
                             `create_name` VARCHAR(225)    COMMENT '创建用户名称' ,
                             `create_time` DATETIME    COMMENT '创建用户时间' ,
                             `update_id` VARCHAR(255)    COMMENT '最后修改用户 ID' ,
                             `update_name` VARCHAR(255)    COMMENT '最后修改用户名称' ,
                             `update_time` DATETIME    COMMENT '更新时间' ,
                             `yn` int  DEFAULT '0'  COMMENT '删除标记，0-未删除，其他表示已删除' ,
                             PRIMARY KEY (id)
)  ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '敏感词';

-- V1_044__alter_entity.sql
ALTER TABLE gs_entity MODIFY COLUMN yn int unsigned NOT NULL COMMENT '删除状态:0-未删除，1-已删除' DEFAULT 0;

ALTER TABLE gs_entity_data MODIFY COLUMN yn int unsigned NOT NULL COMMENT '删除状态:0-未删除，1-已删除' DEFAULT 0;

ALTER TABLE gs_entity DROP INDEX uk_entity_code;

ALTER TABLE gs_entity ADD UNIQUE INDEX uk_entity_code (`entity_code`, `yn`);

ALTER TABLE gs_entity_data DROP INDEX uk_entity_code;

-- V1_045__alter_intent
ALTER TABLE agent_intent_manager
    ADD COLUMN rules VARCHAR(2048) COMMENT '规则' DEFAULT NULL;
ALTER TABLE agent_intent_manager
    ADD UNIQUE INDEX uk_intent_manager (`app_code`, `name`, `yn`);

-- V1_046__recognition_intent_prompt
INSERT INTO prompt_template
( yn, code, app_code, name, description, content, model_param, create_id, create_name, create_time, update_id, update_name, update_time)
VALUES( 0, 'INTENT_RECOGNITION_PROMPT_TEMPLATE', '0', '大模型识别意图', '大模型识别意图', '你是一个高度智能化的助手，能够精准解析用户意图，并根据提供的意图描述信息选择最合适的意图。你的任务是接收一个查询（{{query}}），从给定的意图列表（{{intentList}}）中选出最佳匹配的意图，并以JSON格式返回结果。

### 输入说明：
1. **查询（{{query}}）**：这是用户的原始输入或问题。
2. **意图列表（{{intentList}}）**：这是一个包含多个意图的对象数组，每个对象包含以下字段：
   - ````"name"````: 意图名称。
   - ````"intentCode"````: 意图唯一编码。
   - ````"description"````: 意图描述，详细说明该意图适用于何种场景或需求。

### 输出要求：
- 输出必须是一个JSON对象，且严格遵循以下格式：
  ````````````json
  {
      "classificationId": "", // 填入所选意图的唯一编码
      "reason": ""           // 填入选定此意图的原因，解释为何该意图最适合当前查询
  }
  ````````````
- 不得输出任何额外信息或注释。

### 处理逻辑：
1. 首先分析查询内容，理解用户的核心需求。
2. 对比意图列表中的每个意图描述，寻找与查询内容最匹配的意图。
3. 如果存在多个可能的匹配项，优先选择描述更具体、更贴近用户需求的意图。
4. 在````reason````字段中清晰阐述选择该意图的理由，包括但不限于：
   - 查询中的关键词如何映射到意图描述。
   - 为什么其他意图不够贴合当前查询。
5. 确保输出完全符合指定的JSON格式。

### 示例：
假设输入如下：
````````````json
{
    "query": "我想知道最近的天气预报",
    "intentList": [
        {
            "name": "天气查询",
            "intentCode": "weather_check",
            "description": "用于查询天气预报及相关气象信息"
        },
        {
            "name": "新闻浏览",
            "intentCode": "news_browsing",
            "description": "用于获取最新的新闻资讯"
        }
    ]
}
````````````
则输出应为：
{
    "classificationId": "weather_check",
    "reason": "用户的查询明确提到了''天气预报''，这与''天气查询''意图的描述完全一致，因此选择了该意图。而''新闻浏览''意图与此查询无关。"
}', NULL, 'admin', '0', '2025-02-12 10:05:48', '0', 'admin', '2025-02-12 10:06:32');

-- V1_047__add_agent_trigger
CREATE TABLE `agent_ai_trigger` (
                                    `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键 ID',
                                    `agent_code` varchar(100) DEFAULT NULL COMMENT '机器人编码',
                                    `trigger_name` varchar(100) DEFAULT NULL COMMENT '触发器名称',
                                    `trigger_type` varchar(100) DEFAULT NULL COMMENT '触发器类型',
                                    `instruct_code` varchar(100) DEFAULT NULL COMMENT '指令编码',
                                    `instruct_name` varchar(100) DEFAULT NULL COMMENT '指令名称',
                                    `execute_type` varchar(100) DEFAULT NULL COMMENT '任务执行类型',
                                    `execute_id` varchar(100) DEFAULT NULL COMMENT '插件/业务流ID',
                                    `execute_name` varchar(100) DEFAULT NULL COMMENT '插件/业务流名称',
                                    `param` varchar(1000) DEFAULT NULL COMMENT '插件/业务流参数Json',
                                    `version` varchar(100) DEFAULT NULL COMMENT '版本',
                                    `tenant_id` varchar(128) DEFAULT NULL COMMENT '租户 ID',
                                    `create_id` varchar(100) DEFAULT NULL COMMENT '创建用户 ID',
                                    `create_name` varchar(225) DEFAULT NULL COMMENT '创建用户名称',
                                    `create_time` datetime DEFAULT NULL COMMENT '创建用户时间',
                                    `update_id` varchar(255) DEFAULT NULL COMMENT '最后修改用户 ID',
                                    `update_name` varchar(255) DEFAULT NULL COMMENT '最后修改用户名称',
                                    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                    `yn` int DEFAULT '0' COMMENT '删除标记，0-未删除，其他表示已删除',
                                    PRIMARY KEY (`id`),
                                    KEY `agent_code` (`agent_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='机器人触发器';

-- V1_048__update_prompt
update prompt_template set content='"""
### Task
根据主题，对大纲内容进行扩展，生成一份详细的Markdown格式的报告。

### Instructions
- **内容要求**：严格按照给定的大纲格式输出内容，报告应包括每个部分的详细描述，语言应专业、清晰，适合目标读者。请确保逻辑严谨，结构清晰，内容要全面完整。
- **层级结构**：
    1. 第一层级下的内容为概括性总结，呈现主要观点或结论，确保信息简明扼要。
    2. 第二层级的内容为详细的分析，提供具体的数据、案例或理论支持，深入探讨主题的各个方面。
    3. 第三层级的内容为更细致的说明或补充，进一步解释第二层级中的内容，提供更多的细节或例子，以保证报告内容全面完整。
- **格式要求**：
    1. 一级标题用例如1，2，3...等序号标明，标题以#开始，如#1。
    2. 二级标题用例如1.1，1.2...等序号标明，标题以##开始，如##1.1。
    3. 三级标题用例如1.1.1，1.1.2...等序号标明，标题以以###开始,如###1.1.1。
    4. 注意，不要输出大纲以外的标题,输入的大纲是一级标题开始，报告内容也严格按照大纲从一级标题开始
  具体格式参考如下：
#1 一级标题
    报告内容...
##1.1 二级标题
    报告内容...
###1.1.1 三级标题
    报告内容...
##1.2 二级标题
    报告内容...
###1.2.1 三级标题
    报告内容...
#2 二级标题
    报告内容...
##2.1 二级标题
    报告内容...
###2.1.1 三级标题
    报告内容...


### Question
输入的大纲为：
${content}
根据指令要求和大纲，生成一份详细的报告
"""'
where `code`='CONTENT' AND app_code="0" limit 1;

-- V1_049__add_prompt
INSERT INTO prompt_template
( yn, code, app_code, name, description, content, model_param, create_id, create_name, create_time, update_id, update_name, update_time)
VALUES( 0, 'LLM_ENTITY_RECOGNIZE_PROMPT_TEMPLATE', '0', '大模型识别实体', '大模型识别实体', '你是一个高度智能化的助手，能够精准识别对话内容中的实体。你的任务是接收一个对话内容，从给定的实体列表中选出若干实体，并以JSON格式返回结果。

### 输入说明：
1. **对话内容**：{{query}}。
2. **实体列表**：{{entityList}}, 这是一个包含多个实体的对象数组，每个实体包含以下字段：
   - ``"entityCode"``: 实体编码。
   - ``"entityName"``: 实体名称。
   - ``"entityDesc"``: 实体描述。
   - ``"entityPrompt"``: 实体提示词。
   - ``"entityValues"``: 实体值限定范围，是一个字符串数组，可为空，为空代表不限制。

### 输出要求：
- 输出必须是一个JSON数组对象，且严格遵循以下格式：
  ``````json
  [
    {
        "entityCode": "", // 实体编码
        "entityValues": ["实体值1","实体值2"]  // 实体值列表
    }
  ]
  ``````
- 不得输出任何额外信息或注释。

### 示例：
假设输入如下：
``````json
{
    "query": "今天北京天气怎么样",
    "intentList": [
        {
            "entityCode": "city",
            "entityName": "城市",
            "entityDesc": "中国城市",
            "entityPrompt": "中国的城市",
            "entityValues": ["北京","成都"],
        },
        {
            "entityCode": "date",
            "entityName": "时间",
            "entityDesc": "日期时间",
            "entityPrompt": "时间"
        }
    ]
}
``````
则输出应为：
[
    {
         "entityCode": "city",
         "entityValues": ["北京"]
    },
    {
          "entityCode": "date",
          "entityValues": ["今天"]
    }
]', NULL, 'admin', '0', '2025-02-13 10:05:48', '0', 'admin', '2025-02-13 10:06:32');

INSERT INTO prompt_template
( yn, code, app_code, name, description, content, model_param, create_id, create_name, create_time, update_id, update_name, update_time)
VALUES( 0, 'LLM_ENTITY_VALIDATOR_PROMPT_TEMPLATE', '0', '大模型校验实体', '大模型校验实体', '你是一个高度智能化的助手，能够精准判断实体值是否满足条件。你的任务是接收一个实体值和条件描述，判断实体值是否满足条件，如果满足条件则返回“是”，不满足则返回“否”。

### 输入说明：
1. **实体值（entityValue）**：实体值为：{{entityValue}}。
2. **条件描述（validatorPrompt）**：条件描述为：{{validatorPrompt}}。

### 输出要求：
- 输出只能是“是”或“否”，不得输出任何额外信息或注释。

### 示例：
假设输入如下：
``````json
{
    "entityValue": "18224461382",
    "validatorPrompt": "11位手机号码"
}
``````
则输出应为：
是
', NULL, 'admin', '0', '2025-02-13 10:05:48', '0', 'admin', '2025-02-13 10:06:32');

INSERT INTO prompt_template
( yn, code, app_code, name, description, content, model_param, create_id, create_name, create_time, update_id, update_name, update_time)
VALUES( 0, 'REPLY_RECOGNIZE_PROMPT_TEMPLATE', '0', '大模型识别用户回复', '大模型识别用户回复', '你是一个高度智能化的助手，能够精准判断回复内容是否为提问内容的答案。你的任务是接收一个提问内容和回复内容，判断回复内容是否为提问内容的答案，如果满足条件则返回“是”，不满足则返回“否”。

### 输入说明：
1. **提问内容（question）**：{{question}}。
2. **回复内容（reply）**：{{reply}}。

### 输出要求：
- 输出只能是“是”或“否”，不得输出任何额外信息或注释。

### 示例：
假设输入如下：
``````json
{
    "question": "你叫上面名字",
    "reply": "我叫小明"
}
``````
则输出应为：
是
', NULL, 'admin', '0', '2025-02-13 10:05:48', '0', 'admin', '2025-02-13 10:06:32');

INSERT INTO prompt_template
( yn, code, app_code, name, description, content, model_param, create_id, create_name, create_time, update_id, update_name, update_time)
VALUES( 0, 'REPLY_VALIDATOR_PROMPT_TEMPLATE', '0', '大模型校验用户回复', '大模型校验用户回复', '你是一个高度智能化的助手，能够精准判断回复内容是否满足条件。你的任务是接收一个回复内容和条件描述，判断回复内容是否满足条件，如果满足条件则返回“是”，不满足则返回“否”。

### 输入说明：
1. **回复内容（reply）**：实体值为：{{reply}}。
2. **条件描述（validatorPrompt）**：条件描述为：{{validatorPrompt}}。

### 输出要求：
- 输出只能是“是”或“否”，不得输出任何额外信息或注释。

### 示例：
假设输入如下：
``````json
{
    "entityValue": "18224461382",
    "validatorPrompt": "11位数字"
}
``````
则输出应为：
是', NULL, 'admin', '0', '2025-02-13 10:05:48', '0', 'admin', '2025-02-13 10:06:32');

INSERT INTO prompt_template
( yn, code, app_code, name, description, content, model_param, create_id, create_name, create_time, update_id, update_name, update_time)
VALUES( 0, 'OPTIMIZE_GUIDE_ASK_PROMPT_TEMPLATE', '0', '大模型润色引导话术', '大模型润色引导话术', '你是一个高度智能化的助手，能够对用户引导话术进行润色。你的任务是接收一个用户引导话术，对其进行润色优化，输出优化后的文本。

### 输入说明：
1. **用户引导话术**：{{guideAsk}}。

### 输出要求：
- 输出优化后的文本内容，不得输出任何额外信息或注释。

### 示例：
假设输入如下：
你手机号是多少？
则输出应为：
请问你的手机号码是多少呢？', NULL, 'admin', '0', '2025-02-13 10:05:48', '0', 'admin', '2025-02-13 10:06:32');


-- V1_050__alter_model
update agent_default_config set config_value = '{"doc": {"docAccept": 0, "enableSortModel": false, "sortCode": "model_992668744299715591", "topn": 3}, "faq": {"enableLLMModel": false,"enableSortModel": false,"faqAccept": 0,"llmaccept": 0,"sortCode": "model_992668744299715591","topn": 3 },"retriever": 3,"searchStrategy": "MIX"}' where config_key = "agentConfig.kms.config";

update agent_default_config set config_value = '{"globalVarSwitch": false,"securityFenceSwitch": true,"securityFenceScript": "命中敏感词","globalVarList": null,"noAnswerCount": 3,"noAnswerCountSwitch": false,"noAnswerCountType": "CONTINUE_TRIGGER","manuCount": 3,"manuCountSwitch": false,"manuCountType": "CONTINUE_TRIGGER","manuKeyWordList": ["转人工"]}' where config_key = "agentConfig.global.policy";

-- V1_051__add_intent_config
CREATE TABLE `agent_intent_config`
(
    `id`                   bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `yn`                   bigint unsigned NOT NULL DEFAULT '0' COMMENT '删除标记,0:未删除,其他表示已删除',
    `code`                 varchar(64)  NOT NULL COMMENT 'Code',
    `app_code`             varchar(64)  NOT NULL COMMENT '应用Code',

    `search_strategy`      varchar(32)           DEFAULT NULL COMMENT '知识库检索策略',
    `use_rerank`           bit(1)       NOT NULL DEFAULT b'0' COMMENT '是否使用重排序',
    `dimension`            varchar(255) NOT NULL COMMENT '配置维度:系统维度、租户维度、应用维度、业务维度',
    `rerank_model_code`    varchar(64)           DEFAULT NULL COMMENT '重排序的模型code',
    `embedding_model_code` varchar(64)           DEFAULT NULL COMMENT '文本表征模型code',
    `threshold` double DEFAULT NULL COMMENT '阈值',

    `create_id`            varchar(255) NOT NULL DEFAULT '0' COMMENT '创建用户ID',
    `create_name`          varchar(255) NOT NULL DEFAULT '未知用户' COMMENT '创建用户名',
    `create_time`          datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_id`            varchar(255) NOT NULL DEFAULT '0' COMMENT '最后修改用户ID',
    `update_name`          varchar(255) NOT NULL DEFAULT '未知用户' COMMENT '最后修改用户名',
    `update_time`          datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY                    `idx_agent_intent_config_code` (`app_code`, `code`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT ='意图高级配置表';


INSERT INTO `agent_intent_config` (`yn`, `code`, `app_code`, `search_strategy`, `use_rerank`, `dimension`,
                                   `rerank_model_code`, `embedding_model_code`, `threshold`, `create_id`,
                                   `create_name`, `create_time`, `update_id`, `update_name`, `update_time`)
VALUES (0, '0', '0', 'MIX', 0, 'SYSTEM',
        123, 456, 0.9, '0', '未知用户', NOW(), '_empty_', '未知用户', NOW());

-- V1_052__add_prompt
INSERT INTO prompt_template( yn, code, app_code, name, description, content, model_param, create_id, create_name, create_time, update_id, update_name, update_time)
VALUES( 0, 'INTENT_SIMILAR', '0', '意图例句生成', '意图例句生成', '"""
### Task
根据输入的用户意图输出3条与其相关意图，建议采用动宾短语。要求: 只输出意图，每行输出一条

### Examples
- input: 购买机票
- output:
订购机票

### Question
输入文本为:
${content}

"""', NULL, 'admin', '0', '2024-10-11 00:00:00', 'admin', 'admin', '2024-10-11 00:00:00');

-- V1_053__alter_app_code
ALTER TABLE `gs_instruct`
    ADD COLUMN `app_code` varchar(100) NULL COMMENT '应用编码' AFTER `id`;
ALTER TABLE `gs_sensitive`
    ADD COLUMN `app_code` varchar(100) NULL COMMENT '应用编码' AFTER `id`;

-- V1_054__update_llm_prompt
update prompt_template set content='你是一个万能助手，根据下面的信息执行任务，用户提示信息：{{prompt}}，系统提示词：{{systemPrompt}},查询工具结果{{toolResult}},输出格式为：{{responseFormat}}'
where `code`='LLM_PROMPT_TEMPLATE' AND app_code="0" limit 1;

-- V1_055__add_intent_example
CREATE TABLE `agent_intent_example`
(
    `id`              bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `yn`              bigint unsigned NOT NULL DEFAULT '0' COMMENT '删除标记,0:未删除,其他表示已删除',
    `code`            varchar(64)     NOT NULL COMMENT 'Code',
    `app_code`        varchar(64)     NOT NULL COMMENT '应用Code',
    `intent_code`            varchar(64)    NOT NULL COMMENT '意图关联code',
    `example`            varchar(255)    NOT NULL COMMENT '意图例句',

    `create_id`       varchar(255)    NOT NULL DEFAULT '0' COMMENT '创建用户ID',
    `create_name`     varchar(255)    NOT NULL DEFAULT '未知用户' COMMENT '创建用户名',
    `create_time`     datetime        NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_id`       varchar(255)    NOT NULL DEFAULT '0' COMMENT '最后修改用户ID',
    `update_name`     varchar(255)    NOT NULL DEFAULT '未知用户' COMMENT '最后修改用户名',
    `update_time`     datetime        NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_agent_intent_example_name` (`app_code`, `intent_code`),
    KEY `idx_agent_intent_example_code` (`app_code`, `example`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_ai_ci COMMENT ='意图例句关联表';

-- V1_056__alter_intent_manager
alter table agent_intent_manager drop column examples;

-- V1_057__update_prompt
update prompt_template set content='"""
### Task
根据输入的用户意图输出5条与其相关意图，建议采用动宾短语。要求: 只输出意图，每行输出一条

### Examples
- input: 购买机票
- output:
订购机票

### Question
输入文本为:
${content}

"""'
where `code`='INTENT_SIMILAR' AND app_code="0" limit 1;

-- V1_058__alter_table
CREATE TABLE IF NOT EXISTS `bot_workflow_sub_relation`
(
    `id`                    bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增 id',
    `main_workflow_id`      varchar(255)                                                  NOT NULL COMMENT '主对话流id',
    `sub_workflow_id`       varchar(255)                                                  NOT NULL COMMENT '子对话流id',
    `sub_workflow_name`     varchar(255)                                                  NOT NULL COMMENT '子对话流id',
    `main_workflow_version` varchar(255)                                                  NOT NULL COMMENT '主对话流版本',
    `sub_workflow_version`  varchar(255)                                                  NOT NULL COMMENT '子对话流版本',
    `yn`                    int                                                           NOT NULL DEFAULT '0',
    `app_code`              varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '空间id',
    `tenant_id`             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci          DEFAULT NULL,
    `create_id`             varchar(255)                                                  NOT NULL,
    `create_name`           varchar(255)                                                  NOT NULL,
    `create_time`           datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_id`             varchar(255)                                                  NOT NULL,
    `update_name`           varchar(255)                                                  NOT NULL,
    `update_time`           datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    key                     idx_work_flow(main_workflow_id, main_workflow_version)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


CREATE TABLE IF NOT EXISTS `bot_workflow_intent_relation`
(
    `id`               bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增 id',
    `workflow_id`      varchar(255)                                                  NOT NULL COMMENT '工作流id',
    `intent_code`      varchar(255)                                                  NOT NULL COMMENT '意图code',
    `intent_name`      varchar(255)                                                  NOT NULL COMMENT '意图名称',
    `workflow_version` varchar(255)                                                  NOT NULL COMMENT '子对话流版本',
    `yn`               int                                                           NOT NULL DEFAULT '0',
    `app_code`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '空间id',
    `tenant_id`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci          DEFAULT NULL,
    `create_id`        varchar(255)                                                  NOT NULL,
    `create_name`      varchar(255)                                                  NOT NULL,
    `create_time`      datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_id`        varchar(255)                                                  NOT NULL,
    `update_name`      varchar(255)                                                  NOT NULL,
    `update_time`      datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    key                idx_workflow(workflow_id, workflow_version),
    key                index_intent(intent_code, intent_name)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

CREATE TABLE IF NOT EXISTS `plugin_oauth_status`
(
    `id`           bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增 id',
    `plugin_id`    varchar(255)    NOT NULL,
    `status`       int             NOT NULL,
    `token`        varchar(255)    not null,
    `refresh_token` varchar(255)    NOT NULL,
    `expires_in`   varchar(255)    NOT NULL,
    `scope`        varchar(255)             DEFAULT NULL,
    `token_type`   varchar(255)    NOT NULL,
    `created_at`   long            NOT NULL,
    `yn`           int             NOT NULL DEFAULT '0',
    `create_id`    varchar(100)    NOT NULL DEFAULT '1' COMMENT '创建用户ID',
    `create_name`  varchar(256)    NOT NULL DEFAULT '' COMMENT '记录创建人名称',
    `update_id`    varchar(100)    NOT NULL DEFAULT '1' COMMENT '最后修改用户ID',
    `update_name`  varchar(256)    NOT NULL DEFAULT '' COMMENT '最后一次记录操作人',
    `create_time`  datetime(3)     NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '记录创建时间',
    `update_time`  datetime(3)     NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '记录最后一次更新时间',
    PRIMARY KEY (`id`),
    KEY `plugin_id` (`plugin_id`, `yn`)
    ) ENGINE = InnoDB
    DEFAULT CHARSET = utf8mb4
    COLLATE = utf8mb4_0900_ai_ci COMMENT ='插件授权状态表';


alter table bot_workflow add column bot_type varchar(100) NULL COMMENT '机器人类型';

alter table bot_workflow add column workflow_type varchar(100) NULL COMMENT '业务流类型';

alter table agent_workflow_bind add column workflow_type varchar(100) NULL COMMENT '业务流类型';

alter table agent_workflow_bind add column workflow_version int NULL COMMENT '工作流版本';

-- V1_059__update_model
update agent_default_config set config_value = '{"doc": {"docAccept": 0.5, "enableSortModel": true, "sortCode": "model_925301025288359936", "topn": 3}, "faq": {"enableLLMModel": true,"enableSortModel": true,"faqAccept": 0.8,"llmAccept": 0.5,"sortCode": "model_925301025288359936","topn": 3 },"retriever": 3,"searchStrategy": "MIX"}' where config_key = "agentConfig.kms.config";

update agent_default_config set config_value = '{"globalVarSwitch": false,"securityFenceSwitch": true,"securityFenceScript": "检测到您输入的内容可能不太合适哦~请换一种表达方式吧!感谢您的理解!","globalVarList": null,"noAnswerCount": 3,"noAnswerCountSwitch": true,"noAnswerCountType": "CONTINUE_TRIGGER","manuCount": 3,"manuCountSwitch": true,"manuCountType": "CONTINUE_TRIGGER","manuKeyWordList": ["转人工"],"manuScript": "您好，为了更好地帮助您，我将为您转接人工客服，请稍等片刻，感谢您的理解!"}' where config_key = "agentConfig.global.policy";

-- V1_060__update_workflow_type
update bot_workflow set workflow_type = 'dialogflow' where workflow_type = 'dialog';

update bot_workflow set workflow_type = 'sub_dialogflow' where workflow_type = 'sub_dialog';

-- V1_061__update_workflow_type
update agent_workflow_bind set workflow_type = 'dialogflow' where workflow_type = 'dialog';

-- V1_062__update_intent_config
update agent_intent_config
set rerank_model_code='model_925301025288359936',
    embedding_model_code='model_859333379638693890',
    threshold=0.8
where dimension = 'SYSTEM';

-- V1_063__alter_worklfow
alter table bot_workflow add column main_workflow_id varchar(255) NULL COMMENT '主对话流id';

-- V1_064__alter_workflow_default_value
alter table bot_workflow modify column workflow_name varchar(255) default '' COMMENT '工作流名称';

-- V1_065__alter_entity
ALTER TABLE gs_entity DROP INDEX uk_entity_code;

ALTER TABLE gs_entity ADD UNIQUE INDEX uk_entity_code (`entity_code`, `tenant_id`, `yn`);

-- V1_066__alter_workflow_desc
drop table bot_workflow_sub_relation;

alter table bot_workflow modify column description varchar(255) default '' COMMENT '工作流描述';

-- V1_0385__alter_knowledge_base.sql
ALTER TABLE `knowledge_base`
    ADD COLUMN `role_type` varchar(32) NOT NULL DEFAULT 'PRI' COMMENT '知识库权限类型' AFTER `cover_key`;
