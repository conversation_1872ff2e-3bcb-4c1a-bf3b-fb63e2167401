package com.chinatelecom.gs.engine.robot.sdk.v2.vo.kms.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@Schema(description = "机器人数量统计响应对象")
public class AgentCountResponse implements Serializable {
    @Schema(description = "租户ID", example = "tenant_001")
    private String tenantId;

    @Schema(description = "用户ID", example = "user_001")
    private String userId;

    @Schema(description = "机器人列表")
    private List<AgentBasicInfoResponse> agentList;
}