UPDATE `gs_base_config` SET `config_data`='{"publishSwitch":false,"auditSwitch":false,"processCode":"","introductionSwitch":false,"safeFenceSwitch":false}'
where `code`='system-publishConfig' and `dimension`='SYSTEM' and `config_type`='publishConfig' and `business_no`='SYSTEM' limit 1;


INSERT INTO `gs_base_config` (yn, code, name, dimension, config_type, business_no, config_data,
                              obj_class, description, create_id, create_name, create_time,
                              update_id, update_name, update_time)
VALUES (0, 'system-globalConfig', 'system-globalConfig', 'SYSTEM', 'globalConfig', 'SYSTEM',
        '{"appBannerEnable":false,"appBanner":"","exportButtonHidden":false,"pdfDemotion":false,"homePage":""}', '', '发布配置',
        '_empty_', '0', '2024-10-11 00:00:00', '_empty_', '未知用户', '2024-10-11 00:00:00');
