package com.chinatelecom.gs.engine.channel.manage.convertor;


import com.chinatelecom.gs.engine.channel.dao.po.ChannelInfoPO;
import com.chinatelecom.gs.engine.channel.dao.po.ChannelMessageRecordPO;
import com.chinatelecom.gs.engine.channel.service.dto.ChannelInfoDTO;
import com.chinatelecom.gs.engine.channel.service.dto.ChannelMsgRecordDTO;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.data.ChannelInfoVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2023/12/25 14:52
 * @description
 */
@Mapper
public interface DTOBeanConvertor {

    DTOBeanConvertor INSTANCE = Mappers.getMapper(DTOBeanConvertor.class);

    ChannelInfoPO convert(ChannelInfoDTO channelInfoDTO);

    ChannelInfoDTO convert(ChannelInfoPO channelInfoPO);

    ChannelMessageRecordPO convert(ChannelMsgRecordDTO dto);

    ChannelInfoVO convertVo(ChannelInfoPO channelInfoPO);
}
