CREATE TABLE `agent_ai_model` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `tenant_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '',
  `robot_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '机器人 code',
  `model_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '模型编码',
  `model_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '模型名称',
  `model_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '模型类型',
  `model_state` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '模型状态',
  `data_format` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '数据格式',
  `data_dimension` int DEFAULT NULL COMMENT '数据维度',
  `api_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'apikey',
  `intent_number` int NOT NULL DEFAULT '0' COMMENT '意图个数',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '描述',
  `external_model_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '外部模型 id',
  `external_model_url` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '外部模型访问地址',
  `external_model_config` json DEFAULT NULL COMMENT '模型拓展配置',
  `global_flag` tinyint NOT NULL DEFAULT '0' COMMENT '是否是全局/是否是预置（0-非全局，1-全局）',
  `model_config` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '模型配置',
  `yn` bigint unsigned NOT NULL DEFAULT '0' COMMENT '删除标记,0:未删除,其他表示已删除',
  `create_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '1' COMMENT '创建用户 ID',
  `create_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建用户名',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '1' COMMENT '最后修改用户 ID',
  `update_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '最后修改用户名',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `distinguish_model_config` tinyint NOT NULL DEFAULT '0' COMMENT '是否区分模型配置（是否区分用户请求或离线文档），0-不区分，1-区分',
  `model_secret` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '大模型 api secret',
  `model_provider` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '大模型提供方',
  `threshold` double DEFAULT NULL COMMENT '分类模型阈值',
  `app_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '应用Code',
  PRIMARY KEY (`id`),
  KEY `idx_model_code` (`model_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='算法模型表';

CREATE TABLE `agent_basic_info` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `agent_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'agent 编码',
  `agent_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT 'agent 名称',
  `agent_picture` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT 'agent 头像',
  `kms_app_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '知识库空间 id',
  `agent_desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT 'agent 描述',
  `agent_reply` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '人设与回复',
  `agent_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT 'agent 类型',
  `template_category_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT 'agent 类型',
  `status` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '0',
  `version` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '0',
  `enable_dialog` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '0',
  `yn` bigint unsigned NOT NULL DEFAULT '0' COMMENT '记录删除标识',
  `create_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `create_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `create_time` datetime NOT NULL,
  `update_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `update_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `update_time` datetime NOT NULL,
  `tenant_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '1',
  `scope` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'UNPUBLISHED',
  `app_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '应用Code',
  `is_default` varchar(100) COLLATE utf8mb4_0900_ai_ci DEFAULT '0' COMMENT '是否默认模型，0:否，1:是',
  PRIMARY KEY (`id`),
  KEY `idx_agent_code` (`agent_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



CREATE TABLE `agent_config` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增 id',
  `agent_code` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '配置内容的 key',
  `config_key` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '配置内容的 key',
  `config_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '配置详细内容',
  `type` bigint NOT NULL DEFAULT '1' COMMENT '配置类型',
  `category` bigint NOT NULL DEFAULT '1' COMMENT '配置分类',
  `version` bigint NOT NULL DEFAULT '1' COMMENT '配置版本',
  `yn` bigint unsigned NOT NULL DEFAULT '0' COMMENT '记录删除标识',
  `create_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '1' COMMENT '创建用户 ID',
  `create_name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '记录创建人名称',
  `update_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '1' COMMENT '最后修改用户 ID',
  `update_name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '最后一次记录操作人',
  `create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '记录创建时间',
  `update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '记录最后一次更新时间',
  `tenant_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '',
  `app_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '应用Code',
  PRIMARY KEY (`id`),
  UNIQUE KEY `config_key_unique` (`agent_code`,`config_key`,`version`,`yn`),
  KEY `idx_agent_code` (`agent_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='agent 配置表';



CREATE TABLE `agent_default_config` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增 id',
  `config_key` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '配置内容的 key',
  `config_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '配置详细内容',
  `type` bigint NOT NULL DEFAULT '1' COMMENT '配置类型',
  `category` bigint NOT NULL DEFAULT '1' COMMENT '配置分类',
  `yn` bigint unsigned NOT NULL DEFAULT '0' COMMENT '记录删除标识',
  `create_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '1' COMMENT '创建用户 ID',
  `create_name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '记录创建人名称',
  `update_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '1' COMMENT '最后修改用户 ID',
  `update_name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '最后一次记录操作人',
  `create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '记录创建时间',
  `update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '记录最后一次更新时间',
  `version` bigint NOT NULL DEFAULT '1' COMMENT '配置版本',
  `tenant_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '',
  `app_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '应用Code',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='agent 默认配置表';



CREATE TABLE `agent_dialog_count_record` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增 id',
  `tenant_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '',
  `agent_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT 'agent 编码',
  `create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '记录创建时间',
  `update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '记录最后一次更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `agent_code_key` (`agent_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='会话统计记录表';



CREATE TABLE `agent_dialog_record` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增 id',
  `tenant_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '',
  `agent_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '机器人 code',
  `agent_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '机器人名称',
  `session_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '会话 id',
  `customer_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '客户 id',
  `channel_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '入口 code',
  `channel_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '入口名称',
  `dialog_start_time` datetime(3) NOT NULL COMMENT '会话开始时间',
  `dialog_end_time` datetime(3) NOT NULL COMMENT '会话结束时间',
  `dialog_duration` int NOT NULL DEFAULT '0' COMMENT '会话时长',
  `to_human` tinyint NOT NULL DEFAULT '0' COMMENT '是否转人工',
  `customer_message_num` int NOT NULL DEFAULT '0' COMMENT '客户消息数',
  `agent_message_num` int NOT NULL DEFAULT '0' COMMENT '机器人消息数',
  `match_num` int NOT NULL DEFAULT '0' COMMENT '命中意图数',
  `unknown_num` int NOT NULL DEFAULT '0' COMMENT '未命中意图数',
  `create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '记录创建时间',
  `update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '记录最后一次更新时间',
  `yn` bigint unsigned NOT NULL DEFAULT '0' COMMENT '记录删除标识',
  `app_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '应用Code',
  PRIMARY KEY (`id`),
  UNIQUE KEY `session_id_key` (`session_id`,`yn`),
  KEY `bot_code_key` (`agent_code`,`yn`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='会话记录表';



CREATE TABLE `agent_index_create_record` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增 id',
  `tenant_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '支撑平台租户 id',
  `type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '类型',
  `app_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '编码',
  `index_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '索引名称',
  `state` bigint NOT NULL DEFAULT '1' COMMENT '是否成功,1:成功，0：失败',
  `fail_reason` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '失败原因',
  `create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '记录创建时间',
  `yn` bigint unsigned NOT NULL DEFAULT '0' COMMENT '记录删除标识',
  `update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '记录最后一次更新时间',
  PRIMARY KEY (`id`),
  KEY `app_code_key` (`app_code`),
  KEY `index_key` (`index_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='索引创建记录表';



CREATE TABLE `agent_knowledge_base` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `agent_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `knowledge_base_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '知识库 id',
  `yn` bigint unsigned NOT NULL DEFAULT '0' COMMENT '记录删除标识',
  `create_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '1' COMMENT '记录创建人 id',
  `create_name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '记录创建人名称',
  `update_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '1' COMMENT '最后修改用户 ID',
  `update_name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '最后一次记录操作人',
  `create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '记录创建时间',
  `update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '记录最后一次更新时间',
  `tenant_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '',
  `type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '知识库类型',
  `app_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '应用Code',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='agent 和知识库关联表';



CREATE TABLE `agent_version_info` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增 id',
  `tenant_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '支撑平台租户 id',
  `agent_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT 'bot id,以支撑平台的应用 id 做机器人 id',
  `version` bigint NOT NULL DEFAULT '1' COMMENT '版本号',
  `status` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'EDITABLE' COMMENT '版本状态',
  `runtime_status` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'OFFLINE' COMMENT '运行状态',
  `version_desc` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '版本描述',
  `migrate_index_status` tinyint NOT NULL DEFAULT '0' COMMENT '迁移索引状态(0-为迁移完成，1-迁移完成)',
  `yn` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '记录删除标识',
  `create_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '1' COMMENT '创建用户 ID',
  `create_name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '记录创建人名称',
  `update_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '1' COMMENT '最后修改用户 ID',
  `update_name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '最后一次记录操作人',
  `create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '记录创建时间',
  `update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '记录最后一次更新时间',
  `app_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '应用Code',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_agent_version` (`agent_code`,`version`,`yn`),
  KEY `idx_agent_code` (`agent_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



CREATE TABLE `app` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `yn` bigint unsigned NOT NULL DEFAULT '0' COMMENT '删除标记,0:未删除,其他表示已删除',
  `name` varchar(64) NOT NULL COMMENT '名称',
  `code` varchar(64) NOT NULL COMMENT 'Code',
  `personal` bit(1) NOT NULL COMMENT '是否属于个人',
  `default_knowledge_base_code` varchar(64) DEFAULT NULL COMMENT '默认知识库Code',
  `default_catalog_code` varchar(64) DEFAULT NULL COMMENT '默认目录Code',
  `tenant_id` varchar(64) NOT NULL COMMENT '租户ID',
  `bot_code` varchar(64) NOT NULL COMMENT '应用下的机器人Code',
  `create_id` varchar(255) NOT NULL DEFAULT '0' COMMENT '创建用户ID',
  `create_name` varchar(255) NOT NULL DEFAULT '未知用户' COMMENT '创建用户名',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_id` varchar(255) NOT NULL DEFAULT '0' COMMENT '最后修改用户ID',
  `update_name` varchar(255) NOT NULL DEFAULT '未知用户' COMMENT '最后修改用户名',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_app_code` (`code`),
  KEY `idx_app_name` (`tenant_id`,`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='应用表';



CREATE TABLE `app_role` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `code` varchar(64) NOT NULL COMMENT '权限Code',
  `app_code` varchar(64) NOT NULL COMMENT '应用Code',
  `tenant_id` varchar(64) NOT NULL COMMENT '租户ID',
  `role_type` varchar(64) NOT NULL COMMENT '权限类型',
  `role_order` int NOT NULL DEFAULT '0' COMMENT '权限排序',
  `yn` bit(1) NOT NULL DEFAULT b'0' COMMENT '删除标记,0:未删除,其他表示已删除',
  `create_id` varchar(255) NOT NULL DEFAULT '0' COMMENT '创建用户ID',
  `create_name` varchar(255) NOT NULL DEFAULT '未知用户' COMMENT '创建用户名',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_id` varchar(255) NOT NULL DEFAULT '0' COMMENT '最后修改用户ID',
  `update_name` varchar(255) NOT NULL DEFAULT '未知用户' COMMENT '最后修改用户名',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_app_role` (`code`,`app_code`,`role_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='应用权限表';



CREATE TABLE `attachment` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `yn` bigint unsigned NOT NULL DEFAULT '0' COMMENT '删除标记,0:未删除,其他表示已删除',
  `app_code` varchar(64) NOT NULL COMMENT '应用Code',
  `code` varchar(255) NOT NULL COMMENT '业务配置编码',
  `file_name` varchar(1024) DEFAULT NULL COMMENT '文件名称',
  `type` varchar(20) NOT NULL COMMENT '文件类型',
  `attachable_type` varchar(24) NOT NULL COMMENT '关联数据类型',
  `attachable_code` varchar(255) NOT NULL COMMENT '关联数据编码',
  `file_key` varchar(255) DEFAULT NULL COMMENT '文件Key',
  `create_id` varchar(255) NOT NULL DEFAULT '0' COMMENT '创建用户ID',
  `create_name` varchar(255) NOT NULL DEFAULT '未知用户' COMMENT '创建用户名',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_id` varchar(255) NOT NULL DEFAULT '0' COMMENT '最后修改用户ID',
  `update_name` varchar(255) NOT NULL DEFAULT '未知用户' COMMENT '最后修改用户名',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_attachment_file_key` (`file_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='附件表';



CREATE TABLE `bot_workflow` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增 id',
  `workflow_id` varchar(255) NOT NULL COMMENT '工作流id',
  `workflow_name` varchar(255) NOT NULL COMMENT '工作流名称',
  `workflow_cn_name` varchar(255) NOT NULL COMMENT '工作流应用名称',
  `icon_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '工作流图标',
  `scene` varchar(255) DEFAULT NULL COMMENT '场景分类',
  `canvas` text COMMENT '画布',
  `version` int NOT NULL COMMENT '版本',
  `description` varchar(255) NOT NULL COMMENT '工作流描述',
  `debug_status` int NOT NULL COMMENT '调试状态',
  `status` int NOT NULL COMMENT '状态, 1-未发布,2-已发布,3-修复待发布',
  `bot_ref_count` int NOT NULL COMMENT '引用数',
  `yn` int NOT NULL DEFAULT '0',
  `app_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '空间id',
  `tenant_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `create_id` varchar(255) NOT NULL,
  `create_name` varchar(255) NOT NULL,
  `create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `update_id` varchar(255) NOT NULL,
  `update_name` varchar(255) NOT NULL,
  `update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



CREATE TABLE `bot_workflow_edge` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增 id',
  `workflow_id` varchar(255) NOT NULL,
  `source_node_id` varchar(255) NOT NULL,
  `source_port_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `target_node_id` varchar(255) NOT NULL,
  `target_port_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `version` int NOT NULL,
  `yn` int NOT NULL DEFAULT '0',
  `create_id` varchar(255) NOT NULL,
  `create_name` varchar(255) NOT NULL,
  `create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `update_id` varchar(255) NOT NULL,
  `update_name` varchar(255) NOT NULL,
  `update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `app_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



CREATE TABLE `bot_workflow_node` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增 id',
  `workflow_id` varchar(255) NOT NULL,
  `node_id` varchar(255) NOT NULL,
  `node_type` varchar(255) NOT NULL,
  `data` text NOT NULL,
  `version` int NOT NULL,
  `yn` int NOT NULL DEFAULT '0',
  `create_id` varchar(255) NOT NULL,
  `create_name` varchar(255) NOT NULL,
  `create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `update_id` varchar(255) NOT NULL,
  `update_name` varchar(255) NOT NULL,
  `update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `app_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



CREATE TABLE `bot_workflow_scene` (
  `id` int NOT NULL AUTO_INCREMENT,
  `workflow_id` varchar(255) NOT NULL COMMENT '工作流id',
  `scene` varchar(255) NOT NULL COMMENT '工作流id',
  `yn` int NOT NULL DEFAULT '0',
  `app_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '空间id',
  `tenant_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '',
  `create_id` varchar(255) NOT NULL,
  `create_name` varchar(255) NOT NULL,
  `create_time` datetime(3) NOT NULL,
  `update_id` varchar(255) NOT NULL,
  `update_name` varchar(255) NOT NULL,
  `update_time` datetime(3) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;



CREATE TABLE `catalog` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `name` varchar(520) NOT NULL COMMENT '名称',
  `code` varchar(64) NOT NULL COMMENT 'Code',
  `description` varchar(2048) DEFAULT NULL COMMENT '描述',
  `app_code` varchar(64) NOT NULL COMMENT '应用Code',
  `knowledge_base_code` varchar(64) NOT NULL COMMENT '所属知识库Code',
  `parent_code` varchar(64) NOT NULL COMMENT '父目录Code',
  `prev_code` varchar(64) NOT NULL COMMENT '同层级前置目录Code',
  `type` varchar(20) NOT NULL COMMENT '目录类型',
  `level` int NOT NULL COMMENT '层级',
  `knowledge_code` varchar(64) DEFAULT NULL COMMENT '关联知识',
  `yn` bigint unsigned NOT NULL DEFAULT '0' COMMENT '删除标记,0:未删除,其他表示已删除',
  `create_id` varchar(255) NOT NULL DEFAULT '0' COMMENT '创建用户ID',
  `create_name` varchar(255) NOT NULL DEFAULT '未知用户' COMMENT '创建用户名',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_id` varchar(255) NOT NULL DEFAULT '0' COMMENT '最后修改用户ID',
  `update_name` varchar(255) NOT NULL DEFAULT '未知用户' COMMENT '最后修改用户名',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_catalog` (`code`),
  KEY `idx_catalog_knowledge` (`knowledge_code`),
  KEY `idx_catalog_relation` (`knowledge_code`,`parent_code`,`prev_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='目录表';



CREATE TABLE `collection` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `yn` bigint unsigned NOT NULL DEFAULT '0' COMMENT '删除标记,0:未删除,其他表示已删除',
  `code` varchar(64) NOT NULL COMMENT 'Code',
  `app_code` varchar(64) NOT NULL COMMENT '应用Code',
  `target_code` varchar(64) NOT NULL COMMENT '关联知识或知识库code',
  `target_type` varchar(20) NOT NULL COMMENT '关联类型',
  `create_id` varchar(255) NOT NULL DEFAULT '0' COMMENT '创建用户ID',
  `create_name` varchar(255) NOT NULL DEFAULT '未知用户' COMMENT '创建用户名',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_id` varchar(255) NOT NULL DEFAULT '0' COMMENT '最后修改用户ID',
  `update_name` varchar(255) NOT NULL DEFAULT '未知用户' COMMENT '最后修改用户名',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_collection_target` (`yn`,`target_code`,`target_type`),
  KEY `idx_collection_code` (`app_code`,`code`),
  KEY `idx_collection_app_target` (`app_code`,`target_code`,`target_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='收藏表';



CREATE TABLE `faq_similar_question` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `yn` bigint unsigned NOT NULL DEFAULT '0' COMMENT '删除标记,0:未删除,其他表示已删除',
  `code` varchar(255) NOT NULL COMMENT '业务配置编码',
  `app_code` varchar(64) NOT NULL COMMENT '应用Code',
  `knowledge_code` varchar(255) NOT NULL COMMENT '关联知识code',
  `knowledge_base_code` varchar(64) NOT NULL COMMENT '所属知识库Code',
  `faq_code` varchar(255) NOT NULL COMMENT '关联问答对code',
  `similar_question` varchar(500) NOT NULL COMMENT '问题',
  `create_id` varchar(255) NOT NULL DEFAULT '0' COMMENT '创建用户ID',
  `create_name` varchar(255) NOT NULL DEFAULT '未知用户' COMMENT '创建用户名',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_id` varchar(255) NOT NULL DEFAULT '0' COMMENT '最后修改用户ID',
  `update_name` varchar(255) NOT NULL DEFAULT '未知用户' COMMENT '最后修改用户名',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_faq_similar_question_code` (`app_code`,`code`),
  KEY `idx_faq_similar_knowledge_code` (`app_code`,`knowledge_code`),
  KEY `idx_faq_similar_question` (`similar_question`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='相似问表';



CREATE TABLE `file` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `yn` bigint unsigned NOT NULL DEFAULT '0' COMMENT '删除标记,0:未删除,其他表示已删除',
  `app_code` varchar(64) NOT NULL COMMENT '应用Code',
  `code` varchar(255) NOT NULL COMMENT '业务配置编码',
  `file_key` varchar(255) NOT NULL COMMENT '文件路径',
  `md5` varchar(255) NOT NULL COMMENT 'md5',
  `total_size` bigint unsigned NOT NULL COMMENT '文件大小',
  `access_time` datetime NOT NULL COMMENT '最近访问时间',
  `create_id` varchar(255) NOT NULL DEFAULT '0' COMMENT '创建用户ID',
  `create_name` varchar(255) NOT NULL DEFAULT '未知用户' COMMENT '创建用户名',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_id` varchar(255) NOT NULL DEFAULT '0' COMMENT '最后修改用户ID',
  `update_name` varchar(255) NOT NULL DEFAULT '未知用户' COMMENT '最后修改用户名',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='文件缓存表';


CREATE TABLE `gs_base_config` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `yn` bigint unsigned NOT NULL DEFAULT '0' COMMENT '删除标记,0:未删除,其他表示已删除',
  `code` varchar(255) NOT NULL COMMENT '业务配置编码',
  `dimension` varchar(255) NOT NULL COMMENT '配置维度:系统维度、租户维度、应用维度、业务维度',
  `scene` varchar(255) NOT NULL COMMENT '业务配置场景',
  `scene_desc` varchar(255) NOT NULL COMMENT '业务配置场景说明',
  `business_no` varchar(255) NOT NULL COMMENT '业务场景配置唯一标识',
  `obj_value` text COMMENT '配置json格式value值',
  `obj_class` varchar(1024) DEFAULT NULL COMMENT '配置json的class对象',
  `create_id` varchar(255) NOT NULL DEFAULT '0' COMMENT '创建用户ID',
  `create_name` varchar(255) NOT NULL DEFAULT '未知用户' COMMENT '创建用户名',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_id` varchar(255) NOT NULL DEFAULT '0' COMMENT '最后修改用户ID',
  `update_name` varchar(255) NOT NULL DEFAULT '未知用户' COMMENT '最后修改用户名',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `idx_business_no_key_scene` (`business_no`,`scene`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='公共配置信息表';



CREATE TABLE `knowledge` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `yn` bigint unsigned NOT NULL DEFAULT '0' COMMENT '删除标记,0:未删除,其他表示已删除',
  `app_code` varchar(64) NOT NULL COMMENT '应用Code',
  `name` varchar(520) NOT NULL COMMENT '名称',
  `code` varchar(64) NOT NULL COMMENT 'Code',
  `knowledge_base_code` bigint unsigned NOT NULL COMMENT '所属知识库编码',
  `type` varchar(20) NOT NULL COMMENT '类型',
  `on` bit(1) NOT NULL DEFAULT b'1' COMMENT '开关',
  `status` varchar(20) NOT NULL COMMENT '状态',
  `indexing_status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '索引状态',
  `image_indexing_status` varchar(20) NOT NULL DEFAULT 'success' COMMENT '图片索引状态',
  `original_file_key` varchar(1024) DEFAULT NULL COMMENT '原始文件key',
  `view_file_key` varchar(1024) DEFAULT NULL COMMENT '预览文件key',
  `view_count` bigint unsigned NOT NULL DEFAULT '0' COMMENT '浏览次数',
  `knowledge_tip` varchar(128) DEFAULT NULL COMMENT '消息提示',
  `slice_file_info` varchar(160) DEFAULT NULL COMMENT '文件切片信息',
  `publish_status` varchar(255) NOT NULL DEFAULT 'UNPUBLISHED' COMMENT '发布状态',
  `publish_time` datetime DEFAULT NULL COMMENT '发布时间',
  `publish_count` int NOT NULL DEFAULT '0' COMMENT '发布次数',
  `tag_codes` text COMMENT '关联标签信息',
  `cover_file_key` varchar(255) DEFAULT NULL COMMENT '封面key',
  `description` text COMMENT '描述',
  `create_id` varchar(255) NOT NULL DEFAULT '0' COMMENT '创建用户ID',
  `create_name` varchar(255) NOT NULL DEFAULT '未知用户' COMMENT '创建用户名',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_id` varchar(255) NOT NULL DEFAULT '0' COMMENT '最后修改用户ID',
  `update_name` varchar(255) NOT NULL DEFAULT '未知用户' COMMENT '最后修改用户名',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_knowledge_name` (`knowledge_base_code`,`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='知识表';



CREATE TABLE `knowledge_base` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `name` varchar(64) NOT NULL COMMENT '名称',
  `code` varchar(64) NOT NULL COMMENT 'Code',
  `type` varchar(16) NOT NULL DEFAULT 'FILE' COMMENT '类型',
  `desc` varchar(2048) DEFAULT NULL COMMENT '描述',
  `on` bit(1) NOT NULL DEFAULT b'1' COMMENT '开关',
  `config` text COMMENT '高级配置',
  `app_code` varchar(64) NOT NULL COMMENT '应用Code',
  `icon` varchar(64) DEFAULT NULL COMMENT '图标',
  `cover_key` varchar(100) DEFAULT NULL COMMENT '封面文件key',
  `yn` bigint unsigned NOT NULL DEFAULT '0' COMMENT '删除标记,0:未删除,其他表示已删除',
  `create_id` varchar(255) NOT NULL DEFAULT '0' COMMENT '创建用户ID',
  `create_name` varchar(255) NOT NULL DEFAULT '未知用户' COMMENT '创建用户名',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_id` varchar(255) NOT NULL DEFAULT '0' COMMENT '最后修改用户ID',
  `update_name` varchar(255) NOT NULL DEFAULT '未知用户' COMMENT '最后修改用户名',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_code` (`code`),
  KEY `idx_app_code` (`app_code`,`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='知识库表';



CREATE TABLE `knowledge_essential` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `yn` bigint unsigned NOT NULL DEFAULT '0' COMMENT '删除标记,0:未删除,其他表示已删除',
  `app_code` varchar(64) NOT NULL COMMENT '应用Code',
  `code` varchar(255) NOT NULL COMMENT '业务配置编码',
  `knowledge_code` varchar(64) NOT NULL COMMENT '所属知识编码',
  `title` varchar(512) NOT NULL COMMENT '标题',
  `content` text NOT NULL COMMENT '内容',
  `source` text NOT NULL COMMENT '来源',
  `parent_code` varchar(64) DEFAULT NULL COMMENT '父节点Code',
  `create_id` varchar(255) NOT NULL DEFAULT '0' COMMENT '创建用户ID',
  `create_name` varchar(255) NOT NULL DEFAULT '未知用户' COMMENT '创建用户名',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_id` varchar(255) NOT NULL DEFAULT '0' COMMENT '最后修改用户ID',
  `update_name` varchar(255) NOT NULL DEFAULT '未知用户' COMMENT '最后修改用户名',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_knowledge_code` (`knowledge_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='知识要素表';



CREATE TABLE `knowledge_extra_info` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `yn` bigint unsigned NOT NULL DEFAULT '0' COMMENT '删除标记,0:未删除,其他表示已删除',
  `app_code` varchar(64) NOT NULL COMMENT '应用Code',
  `code` varchar(64) NOT NULL COMMENT 'Code',
  `extra_key` varchar(64) NOT NULL DEFAULT 'DEFAULT' COMMENT '存储任务编号',
  `knowledge_code` bigint unsigned NOT NULL COMMENT '所属知识编码',
  `content` mediumtext NOT NULL COMMENT '内容',
  `type` varchar(20) NOT NULL COMMENT '类型',
  `create_id` varchar(255) NOT NULL DEFAULT '0' COMMENT '创建用户ID',
  `create_name` varchar(255) NOT NULL DEFAULT '未知用户' COMMENT '创建用户名',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_id` varchar(255) NOT NULL DEFAULT '0' COMMENT '最后修改用户ID',
  `update_name` varchar(255) NOT NULL DEFAULT '未知用户' COMMENT '最后修改用户名',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_knowledge_extra_info_knowledge` (`knowledge_code`,`extra_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='知识额外信息表';



CREATE TABLE `knowledge_extract` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `knowledge_code` varchar(255) NOT NULL COMMENT '知识id',
  `status` varchar(50) NOT NULL DEFAULT '1' COMMENT '0：废弃，1：使用中',
  `failure_reason` varchar(1000) DEFAULT NULL COMMENT '失败原因',
  `extract_status` varchar(10) NOT NULL DEFAULT '0' COMMENT '0：抽取中,1:抽取失败，2：抽取成功',
  `create_id` varchar(255) NOT NULL DEFAULT '1' COMMENT '创建人id',
  `create_name` varchar(255) NOT NULL DEFAULT '未知用户' COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_id` varchar(255) NOT NULL DEFAULT '0' COMMENT '修改人标识',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `update_name` varchar(255) NOT NULL DEFAULT '未知用户' COMMENT '修改人名字',
  `yn` bigint NOT NULL DEFAULT '0' COMMENT '0:未删除,其他表示已删除',
  `code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '业务code',
  `app_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_extract_yn_code` (`app_code`,`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='知识抽取主表';



CREATE TABLE `knowledge_faq` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `yn` bigint unsigned NOT NULL DEFAULT '0' COMMENT '删除标记,0:未删除,其他表示已删除',
  `code` varchar(255) NOT NULL COMMENT '业务配置编码',
  `app_code` varchar(64) NOT NULL COMMENT '应用Code',
  `knowledge_code` varchar(255) NOT NULL COMMENT '关联知识code',
  `knowledge_base_code` varchar(64) NOT NULL COMMENT '所属知识库Code',
  `on` bit(1) NOT NULL DEFAULT b'1' COMMENT '开关',
  `question` varchar(1024) NOT NULL COMMENT '问题',
  `answer_type` varchar(32) NOT NULL COMMENT '回答类型',
  `answer` text NOT NULL COMMENT '回答',
  `is_auto` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否自动抽取',
  `publish_status` varchar(255) NOT NULL DEFAULT 'UNPUBLISHED' COMMENT '发布状态',
  `publish_time` datetime DEFAULT NULL COMMENT '发布时间',
  `publish_count` int NOT NULL DEFAULT '0' COMMENT '发布次数',
  `create_id` varchar(255) NOT NULL DEFAULT '0' COMMENT '创建用户ID',
  `create_name` varchar(255) NOT NULL DEFAULT '未知用户' COMMENT '创建用户名',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_id` varchar(255) NOT NULL DEFAULT '0' COMMENT '最后修改用户ID',
  `update_name` varchar(255) NOT NULL DEFAULT '未知用户' COMMENT '最后修改用户名',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_knowledge_faq_knowledge_id` (`knowledge_code`),
  KEY `idx_knowledge_faq_code` (`app_code`,`code`),
  KEY `idx_knowledge_faq_knowledge_code` (`app_code`,`knowledge_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='问答表';



CREATE TABLE `knowledge_faq_prod` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `yn` bigint unsigned NOT NULL DEFAULT '0' COMMENT '删除标记,0:未删除,其他表示已删除',
  `code` varchar(255) NOT NULL COMMENT '业务配置编码',
  `app_code` varchar(64) NOT NULL COMMENT '应用Code',
  `knowledge_code` varchar(255) NOT NULL COMMENT '关联知识code',
  `content` mediumtext COMMENT '发布内容',
  `create_id` varchar(255) NOT NULL DEFAULT '0' COMMENT '创建用户ID',
  `create_name` varchar(255) NOT NULL DEFAULT '未知用户' COMMENT '创建用户名',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_id` varchar(255) NOT NULL DEFAULT '0' COMMENT '最后修改用户ID',
  `update_name` varchar(255) NOT NULL DEFAULT '未知用户' COMMENT '最后修改用户名',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_knowledge_code` (`knowledge_code`),
  KEY `knowledge_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='问答线上表';



CREATE TABLE `knowledge_faq_temp` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `yn` bigint NOT NULL DEFAULT '0' COMMENT '删除标记',
  `code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '业务配置编码',
  `knowledge_code` varchar(255) NOT NULL COMMENT '关联知识code',
  `on` bit(1) NOT NULL DEFAULT b'1' COMMENT '开关',
  `question` varchar(1024) NOT NULL COMMENT '问题',
  `answer_type` varchar(32) NOT NULL COMMENT '回答类型',
  `answer` text NOT NULL COMMENT '回答',
  `corpus` text COMMENT '相似问提',
  `corpus_count` int DEFAULT NULL COMMENT '相似问个数',
  `extract_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '抽取id',
  `create_id` varchar(255) NOT NULL DEFAULT '0' COMMENT '创建人id',
  `create_name` varchar(255) NOT NULL DEFAULT '未知用户' COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_id` varchar(255) NOT NULL DEFAULT '0' COMMENT '创建人标识',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `update_name` varchar(255) NOT NULL DEFAULT '未知用户' COMMENT '修改人名字',
  `store_time` datetime DEFAULT NULL COMMENT '入库时间',
  `app_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `store_status` varchar(8) NOT NULL DEFAULT '0' COMMENT '入库状态 0：未入库 1：已入库',
  `is_auto` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `knowledge_faq_temp_un` (`code`),
  KEY `idx_faq_temp_yn_code` (`app_code`,`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='问题对临时表';



CREATE TABLE `knowledge_prod` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `yn` bigint unsigned NOT NULL DEFAULT '0' COMMENT '删除标记,0:未删除,其他表示已删除',
  `app_code` varchar(64) NOT NULL COMMENT '应用Code',
  `code` varchar(64) NOT NULL COMMENT '知识code',
  `content` mediumtext COMMENT '发布内容',
  `create_id` varchar(255) NOT NULL DEFAULT '0' COMMENT '创建用户ID',
  `create_name` varchar(255) NOT NULL DEFAULT '未知用户' COMMENT '创建用户名',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_id` varchar(255) NOT NULL DEFAULT '0' COMMENT '最后修改用户ID',
  `update_name` varchar(255) NOT NULL DEFAULT '未知用户' COMMENT '最后修改用户名',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='知识线上表';



CREATE TABLE `knowledge_report` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `name` varchar(64) NOT NULL COMMENT '名称',
  `code` varchar(64) NOT NULL COMMENT 'Code',
  `app_code` varchar(64) NOT NULL COMMENT '应用Code',
  `theme_text` varchar(512) NOT NULL COMMENT '用户原始主题',
  `content` mediumtext COMMENT '文章内容',
  `type` varchar(64) NOT NULL COMMENT '文章内容格式',
  `outline_data` text COMMENT '大纲结构',
  `chose` varchar(10000) DEFAULT NULL COMMENT '智能问答选择项',
  `yn` bigint unsigned NOT NULL DEFAULT '0' COMMENT '删除标记,0:未删除,其他表示已删除',
  `create_id` varchar(255) NOT NULL DEFAULT '0' COMMENT '创建用户ID',
  `create_name` varchar(255) NOT NULL DEFAULT '未知用户' COMMENT '创建用户名',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_id` varchar(255) NOT NULL DEFAULT '0' COMMENT '最后修改用户ID',
  `update_name` varchar(255) NOT NULL DEFAULT '未知用户' COMMENT '最后修改用户名',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_code` (`code`),
  KEY `idx_app_code` (`app_code`,`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='知识报告表';



CREATE TABLE `prompt_template` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `yn` bigint unsigned NOT NULL DEFAULT '0' COMMENT '删除标记,0:未删除,其他表示已删除',
  `code` varchar(64) NOT NULL COMMENT 'Code',
  `app_code` varchar(64) NOT NULL COMMENT '应用Code',
  `name` varchar(64) NOT NULL COMMENT '名称',
  `description` varchar(512) DEFAULT NULL COMMENT '描述',
  `content` text COMMENT '内容',
  `model_param` varchar(2048) DEFAULT NULL COMMENT '模型参数',
  `create_id` varchar(255) NOT NULL DEFAULT '0' COMMENT '创建用户ID',
  `create_name` varchar(255) NOT NULL DEFAULT '未知用户' COMMENT '创建用户名',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_id` varchar(255) NOT NULL DEFAULT '0' COMMENT '最后修改用户ID',
  `update_name` varchar(255) NOT NULL DEFAULT '未知用户' COMMENT '最后修改用户名',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_prompt_template_code` (`app_code`,`code`),
  KEY `idx_prompt_template_name` (`app_code`,`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='prompt模板表';



CREATE TABLE `robot_ai_intent` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键 ID',
  `tenant_id` varchar(128) DEFAULT '' COMMENT '租户 ID',
  `app_id` varchar(255) DEFAULT NULL COMMENT '应用id',
  `model_code` varchar(255) NOT NULL COMMENT '模型编码',
  `intent_code` varchar(255) NOT NULL COMMENT '意图编码',
  `intent_name` varchar(2048) NOT NULL COMMENT '意图名称',
  `intent_id` varchar(255) DEFAULT NULL COMMENT '意图 ID(全局唯一)',
  `yn` decimal(20,0) NOT NULL DEFAULT '0' COMMENT '删除标记，0-未删除，其他表示已删除',
  `create_id` varchar(100) NOT NULL DEFAULT '1' COMMENT '创建用户 ID',
  `create_name` varchar(255) NOT NULL COMMENT '创建用户名称',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建用户时间',
  `update_id` varchar(100) NOT NULL DEFAULT '1' COMMENT '最后修改用户 ID',
  `update_name` varchar(255) NOT NULL COMMENT '最后修改用户名称',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) COMMENT '主键'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='算法模型意图表';



CREATE TABLE `robot_ai_model` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键 ID',
  `tenant_id` varchar(128) DEFAULT '' COMMENT '租户 ID',
  `app_id` varchar(255) DEFAULT NULL COMMENT '应用id',
  `model_code` varchar(255) NOT NULL COMMENT '模型编码',
  `model_name` varchar(255) NOT NULL COMMENT '模型名称',
  `model_type` varchar(255) NOT NULL COMMENT '模型类型',
  `model_state` varchar(255) DEFAULT NULL COMMENT '模型状态',
  `data_format` varchar(255) DEFAULT NULL COMMENT '数据格式',
  `data_dimension` int DEFAULT NULL COMMENT '数据维度',
  `api_key` varchar(255) DEFAULT NULL COMMENT 'API 密钥',
  `intent_number` int NOT NULL DEFAULT '0' COMMENT '意图数量',
  `description` varchar(255) DEFAULT NULL COMMENT '描述信息',
  `external_model_id` varchar(255) DEFAULT NULL COMMENT '外部模型 ID',
  `external_model_url` varchar(1024) DEFAULT NULL COMMENT '外部模型 URL',
  `external_model_config` varchar(10000) DEFAULT NULL COMMENT '模型拓展配置',
  `global_flag` tinyint NOT NULL DEFAULT '0' COMMENT '是否为全局/是否是预置，0-否，1-是',
  `model_config` text COMMENT '模型配置',
  `yn` decimal(20,0) NOT NULL DEFAULT '0' COMMENT '删除标记，0-未删除，其他表示已删除',
  `create_id` varchar(100) NOT NULL DEFAULT '1' COMMENT '创建用户 ID',
  `create_name` varchar(255) NOT NULL COMMENT '创建用户名称',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_id` varchar(100) NOT NULL DEFAULT '1' COMMENT '最后修改用户 ID',
  `update_name` varchar(255) NOT NULL COMMENT '足后修改用户名称',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `distinguish_model_config` tinyint NOT NULL DEFAULT '0' COMMENT '是否区分模型配置(是否区分用户请求或离线文档)，0-不区分，1-区分',
  `model_secret` varchar(255) DEFAULT NULL COMMENT '大模型api secret',
  `model_provider` varchar(255) DEFAULT NULL COMMENT '大模型提供方',
  `threshold` double DEFAULT NULL COMMENT '阈值',
  `is_default` varchar(100) DEFAULT '0' COMMENT '是否默认模型，0:否，1:是',
  PRIMARY KEY (`id`) COMMENT '主键',
  UNIQUE KEY `idx_model_code` (`model_code`) COMMENT '模型编码唯一索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='算法模型表';



CREATE TABLE `session_info` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键 ID',
  `tenant_id` varchar(128) DEFAULT '' COMMENT '租户 ID',
  `app_id` varchar(128) DEFAULT '' COMMENT '会话关联的应用 ID',
  `user_id` varchar(128) DEFAULT '' COMMENT '会话关联的用户 ID',
  `agent_code` varchar(255) DEFAULT NULL COMMENT '机器编码',
  `session_id` varchar(255) DEFAULT NULL COMMENT '会话ID',
  `yn` decimal(20,0) NOT NULL DEFAULT '0' COMMENT '删除标记，0-未删除，其他表示已删除',
  `create_id` varchar(100) NOT NULL DEFAULT '1' COMMENT '创建用户 ID',
  `create_name` varchar(255) NOT NULL COMMENT '创建用户名称',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建用户时间',
  `update_id` varchar(100) NOT NULL DEFAULT '1' COMMENT '最后修改用户 ID',
  `update_name` varchar(255) NOT NULL COMMENT '最后修改用户名称',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) COMMENT '主键'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='会话信息表';



CREATE TABLE `tag_tree_node` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `yn` bigint unsigned NOT NULL DEFAULT '0' COMMENT '删除标记,0:未删除,其他表示已删除',
  `code` varchar(64) NOT NULL COMMENT 'Code',
  `app_code` varchar(64) NOT NULL COMMENT '应用Code',
  `name` varchar(64) NOT NULL COMMENT '名称',
  `parent_code` varchar(64) NOT NULL COMMENT '父标签code',
  `path` varchar(255) NOT NULL COMMENT '标签路径',
  `description` text COMMENT '标签描述',
  `create_id` varchar(255) NOT NULL DEFAULT '0' COMMENT '创建用户ID',
  `create_name` varchar(255) NOT NULL DEFAULT '未知用户' COMMENT '创建用户名',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_id` varchar(255) NOT NULL DEFAULT '0' COMMENT '最后修改用户ID',
  `update_name` varchar(255) NOT NULL DEFAULT '未知用户' COMMENT '最后修改用户名',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_tag_tree_node_code` (`app_code`,`code`),
  KEY `idx_tag_tree_node_path` (`app_code`,`path`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='标签表';



CREATE TABLE `workflow_task` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `workflow_id` varchar(100) NOT NULL COMMENT '工作流id',
  `session_id` varchar(100) NOT NULL COMMENT 'sessionId',
  `message_id` varchar(100) NOT NULL COMMENT '消息id',
  `run_status` int NOT NULL DEFAULT '3' COMMENT '运行状态0-成功，1-失败，3-运行中',
  `yn` int NOT NULL DEFAULT '0',
  `update_time` timestamp NULL DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT NULL,
  `create_id` varchar(100) DEFAULT NULL,
  `create_name` varchar(100) DEFAULT NULL,
  `update_id` varchar(100) DEFAULT NULL,
  `update_name` varchar(100) DEFAULT NULL,
  `app_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='工作流任务执行状态';



CREATE TABLE `workflow_test_log` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `workflow_id` varchar(100) NOT NULL COMMENT '工作流id',
  `node_id` varchar(100) NOT NULL COMMENT '节点id',
  `node_name` varchar(100) NOT NULL COMMENT '节点名称',
  `input_param` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '参数',
  `update_time` timestamp NOT NULL COMMENT '保存时间',
  `create_time` timestamp NOT NULL COMMENT '创建时间',
  `app_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `create_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `update_id` varchar(100) DEFAULT NULL,
  `create_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `update_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `yn` int NOT NULL,
  `run_status` int NOT NULL DEFAULT '0' COMMENT '运行状态0-成功，1-运行失败',
  `output_param` text COMMENT '输出参数',
  `biz_data` text COMMENT '业务参数',
  `cost` bigint DEFAULT NULL COMMENT '执行耗时 s',
  `node_type` varchar(100) DEFAULT NULL COMMENT '节点类型',
  `error` text COMMENT '错误信息',
  `message_id` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='工作流调试日志';



CREATE TABLE `workflow_version_info` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '自增 id',
  `tenant_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT '' COMMENT '支撑平台租户 id',
  `workflow_id` varchar(128) NOT NULL DEFAULT '' COMMENT '工作流id',
  `version` bigint NOT NULL DEFAULT '1' COMMENT '版本号',
  `status` varchar(128) NOT NULL DEFAULT 'EDITABLE' COMMENT '版本状态',
  `runtime_status` varchar(128) NOT NULL DEFAULT 'OFFLINE' COMMENT '运行状态',
  `version_desc` varchar(1024) NOT NULL DEFAULT '' COMMENT '版本描述',
  `yn` int NOT NULL DEFAULT '0' COMMENT '记录删除标识',
  `app_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '1' COMMENT '空间id',
  `create_id` varchar(100) NOT NULL DEFAULT '1' COMMENT '创建用户 ID',
  `create_name` varchar(256) NOT NULL DEFAULT '' COMMENT '记录创建人名称',
  `update_id` varchar(100) NOT NULL DEFAULT '1' COMMENT '最后修改用户 ID',
  `update_name` varchar(256) NOT NULL DEFAULT '' COMMENT '最后一次记录操作人',
  `create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '记录创建时间',
  `update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '记录最后一次更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_workflow_version` (`workflow_id`,`version`,`yn`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='workflow版本信息表';
