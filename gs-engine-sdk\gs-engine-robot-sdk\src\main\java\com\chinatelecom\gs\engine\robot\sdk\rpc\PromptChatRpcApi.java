package com.chinatelecom.gs.engine.robot.sdk.rpc;

import com.chinatelecom.gs.engine.robot.sdk.dto.PromptChatRequest;
import com.chinatelecom.gs.engine.robot.sdk.dto.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author: zhaoyufan177
 * @date: 2025/8/6
 */

@FeignClient(name = "gs-engine-robot", url = "${gs.kmsRpcConfig.gsUrl:}", contextId = "promptChatService", path = "/ais/bot/rpc/prompt")
public interface PromptChatRpcApi {

    @PostMapping(path = "/chatRequestHandle")
    Result<String> chatRequestHandle(@Validated @RequestBody PromptChatRequest request);

}
