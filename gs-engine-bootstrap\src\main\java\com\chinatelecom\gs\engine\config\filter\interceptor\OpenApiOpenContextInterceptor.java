package com.chinatelecom.gs.engine.config.filter.interceptor;

import com.chinatelecom.cloud.platform.client.rpc.PlatformUser;
import com.chinatelecom.cloud.platform.client.rpc.UserInfo;
import com.chinatelecom.cloud.platform.client.util.SsoUtil;
import com.chinatelecom.gs.engine.channel.manage.ChannelSecretManagerService;
import com.chinatelecom.gs.engine.channel.service.dto.ChannelApiSecretDTO;
import com.chinatelecom.gs.engine.common.config.property.GsGlobalConfig;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.context.RequestInfo;
import com.chinatelecom.gs.engine.common.utils.InterceptorUtils;
import com.chinatelecom.gs.engine.common.utils.UserInfoUtils;
import com.chinatelecom.gs.engine.config.filter.BaseHandlerInterceptor;
import com.chinatelelcom.gs.engine.sdk.common.enums.RequestSourceType;
import jakarta.annotation.Resource;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * <AUTHOR>
 * @version 1.0
 * @description OpenApi-Open 上下文拦截器，用于处理 Base64 编码的 API 密钥鉴权
 * @date 2024年12月31日
 */
@Component
@Slf4j
public class OpenApiOpenContextInterceptor implements BaseHandlerInterceptor {

    private static final String HEADER_STRING = "Bearer ";

    @Value("${platform.super.tenant:SuperAis}")
    private String superTenant;

    @Resource
    private GsGlobalConfig gsGlobalConfig;

    @Resource
    private ChannelSecretManagerService channelSecretManagerService;

    public boolean preHandle(ServletRequest request, ServletResponse response) throws Exception {
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;

        // 获取 Authorization 头
        String authHeader = httpRequest.getHeader("Authorization");

        if (StringUtils.isEmpty(authHeader) || !authHeader.startsWith(HEADER_STRING)) {
            // 尝试从 api-key 头获取
            String apiKeyHeader = httpRequest.getHeader("api-key");
            if (StringUtils.isEmpty(apiKeyHeader)) {
                return InterceptorUtils.writeError(httpResponse, "A0025", "缺少API密钥");
            }
            authHeader = HEADER_STRING + apiKeyHeader;
        }

        String apiKey = authHeader.substring(HEADER_STRING.length()).trim();

        // Base64 解码方式：tenantId@userId => Base64 编码
        String userId = "";
        String tenantId = "";

        UserInfo userInfo = null;

        // 先尝试Base64解码方式
        try {
            // Base64 解码
            byte[] decodedBytes = Base64.getDecoder().decode(apiKey);
            String decodedString = new String(decodedBytes, StandardCharsets.UTF_8);

            // 按@分割获取 tenantId 和 userId
            String[] parts = decodedString.split("@");
            if (parts.length == 2) {
                tenantId = parts[0].trim();
                userId = parts[1].trim();
                log.info("使用 Base64 解码方式获取用户信息: tenantId={}, userId={}", tenantId, userId);
                userInfo = UserInfoUtils.getUserInfo(tenantId, userId);
            }
        } catch (IllegalArgumentException e) {
            log.warn("Base64 解码失败，尝试secretDTO查询方式: {}", e.getMessage());
        }

        // 如果Base64方式失败，尝试secretDTO查询方式
        if (userInfo == null) {
            try {
                ChannelApiSecretDTO secretDTO = channelSecretManagerService.getSecretWithSecret(apiKey);
                if (secretDTO != null) {
                    userId = secretDTO.getCreateId();
                    tenantId = secretDTO.getTenantId();
                    log.info("使用secretDTO查询方式获取用户信息: tenantId={}, userId={}", tenantId, userId);
                    userInfo = UserInfoUtils.getUserInfo(tenantId, userId);
                }
            } catch (Exception e) {
                log.error("secretDTO查询失败: {}", e.getMessage());
            }
        }


        // 验证用户信息
        if (userInfo == null) {
            log.error("用户信息不存在: tenantId={}, userId={}", tenantId, userId);
            return InterceptorUtils.writeError(httpResponse, "A0025", "用户信息不存在");
        }

        // 设置请求上下文
        RequestInfo requestInfo = new RequestInfo();
        requestInfo.setTenant(true);
        requestInfo.setRequestSourceType(RequestSourceType.OPENAPI);
        RequestContext.set(requestInfo);
        requestInfo.setAppCode(tenantId);
        requestInfo.setUserId(userId);
        requestInfo.setTenantId(tenantId);
        requestInfo.setUserName(userInfo.getName());
        requestInfo.setTeam(UserInfoUtils.getUserTeam());
        requestInfo.setIsSuperTenant(superTenant.equals(tenantId));
        requestInfo.setAppSourceType(gsGlobalConfig.getSystem().getDefaultAppSourceType());
        requestInfo.setEntry("openApi");

        boolean isAdmin = UserInfoUtils.isAdmin(requestInfo.getTenantId(), requestInfo.getUserId(), requestInfo.getAppSourceType());
        requestInfo.setIsAdmin(isAdmin);

        PlatformUser platformUser = PlatformUser.builder()
                .userId(userId)
                .username(userInfo.getName())
                .corpCode(tenantId)
                .isAdmin(isAdmin)
                .build();
        SsoUtil.add(platformUser);

        return true;
    }

    /**
     * 完成后调用
     *
     * @param request
     * @param response
     * @param ex
     * @throws Exception
     */
    public void afterCompletion(ServletRequest request, ServletResponse response, Exception ex) throws Exception {
        RequestContext.remove();
    }

}