-- 2.6.0版智文知识库授权数据从认证平台迁移到智文本身管理，为兼容历史授权数据，使用以下迁移方案进行迁移


-- -----------------------方案一： 适合认证平台和智文数据库在一个数据库实例中的场景-------------------------

INSERT IGNORE INTO ai_kms_gs_engine.access_control_resource_privilege(`object_id`,`object_name`,`object_type`,`resource_id`,`grant_type`,`resource_type`,`tenant_id`,`yn`,
`create_id`,`create_name`,`create_time`,`update_id`,`update_name`,`update_time`)
SELECT a.`object_id`,u.username,'USER',d.`resource_code`,'edit','KNOWLEDGE',a.`corp_code`,0,a.create_user,u.username,u.create_time,a.create_user,u.username,u.update_time
 FROM cloud_platform.`data_resource_authentication` a LEFT JOIN cloud_platform.`data_resource` d ON a.data_resource_id=d.`id`
 LEFT JOIN cloud_platform.`user` u ON a.`object_id`=u.user_id
WHERE a.app_code='ais-ks' AND object_type=1 AND d.`app_code`='ais-ks' AND d.resource_type='KnowledgeBase' AND a.`is_deleted`=0 AND d.`is_deleted`=0 AND object_id !='_empty_';


INSERT IGNORE INTO ai_kms_gs_engine.access_control_resource_privilege(`object_id`,`object_name`,`object_type`,`resource_id`,`grant_type`,`resource_type`,`tenant_id`,`yn`,
`create_id`,`create_name`,`create_time`,`update_id`,`update_name`,`update_time`)
SELECT a.`object_id`,u.name,'TEAM',d.`resource_code`,'edit','KNOWLEDGE',a.`corp_code`,0,a.create_user,u.name,u.create_time,a.create_user,u.name,u.update_time
 FROM cloud_platform.`data_resource_authentication` a LEFT JOIN cloud_platform.`data_resource` d ON a.data_resource_id=d.`id`
 LEFT JOIN cloud_platform.`team` u ON a.`object_id`=u.team_code
WHERE a.app_code='ais-ks' AND object_type=3 AND d.`app_code`='ais-ks' AND d.resource_type='KnowledgeBase' AND a.`is_deleted`=0 AND d.`is_deleted`=0 AND object_id !='_empty_';

-- -----------------------方案一完毕-------------------------



-- -----------------------方案二： 适合认证平台和智文数据库网络不通的场景-------------------------
-- 临时表(在认证平台数据库执行sql)
CREATE TABLE IF NOT EXISTS `access_control_resource_privilege` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `object_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '授予权限的对象id',
  `object_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `object_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '对象类型：USER-用户、TEAM-团队、DEPART-部门',
  `resource_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '资源的id',
  `grant_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '授予权限的类型',
  `resource_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '资源所属应用类型，机器人、知识库等',
  `tenant_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '租户id',
  `yn` bigint unsigned NOT NULL DEFAULT '0' COMMENT '删除标记,0:未删除,其他表示已删除',
  `create_id` varchar(255) NOT NULL DEFAULT '0' COMMENT '创建用户ID',
  `create_name` varchar(255) NOT NULL DEFAULT '未知用户' COMMENT '创建用户名',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_id` varchar(255) NOT NULL DEFAULT '0' COMMENT '最后修改用户ID',
  `update_name` varchar(255) NOT NULL DEFAULT '未知用户' COMMENT '最后修改用户名',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_access_control_resource_privilege_unique` (`resource_id`,`resource_type`,`object_id`,`object_type`,`yn`),
  KEY `idx_object` (`object_id`,`object_type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='对象资源权限关系表';


-- 知识库授权用户数据迁移(在认证平台数据库执行sql)
INSERT IGNORE INTO access_control_resource_privilege(`object_id`,`object_name`,`object_type`,`resource_id`,`grant_type`,`resource_type`,`tenant_id`,`yn`,
`create_id`,`create_name`,`create_time`,`update_id`,`update_name`,`update_time`)
SELECT a.`object_id`,u.username,'USER',d.`resource_code`,'edit','KNOWLEDGE',a.`corp_code`,0,a.create_user,u.username,u.create_time,a.create_user,u.username,u.update_time
 FROM `data_resource_authentication` a LEFT JOIN `data_resource` d ON a.data_resource_id=d.`id`
 LEFT JOIN `user` u ON a.`object_id`=u.user_id
WHERE a.app_code='ais-ks' AND object_type=1 AND d.`app_code`='ais-ks' AND d.resource_type='KnowledgeBase' AND a.`is_deleted`=0 AND d.`is_deleted`=0 AND object_id !='_empty_';


-- 知识库授权团队数据迁移(在认证平台数据库执行sql)
INSERT IGNORE INTO access_control_resource_privilege(`object_id`,`object_name`,`object_type`,`resource_id`,`grant_type`,`resource_type`,`tenant_id`,`yn`,
`create_id`,`create_name`,`create_time`,`update_id`,`update_name`,`update_time`)
SELECT a.`object_id`,u.name,'TEAM',d.`resource_code`,'edit','KNOWLEDGE',a.`corp_code`,0,a.create_user,u.name,u.create_time,a.create_user,u.name,u.update_time
 FROM `data_resource_authentication` a LEFT JOIN `data_resource` d ON a.data_resource_id=d.`id`
 LEFT JOIN `team` u ON a.`object_id`=u.team_code
WHERE a.app_code='ais-ks' AND object_type=3 AND d.`app_code`='ais-ks' AND d.resource_type='KnowledgeBase' AND a.`is_deleted`=0 AND d.`is_deleted`=0 AND object_id !='_empty_';

-- 导出数据(在认证平台数据库执行sql)
mysqldump -u root -p --no-create-info cloud_platform.access_control_resource_privilege > auth.sql

-- 删除临时表(在认证平台数据库执行sql)
DROP TABLE IF EXISTS `access_control_resource_privilege`;

-- 导入数据到智文数据库，到智文数据库中执行(在智文数据库执行sql)
mysql -u root -p ai_kms_gs_engine < auth.sql
-- -----------------------方案二完毕-------------------------
