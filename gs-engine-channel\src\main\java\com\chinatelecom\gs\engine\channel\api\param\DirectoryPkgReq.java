package com.chinatelecom.gs.engine.channel.api.param;

import com.chinatelecom.gs.engine.channel.api.vo.CommonAuthParam;
import com.chinatelecom.gs.engine.robot.sdk.enums.DirectoryTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 技能分页请求
 * @author: xktang
 * @date: 2024/5/15 下午2:51
 * @version: 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DirectoryPkgReq extends CommonAuthParam implements Serializable {
    @Schema(description = "对话能力")
    private List<DirectoryTypeEnum> directoryType;
}
