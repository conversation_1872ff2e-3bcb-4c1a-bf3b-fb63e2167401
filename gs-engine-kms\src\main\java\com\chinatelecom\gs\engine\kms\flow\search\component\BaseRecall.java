package com.chinatelecom.gs.engine.kms.flow.search.component;

import com.chinatelecom.ai.search.client.AiSearchClient;
import com.chinatelecom.ai.search.json.JsonData;
import com.chinatelecom.ai.search.query_dsl.ExistsQuery;
import com.chinatelecom.ai.search.query_dsl.FieldValue;
import com.chinatelecom.ai.search.query_dsl.Query;
import com.chinatelecom.ai.search.resp.SearchResponse;
import com.chinatelecom.gs.engine.common.config.property.GsGlobalConfig;
import com.chinatelecom.gs.engine.common.constants.Constants;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.context.RequestInfo;
import com.chinatelecom.gs.engine.common.dto.base.BaseCodeDTO;
import com.chinatelecom.gs.engine.common.utils.JsonUtils;
import com.chinatelecom.gs.engine.common.utils.UserInfoUtils;
import com.chinatelecom.gs.engine.kms.convert.common.CommonConverter;
import com.chinatelecom.gs.engine.kms.dto.KnowledgeBaseDTO;
import com.chinatelecom.gs.engine.kms.flow.BaseNodeComponent;
import com.chinatelecom.gs.engine.kms.flow.search.dto.KsSearchResult;
import com.chinatelecom.gs.engine.kms.flow.search.dto.SearchRecallWrapper;
import com.chinatelecom.gs.engine.kms.repository.KnowledgeBaseRepository;
import com.chinatelecom.gs.engine.kms.repository.TagAppRepository;
import com.chinatelecom.gs.engine.kms.sdk.enums.*;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.base.KnowledgeBaseQueryParam;
import com.chinatelecom.gs.engine.kms.sdk.vo.search.SearchParam;
import com.chinatelecom.gs.engine.kms.sdk.vo.search.SearchParam.TagFilter;
import com.chinatelecom.gs.engine.kms.search.IndexBuild;
import com.chinatelecom.gs.engine.kms.search.model.BaseIndexData;
import com.chinatelecom.gs.engine.kms.service.CatalogService;
import com.chinatelecom.gs.engine.kms.service.KnowledgeBaseService;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

/**
 * @Author: gaoxianjun
 * @CreateTime: 2024-12-31
 * @Description:
 * @Version: 1.0
 */
@Slf4j
public abstract class BaseRecall extends BaseNodeComponent {

    @Resource
    @Lazy
    protected AiSearchClient aiSearchClient;
    @Resource
    protected KnowledgeBaseRepository knowledgeBaseRepository;

    @Resource
    @Qualifier("searchFlowPoolExecutor")
    protected ExecutorService searchFlowPoolExecutor;
    @Resource
    protected GsGlobalConfig gsGlobalConfig;

    @Resource
    protected KnowledgeBaseService knowledgeBaseService;
    @Resource
    private CatalogService catalogService;
    @Resource
    private TagAppRepository tagAppRepository;

    protected Map<String, KnowledgeBaseDTO> findSearchKnowledgeBaseMap(SearchParam.Filter filter, SearchRange range) {
        List<SearchParam.KnowledgeFilter> knowledgeFilters = filter.getKnowledgeFilters();

        Collection<String> codes = null;
        if (CollectionUtils.isNotEmpty(knowledgeFilters)) {
            codes = knowledgeFilters.stream().map(SearchParam.KnowledgeFilter::getKnowledgeBaseCode).collect(Collectors.toList());
        }

        KnowledgeBaseQueryParam param = new KnowledgeBaseQueryParam();
        if (CollectionUtils.isNotEmpty(codes)) {
            param.setKnowledgeBaseCodes(new ArrayList<>(codes));
        }
        param.setCheckRole(filter.isCheckRole());
        param.setSourceSystem(filter.getSourceSystem());
        if (SearchRange.createdByMe == range) {
            param.setCreatedByMe(true);
        }
        List<KnowledgeBaseDTO> knowledgeBaseDTOS = knowledgeBaseService.queryAllAuthedKB(param);
        // todo 增加一些过滤方法以及最大数量限制
        Map<String, KnowledgeBaseDTO> result = CollectionUtils.emptyIfNull(knowledgeBaseDTOS).stream().collect(Collectors.toMap(BaseCodeDTO::getCode, it -> it, (v1, v2) -> v1));
        log.info("过滤知识库查询列表：{}", JsonUtils.toJsonString(result.keySet()));
        return result;
    }

    protected IndexType getKnowledgeIndexType(KnowledgeBaseDTO knowledgeBaseDTO) {
        IndexType type;
        if (knowledgeBaseDTO.getType() == KnowledgeBaseType.FILE) {
            type = IndexType.doc;
        } else if (knowledgeBaseDTO.getType() == KnowledgeBaseType.EXTERNAL) {
            type = IndexType.external;
        } else {
            type = IndexType.faq;
        }
        return type;
    }

    protected List<Query> buildMustFilters(SearchParam.KnowledgeFilter knowledgeFilter, SearchParam searchParam) {
        SearchParam.Filter filter = searchParam.getFilter();
        List<Query> mustList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(knowledgeFilter.getCatalogCodes())) {
            // 分组过滤
            // 增加查询所有子分组
            List<String> catalogCodes = knowledgeFilter.getCatalogCodes();
            Set<String> allCatalogCodes = new HashSet<>();
            for (String catalogCode : catalogCodes) {
                Set<String> codes = catalogService.findAllCatalogTitleCodes(catalogCode,knowledgeFilter.getKnowledgeBaseCode());
                allCatalogCodes.addAll(codes);
            }
            if (CollectionUtils.isNotEmpty(allCatalogCodes)) {
                Query catalogQuery = buildQuery(new ArrayList<>(allCatalogCodes), IndexBuild.PARENT_CATALOG_CODE);
                mustList.add(catalogQuery);
            }
        }
        if (CollectionUtils.isNotEmpty(knowledgeFilter.getKnowledgeCodes())) {
            // 知识点过滤
            Query knowledgeQuery = buildQuery(knowledgeFilter.getKnowledgeCodes(), IndexBuild.KNOWLEDGE_CODE);
            mustList.add(knowledgeQuery);
        }

        // 知识库过滤
        Query knowledgeBaseQuery = buildQuery(ImmutableList.of(knowledgeFilter.getKnowledgeBaseCode()), IndexBuild.KNOWLEDGE_BASE_CODE);
        mustList.add(knowledgeBaseQuery);
        //todo 兼容之前的TagCodes过滤 后续版本去除
        if (CollectionUtils.isNotEmpty(filter.getTagCodes())) {
            //标签过滤
            Query tagQuery = buildQuery(filter.getTagCodes(), IndexBuild.TAG);
            mustList.add(tagQuery);
        } else if (filter != null && filter.getTagFilter() != null) {
            //标签过滤
            Query tagQuery = buildBoolQuery(filter.getTagFilter(), IndexBuild.TAG);
            mustList.add(tagQuery);
        }

        //时间过滤
        List<SearchParam.TimeFilter> timeFilters = filter.getTimeFilters();
        if(CollectionUtils.isNotEmpty(timeFilters)) {
            for (SearchParam.TimeFilter timeFilter : timeFilters) {
                if (timeFilter.getTimeFilterType() == null) {
                    continue;
                }
                String field = timeFilter.getTimeFilterType() == SearchParam.TimeFilterType.updateTime ? IndexBuild.UPDATE_TIME : IndexBuild.PUBLISH_TIME;
                Query timeQuery = Query.of(q -> q.range(r -> r.field(field)
                        .gte(timeFilter.getStartTime() != null ? JsonData.fromJson(timeFilter.getStartTime().toString()) : null)
                        .lte(timeFilter.getEndTime() != null ? JsonData.fromJson(timeFilter.getEndTime().toString()) : null)));
                mustList.add(timeQuery);
            }
        }

        if (filter.isOnlyOnState()) {
            mustList.add(term(IndexBuild.KNOWLEDGE_ON, true));
        }

        if (searchParam.getOrderBy() != null && searchParam.getOrderBy() == SearchOrderType.P_TIME_DESC) {
            mustList.add(Query.of(q -> q.exists(ExistsQuery.of(t -> t.field(IndexBuild.PUBLISH_TIME)))));
        }

        // 添加文件类型过滤
        if (CollectionUtils.isNotEmpty(filter.getFileTypeFilters())) {
            List<String> fileTypeStrings = filter.getFileTypeFilters().stream()
                    .map(Enum::name)
                    .collect(Collectors.toList());
            mustList.add(buildQuery(fileTypeStrings, IndexBuild.TYPE));
        }

        return mustList;
    }

    protected List<Query> buildMustNotFilters(SearchParam.KnowledgeFilter knowledgeFilter, SearchParam searchParam) {
        List<Query> mustNotList = Lists.newArrayList();

        boolean supportMultiModelRecall = UserInfoUtils.checkSupportMultiModelRecall();
        if (!supportMultiModelRecall) {
            log.info("当前查询不具备多模态权限，限制多模态文件的查询，query:{}", searchParam.getQuery());
            // 限制文件类型
            List<String> fileTypeStrings = KnowledgeType.MULTIMODAL_TYPE.stream().map(Enum::name).collect(Collectors.toList());
            mustNotList.add(buildQuery(fileTypeStrings, IndexBuild.TYPE));
            // 限制内容分割处理类型
            List<String> splitIndexTypeList = SplitIndexType.MULTIMODAL_TYPE.stream().map(Enum::name).collect(Collectors.toList());
            mustNotList.add(buildQuery(splitIndexTypeList, IndexBuild.SPLIT_INDEX_TYPE));
        }

        //过滤掉删除未发布状态
        Query publishStatusQuery = Query.of(q -> q.term(t -> t.field(IndexBuild.PUBLISH_STATUS).value(PublishStatus.DELETE_UNPUBLISH.name())));
        mustNotList.add(publishStatusQuery);

        return mustNotList;
    }

    protected Query term(String field, boolean value) {
        return Query.of(q -> q.term(t -> t.field(field).value(value)));
    }

    protected Query buildQuery(List<String> filterCodes, String fieldName) {
        List<FieldValue> values = filterCodes.stream().map(FieldValue::of).collect(Collectors.toList());
        return Query.of(q -> q.terms(t -> t.field(fieldName).terms(f -> f.value(values))));
    }

    private Query buildBoolQuery(TagFilter tagFilter, String fieldName) {
        if (tagFilter == null) {
            return null;
        }

        // 如果未传condition，默认AND
        final CondOperatorEnum condition = Optional.ofNullable(tagFilter.getCondition())
                .orElse(CondOperatorEnum.AND);
        List<Query> subQueries = new ArrayList<>();

        // 处理当前层级的tags
        processCurrentTags(tagFilter, fieldName, condition, subQueries);

        // 递归处理子查询
        processSubQueries(tagFilter, fieldName, subQueries);

        return buildFinalBoolQuery(subQueries, condition);
    }

    // 处理当前层级的tags逻辑
    private void processCurrentTags(TagFilter tagFilter, String fieldName,
                                    CondOperatorEnum condition, List<Query> subQueries) {
        if (CollectionUtils.isEmpty(tagFilter.getTags())) {
            return;
        }
        //获取下级标签
        Set<String> tagCodes = tagAppRepository.fetchAllSubTag(tagFilter.getTags());
        tagCodes.addAll(tagFilter.getTags());
        if (CollectionUtils.isEmpty(tagCodes)) {
            return;
        }

        if (condition == CondOperatorEnum.AND) {
            // AND条件拆分为多个term查询
            tagCodes.stream()
                    .map(tag -> buildTermQuery(fieldName, tag))
                    .forEach(subQueries::add);
        } else {
            // OR条件合并为单个terms查询
            subQueries.add(buildTermsQuery(fieldName, tagCodes));
        }
    }

    // 递归处理子查询
    private void processSubQueries(TagFilter tagFilter, String fieldName, List<Query> subQueries) {
        if (CollectionUtils.isEmpty(tagFilter.getSubQueries())) {
            return;
        }

        tagFilter.getSubQueries().stream()
                .map(subFilter -> buildBoolQuery(subFilter, fieldName))
                .filter(Objects::nonNull)
                .forEach(subQueries::add);
    }

    // 构建最终bool查询
    private Query buildFinalBoolQuery(List<Query> subQueries, CondOperatorEnum condition) {
        if (CollectionUtils.isEmpty(subQueries)) {
            return null;
        }

        return Query.of(q -> q.bool(b ->
                condition == CondOperatorEnum.OR
                        ? b.should(subQueries)
                        : b.must(subQueries)
        ));
    }

    // 辅助方法：构建term查询
    private Query buildTermQuery(String fieldName, String value) {
        return Query.of(q -> q.term(t ->
                t.field(fieldName).value(value)
        ));
    }

    // 辅助方法：构建terms查询
    private Query buildTermsQuery(String fieldName, Collection<String> values) {
        return Query.of(q -> q.terms(t ->
                t.field(fieldName)
                        .terms(ts -> ts.value(
                                values.stream()
                                        .map(FieldValue::of)
                                        .collect(Collectors.toList())
                        ))
        ));
    }

    /**
     * 转化召回结果
     * @param ksSearchResults
     * @return
     */
    protected List<SearchRecallWrapper<BaseIndexData>> buildSearchRecallByKSR(List<KsSearchResult> ksSearchResults) {
        List<SearchRecallWrapper<BaseIndexData>> retrieveItems = new ArrayList<>();
        Set<String> existDocIds = new HashSet<>();
        if (CollectionUtils.isNotEmpty(ksSearchResults)) {
            for (KsSearchResult ksSearchResult : ksSearchResults) {
                SearchResponse<BaseIndexData> baseIndex = ksSearchResult.getSearchResponse();
                SearchResponse.SearchResult<BaseIndexData> baseIndexData = baseIndex.getData();
                if (baseIndexData != null && CollectionUtils.isNotEmpty(baseIndexData.getItems())) {
                    List<SearchRecallWrapper<BaseIndexData>> wrapperList = baseIndexData.getItems().stream()
                            .filter(t -> !existDocIds.contains(t.getId()))
                            .map(t -> {
                                SearchRecallWrapper<BaseIndexData> searchRecallWrapper = CommonConverter.INSTANCE.convert(t);
                                searchRecallWrapper.setRecallType(ksSearchResult.getRecallType());
                                existDocIds.add(t.getId());
                                return searchRecallWrapper;
                            })
                            .collect(Collectors.toList());
                    retrieveItems.addAll(wrapperList);
                }
            }
        }
        return retrieveItems;
    }

    /**
     * 转换所有召回结果
     *
     * @param indexDataResults
     * @return
     */
    protected List<SearchRecallWrapper<BaseIndexData>> buildSearchRecall(List<SearchResponse<BaseIndexData>> indexDataResults) {
        List<SearchRecallWrapper<BaseIndexData>> retrieveItems = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(indexDataResults)) {
            for (SearchResponse<BaseIndexData> baseIndex : indexDataResults) {
                SearchResponse.SearchResult<BaseIndexData> baseIndexData = baseIndex.getData();
                if (baseIndexData != null && CollectionUtils.isNotEmpty(baseIndexData.getItems())) {
                    List<SearchRecallWrapper<BaseIndexData>> recallList = baseIndexData.getItems().stream()
                            .map(CommonConverter.INSTANCE::convert)
                            .collect(Collectors.toList());
                    retrieveItems.addAll(recallList);
                }
            }
        }
        return retrieveItems;
    }

}
