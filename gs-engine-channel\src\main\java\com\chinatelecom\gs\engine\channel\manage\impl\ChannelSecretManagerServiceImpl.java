package com.chinatelecom.gs.engine.channel.manage.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinatelecom.gs.engine.channel.api.converter.VOBeanConvertor;
import com.chinatelecom.gs.engine.channel.common.enums.ChannelTypeEnum;
import com.chinatelecom.gs.engine.channel.dao.po.ChannelApiSecretPO;
import com.chinatelecom.gs.engine.channel.dao.po.YN;
import com.chinatelecom.gs.engine.channel.dao.repository.ChannelApiSecretRepository;
import com.chinatelecom.gs.engine.channel.dao.repository.ChannelInfoRepository;
import com.chinatelecom.gs.engine.channel.manage.ChannelConfigService;
import com.chinatelecom.gs.engine.channel.manage.ChannelSecretManagerService;
import com.chinatelecom.gs.engine.channel.service.dto.ChannelApiSecretDTO;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.context.RequestInfo;
import com.chinatelecom.gs.engine.robot.sdk.config.enums.ApiSecretType;
import com.chinatelecom.gs.engine.robot.sdk.v2.utils.IdGenerator;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.page.PageImpl;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * @Description: TelechatSecretManagerServiceImpl
 * @Author: qingx
 * @Time: 2023/08/29
 * @Version: 1.0
 */
@Slf4j
@Service
public class ChannelSecretManagerServiceImpl implements ChannelSecretManagerService {

    @Autowired
    private ChannelApiSecretRepository channelApiSecretRepository;
    @Autowired
    private ChannelConfigService channelConfigService;
    @Autowired
    private ChannelInfoRepository channelInfoRepository;

    @Override
    public com.chinatelecom.gs.engine.robot.sdk.v2.vo.page.Page<ChannelApiSecretDTO> list(String appId, ApiSecretType apiSecretType, Integer pageNo, Integer pageSize) {
        pageNo = pageNo != null ? pageNo : 1;
        pageSize = pageSize != null ? pageSize : 10;

        LambdaQueryWrapper<ChannelApiSecretPO> condition = Wrappers.lambdaQuery();
        condition.eq(ChannelApiSecretPO::getAppId, appId);
        condition.eq(ChannelApiSecretPO::getSecretType, apiSecretType);
        condition.eq(ChannelApiSecretPO::getYn, YN.YES.getValue());
        condition.orderByDesc(ChannelApiSecretPO::getCreateTime);

        IPage<ChannelApiSecretPO> page = channelApiSecretRepository.page(new Page<>(pageNo, pageSize), condition);
        if (page == null || page.getRecords() == null) {
            log.info("没有查询到密钥");
            return null;
        }

        List<ChannelApiSecretDTO> dtos = page.getRecords().stream().map(secret -> {
            ChannelApiSecretDTO dto;
            BeanUtils.copyProperties(secret, dto = new ChannelApiSecretDTO());
            return dto;
        }).collect(Collectors.toList());

        return PageImpl.of(page.getCurrent(), page.getSize(), page.getTotal(), page.getPages(), dtos);

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean create(String appId, String channelId, String secretName, ApiSecretType apiSecretType) {
        if (!StringUtils.hasLength(channelId)) {
            channelId = channelInfoRepository.addChannelInfo(appId, ChannelTypeEnum.API);
        }
        Assert.hasText(appId, "应用id不能为空");
        Assert.hasText(secretName, "密钥名称不能为空");

//         检查密钥名称重复
        checkNameRepeat(appId, secretName); //新增channelId，密钥名称不做判重

        // 保存密钥
        ChannelApiSecretPO po = new ChannelApiSecretPO();
        RequestInfo requestInfo = RequestContext.get();
        po.setTenantId(requestInfo.getTenantId());
        po.setAppId(appId);
        po.setChannelId(channelId);
        po.setSecretType(apiSecretType);
        po.setSecretId(IdGenerator.id());
        po.setName(secretName);
        po.setSecret(regenerateSecretIfRepeat());
        po.setYn(YN.YES.getValue());
        po.setCreateId(requestInfo.getUserId());
        po.setCreateName(requestInfo.getUserName());
        po.setCreateTime(LocalDateTime.now());
        po.setAppCode(RequestContext.getAppCode());
        po.setSourceSystem(RequestContext.getAppSourceType());
        return channelApiSecretRepository.save(po);
    }

    @Override
    public boolean modify(String appId, String secretId, String secretName) {
        Assert.hasText(appId, "应用id不能为空");
        Assert.hasText(secretId, "密钥id不能为空");

        ChannelApiSecretDTO apiSecret = channelApiSecretRepository.findBySecretId(secretId, appId);
        if (apiSecret == null) {
            throw new BizException("A0059", "修改失败, 密钥不存在。");
        }

        // 修改了名称, 校验名称是否重复
        if (!Objects.equals(secretName, apiSecret.getName())) {
            checkNameRepeat(secretId, secretName);
        }

        RequestInfo requestInfo = RequestContext.get();
        LambdaUpdateWrapper<ChannelApiSecretPO> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(ChannelApiSecretPO::getName, secretName);
        updateWrapper.eq(ChannelApiSecretPO::getId, apiSecret.getId());
        return channelApiSecretRepository.update(updateWrapper);
    }

    @Override
    public boolean remove(String appId, String secretId) {

        Assert.hasText(appId, "应用id不能为空");
        Assert.hasText(secretId, "密钥id不能为空");

        final int querySize = 2;
        List<ChannelApiSecretDTO> apiSecretList = channelApiSecretRepository.channelApiSecretListByAppId(appId, ApiSecretType.API, querySize);
        if (CollectionUtils.isEmpty(apiSecretList) || apiSecretList.size() != querySize) {
            throw new BizException("A0060", "至少保留一个ID及密钥");
        }

        LambdaQueryWrapper<ChannelApiSecretPO> condition = Wrappers.lambdaQuery();
        condition.eq(ChannelApiSecretPO::getAppId, appId);
        condition.eq(ChannelApiSecretPO::getSecretId, secretId);
        condition.eq(ChannelApiSecretPO::getYn, YN.YES.getValue());

        ChannelApiSecretPO entity = channelApiSecretRepository.getOne(condition);

        //删除api密钥，同步删除channelInfo
        String channelId = entity.getChannelId();
        channelInfoRepository.removeByChannelId(channelId);
        return channelApiSecretRepository.removeById(entity);
    }


    @Override
    public ChannelApiSecretDTO getSecretWithSecretId(String secretId) {
        LambdaQueryWrapper<ChannelApiSecretPO> condition = Wrappers.lambdaQuery();
        condition.eq(ChannelApiSecretPO::getSecretId, secretId);
        condition.eq(ChannelApiSecretPO::getYn, YN.YES.getValue());
        ChannelApiSecretPO po = channelApiSecretRepository.getOne(condition);
        return VOBeanConvertor.INSTANCE.convert(po);
    }
    @Override
    public ChannelApiSecretDTO getSecretWithSecret(String secret){
        LambdaQueryWrapper<ChannelApiSecretPO> condition = Wrappers.lambdaQuery();
        condition.eq(ChannelApiSecretPO::getSecret, secret);
        condition.eq(ChannelApiSecretPO::getYn, YN.YES.getValue());
        ChannelApiSecretPO po = channelApiSecretRepository.getOne(condition);
        return VOBeanConvertor.INSTANCE.convert(po);
    }

    @Override
    public ChannelApiSecretDTO getSecretWithChannelId(String channelId) {
        LambdaQueryWrapper<ChannelApiSecretPO> condition = Wrappers.lambdaQuery();
        condition.eq(ChannelApiSecretPO::getChannelId, channelId);
        condition.eq(ChannelApiSecretPO::getYn, YN.YES.getValue());
        ChannelApiSecretPO po = channelApiSecretRepository.getOne(condition);
        return VOBeanConvertor.INSTANCE.convert(po);
    }

    @Override
    public boolean removeSecretWithChannelId(String channelId) {
        LambdaQueryWrapper<ChannelApiSecretPO> condition = Wrappers.lambdaQuery();
        condition.eq(ChannelApiSecretPO::getChannelId, channelId);
        condition.eq(ChannelApiSecretPO::getYn, YN.YES.getValue());

        return channelApiSecretRepository.remove(condition);
    }

    @Override
    public String regenerate(String appId, String secretId) {
        //通过secretId查询到secret，并更新secret
        LambdaQueryWrapper<ChannelApiSecretPO> condition = Wrappers.lambdaQuery();
        condition.eq(ChannelApiSecretPO::getAppId, appId);
        condition.eq(ChannelApiSecretPO::getSecretId, secretId);
        condition.eq(ChannelApiSecretPO::getYn, YN.YES.getValue());
        ChannelApiSecretPO secret = channelApiSecretRepository.getOne(condition);
        if (secret != null) {
            String newSecret = regenerateSecretIfRepeat();
            LambdaUpdateWrapper<ChannelApiSecretPO> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.set(ChannelApiSecretPO::getSecret, newSecret);
            updateWrapper.eq(ChannelApiSecretPO::getId, secret.getId());
            channelApiSecretRepository.update(updateWrapper);
            return newSecret;
        }
        return "";
    }

    @Override
    public boolean isSecretExist(String appId, ApiSecretType apiSecretType) {
        LambdaQueryWrapper<ChannelApiSecretPO> condition = Wrappers.lambdaQuery();
        condition.eq(ChannelApiSecretPO::getAppId, appId);
        condition.eq(ChannelApiSecretPO::getSecretType, apiSecretType);
        condition.eq(ChannelApiSecretPO::getYn, YN.YES.getValue());
        condition.orderByDesc(ChannelApiSecretPO::getCreateTime);

        List<ChannelApiSecretPO> list = channelApiSecretRepository.list(condition);

        if (org.apache.commons.collections4.CollectionUtils.isEmpty(list)) {
            log.info("appId:{}, 没有查询到密钥", appId);
            return false;
        }
        return true;
    }

    private String regenerateSecretIfRepeat() {
        // 密钥生成, 重试3次
        int retries = 3;
        while (retries > 0) {
            try {
                // 检查秘钥是否重复
                String secret;
                checkSecretRepeat(secret = generateSecret());
                return secret;
            } catch (RuntimeException ex) {
                log.error("密钥重复, 重试.");
                retries--;
            }
        }
        throw new BizException("A0061", "密钥生成失败, 为空");
    }

    private String generateSecret() {
        return UUID.randomUUID().toString().replaceAll("-", "").toUpperCase();
    }

    public void checkNameRepeat(String appId, String secretName) {
        LambdaQueryWrapper<ChannelApiSecretPO> condition = Wrappers.lambdaQuery();
        condition.eq(ChannelApiSecretPO::getAppId, appId);
        condition.eq(ChannelApiSecretPO::getName, secretName);
        condition.eq(ChannelApiSecretPO::getYn, YN.YES.getValue());
        List<ChannelApiSecretPO> secrets = channelApiSecretRepository.list(condition);
        if (!CollectionUtils.isEmpty(secrets)) {
            log.error("密钥名称重复, 请重新修改密钥名称。");
            throw new BizException("A0062", "密钥名称重复, 请重新修改密钥名称。");
        }
    }

    private void checkSecretRepeat(String secret) {
        LambdaQueryWrapper<ChannelApiSecretPO> condition = Wrappers.lambdaQuery();
        condition.eq(ChannelApiSecretPO::getSecret, secret);
        condition.eq(ChannelApiSecretPO::getYn, YN.YES.getValue());
        List<ChannelApiSecretPO> secrets = channelApiSecretRepository.list(condition);
        if (!CollectionUtils.isEmpty(secrets)) {
            log.error("密钥内容重复, 请重新修改密钥内容。");
            throw new BizException("A0062", "密钥内容重复, 请重新修改密钥内容。");
        }
    }


}
