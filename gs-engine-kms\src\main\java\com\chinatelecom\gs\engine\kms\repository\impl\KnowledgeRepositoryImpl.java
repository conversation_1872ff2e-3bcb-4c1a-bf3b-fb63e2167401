package com.chinatelecom.gs.engine.kms.repository.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.chinatelecom.gs.engine.common.infra.base.impl.BaseByCodeRepositoryImpl;
import com.chinatelecom.gs.engine.common.utils.BizAssert;
import com.chinatelecom.gs.engine.kms.convert.dto.KnowledgeDtoConverter;
import com.chinatelecom.gs.engine.kms.dto.KnwlPublishStatisticsDTO;
import com.chinatelecom.gs.engine.kms.dto.knowledge.KnowledgeParseResult;
import com.chinatelecom.gs.engine.kms.infra.mapper.KnowledgeMapper;
import com.chinatelecom.gs.engine.kms.infra.po.KnowledgePO;
import com.chinatelecom.gs.engine.kms.model.dto.CatalogDTO;
import com.chinatelecom.gs.engine.kms.model.dto.KnowledgeDTO;
import com.chinatelecom.gs.engine.kms.model.dto.KnowledgeDetailDTO;
import com.chinatelecom.gs.engine.kms.repository.CatalogRepository;
import com.chinatelecom.gs.engine.kms.repository.KnowledgeBaseRepository;
import com.chinatelecom.gs.engine.kms.repository.KnowledgeRepository;
import com.chinatelecom.gs.engine.kms.sdk.enums.KnowledgeStatus;
import com.chinatelecom.gs.engine.kms.sdk.enums.PublishStatus;
import com.chinatelecom.gs.engine.kms.sdk.vo.common.CodeName;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.base.KBPublishStatisticsVO;
import com.google.common.collect.ImmutableList;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 知识表 Repository接口实现
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-06
 */


@Service
@Slf4j
public class KnowledgeRepositoryImpl extends BaseByCodeRepositoryImpl<KnowledgeMapper, KnowledgePO, KnowledgeDTO> implements KnowledgeRepository {

    @Resource
    private CatalogRepository catalogRepository;

    @Resource
    private KnowledgeMapper knowledgeMapper;

    @Resource
    private KnowledgeBaseRepository knowledgeBaseRepository;

    @Override
    public KnowledgeDTO convertToDto(KnowledgePO source) {
        return KnowledgeDtoConverter.INSTANCE.convert(source);
    }

    @Override
    public KnowledgePO convertToPo(KnowledgeDTO source) {
        return KnowledgeDtoConverter.INSTANCE.convert(source);
    }


    @Override
    public void updateViewFileKey(String knowledgeCode, String viewFileUrl, Integer progress) {
        LambdaUpdateWrapper<KnowledgePO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(KnowledgePO::getViewFileKey, viewFileUrl)
                .set(KnowledgePO::getProgress, progress)
                .eq(KnowledgePO::getCode, knowledgeCode);
        this.getBaseMapper().update(wrapper);
    }

    @Override
    public void updateKnowParseResult(String knowledgeCode, KnowledgeParseResult knowledgeParseResult) {
        if (ObjectUtils.allNull(knowledgeParseResult.getStatus(), knowledgeParseResult.getIndexingStatus(),
                knowledgeParseResult.getImageIndexingStatus(), knowledgeParseResult.getKnowledgeTip())) {
            return;
        }
        String knowledgeTip = knowledgeParseResult.getKnowledgeTip();
        if (StringUtils.isNotBlank(knowledgeTip) && knowledgeTip.length() > 120) {
            knowledgeTip = StringUtils.substring(knowledgeTip, 0, 120);
        }
        String devMsg = knowledgeParseResult.getDevMessage();
        if (StringUtils.isNotBlank(devMsg) && devMsg.length() > 2048) {
            devMsg = StringUtils.substring(devMsg, 0, 2048);
        }

        LambdaUpdateWrapper<KnowledgePO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(Objects.nonNull(knowledgeParseResult.getStatus()), KnowledgePO::getStatus, knowledgeParseResult.getStatus())
                .set(Objects.nonNull(knowledgeParseResult.getIndexingStatus()), KnowledgePO::getIndexingStatus, knowledgeParseResult.getIndexingStatus())
                .set(Objects.nonNull(knowledgeParseResult.getImageIndexingStatus()), KnowledgePO::getImageIndexingStatus, knowledgeParseResult.getImageIndexingStatus())
                .set(Objects.nonNull(knowledgeTip), KnowledgePO::getKnowledgeTip, knowledgeTip)
                .set(Objects.nonNull(devMsg), KnowledgePO::getDevMessage, devMsg)
                .eq(KnowledgePO::getCode, knowledgeCode);
        this.getBaseMapper().update(wrapper);
    }

    @Override
    public void updateKnowledgeTaskInfo(String knowledgeCode, String taskId, String imageTaskId, Integer progress) {
        if (ObjectUtils.allNull(taskId, imageTaskId, progress)) {
            return;
        }

        LambdaUpdateWrapper<KnowledgePO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(StringUtils.isNotBlank(taskId), KnowledgePO::getTaskId, taskId)
                .set(StringUtils.isNotBlank(imageTaskId), KnowledgePO::getImageTaskId, imageTaskId)
                .set(progress != null, KnowledgePO::getProgress, progress)
                .eq(KnowledgePO::getCode, knowledgeCode);
        this.getBaseMapper().update(wrapper);
    }

    @Override
    public KnowledgeDetailDTO selectKnowledgeDetail(String knowledgeCode) {
        KnowledgeDTO dto = selectByCode(knowledgeCode);
        KnowledgeDetailDTO result = KnowledgeDtoConverter.INSTANCE.convertDetail(dto);
        // 设置其他属性值
        CatalogDTO catalogDTO = catalogRepository.findByKnowledgeCode(knowledgeCode);
        BizAssert.notNull(catalogDTO, "AA014", "目录数据不存在");
        result.setParentCatalogCode(catalogDTO.getParentCode());
        return result;
    }

    @Override
    public List<KnowledgeDTO> findByCodes(Collection<String> codes) {
        if (codes == null || codes.isEmpty()) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<KnowledgePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(KnowledgePO::getCode, codes);
        List<KnowledgePO> knowledgePOList = getBaseMapper().selectList(queryWrapper);
        return convertToDto(knowledgePOList);
    }

    @Override
    public void updatePublishStatus(Collection<String> knowledgeCodes, PublishStatus publishStatus) {
        LambdaUpdateWrapper<KnowledgePO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(KnowledgePO::getPublishStatus, publishStatus)
                .set(PublishStatus.PUBLISHED == publishStatus, KnowledgePO::getPublishTime, LocalDateTime.now())
                .setSql(PublishStatus.PUBLISHED == publishStatus, "publish_count = publish_count + 1")
                .in(KnowledgePO::getCode, knowledgeCodes);
        this.getBaseMapper().update(wrapper);
    }

    @Override
    public boolean updatePublishStatus2Update(Collection<String> knowledgeCodes) {
        LambdaUpdateWrapper<KnowledgePO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(KnowledgePO::getPublishStatus, PublishStatus.UPDATE_UNPUBLISH)
                .in(KnowledgePO::getCode, knowledgeCodes)
                .in(KnowledgePO::getPublishStatus, PublishStatus.ALLOW_UPDATE);
        return SqlHelper.retBool(this.getBaseMapper().update(wrapper));
    }

    @Override
    public boolean updatePublishStatus2Unpublished(Collection<String> knowledgeCodes) {
        LambdaUpdateWrapper<KnowledgePO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(KnowledgePO::getPublishStatus, PublishStatus.UNPUBLISHED)
                .in(KnowledgePO::getCode, knowledgeCodes)
                .in(KnowledgePO::getPublishStatus, PublishStatus.ALLOW_UNPUBLISHED);
        return SqlHelper.retBool(this.getBaseMapper().update(wrapper));
    }

    @Override
    public boolean updateOn(Collection<String> knowledgeCodes, boolean on) {
        LambdaUpdateWrapper<KnowledgePO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(KnowledgePO::getOn, on)
                .in(KnowledgePO::getCode, knowledgeCodes);
        return SqlHelper.retBool(this.getBaseMapper().update(wrapper));
    }

    @Override
    public List<KnowledgeDTO> selectAllowPublish(String knowledgeBaseCode, Collection<String> knowledgeCode) {
        LambdaQueryWrapper<KnowledgePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(KnowledgePO::getKnowledgeBaseCode, knowledgeBaseCode)
                .in(CollectionUtils.isNotEmpty(knowledgeCode), KnowledgePO::getCode, knowledgeCode)
                .eq(KnowledgePO::getStatus, KnowledgeStatus.success)
                .in(KnowledgePO::getPublishStatus, PublishStatus.ALLOW_PUBLISH);
        List<KnowledgePO> knowledgePOList = getBaseMapper().selectList(queryWrapper);
        return convertToDto(knowledgePOList);
    }

    @Override
    public List<KnowledgeDTO> selectAllowPublishFaq(String knowledgeBaseCode, Collection<String> knowledgeCode) {
        LambdaQueryWrapper<KnowledgePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(KnowledgePO::getKnowledgeBaseCode, knowledgeBaseCode)
                .in(KnowledgePO::getCode, knowledgeCode)
                .eq(KnowledgePO::getStatus, KnowledgeStatus.success);
        List<KnowledgePO> knowledgePOList = getBaseMapper().selectList(queryWrapper);
        return convertToDto(knowledgePOList);
    }

    @Override
    public List<String> filterCodes(String knowledgeBaseCode, List<String> statusList, List<String> publicStatusList) {
        LambdaQueryWrapper<KnowledgePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(KnowledgePO::getKnowledgeBaseCode, knowledgeBaseCode);
        if (statusList != null && !statusList.isEmpty()) {
            queryWrapper.in(KnowledgePO::getStatus, statusList);
        }
        if (publicStatusList != null && !publicStatusList.isEmpty()) {
            queryWrapper.in(KnowledgePO::getPublishStatus, publicStatusList);
        }
        List<KnowledgePO> knowledgePOList = getBaseMapper().selectList(queryWrapper);
        return getBaseMapper().selectList(queryWrapper).stream().map(KnowledgePO::getCode).collect(Collectors.toList());
    }

    @Override
    public void cancelPublish(String auditCode, List<String> knowledgeCodes) {
        knowledgeMapper.cancelPublish(auditCode, knowledgeCodes);
    }

    @Override
    public List<KnowledgeDTO> findCopyInfoByCodes(String knowledgeBaseCode, List<String> knowledgeCodes) {
        LambdaQueryWrapper<KnowledgePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(KnowledgePO::getCode, KnowledgePO::getName, KnowledgePO::getOriginalFileKey)
                .eq(KnowledgePO::getKnowledgeBaseCode, knowledgeBaseCode)
                .in(KnowledgePO::getCode, knowledgeCodes);
        List<KnowledgePO> knowledgePOList = getBaseMapper().selectList(queryWrapper);
        return convertToDto(knowledgePOList);
    }

    @Override
    public void updateKnowledgeStatusTimeout(LocalDateTime localDateTime) {
        LambdaUpdateWrapper<KnowledgePO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(KnowledgePO::getStatus, KnowledgeStatus.fail)
                .set(KnowledgePO::getIndexingStatus, KnowledgeStatus.fail)
                .set(KnowledgePO::getImageIndexingStatus, KnowledgeStatus.fail)
                .in(KnowledgePO::getIndexingStatus, ImmutableList.of(KnowledgeStatus.pending, KnowledgeStatus.queuing))
                .lt(KnowledgePO::getUpdateTime, localDateTime)
                .last(getLimitSql(1000));
        SqlHelper.retBool(this.getBaseMapper().update(wrapper));
    }

    @Override
    public List<KnowledgeDTO> queryParsePending(LocalDateTime localDateTime) {
        LambdaQueryWrapper<KnowledgePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .or(p -> p.in(KnowledgePO::getIndexingStatus, ImmutableList.of(KnowledgeStatus.pending, KnowledgeStatus.queuing))
                        .in(KnowledgePO::getImageIndexingStatus, ImmutableList.of(KnowledgeStatus.pending, KnowledgeStatus.queuing))
                )
                .gt(KnowledgePO::getUpdateTime, localDateTime)
                .last(getLimitSql(1000));
        List<KnowledgePO> knowledgePOList = getBaseMapper().selectList(queryWrapper);
        return convertToDto(knowledgePOList);
    }

    @Override
    public List<CodeName> findNameByCodes(List<String> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return Collections.EMPTY_LIST;
        }
        LambdaQueryWrapper<KnowledgePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(KnowledgePO::getCode, KnowledgePO::getName).in(KnowledgePO::getCode, codes);
        List<KnowledgePO> poList = getBaseMapper().selectList(queryWrapper);
        return poList.stream().map(po -> CodeName.builder().code(po.getCode()).name(po.getName()).build()).collect(Collectors.toList());
    }

    @Override
    public Map<String, KBPublishStatisticsVO> countPublishInfo(Collection<String> knowledgeBaseCodes) {
        if (CollectionUtils.isEmpty(knowledgeBaseCodes)) {
            return MapUtils.EMPTY_SORTED_MAP;
        }

        List<KnwlPublishStatisticsDTO> knwlList = knowledgeMapper.groupByPublishStatus(knowledgeBaseCodes);

        Map<String, KBPublishStatisticsVO> result = new HashMap<>();
        if (CollectionUtils.isNotEmpty(knwlList)) {
            Map<String, List<KnwlPublishStatisticsDTO>> publishGroupMap = knwlList.stream().collect(
                    Collectors.groupingBy(KnwlPublishStatisticsDTO::getKnowledgeBaseCode));

            publishGroupMap.entrySet().forEach(entry -> {
                String knowledgeBaseCode = entry.getKey();
                List<KnwlPublishStatisticsDTO> publishList = entry.getValue();
                Long publishedCount = 0L;
                Long unpublishedCount = 0L;
                for (KnwlPublishStatisticsDTO dto : publishList) {
                    if (StringUtils.equals(dto.getPublishStatus(), PublishStatus.PUBLISHED.name())) {
                        publishedCount += dto.getCount();
                    } else {
                        unpublishedCount += dto.getCount();
                    }
                }
                KBPublishStatisticsVO vo = new KBPublishStatisticsVO();
                vo.setPublishedCount(publishedCount);
                vo.setUnpublishedCount(unpublishedCount);
                vo.setKnowledgeBaseCode(knowledgeBaseCode);
                result.put(knowledgeBaseCode, vo);
            });
            List<CodeName> nameByCodes = knowledgeBaseRepository.findNameByCodes(knwlList.stream().map(KnwlPublishStatisticsDTO::getKnowledgeBaseCode).collect(Collectors.toList()));
            if (CollectionUtils.isNotEmpty(nameByCodes)) {
                nameByCodes.forEach(entity -> {
                    KBPublishStatisticsVO vo = result.get(entity.getCode());
                    if (vo != null) {
                        vo.setName(entity.getName());
                    }
                });
            }

        }
        return result;
    }

    @Override
    public List<KnowledgeDTO> queryNeedRefreshFileLength(Long startId, Integer scrollSize) {
        LambdaQueryWrapper<KnowledgePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(KnowledgePO::getId, KnowledgePO::getOriginalFileKey, KnowledgePO::getCode, KnowledgePO::getKnowledgeBaseCode)
                .eq(KnowledgePO::getFileLength, 0)
                .eq(KnowledgePO::getStatus, KnowledgeStatus.success)
                .gt(KnowledgePO::getId, startId)
                .last(getLimitSql(scrollSize));
        List<KnowledgePO> knowledgePOList = getBaseMapper().selectList(queryWrapper);
        return convertToDto(knowledgePOList);
    }

    @Override
    public void updateFileLength(Long id, long fileLength) {
        LambdaUpdateWrapper<KnowledgePO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(KnowledgePO::getFileLength, fileLength)
                .eq(KnowledgePO::getId, id)
                .last(getOnlyOneSql());
        SqlHelper.retBool(this.getBaseMapper().update(wrapper));
    }

    @Override
    public void updateUpdateTime(String knowledgeCode) {
        LambdaUpdateWrapper<KnowledgePO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(KnowledgePO::getCode, knowledgeCode)
                .eq(KnowledgePO::getCode, knowledgeCode)
                .last(getOnlyOneSql());
        this.getBaseMapper().update(wrapper);
    }

}

