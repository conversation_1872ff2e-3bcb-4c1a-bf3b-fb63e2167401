package com.chinatelecom.gs.engine.common.utils;

import com.chinatelecom.cloud.common.pojo.BaseResult;
import com.chinatelecom.cloud.platform.client.rpc.Permission;
import com.chinatelecom.cloud.platform.client.rpc.Team;
import com.chinatelecom.cloud.platform.client.rpc.UserInfo;
import com.chinatelecom.cloud.platform.client.util.AuthUtils;
import com.chinatelecom.gs.engine.common.constants.Constants;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.context.RequestInfo;
import com.chinatelecom.gs.engine.common.context.TeamInfo;
import com.chinatelecom.gs.engine.common.platform.AppOwnerRequest;
import com.chinatelecom.gs.engine.common.platform.GovernmentAuthClient;
import com.chinatelecom.gs.engine.common.platform.KsAuthClient;
import com.chinatelecom.gs.engine.common.platform.PlatformAuthService;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import com.chinatelelcom.gs.engine.sdk.common.enums.AppSourceType;
import com.chinatelelcom.gs.engine.sdk.common.utils.SpringContextUtils;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024年12月30日
 */
@Slf4j
public class UserInfoUtils {

    private static final Cache<String, List<TeamInfo>> CACHE = Caffeine.newBuilder().expireAfterWrite(10, TimeUnit.SECONDS).maximumSize(1000).build();

    private static final Cache<String, UserInfo> USER_CACHE = Caffeine.newBuilder().expireAfterWrite(60, TimeUnit.SECONDS).maximumSize(1000).build();

    private static final Cache<String, Boolean> USER_IS_ADMIN_CACHE = Caffeine.newBuilder().expireAfterWrite(60, TimeUnit.SECONDS).maximumSize(1000).build();


    /**
     * 获取用户团队信息
     *
     * @return
     */
    public static List<TeamInfo> getUserTeam() {
        String key = StringUtils.join(RequestContext.getUserId(), "_", RequestContext.getTenantId());

        List<TeamInfo> result = CACHE.getIfPresent(key);
        if (result == null) {
            List<TeamInfo> teamList = Lists.newArrayList();
            try {
                BaseResult<List<Team>> baseResult = AuthUtils.getTeamByUser(RequestContext.getUserId(), RequestContext.getTenantId());
                if (baseResult != null && baseResult.ifSuccess()) {
                    List<Team> teams = baseResult.getData();
                    if (CollectionUtils.isNotEmpty(teams)) {
                        teamList.addAll(teams.stream().map(team -> TeamInfo.builder()
                                .teamCode(team.getTeamCode()).name(team.getName()).build()).collect(Collectors.toList()));
                        CACHE.put(key, teamList);
                    }
                } else {
                    log.warn("查询用户团队信息失败，result:{}", JsonUtils.toJsonString(baseResult));
                }
            } catch (Exception e) {
                log.error("查询用户团队信息异常", e);
//            throw new BizException(e, "00023", "查询用户团队信息异常");
            }
            return teamList;
        } else {
            return result;
        }
    }

    public static UserInfo getUserInfo(String tenantId, String userId) {
        String key = StringUtils.join(userId, "_", tenantId);
        UserInfo result = USER_CACHE.getIfPresent(key);
        if (result == null) {
            BaseResult<UserInfo> baseResult = AuthUtils.getUserById(tenantId, userId);
            if (baseResult != null && baseResult.ifSuccess() && baseResult.getData() != null) {
                result = baseResult.getData();
                USER_CACHE.put(key, result);
            }
        }
        return result;
    }

    public static boolean isAdmin(String tenantId, String userId, AppSourceType appSourceType) {
        String key = StringUtils.join(userId, "_", tenantId, "_", appSourceType != null ? appSourceType.name() : StringUtils.EMPTY);
        Boolean result = USER_IS_ADMIN_CACHE.getIfPresent(key);
        if (result == null) {
            try {
                result = checkIsAdmin(appSourceType, userId, tenantId);
                USER_IS_ADMIN_CACHE.put(key, result);
            } catch (Exception e) {
                log.error("查询用户是否管理员失败, tenantId: {}， userId:{}", tenantId, userId);
                result = false;
            }
        }
        return result;
    }

    private static boolean checkIsAdmin(AppSourceType appSourceType, String userId, String corpCode) {
        log.info("查询账号是否为管理员 source:{},userId:{},corpCode:{}", appSourceType, userId, corpCode);
        boolean result = false;
        try {
            if (AppSourceType.KS.equals(appSourceType)) {
                Result<Boolean> appOwner = SpringContextUtils.getBean(KsAuthClient.class).isAppOwner();
                result = appOwner.isSuccess() && appOwner.getData();
            } else if (AppSourceType.GOVERNMENT.equals(appSourceType)) {
                AppOwnerRequest appOwnerRequest = new AppOwnerRequest();
                appOwnerRequest.setCorpCode(corpCode);
                appOwnerRequest.setUserId(userId);
                Result<Boolean> appOwner = SpringContextUtils.getBean(GovernmentAuthClient.class).isAppOwner(appOwnerRequest);
                result = appOwner.isSuccess() && appOwner.getData();
            } else {
                BaseResult<Boolean> appOwner = AuthUtils.isAppOwner(userId, corpCode);
                result = appOwner != null && appOwner.ifSuccess() && appOwner.getData() != null && appOwner.getData();
            }
        } catch (Exception e) {
            log.error("向子账号查询账号信息失败,本次返回该用户为非管理员身份：userId:{}", userId, e);
            return false;
        }
        return result;
    }




    /**
     * 检查是否拥有某个资源权限
     *
     * @param resourceCode
     * @return
     */
    public static boolean checkResource(String resourceCode) {
        boolean result = false;
        PlatformAuthService platformAuthService = SpringContextUtils.getBean(PlatformAuthService.class);
        List<String> resourceList = platformAuthService.getResourceList();
        if (CollectionUtils.isNotEmpty(resourceList)) {
            result = resourceList.contains(resourceCode);
        }
        return result;
    }

    /**
     * 判断是否具备多模态权限
     * @return
     */
    public static boolean checkSupportMultiModelRecall() {
        boolean supportMultiModelRecall = false;
        try {
            RequestInfo requestInfo = RequestContext.getAndCheck();
            String tenantId = RequestContext.getTenantId();
            String userId = RequestContext.getUserId();
            if (StringUtils.isNotBlank(requestInfo.getAssociatedUserid())) {
                userId = requestInfo.getAssociatedUserid();
            }
            log.info("检查用户是否具备多模态权限：tenantId:{},userId:{},权限编码：{}", tenantId, userId, Constants.MULTIMODAL_RECALL);

            supportMultiModelRecall = UserInfoUtils.checkResource(Constants.MULTIMODAL_RECALL);
        } catch (Exception e) {
            log.error("查询多模态资源信息失败，错误将忽略，默认没有多模态问答权限", e);
        }
        return supportMultiModelRecall;
    }


}
